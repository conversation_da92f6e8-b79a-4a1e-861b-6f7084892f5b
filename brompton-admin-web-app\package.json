{"name": "brompton-admin-web-app", "version": "0.2.0", "private": true, "engines": {"node": "16.15.0"}, "scripts": {"dev": "next dev", "build": "next build", "build:uat": "env-cmd -f .env.uat next build", "build:dev": "env-cmd -f .env.dev next build", "build:prod": "env-cmd -f .env.prod next build", "start": "next start -p 8080", "lint": "next lint", "test": "jest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "export": "next build && next export", "format": "prettier --write \"**/*.{js,jsx,ts,tsx}\" \"!node_modules/**\" \"!out/**\" \"!.next/**\" \"!.yalc/**\""}, "dependencies": {"@emotion/cache": "latest", "@emotion/react": "latest", "@emotion/server": "latest", "@emotion/styled": "latest", "@fontsource/roboto": "^4.5.8", "@hookform/resolvers": "^2.9.11", "@joint/core": "^4.0.4", "@material-ui/core": "latest", "@mui/icons-material": "latest", "@mui/lab": "^5.0.0-alpha.124", "@mui/material": "latest", "@mui/x-data-grid": "^6.18.5", "@mui/x-date-pickers": "^6.16.2", "@openobserve/browser-logs": "^0.2.11", "@openobserve/browser-rum": "^0.2.11", "@projectstorm/react-canvas-core": "^7.0.2", "@projectstorm/react-diagrams": "^7.0.3", "@reduxjs/toolkit": "^1.9.7", "@types/js-cookie": "^3.0.5", "@types/mathjs": "^9.4.2", "@types/node": "20.8.6", "@types/react": "18.2.28", "@types/react-dom": "18.2.13", "axios": "^1.3.2", "dayjs": "^1.11.10", "eslint": "8.51.0", "eslint-config-next": "13.5.5", "eslint-plugin-prettier": "^4.2.1", "js-cookie": "^3.0.5", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "mathjs": "^13.0.0", "next": "latest", "papaparse": "^5.4.1", "plotly.js": "^2.26.2", "plotly.js-dist-min": "^2.35.2", "react": "latest", "react-date-range": "^2.0.1", "react-date-time-range-picker": "link:../react-date-time-range-picker/dist", "react-dom": "latest", "react-draggable": "^4.4.6", "react-grid-layout": "^1.4.4", "react-hook-form": "^7.43.1", "react-inlinesvg": "^3.0.1", "react-plotly.js": "^2.6.0", "react-redux": "^8.1.3", "redux-persist": "^6.0.0", "typescript": "5.2.2", "xlsx": "^0.18.5", "yup": "^1.0.0"}, "devDependencies": {"@playwright/test": "^1.42.1", "@storybook/addon-essentials": "^7.4.6", "@storybook/addon-interactions": "^7.4.6", "@storybook/addon-links": "^7.4.6", "@storybook/addon-onboarding": "^1.0.8", "@storybook/blocks": "^7.4.6", "@storybook/nextjs": "^7.4.6", "@storybook/react": "^7.4.6", "@storybook/testing-library": "^0.2.2", "@testing-library/dom": "^9.3.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/node": "latest", "@types/papaparse": "^5.3.9", "@types/react": "latest", "@types/react-dom": "latest", "@types/react-grid-layout": "^1.3.5", "@types/react-plotly.js": "^2.6.1", "@typescript-eslint/eslint-plugin": "^5.51.0", "@typescript-eslint/parser": "^5.0.0", "env-cmd": "^10.1.0", "eslint": "latest", "eslint-config-next": "latest", "eslint-config-prettier": "^8.6.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.6.15", "jest": "~29.5.0", "jest-environment-jsdom": "^29.5.0", "prettier": "^2.8.4", "storybook": "^7.4.6", "typescript": "latest"}, "packageManager": "yarn@3.6.1"}