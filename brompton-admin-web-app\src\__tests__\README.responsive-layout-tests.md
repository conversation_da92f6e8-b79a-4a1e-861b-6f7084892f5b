# Responsive Layout Test Suite

This document describes the comprehensive test suite for the responsive layout functionality implemented in the Brompton Admin Web App.

## Overview

The responsive layout feature allows users to switch between desktop and mobile layouts for dashboard widgets. The test suite covers all aspects of this functionality, from individual components to complete integration workflows.

## Test Files Structure

```
src/
├── components/dashboard/__tests__/
│   ├── ResponsiveLayoutToggle.test.tsx
│   └── TopPanel.responsive.test.tsx
├── redux/
│   ├── api/__tests__/
│   │   └── dashboardApi.responsive.test.ts
│   ├── selectors/__tests__/
│   │   └── widgetSelectors.responsive.test.ts
│   └── slices/__tests__/
│       └── dashboardSlice.responsive.test.ts
├── types/__tests__/
│   └── dashboard.responsive.test.ts
└── __tests__/
    ├── responsive-layout.integration.test.ts
    └── README.responsive-layout-tests.md
```

## Test Categories

### 1. Component Tests

#### ResponsiveLayoutToggle.test.tsx
Tests the responsive layout toggle component:
- Rendering desktop and mobile buttons
- Correct selection state display
- User interaction handling
- Disabled state behavior
- Accessibility attributes

#### TopPanel.responsive.test.tsx
Tests integration of ResponsiveLayoutToggle in TopPanel:
- Conditional rendering based on route
- Integration with other TopPanel controls
- Proper spacing and layout

### 2. Redux Tests

#### dashboardSlice.responsive.test.ts
Tests Redux slice actions and reducers:
- `setDesktopMobileMode` action
- `updateLayout` action with responsive layouts
- `setWidgetsLayout` action
- `addWidget` action with responsive layouts
- `setNewDashboard` action initialization
- State consistency across operations

#### widgetSelectors.responsive.test.ts
Tests Redux selectors:
- `getDesktopMobileMode` selector
- `getResponsiveLayouts` selector
- `getCurrentLayoutMode` selector
- Selector memoization
- Edge cases and defaults

#### dashboardApi.responsive.test.ts
Tests API transformation logic:
- Dashboard data parsing with responsive layouts
- Backward compatibility with legacy data
- Layout selection based on current mode
- Default value handling

### 3. Type Tests

#### dashboard.responsive.test.ts
Tests TypeScript type definitions:
- DashboardState type extensions
- Optional property handling
- Layout type compatibility
- Backward compatibility
- Type safety enforcement

### 4. Integration Tests

#### responsive-layout.integration.test.ts
Tests complete workflows:
- Desktop to mobile switching
- Layout modifications in different modes
- Widget addition in different modes
- Multiple mode switches
- State consistency
- Edge case handling

## Running the Tests

### Run All Responsive Layout Tests
```bash
npm test -- --testPathPattern="responsive"
```

### Run Specific Test Categories

#### Component Tests
```bash
npm test -- src/components/dashboard/__tests__/ResponsiveLayoutToggle.test.tsx
npm test -- src/components/dashboard/__tests__/TopPanel.responsive.test.tsx
```

#### Redux Tests
```bash
npm test -- src/redux/slices/__tests__/dashboardSlice.responsive.test.ts
npm test -- src/redux/selectors/__tests__/widgetSelectors.responsive.test.ts
npm test -- src/redux/api/__tests__/dashboardApi.responsive.test.ts
```

#### Type Tests
```bash
npm test -- src/types/__tests__/dashboard.responsive.test.ts
```

#### Integration Tests
```bash
npm test -- src/__tests__/responsive-layout.integration.test.ts
```

### Run Tests with Coverage
```bash
npm test -- --coverage --testPathPattern="responsive"
```

## Test Scenarios Covered

### User Workflows
1. **Basic Mode Switching**: User clicks toggle to switch between desktop and mobile
2. **Layout Modification**: User drags/resizes widgets in each mode
3. **Widget Addition**: User adds widgets in different modes
4. **Mode Persistence**: Layout changes persist when switching modes
5. **Dashboard Creation**: New dashboards initialize with responsive layouts

### Edge Cases
1. **Missing Responsive Layouts**: Backward compatibility with legacy dashboards
2. **Empty Layouts**: Handling of dashboards with no widgets
3. **Invalid Mode Values**: Graceful handling of unexpected mode values
4. **Undefined State**: Proper defaults when state is undefined

### Error Scenarios
1. **Malformed Data**: API returns invalid responsive layout data
2. **Missing Properties**: Dashboard data missing responsive layout properties
3. **Type Mismatches**: Incorrect data types in responsive layout structure

## Test Data

### Mock Layouts
```typescript
const mockDesktopLayout: Layout[] = [
  { i: '1', x: 0, y: 0, w: 6, h: 4 },
  { i: '2', x: 6, y: 0, w: 6, h: 4 },
];

const mockMobileLayout: Layout[] = [
  { i: '1', x: 0, y: 0, w: 12, h: 4 },
  { i: '2', x: 0, y: 4, w: 12, h: 4 },
];
```

### Mock Dashboard State
```typescript
const mockDashboardState = {
  desktopMobile: 0,
  widget: {
    widgets: [...],
    widgetLayout: mockDesktopLayout,
    deleteWidgets: [],
    lastWidgetId: 2,
  },
  responsiveLayouts: {
    desktop: { widgetLayout: mockDesktopLayout },
    mobile: { widgetLayout: mockMobileLayout },
  },
};
```

## Assertions and Expectations

### State Consistency
- Current layout matches active mode
- Responsive layouts are properly maintained
- Widget data remains consistent across modes

### User Interface
- Correct button selection states
- Proper accessibility attributes
- Responsive component rendering

### Data Integrity
- Layout data structure validity
- Type safety compliance
- Backward compatibility maintenance

## Debugging Test Failures

### Common Issues
1. **Mock Setup**: Ensure all required mocks are properly configured
2. **State Initialization**: Verify initial state matches expected structure
3. **Async Operations**: Handle async state updates in integration tests
4. **Type Errors**: Check TypeScript type definitions match implementation

### Debug Commands
```bash
# Run tests in debug mode
npm test -- --verbose --testPathPattern="responsive"

# Run specific test with detailed output
npm test -- --verbose ResponsiveLayoutToggle.test.tsx

# Run tests in watch mode for development
npm test -- --watch --testPathPattern="responsive"
```

## Maintenance

### Adding New Tests
1. Follow existing naming conventions
2. Use appropriate test categories
3. Include both positive and negative test cases
4. Add integration tests for new workflows

### Updating Tests
1. Update tests when modifying responsive layout functionality
2. Ensure backward compatibility tests remain valid
3. Add new edge cases as they are discovered
4. Keep mock data synchronized with actual data structures

## Coverage Goals

- **Component Tests**: 100% line coverage for ResponsiveLayoutToggle
- **Redux Tests**: 100% coverage for responsive layout actions and selectors
- **Integration Tests**: Cover all major user workflows
- **Type Tests**: Verify all type definitions and edge cases

## Dependencies

### Test Libraries
- Jest: Test runner and assertion library
- React Testing Library: Component testing utilities
- Redux Toolkit: State management testing
- TypeScript: Type checking and compilation

### Mock Requirements
- Next.js router mocking
- Redux store configuration
- Material-UI theme provider
- Custom hook mocking
