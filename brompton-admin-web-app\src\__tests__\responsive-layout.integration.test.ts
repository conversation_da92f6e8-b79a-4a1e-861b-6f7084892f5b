import { configureStore } from '@reduxjs/toolkit';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { Layout } from 'react-grid-layout';

/**
 * Integration tests for the responsive layout functionality
 * Tests the complete flow from user interaction to state updates
 */
describe('Responsive Layout Integration Tests', () => {
  let store: ReturnType<typeof configureStore>;

  const mockDesktopLayout: Layout[] = [
    { i: '1', x: 0, y: 0, w: 6, h: 4 },
    { i: '2', x: 6, y: 0, w: 6, h: 4 },
    { i: '3', x: 0, y: 4, w: 12, h: 2 },
  ];

  const mockMobileLayout: Layout[] = [
    { i: '1', x: 0, y: 0, w: 12, h: 4 },
    { i: '2', x: 0, y: 4, w: 12, h: 4 },
    { i: '3', x: 0, y: 8, w: 12, h: 2 },
  ];

  beforeEach(() => {
    store = configureStore({
      reducer: {
        dashboard: dashboardSlice.reducer,
      },
      preloadedState: {
        dashboard: {
          ...dashboardSlice.getInitialState(),
          desktopMobile: 0,
          widget: {
            widgets: [
              { id: '1', type: 'stats', settings: {} },
              { id: '2', type: 'chart', settings: { chartType: 'bar', settings: {} } },
              { id: '3', type: 'kpi-table', settings: {} },
            ],
            widgetLayout: mockDesktopLayout,
            deleteWidgets: [],
            lastWidgetId: 3,
          },
          responsiveLayouts: {
            desktop: { widgetLayout: mockDesktopLayout },
            mobile: { widgetLayout: mockMobileLayout },
          },
        },
      },
    });
  });

  describe('Complete Responsive Layout Workflow', () => {
    it('should handle complete desktop to mobile switch workflow', () => {
      // Initial state - desktop mode
      let state = store.getState();
      expect(state.dashboard.desktopMobile).toBe(0);
      expect(state.dashboard.widget.widgetLayout).toEqual(mockDesktopLayout);

      // User modifies layout in desktop mode
      const modifiedDesktopLayout: Layout[] = [
        { i: '1', x: 2, y: 2, w: 8, h: 6 },
        { i: '2', x: 0, y: 0, w: 6, h: 2 },
        { i: '3', x: 6, y: 2, w: 6, h: 4 },
      ];

      store.dispatch(dashboardSlice.actions.updateLayout(modifiedDesktopLayout));

      state = store.getState();
      expect(state.dashboard.widget.widgetLayout).toEqual(modifiedDesktopLayout);
      expect(state.dashboard.responsiveLayouts?.desktop.widgetLayout).toEqual(modifiedDesktopLayout);
      expect(state.dashboard.isDirty).toBe(true);

      // User switches to mobile mode
      store.dispatch(dashboardSlice.actions.setDesktopMobileMode(1));

      state = store.getState();
      expect(state.dashboard.desktopMobile).toBe(1);
      expect(state.dashboard.widget.widgetLayout).toEqual(mockMobileLayout);
      expect(state.dashboard.responsiveLayouts?.desktop.widgetLayout).toEqual(modifiedDesktopLayout);
      expect(state.dashboard.responsiveLayouts?.mobile.widgetLayout).toEqual(mockMobileLayout);

      // User modifies layout in mobile mode
      const modifiedMobileLayout: Layout[] = [
        { i: '1', x: 0, y: 0, w: 12, h: 6 },
        { i: '2', x: 0, y: 6, w: 12, h: 3 },
        { i: '3', x: 0, y: 9, w: 12, h: 3 },
      ];

      store.dispatch(dashboardSlice.actions.updateLayout(modifiedMobileLayout));

      state = store.getState();
      expect(state.dashboard.widget.widgetLayout).toEqual(modifiedMobileLayout);
      expect(state.dashboard.responsiveLayouts?.mobile.widgetLayout).toEqual(modifiedMobileLayout);
      expect(state.dashboard.responsiveLayouts?.desktop.widgetLayout).toEqual(modifiedDesktopLayout);

      // User switches back to desktop mode
      store.dispatch(dashboardSlice.actions.setDesktopMobileMode(0));

      state = store.getState();
      expect(state.dashboard.desktopMobile).toBe(0);
      expect(state.dashboard.widget.widgetLayout).toEqual(modifiedDesktopLayout);
      expect(state.dashboard.responsiveLayouts?.mobile.widgetLayout).toEqual(modifiedMobileLayout);
    });

    it('should handle adding widgets in different modes', () => {
      // Add widget in desktop mode
      const newDesktopLayout: Layout[] = [
        ...mockDesktopLayout,
        { i: '4', x: 0, y: 6, w: 6, h: 4 },
      ];

      store.dispatch(dashboardSlice.actions.addWidget({
        widgetMode: 'dashboard',
        type: 'stats',
        layout: newDesktopLayout,
        layoutItem: { i: 'a', x: 0, y: 6, w: 2, h: 2 },
      }));

      let state = store.getState();
      expect(state.dashboard.widget.widgetLayout).toEqual(newDesktopLayout);
      expect(state.dashboard.responsiveLayouts?.desktop.widgetLayout).toEqual(newDesktopLayout);
      expect(state.dashboard.responsiveLayouts?.mobile.widgetLayout).toEqual(mockMobileLayout);

      // Switch to mobile and add another widget
      store.dispatch(dashboardSlice.actions.setDesktopMobileMode(1));

      const newMobileLayout: Layout[] = [
        ...mockMobileLayout,
        { i: '5', x: 0, y: 10, w: 12, h: 4 },
      ];

      store.dispatch(dashboardSlice.actions.addWidget({
        widgetMode: 'dashboard',
        type: 'chart-bar',
        layout: newMobileLayout,
        layoutItem: { i: 'a', x: 0, y: 10, w: 2, h: 2 },
      }));

      state = store.getState();
      expect(state.dashboard.widget.widgetLayout).toEqual(newMobileLayout);
      expect(state.dashboard.responsiveLayouts?.mobile.widgetLayout).toEqual(newMobileLayout);
      expect(state.dashboard.responsiveLayouts?.desktop.widgetLayout).toEqual(newDesktopLayout);
    });

    it('should handle new dashboard creation with responsive layouts', () => {
      // Modify current state
      store.dispatch(dashboardSlice.actions.updateLayout([
        { i: '1', x: 5, y: 5, w: 2, h: 2 },
      ]));

      // Create new dashboard
      store.dispatch(dashboardSlice.actions.setNewDashboard());

      const state = store.getState();
      expect(state.dashboard.desktopMobile).toBe(0);
      expect(state.dashboard.widget.widgets).toEqual([]);
      expect(state.dashboard.widget.widgetLayout).toEqual([]);
      expect(state.dashboard.responsiveLayouts).toEqual({
        desktop: { widgetLayout: [] },
        mobile: { widgetLayout: [] },
      });
    });

    it('should preserve layout changes when switching modes multiple times', () => {
      const iterations = 3;
      const layoutChanges: { desktop: Layout[]; mobile: Layout[] }[] = [];

      for (let i = 0; i < iterations; i++) {
        // Modify desktop layout
        const desktopLayout: Layout[] = [
          { i: '1', x: i, y: i, w: 6 + i, h: 4 + i },
        ];
        store.dispatch(dashboardSlice.actions.updateLayout(desktopLayout));

        // Switch to mobile
        store.dispatch(dashboardSlice.actions.setDesktopMobileMode(1));

        // Modify mobile layout
        const mobileLayout: Layout[] = [
          { i: '1', x: 0, y: i * 2, w: 12, h: 4 + i },
        ];
        store.dispatch(dashboardSlice.actions.updateLayout(mobileLayout));

        layoutChanges.push({ desktop: desktopLayout, mobile: mobileLayout });

        // Switch back to desktop
        store.dispatch(dashboardSlice.actions.setDesktopMobileMode(0));

        const state = store.getState();
        expect(state.dashboard.widget.widgetLayout).toEqual(desktopLayout);
        expect(state.dashboard.responsiveLayouts?.desktop.widgetLayout).toEqual(desktopLayout);
        expect(state.dashboard.responsiveLayouts?.mobile.widgetLayout).toEqual(mobileLayout);
      }

      // Verify final state maintains all changes
      const finalState = store.getState();
      const lastChange = layoutChanges[iterations - 1];
      expect(finalState.dashboard.responsiveLayouts?.desktop.widgetLayout).toEqual(lastChange.desktop);
      expect(finalState.dashboard.responsiveLayouts?.mobile.widgetLayout).toEqual(lastChange.mobile);
    });

    it('should handle edge cases gracefully', () => {
      // Test with undefined responsiveLayouts
      store.dispatch({
        type: 'dashboard/setState',
        payload: {
          ...store.getState().dashboard,
          responsiveLayouts: undefined,
        },
      });

      // Should not crash when switching modes
      store.dispatch(dashboardSlice.actions.setDesktopMobileMode(1));
      
      let state = store.getState();
      expect(state.dashboard.desktopMobile).toBe(1);

      // Test with empty layouts
      store.dispatch(dashboardSlice.actions.updateLayout([]));
      
      state = store.getState();
      expect(state.dashboard.widget.widgetLayout).toEqual([]);
    });
  });

  describe('State Consistency', () => {
    it('should maintain state consistency across all responsive layout operations', () => {
      const operations = [
        () => store.dispatch(dashboardSlice.actions.setDesktopMobileMode(1)),
        () => store.dispatch(dashboardSlice.actions.updateLayout([{ i: '1', x: 0, y: 0, w: 12, h: 8 }])),
        () => store.dispatch(dashboardSlice.actions.setDesktopMobileMode(0)),
        () => store.dispatch(dashboardSlice.actions.setWidgetsLayout([{ i: '1', x: 3, y: 3, w: 6, h: 6 }])),
      ];

      operations.forEach((operation, index) => {
        operation();
        const state = store.getState();
        
        // Verify state consistency
        expect(state.dashboard.desktopMobile).toBeGreaterThanOrEqual(0);
        expect(state.dashboard.desktopMobile).toBeLessThanOrEqual(1);
        expect(Array.isArray(state.dashboard.widget.widgetLayout)).toBe(true);
        expect(state.dashboard.responsiveLayouts).toBeDefined();
        expect(state.dashboard.responsiveLayouts?.desktop).toBeDefined();
        expect(state.dashboard.responsiveLayouts?.mobile).toBeDefined();
        
        // Verify current layout matches the active mode
        const currentMode = state.dashboard.desktopMobile === 0 ? 'desktop' : 'mobile';
        const expectedLayout = state.dashboard.responsiveLayouts?.[currentMode]?.widgetLayout;
        expect(state.dashboard.widget.widgetLayout).toEqual(expectedLayout);
      });
    });
  });
});
