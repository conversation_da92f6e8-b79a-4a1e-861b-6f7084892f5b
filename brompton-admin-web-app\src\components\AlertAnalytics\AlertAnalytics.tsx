import {
  Autocomplete,
  Box,
  Card,
  CircularProgress,
  Container,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import { useMemo, useState } from 'react';
import Plot from 'react-plotly.js';
import useAlertAnalytics from '~/hooks/useAlertAnalytics';
import { theme } from '~/pages/_app';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import HomeButton from '../common/Home/HomeButton';
import Loader from '../common/Loader';
import PageName from '../common/PageName/PageName';
import AnalyticsPie, { AVAIL_FILTER_OPTIONS, FILTER_OPTIONS } from './AnalyticsPie';
type AssetOption = {
  label: string;
  id: number;
};

type MeasureOption = {
  label: string;
  measurement_id: number;
};

const AlertAnalytics = () => {
  const [snackbarState, showErrorAlert] = useSnackbar();
  const [interval, setInterval] = useState<'daily' | 'weekly' | 'monthly'>('weekly');
  const [selectedAsset, setSelectedAsset] = useState<AssetOption | null>(null);
  const [selectedMeasure, setSelectedMeasure] = useState<MeasureOption | null>(null);
  const [selectedDay, setSelectedDay] = useState<string | null>(null);
  const [drilledFilters, setDrilledFilter] = useState<
    Record<number, { option: AVAIL_FILTER_OPTIONS; label: string } | null>
  >({
    0: null,
    1: null,
  });
  const [selectedFilters, setSelectedFilters] = useState<Record<number, AVAIL_FILTER_OPTIONS>>(
    () => {
      // Assign a default filter for each chart from FILTER_OPTIONS
      const initialFilters: Record<number, AVAIL_FILTER_OPTIONS> = {};
      FILTER_OPTIONS.forEach((filter, index) => {
        if (index < 3) {
          initialFilters[index] = filter;
        }
      });
      return initialFilters;
    },
  );

  const {
    aggPeriods,
    assetTypeListData,
    measurementTypes,
    thresholdTypes,
    isAssetLoading,
    isAssetReloading,
    assetTypesWithPath,
    alertAnalyticsDrill,
    isDrilledFetching,
    isMeasurementLoading,
    isMeasurementFetching,
    alertAnalytics,
    isError,
    isFetching,
    measurementOptions,
    drilldownDataInfo,
  } = useAlertAnalytics({ selectedDay, selectedAsset, selectedMeasure, interval, showErrorAlert });

  // Handle filter change for a specific chart
  const handleFilterChange = (index: number, newFilter: AVAIL_FILTER_OPTIONS) => {
    setSelectedFilters((prevFilters) => ({
      ...prevFilters,
      [index]: newFilter,
    }));
  };

  const chartData = useMemo(() => {
    if (!alertAnalytics || !Array.isArray(alertAnalytics)) {
      return { counts: [], durations: [], labels: [] };
    }

    const aggregatedData: Record<string, { totalCount: number; totalDuration: number }> = {};

    const getPeriodKey = (dateString: string, interval: 'daily' | 'weekly' | 'monthly'): string => {
      const date = new Date(dateString);

      if (interval === 'daily') {
        const formattedDate = date.toLocaleDateString('en-US', {
          timeZone: 'UTC',
          month: 'short',
          day: 'numeric',
          year: 'numeric',
        });
        return formattedDate;
      } else if (interval === 'weekly') {
        const day = date.getUTCDay();
        const diff = date.getUTCDate() - day; // Get the previous Sunday
        const startOfWeek = new Date(Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), diff));
        const formattedDate = startOfWeek.toLocaleDateString('en-US', {
          timeZone: 'UTC',
          month: 'short',
          day: 'numeric',
          year: 'numeric',
        });

        return formattedDate;
      } else if (interval === 'monthly') {
        const startOfMonth = new Date(Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), 1));
        const formattedDate = startOfMonth.toLocaleDateString('en-US', {
          timeZone: 'UTC',
          month: 'short',
          year: 'numeric',
        });

        return formattedDate;
      }

      const defaultFormattedDate = date.toLocaleDateString('en-US', {
        month: 'short',
        year: 'numeric',
      });
      return defaultFormattedDate;
    };

    alertAnalytics.forEach((entry) => {
      const period = getPeriodKey(entry.period_start, interval);
      const count = parseInt(entry.excursion_count, 10);
      const duration = parseFloat(entry.total_duration_seconds);

      if (!aggregatedData[period]) {
        aggregatedData[period] = { totalCount: 0, totalDuration: 0 };
      }
      aggregatedData[period].totalCount += isNaN(count) ? 0 : count;
      aggregatedData[period].totalDuration += isNaN(duration) ? 0 : duration;
    });

    const labels = Object.keys(aggregatedData).sort((a, b) => {
      return new Date(a).getTime() - new Date(b).getTime();
    });

    const counts = labels.map((label) => aggregatedData[label].totalCount);
    const durations = labels.map((label) => aggregatedData[label].totalDuration);

    return { labels, counts, durations };
  }, [alertAnalytics]);

  const handleAssetChange = (event: React.SyntheticEvent, newValue: AssetOption | null) => {
    setSelectedAsset(newValue);
    setSelectedMeasure(null);
    setSelectedDay(null);
  };

  const handleMeasurementChange = (event: React.SyntheticEvent, newValue: MeasureOption | null) => {
    setSelectedMeasure(newValue);
    setSelectedDay(null);
  };

  const getCounts = () => {
    return alertAnalyticsDrill?.map((entry) => parseInt(entry.excursion_count, 10)) ?? [];
  };

  const getLabels = () => {
    return alertAnalyticsDrill?.map((entry) => formatDate(entry.period_start)) ?? [];
  };

  const getDurations = () => {
    return alertAnalyticsDrill?.map((entry) => parseFloat(entry.total_duration_seconds)) ?? [];
  };

  const getAggsGroupedCounts = () => {
    return (
      alertAnalyticsDrill?.reduce<Record<number, number>>((acc, { agg }) => {
        acc[agg] = (acc[agg] || 0) + 1;
        return acc;
      }, {}) ?? {}
    );
  };
  const getStatesGroupedCounts = () => {
    return (
      alertAnalyticsDrill?.reduce<Record<string, number>>((acc, { state }) => {
        acc[state] = (acc[state] || 0) + 1;
        return acc;
      }, {}) ?? {}
    );
  };
  const getThresholdTypeGroupedCounts = () => {
    return (
      alertAnalyticsDrill?.reduce<Record<number, number>>((acc, { threshold_type }) => {
        acc[threshold_type] = (acc[threshold_type] || 0) + 1;
        return acc;
      }, {}) ?? {}
    );
  };
  const getMTypeGroupedCounts = () => {
    return (
      alertAnalyticsDrill?.reduce<Record<number, number>>((acc, { m_type }) => {
        acc[m_type] = (acc[m_type] || 0) + 1;
        return acc;
      }, {}) ?? {}
    );
  };

  const getAssetTypeGroupedCounts = () => {
    return (
      alertAnalyticsDrill?.reduce<Record<number, number>>((acc, { a_type }) => {
        acc[a_type] = (acc[a_type] || 0) + 1;
        return acc;
      }, {}) ?? {}
    );
  };

  const getEnabledDisabledGroupedCounts = () => {
    return (
      alertAnalyticsDrill?.reduce<Record<string, number>>(
        (acc, { enabled }) => {
          const key: 'Enabled' | 'Disabled' = enabled ? 'Enabled' : 'Disabled';
          acc[key] = (acc[key] || 0) + 1;
          return acc;
        },
        { Enabled: 0, Disabled: 0 }, // Ensure both keys exist, even if count is 0
      ) ?? { Enabled: 0, Disabled: 0 }
    );
  };

  const getTimeDurations = () => {
    return (
      alertAnalyticsDrill?.map((entry) => ({
        label: formatDate(entry.period_start),
        value: parseFloat(entry.total_duration_seconds) / 3600,
      })) ?? []
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    if (interval === 'monthly') {
      return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    } else if (interval === 'weekly') {
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      return `Week of ${weekStart.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      })}`;
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      });
    }
  };
  const drilldownData = useMemo(() => {
    if (!alertAnalyticsDrill || !Array.isArray(alertAnalyticsDrill)) {
      return {
        labels: [],
        counts: [],
        durations: [],
        timeDurations: [],
        aggs: {},
        state: {},
        enabled_disabled: {},
        threshold_Type: {},
        measure_type: {},
        asset_type: {},
      };
    }
    return {
      labels: getLabels(),
      counts: getCounts(),
      durations: getDurations(),
      timeDurations: getTimeDurations(),
      aggs: getAggsGroupedCounts(),
      state: getStatesGroupedCounts(),
      enabled_disabled: getEnabledDisabledGroupedCounts(),
      threshold_Type: getThresholdTypeGroupedCounts(),
      measure_type: getMTypeGroupedCounts(),
      asset_type: getAssetTypeGroupedCounts(),
    };
  }, [alertAnalyticsDrill, interval]);
  const getFilteredData = (
    data: typeof drilldownData,
    index: number,
    option: AVAIL_FILTER_OPTIONS,
  ) => {
    let filteredData = data;
    if (index > 0 && drilledFilters[0]) {
      const drilledOption = drilledFilters[0].option as keyof typeof filteredData;
      const drilledLabel = drilledFilters[0].label;
      // console.log(drilledOption, drilledLabel, alertAnalyticsDrill);
      // Apply first chart filter to the second and third chart
      filteredData = {
        ...filteredData,
        [option]: Object.fromEntries(
          Object.entries(filteredData[drilledOption] ?? {}).filter(([key]) => key === drilledLabel),
        ),
      };
    }

    if (index > 1 && drilledFilters[1]) {
      // Apply second chart filter only to the third chart
      const drilledOption = drilledFilters[1].option as keyof typeof filteredData;
      const drilledLabel = drilledFilters[1].label;
      console.log(
        drilledOption,
        drilledLabel,
        Object.fromEntries(
          Object.entries(filteredData[drilledOption] ?? {}).filter(([key]) => key === drilledLabel),
        ),
      );
      // Apply first chart filter to the second and third chart
      filteredData = {
        ...filteredData,
        [option]: Object.fromEntries(
          Object.entries(filteredData[drilledOption] ?? {}).filter(([key]) => key === drilledLabel),
        ),
      };
    }

    return filteredData;
  };

  return (
    <Container
      sx={{
        maxWidth: '100%',
        width: '100%',
        '@media (min-width: 1200px)': { width: '100%', maxWidth: '100%' },
      }}
    >
      <AlertSnackbar {...snackbarState} />

      <Box py={2}>
        <PageName name="Alert Analytics" />
      </Box>

      <Box sx={{ my: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
        <FormControl fullWidth>
          <InputLabel id="interval-label">Interval</InputLabel>
          <Select
            labelId="interval-label"
            label="Interval"
            fullWidth
            value={interval}
            onChange={(e) => {
              setInterval(e.target.value as 'daily' | 'weekly' | 'monthly');
              setSelectedDay(null);
            }}
          >
            <MenuItem value="daily">Daily</MenuItem>
            <MenuItem value="weekly">Weekly</MenuItem>
            <MenuItem value="monthly">Monthly</MenuItem>
          </Select>
        </FormControl>

        <FormControl fullWidth>
          <Autocomplete
            fullWidth
            id={`alert-analytics-assets`}
            loading={isAssetReloading}
            options={
              !isAssetReloading
                ? assetTypesWithPath.map((asset) => ({
                    label: asset.label,
                    id: asset.id,
                  }))
                : []
            }
            getOptionLabel={(option) => option.label}
            onChange={handleAssetChange}
            value={selectedAsset}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Asset"
                variant="outlined"
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {isAssetLoading ? <CircularProgress color="inherit" size={20} /> : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
          />
        </FormControl>

        <FormControl fullWidth>
          <Autocomplete
            disabled={!selectedAsset}
            fullWidth
            id={`alert-analytics-measures`}
            loading={isMeasurementFetching}
            options={measurementOptions}
            getOptionLabel={(option) => option.label}
            onChange={handleMeasurementChange}
            value={selectedMeasure}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Measurement"
                variant="outlined"
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {isMeasurementLoading ? <CircularProgress color="inherit" size={20} /> : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
          />
        </FormControl>
      </Box>

      <Box sx={{ mt: 4 }}>
        {isFetching ? (
          <Loader style={{ height: '70vh' }} />
        ) : isError ? (
          <Box>Error loading data</Box>
        ) : (
          <>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Box component={Card} sx={{ width: '100%' }}>
                  <Plot
                    onClick={(event) => {
                      const selectedDate = event.points[0].x
                        ? new Date(event.points[0].x)
                            .toLocaleDateString('en-GB', {
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit',
                            })
                            .split('/')
                            .reverse()
                            .join('-')
                        : null;
                      setSelectedDay(selectedDate);
                    }}
                    data={[
                      {
                        x: chartData.labels,
                        y: chartData.counts,
                        type: 'bar',
                        name: 'Counts',
                      },
                    ]}
                    layout={{
                      title: 'Counts',
                      xaxis: { title: 'Period' },
                      yaxis: { title: 'Value' },
                      autosize: true,
                    }}
                    style={{ width: '100%' }}
                    config={{
                      displayModeBar: false,
                      responsive: true,
                    }}
                  />
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box component={Card} sx={{ width: '100%' }}>
                  <Plot
                    onClick={(event) => {
                      const selectedDate = event.points[0].x
                        ? new Date(event.points[0].x)
                            .toLocaleDateString('en-GB', {
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit',
                            })
                            .split('/')
                            .reverse()
                            .join('-')
                        : null;
                      setSelectedDay(selectedDate);
                    }}
                    data={[
                      {
                        x: chartData.labels,
                        y: chartData.durations,
                        type: 'bar',
                        name: 'Durations',
                        marker: { color: theme.palette.primary.main },
                      },
                    ]}
                    layout={{
                      title: 'Durations',
                      xaxis: { title: 'Period' },
                      yaxis: { title: 'Seconds' },
                      autosize: true,
                    }}
                    style={{ width: '100%' }}
                    config={{
                      displayModeBar: false,
                      responsive: true,
                    }}
                  />
                </Box>
              </Grid>
            </Grid>

            <Box sx={{ width: '100%', my: 3 }}>
              {isDrilledFetching ? (
                <Loader style={{ width: '100%' }} />
              ) : (
                <>
                  {selectedDay &&
                    (() => {
                      const allEmpty = [
                        drilldownDataInfo.counts,
                        drilldownDataInfo.durations,
                        drilldownDataInfo.timeDurations.map((d) => d.value),
                        // drilldownData.counts,
                        // drilldownData.durations,
                        // drilldownData.timeDurations.map((d) => d.value),
                      ].every((values) => values.length === 0);

                      if (allEmpty) {
                        return (
                          <Box sx={{ textAlign: 'center', width: '100%', my: 2 }}>
                            <Typography variant="body1">
                              No data available for the selected time period:{' '}
                              {selectedDay
                                ? new Date(selectedDay).toLocaleDateString('en-GB', {
                                    day: '2-digit',
                                    month: '2-digit',
                                    year: 'numeric',
                                  })
                                : ''}
                            </Typography>
                          </Box>
                        );
                      }
                      return (
                        <Grid container spacing={2}>
                          {Object.values(selectedFilters).map((filter, index) => {
                            const filterdData = drilldownData;
                            const chart: {
                              labels: string[];
                              values: number[];
                              ids?: string[];
                            } = {
                              labels: [],
                              values: [],
                              ids: [],
                            };

                            switch (filter) {
                              case 'aggs': {
                                const aggsData = filterdData.aggs || {}; // Ensure `aggs` is always an object
                                const aggLabelsMap = new Map(
                                  (aggPeriods?.items ?? []).map((period) => [
                                    period.id.toString(),
                                    period.label,
                                  ]),
                                );
                                Object.keys(aggsData).forEach((agg) => {
                                  const label = aggLabelsMap.get(agg) ?? '';
                                  chart.labels.push(label);
                                });
                                chart.values = Object.values(aggsData);
                                break;
                              }
                              case 'threshold_Type': {
                                const thresholdData = filterdData.threshold_Type || {}; // Ensure `thresholdType` is always an object
                                const thresholdLabelsMap = new Map(
                                  (thresholdTypes?.items ?? []).map((period) => [
                                    period.id.toString(),
                                    period.threshold,
                                  ]),
                                );
                                chart.labels = Object.keys(thresholdData).map(
                                  (th) => thresholdLabelsMap.get(th) ?? '',
                                ); // Fast lookup
                                chart.values = Object.values(thresholdData);
                                break;
                              }
                              case 'state': {
                                const stateData = filterdData.state || {}; // Ensure `thresholdType` is always an object
                                chart.labels = Object.keys(stateData);
                                chart.values = Object.values(stateData);
                                break;
                              }
                              case 'measure_type': {
                                const measureTypeData = filterdData.measure_type || {}; // Ensure `measure_type` is always an object
                                const measureTypeLabelsMap = new Map(
                                  (measurementTypes ?? []).map((type) => [
                                    type.id.toString(),
                                    type.name,
                                  ]),
                                );
                                chart.labels = Object.keys(measureTypeData).map(
                                  (m_type) => measureTypeLabelsMap.get(m_type) ?? '',
                                ); // Fast lookup
                                chart.values = Object.values(measureTypeData); // Ensure correct value source
                                break;
                              }
                              case 'asset_type': {
                                const assetTypeData = filterdData.asset_type || {}; // Ensure `measure_type` is always an object
                                const assetTypeLabelMap = new Map(
                                  (assetTypeListData ?? []).map((type) => [
                                    type.id.toString(),
                                    type.name,
                                  ]),
                                );
                                chart.labels = Object.keys(assetTypeData).map(
                                  (m_type) => assetTypeLabelMap.get(m_type) ?? '',
                                ); // Fast lookup
                                chart.values = Object.values(assetTypeData); // Ensure correct value source
                                break;
                              }
                              default:
                                break;
                            }
                            return (
                              <Grid
                                item
                                xs={12}
                                md={4}
                                key={index}
                                sx={{ display: 'flex', justifyContent: 'center', width: 'inherit' }}
                              >
                                <AnalyticsPie
                                  selectedFilters={selectedFilters}
                                  chart={chart}
                                  selectedFilter={selectedFilters[index]}
                                  availableFilters={FILTER_OPTIONS} // Show all filters
                                  onFilterChange={(newFilter) => {
                                    handleFilterChange(index, newFilter);
                                  }}
                                  drillFilter={
                                    index <= 1
                                      ? (filter) => {
                                          setDrilledFilter({
                                            ...drilledFilters,
                                            [index]: {
                                              option: selectedFilters[index],
                                              label: filter,
                                            },
                                          });
                                        }
                                      : undefined
                                  }
                                />
                              </Grid>
                            );
                          })}
                        </Grid>
                      );
                    })()}
                </>
              )}
            </Box>
          </>
        )}
      </Box>
    </Container>
  );
};

export default AlertAnalytics;
