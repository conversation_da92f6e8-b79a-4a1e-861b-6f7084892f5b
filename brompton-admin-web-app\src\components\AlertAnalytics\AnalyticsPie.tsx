import { Autocomplete, Box, Card, TextField } from '@mui/material';
import Plot from 'react-plotly.js';

type AlertAnalyticsProps = {
  selectedFilters: Record<number, AVAIL_FILTER_OPTIONS>;
  chart: {
    labels: string[];
    values: number[];
  };
  selectedFilter: AVAIL_FILTER_OPTIONS;
  availableFilters: string[]; // All filters are available
  onFilterChange: (newFilter: AVAIL_FILTER_OPTIONS) => void;
  drillFilter?: (filter: AVAIL_FILTER_OPTIONS) => void;
};
export type AVAIL_FILTER_OPTIONS =
  | 'threshold_Type'
  | 'aggs'
  | 'aggregate period'
  | 'state'
  | 'asset_type'
  | 'measure_type';
// | 'metrics';
export const FILTER_OPTIONS: AVAIL_FILTER_OPTIONS[] = [
  'state',
  'threshold_Type',
  'aggs',
  // 'aggregate period',
  'asset_type',
  'measure_type',
  // 'metrics',
];
const formatFilterLabel = (filter: AVAIL_FILTER_OPTIONS) => {
  return filter
    .replace(/_/g, ' ') // Replace underscores with spaces
    .replace(/^\w/, (c) => c.toUpperCase()); // Capitalize first letter
};
const AnalyticsPie = ({
  selectedFilters,
  chart,
  selectedFilter,
  availableFilters,
  onFilterChange,
  drillFilter,
}: AlertAnalyticsProps) => {
  return (
    <Box
      sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column', p: 2 }}
      component={Card}
    >
      <Autocomplete
        options={availableFilters}
        value={selectedFilter}
        onChange={(_, newValue) => newValue && onFilterChange(newValue as AVAIL_FILTER_OPTIONS)}
        renderInput={(params) => <TextField {...params} label="Select Filter" variant="outlined" />}
        size="small"
        getOptionLabel={(option) => formatFilterLabel(option as AVAIL_FILTER_OPTIONS)}
        disableClearable
        getOptionDisabled={(option) =>
          Object.values(selectedFilters).includes(option as AVAIL_FILTER_OPTIONS) &&
          option !== selectedFilter
        }
      />

      <Plot
        style={{ width: '100%', height: '100%', minHeight: 300 }}
        onClick={(event) => {
          if (event.points && event.points.length > 0 && drillFilter) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            const clickedLabel = event.points[0].label; // ✅ Get the clicked label
            drillFilter(clickedLabel as AVAIL_FILTER_OPTIONS);
          }
        }}
        data={[
          {
            labels: chart.labels,
            values: chart.values,
            type: 'pie',
            hole: 0.4,
          },
        ]}
        layout={{
          margin: { l: 50, r: 50, t: 50, b: 50 },
          showlegend: true,
        }}
        config={{
          responsive: true,
          displaylogo: false,
          displayModeBar: false,
          modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
        }}
      />
    </Box>
  );
};

export default AnalyticsPie;
