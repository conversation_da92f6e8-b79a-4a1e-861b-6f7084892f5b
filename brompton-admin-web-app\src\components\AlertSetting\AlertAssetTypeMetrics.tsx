import DeleteIcon from '@mui/icons-material/Delete';
import { Autocomplete, Box, Button, Card, IconButton, TextField } from '@mui/material';
import { useMemo } from 'react';
import { useGetAllBackOfficeAssetTypesQuery } from '~/redux/api/assetsApi';
import { AlertWidget } from '~/types/widgets';
import { mapListToOptions } from '~/utils/utils';
import AssetMetrics from './AssetMetrics';

type AlertAssetTypeMetricsProps = {
  settings: AlertWidget;
  handleSettingsChange: (value: ((prevState: AlertWidget) => AlertWidget) | AlertWidget) => void;
};

const AlertAssetTypeMetrics = ({ settings, handleSettingsChange }: AlertAssetTypeMetricsProps) => {
  const { data: assetTypeListData, isFetching: isAssetTypeLoading } =
    useGetAllBackOfficeAssetTypesQuery();

  const assetTypeListOptions = useMemo(
    () =>
      mapListToOptions(
        assetTypeListData?.map((item) => ({
          ...item,
          name: `${item.name}`,
          id: item.id,
        })) ?? [],
      ),
    [assetTypeListData],
  );

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ width: '100%', display: 'flex', justifyContent: 'end' }}>
        <Button
          color="primary"
          variant="contained"
          onClick={() => {
            handleSettingsChange((prev) => ({
              ...prev,
              assetTypeMetrics: [
                ...prev.assetTypeMetrics,
                {
                  assetType: '',
                  metrics: [],
                },
              ],
            }));
          }}
        >
          Add
        </Button>
      </Box>
      {settings.assetTypeMetrics.map((assetTypeMetric, index) => (
        <Box key={index} sx={{ width: '100%', mt: 2, mb: 2, p: 2 }} component={Card}>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <Autocomplete
              disablePortal
              id={`combo-box-${index}`}
              value={
                assetTypeListOptions.find((option) => option.id === assetTypeMetric.assetType) ||
                null
              }
              options={assetTypeListOptions ?? []}
              isOptionEqualToValue={(option, value) => option.id === value.id}
              loading={isAssetTypeLoading}
              sx={{ width: assetTypeMetric.assetType !== '' ? '50%' : '100%' }}
              onChange={(event, value) => {
                handleSettingsChange((prev) => {
                  const updatedMetrics = prev.assetTypeMetrics.map((metric, i) =>
                    i === index ? { ...metric, assetType: value?.id || '' } : metric,
                  );
                  return { ...prev, assetTypeMetrics: updatedMetrics };
                });
              }}
              renderInput={(params) => <TextField {...params} label="Asset Type" />}
            />
            {assetTypeMetric.assetType !== '' && (
              <AssetMetrics
                assetId={assetTypeMetric.assetType}
                selectedMetrics={assetTypeMetric.metrics} // ✅ Pass selected metrics
                onChange={(selectedMetrics) => {
                  handleSettingsChange((prev) => {
                    const updatedMetrics = prev.assetTypeMetrics.map((metric, i) =>
                      i === index ? { ...metric, metrics: selectedMetrics } : metric,
                    );
                    return { ...prev, assetTypeMetrics: updatedMetrics };
                  });
                }}
              />
            )}
            {settings.assetTypeMetrics.length > 1 && (
              <IconButton
                color="error"
                onClick={() => {
                  handleSettingsChange((prev) => ({
                    ...prev,
                    assetTypeMetrics: prev.assetTypeMetrics.filter((_, i) => i !== index),
                  }));
                }}
              >
                <DeleteIcon />
              </IconButton>
            )}
          </Box>
        </Box>
      ))}
    </Box>
  );
};

export default AlertAssetTypeMetrics;
