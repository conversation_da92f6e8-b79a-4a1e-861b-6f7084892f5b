import { Autocomplete, TextField } from '@mui/material';
import { useMemo } from 'react';
import { useGetAllBackOfficeAssetTypesMetricsQuery } from '~/redux/api/assetsApi';
import { mapListToOptions } from '~/utils/utils';

type AssetMetricsProps = {
  assetId: string;
  selectedMetrics: string[]; // ✅ Accept selected metrics
  onChange: (metrics: string[]) => void; // ✅ Callback for metric selection changes
};

const AssetMetrics = ({ assetId, selectedMetrics, onChange }: AssetMetricsProps) => {
  const { data: assetTypeMetrics, isFetching: isMetricsFetching } =
    useGetAllBackOfficeAssetTypesMetricsQuery({ assetId });

  const assetTypeMetricsListOptions = useMemo(
    () => mapListToOptions(assetTypeMetrics?.items ?? []),
    [assetTypeMetrics],
  );

  return (
    <Autocomplete
      multiple
      options={assetTypeMetricsListOptions ?? []}
      value={assetTypeMetricsListOptions.filter((option) => selectedMetrics.includes(option.id))} // ✅ Correctly set selected values
      isOptionEqualToValue={(option, value) => option.id === value.id}
      loading={isMetricsFetching}
      sx={{ width: '50%' }}
      onChange={(event, newValue) => {
        console.log(newValue);
        onChange(newValue.map((item) => item.id)); // ✅ Pass selected metric IDs
      }}
      renderInput={(params) => <TextField {...params} label="Metric Type" />}
    />
  );
};

export default AssetMetrics;
