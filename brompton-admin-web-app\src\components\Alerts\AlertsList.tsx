import CancelIcon from '@mui/icons-material/Cancel';
import DeleteIcon from '@mui/icons-material/Delete';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import QueryStatsIcon from '@mui/icons-material/QueryStats';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import {
  Box,
  Button,
  Card,
  Checkbox,
  Chip,
  IconButton,
  MenuItem,
  OutlinedInput,
  Select,
  Tooltip,
  Typography,
} from '@mui/material';
import { GridRenderCellParams } from '@mui/x-data-grid';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useHasPowerUserAccess } from '~/hooks/useHasPowerUserAccess';
import { useRolePermission } from '~/hooks/useRolePermission';
import { Alerts } from '~/measurements/domain/types';
import {
  useDeleteAlertMutation,
  useEnableAlertMutation,
  useGetAggregationPeriodsQuery,
  useGetAllAlertsQuery,
  useGetPeriodQuery,
  useGetThresholdTypesQuery,
} from '~/redux/api/alertApi';
import { useGetAllBackOfficeAssetTypesQuery } from '~/redux/api/assetsApi';
import { useGetCustomersQuery } from '~/redux/api/customersApi';
import { useGetAllMeasureTypesQuery } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { AssetTypeOption } from '~/types/asset';
import { assetTypePathMapper } from '~/utils/mappers/asset-type-mapper';
import CustomDialog from '../common/CustomDialog';
import DataTable from '../common/DataTable/DataTable';
import Loader from '../common/Loader';

const AlertsList = () => {
  const ActiveCustomer = useSelector(getActiveCustomer);
  const { hasPermission } = useRolePermission();
  const { globalAdmin, admin } = useHasAdminAccess();
  const hasPowerUserAccess = useHasPowerUserAccess();
  const { data: measurementTypes } = useGetAllMeasureTypesQuery();
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);

  const { data: assetTypeListData, isSuccess: isSuccessfullBackOffieAssetTypes } =
    useGetAllBackOfficeAssetTypesQuery();
  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapper(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes, status]);
  const {
    isFetching,
    data: alerts,
    isError,
    refetch,
  } = useGetAllAlertsQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });
  const [hideInfo, setHideInfo] = useState<boolean>(true);
  const router = useRouter();
  const { addAlert } = router.query;
  const alertValidator = addAlert === 'true' ? true : false;
  const [filterFields, setFilterFields] = useState<{
    customerId?: string;
    measurementTag?: string;
    thresholdType?: string;
    condition?: string;
    aggregation?: string;
    aggregationPeriod?: string;
    numUsers: string;
    enabled?: boolean;
    state?: string;
    showFilters: boolean;
    assetType?: string;
    measurementType?: string;
  }>({
    customerId: undefined,
    measurementTag: undefined,
    thresholdType: undefined,
    condition: undefined,
    aggregation: undefined,
    aggregationPeriod: undefined,
    numUsers: '',
    enabled: undefined,
    state: undefined,
    showFilters: false,
    assetType: undefined,
    measurementType: undefined,
  });
  const [filtedData, setFilteredData] = useState<Alerts[]>([]);
  useEffect(() => {
    if (alerts) {
      let filteredAlerts = alerts.items;

      if (ActiveCustomer?.id) {
        filteredAlerts = filteredAlerts.filter(
          (alert) => alert.customerId.toString() === ActiveCustomer.id.toString(),
        );
      }

      if (filterFields.showFilters) {
        filteredAlerts = filteredAlerts.filter((alert) => {
          const matchesCustomerId =
            !filterFields.customerId ||
            filterFields.customerId?.toString() === alert.customerId.toString();

          const matchesState = !filterFields.state || filterFields.state === alert.state;

          const matchesAggregation =
            !filterFields.aggregation ||
            alert.agg.id.toString() === filterFields.aggregation.toString();

          const matchesAggregationPeriod =
            !filterFields.aggregationPeriod ||
            alert.period?.id.toString() === filterFields.aggregationPeriod.toString();

          const matchesThresholdType =
            !filterFields.thresholdType ||
            alert.thresholdType.id.toString() === filterFields.thresholdType.toString();

          const matchesEnabled =
            filterFields.enabled === undefined ||
            String(alert.enabled) === String(filterFields.enabled);
          const matchesAssetType =
            filterFields.assetType === undefined ||
            alert.asset.type_id?.toString() === filterFields.assetType?.toString();

          const matchesMeasurementType =
            filterFields.measurementType === undefined ||
            alert.measurement.measurementType!.toString() ===
              filterFields.measurementType?.toString();
          return (
            matchesCustomerId &&
            matchesState &&
            matchesAggregation &&
            matchesAggregationPeriod &&
            matchesThresholdType &&
            matchesEnabled &&
            matchesAssetType &&
            matchesMeasurementType
          );
        });
      }
      setFilteredData(filteredAlerts);
    }
  }, [alerts, filterFields, ActiveCustomer]);

  useEffect(() => {
    if (
      filterFields.aggregation === undefined &&
      filterFields.aggregationPeriod === undefined &&
      filterFields.thresholdType === undefined &&
      filterFields.enabled === undefined &&
      filterFields.showFilters &&
      filterFields.customerId === undefined &&
      filterFields.state === undefined &&
      filterFields.assetType === undefined &&
      filterFields.measurementType === undefined &&
      alerts
    ) {
      setFilteredData(alerts.items);
      setFilterFields({ ...filterFields, showFilters: false });
    }
  }, [filterFields, alerts]);
  const [markEnabledData, setMarkEnabledData] = useState<{
    id: number | undefined;
    enabled: boolean;
  }>({
    id: undefined,
    enabled: false,
  });
  const { data: thresholds } = useGetThresholdTypesQuery();
  const { data: aggPeriods } = useGetAggregationPeriodsQuery();
  const { data: periods } = useGetPeriodQuery();
  const { data: customerList } = useGetCustomersQuery({});
  const [
    markEnabled,
    { isLoading: isEnabling, isSuccess: enableSuccess, error: enableError, isError: isEnableError },
  ] = useEnableAlertMutation();
  const [deleteAlert, { isLoading: isDeleting, isSuccess, error, data, isError: isdeleteError }] =
    useDeleteAlertMutation();
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  useEffect(() => {
    if (isEnabling) {
      showSuccessAlert('Alert updated successfully');
    }
    if (enableError) {
      showErrorAlert('Error updateing Alert');
    }
  }, [isEnabling, enableSuccess, enableError, isEnableError]);
  useEffect(() => {
    if (isSuccess) {
      showSuccessAlert('Alert deleted successfully');
      refetch();
    }
    if (isdeleteError) {
      showErrorAlert('Error deleting Alert');
    }
  }, [isdeleteError, isDeleting, isSuccess, error, data]);

  const [deleteAlertId, setDeleteAlertId] = useState<{
    id: number | undefined;
    confirm: boolean;
  }>({
    id: undefined,
    confirm: false,
  });
  const handleDelete = (id: number) => {
    setDeleteAlertId({ id, confirm: true });
  };
  return (
    <Box>
      <AlertSnackbar {...snackbarState} />
      {isFetching ? (
        <Loader />
      ) : (
        <>
          {isError && <Typography>Error fetching alerts</Typography>}
          {alerts && (
            <Box>
              <DataTable
                showAddButton={alertValidator}
                getRowClassName={(param) => {
                  return param.row.state === 'NORMAL' ? 'normal' : 'exceeded';
                }}
                columns={[
                  {
                    field: 'measurementTag',
                    headerName: 'Measurement Tag',
                    flex: 2,
                  },
                  { field: 'description', headerName: 'Alert Description', flex: 1.5 },
                  { field: 'thresholdType', headerName: 'Threshold Type', flex: 1 },
                  { field: 'state', headerName: 'State', flex: 1 },
                  {
                    field: 'enabled',
                    headerName: 'Enabled/Disabled',
                    flex: 1,
                    renderCell: (params: GridRenderCellParams) => (
                      <Checkbox
                        size="small"
                        checked={params.value}
                        disabled={(globalAdmin || admin || hasPowerUserAccess) && !alertValidator}
                        onChange={() => {
                          setMarkEnabledData({
                            id: params.row.id,
                            enabled: !params.value,
                          });
                        }}
                      />
                    ),
                  },
                  {
                    field: 'actions',
                    headerName: 'Actions',
                    flex: 1,
                    renderCell: (params: GridRenderCellParams) => (
                      <Box display="flex" justifyContent="space-between">
                        {hasPermission('alert.update') &&
                          (globalAdmin || admin || hasPowerUserAccess) &&
                          alertValidator && (
                            <Tooltip title="Edit Alert">
                              <IconButton
                                color="primary"
                                onClick={() => {
                                  router.push(`/alerts/edit/${params.row.id}`);
                                }}
                              >
                                <ModeEditIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                        {hasPermission('alert.view') && (
                          <Tooltip title="Details">
                            <IconButton
                              color="primary"
                              onClick={() => {
                                router.push(`/alerts/${params.row.id}`);
                              }}
                            >
                              <RemoveRedEyeIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        {hasPermission('alert.delete') &&
                          (globalAdmin || admin || hasPowerUserAccess) &&
                          alertValidator && (
                            <Tooltip title="Delete Alert">
                              <IconButton color="error" onClick={() => handleDelete(params.row.id)}>
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                      </Box>
                    ),
                  },
                ]}
                data={filtedData.map((alert) => ({
                  id: alert.id,
                  measurementTag: alert.measurement.tag,
                  description: alert.description,
                  thresholdType: alert.thresholdType.threshold,
                  condition: alert.condition ? alert.condition.condition : '-',
                  aggregation: alert.agg.label,
                  aggregationPeriod: alert.period?.label,
                  thresholdValue: alert.thresholdValue ?? 0,
                  resetDeadband: alert.resetDeadband ?? 0,
                  numUsers: alert.alertUsers?.length,
                  enabled: alert.enabled,
                  state:
                    Number(alert.state) === 0
                      ? 'NORMAL'
                      : Number(alert.state) === 1
                      ? 'EXCEEDED'
                      : alert.state,
                  customerId:
                    customerList?.find((customer) => customer.id === alert.customerId)?.name ??
                    'N/A',
                }))}
                filteredData={
                  <>
                    {filterFields.showFilters && (
                      <>
                        {filterFields.customerId && (
                          <Chip
                            sx={{ mr: 1 }}
                            label={`Customer: ${
                              customerList?.find(
                                (cust) =>
                                  cust.id.toString() === filterFields?.customerId?.toString(),
                              )?.nameId
                            }`}
                            onDelete={() => {
                              setFilterFields({ ...filterFields, customerId: undefined });
                              setFilteredData(
                                alerts.items.filter((alert) => {
                                  if (
                                    filterFields.state &&
                                    alert.state === filterFields?.state?.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.aggregation &&
                                    alert.agg.id.toString() === filterFields.aggregation?.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.aggregationPeriod &&
                                    alert.period.id.toString() ===
                                      filterFields.aggregationPeriod.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.thresholdType &&
                                    alert.thresholdType.id.toString() ===
                                      filterFields.thresholdType.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.enabled !== undefined &&
                                    String(alert.enabled) === String(filterFields.enabled)
                                  ) {
                                    return true;
                                  }
                                  return false;
                                }),
                              );
                            }}
                          />
                        )}
                        {filterFields.state && (
                          <Chip
                            sx={{ mr: 1 }}
                            label={`State:${filterFields.state}`}
                            onDelete={() => {
                              setFilterFields({ ...filterFields, state: undefined });
                              setFilteredData(
                                alerts.items.filter((alert) => {
                                  if (
                                    filterFields.customerId &&
                                    alert.customerId.toString() ===
                                      filterFields?.customerId?.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.aggregation &&
                                    alert.agg.id.toString() === filterFields.aggregation?.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.aggregationPeriod &&
                                    alert.period.id.toString() ===
                                      filterFields.aggregationPeriod.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.thresholdType &&
                                    alert.thresholdType.id.toString() ===
                                      filterFields.thresholdType.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.enabled !== undefined &&
                                    String(alert.enabled) === String(filterFields.enabled)
                                  ) {
                                    return true;
                                  }
                                  return false;
                                }),
                              );
                            }}
                          />
                        )}
                        {filterFields.aggregation && (
                          <Chip
                            sx={{ mr: 1 }}
                            label={`Aggregation: ${
                              aggPeriods?.items.find(
                                (agg) => agg.id.toString() === filterFields.aggregation?.toString(),
                              )?.label
                            }`}
                            onDelete={() => {
                              setFilterFields({ ...filterFields, aggregation: undefined });
                              setFilteredData(
                                alerts.items.filter((alert) => {
                                  if (
                                    filterFields.customerId &&
                                    alert.customerId.toString() ===
                                      filterFields?.customerId?.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.state &&
                                    alert.state === filterFields.state?.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.aggregationPeriod &&
                                    alert.period.id.toString() ===
                                      filterFields.aggregationPeriod.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.thresholdType &&
                                    alert.thresholdType.id.toString() ===
                                      filterFields.thresholdType.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.enabled !== undefined &&
                                    String(alert.enabled) === String(filterFields.enabled)
                                  ) {
                                    return true;
                                  }
                                  return false;
                                }),
                              );
                            }}
                          />
                        )}
                        {filterFields.aggregationPeriod && (
                          <Chip
                            sx={{ mr: 1 }}
                            label={`Aggregation Period: ${
                              periods?.items.find(
                                (period) =>
                                  period.id.toString() ===
                                  filterFields.aggregationPeriod?.toString(),
                              )?.label
                            }`}
                            onDelete={() => {
                              setFilterFields({ ...filterFields, aggregationPeriod: undefined });
                              setFilteredData(
                                alerts.items.filter((alert) => {
                                  if (
                                    filterFields.customerId &&
                                    alert.customerId.toString() ===
                                      filterFields?.customerId?.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.aggregation &&
                                    alert.agg.id.toString() === filterFields.aggregation.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.thresholdType &&
                                    alert.thresholdType.id.toString() ===
                                      filterFields.thresholdType.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.enabled !== undefined &&
                                    String(alert.enabled) === String(filterFields.enabled)
                                  ) {
                                    return true;
                                  }
                                  return false;
                                }),
                              );
                            }}
                          />
                        )}
                        {filterFields.thresholdType && (
                          <Chip
                            sx={{ mr: 1 }}
                            label={`Threshold Type: ${
                              thresholds?.items.find(
                                (threshold) =>
                                  threshold.id.toString() ===
                                  filterFields.thresholdType?.toString(),
                              )?.threshold
                            }`}
                            onDelete={() => {
                              setFilterFields({ ...filterFields, thresholdType: undefined });
                              setFilteredData(
                                alerts.items.filter((alert) => {
                                  if (
                                    filterFields.customerId &&
                                    alert.customerId.toString() ===
                                      filterFields?.customerId?.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.aggregation &&
                                    alert.agg.id.toString() === filterFields.aggregation.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.aggregationPeriod &&
                                    alert.period.id.toString() ===
                                      filterFields.aggregationPeriod.toString()
                                  ) {
                                    return true;
                                  }
                                  if (filterFields.state && alert.state === filterFields.state) {
                                    return true;
                                  }
                                  if (
                                    filterFields.enabled !== undefined &&
                                    String(alert.enabled) === String(filterFields.enabled)
                                  ) {
                                    return true;
                                  }
                                  return false;
                                }),
                              );
                            }}
                          />
                        )}
                        {filterFields.enabled !== undefined && (
                          <Chip
                            sx={{ mr: 1 }}
                            label={`Active/Inactive: ${
                              filterFields.enabled !== undefined
                                ? filterFields.enabled
                                  ? 'Active'
                                  : 'Inactive'
                                : ''
                            }`}
                            onDelete={() => {
                              setFilterFields({ ...filterFields, enabled: undefined });
                              setFilteredData(
                                alerts.items.filter((alert) => {
                                  if (
                                    filterFields.customerId &&
                                    alert.customerId.toString() ===
                                      filterFields?.customerId?.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.aggregation &&
                                    alert.agg.id.toString() === filterFields.aggregation.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.thresholdType &&
                                    alert.thresholdType.id.toString() ===
                                      filterFields.thresholdType.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.aggregationPeriod &&
                                    alert.period.id.toString() ===
                                      filterFields.aggregationPeriod.toString()
                                  ) {
                                    return true;
                                  }
                                  return false;
                                }),
                              );
                            }}
                          />
                        )}
                        {/* Chip for Asset Type */}
                        {filterFields.assetType && (
                          <Chip
                            sx={{ mr: 1 }}
                            label={`Asset Type: ${
                              assetTypesWithPath?.find(
                                (assetType) =>
                                  assetType.value.toString() === filterFields.assetType?.toString(),
                              )?.label ?? 'Unknown'
                            }`}
                            onDelete={() => {
                              setFilterFields({ ...filterFields, assetType: undefined });
                              setFilteredData(
                                alerts.items.filter((alert) => {
                                  if (
                                    filterFields.customerId &&
                                    alert.customerId.toString() ===
                                      filterFields?.customerId?.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.aggregation &&
                                    alert.agg.id.toString() === filterFields.aggregation.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.aggregationPeriod &&
                                    alert.period.id.toString() ===
                                      filterFields.aggregationPeriod.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.thresholdType &&
                                    alert.thresholdType.id.toString() ===
                                      filterFields.thresholdType.toString()
                                  ) {
                                    return true;
                                  }
                                  if (filterFields.state && alert.state === filterFields.state) {
                                    return true;
                                  }
                                  if (
                                    filterFields.enabled !== undefined &&
                                    String(alert.enabled) === String(filterFields.enabled)
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.measurementType !== undefined &&
                                    alert.measurement.measurementType?.toString() ===
                                      filterFields.measurementType.toString()
                                  ) {
                                    return true;
                                  }
                                  return false;
                                }),
                              );
                            }}
                          />
                        )}

                        {/* Chip for Measurement Type */}
                        {filterFields.measurementType && (
                          <Chip
                            sx={{ mr: 1 }}
                            label={`Measurement Type: ${
                              measurementTypes?.find(
                                (measureType) =>
                                  measureType.id.toString() ===
                                  filterFields.measurementType?.toString(),
                              )?.name ?? 'Unknown'
                            }`}
                            onDelete={() => {
                              setFilterFields({ ...filterFields, measurementType: undefined });
                              setFilteredData(
                                alerts.items.filter((alert) => {
                                  if (
                                    filterFields.customerId &&
                                    alert.customerId.toString() ===
                                      filterFields?.customerId?.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.aggregation &&
                                    alert.agg.id.toString() === filterFields.aggregation.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.aggregationPeriod &&
                                    alert.period.id.toString() ===
                                      filterFields.aggregationPeriod.toString()
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.thresholdType &&
                                    alert.thresholdType.id.toString() ===
                                      filterFields.thresholdType.toString()
                                  ) {
                                    return true;
                                  }
                                  if (filterFields.state && alert.state === filterFields.state) {
                                    return true;
                                  }
                                  if (
                                    filterFields.enabled !== undefined &&
                                    String(alert.enabled) === String(filterFields.enabled)
                                  ) {
                                    return true;
                                  }
                                  if (
                                    filterFields.assetType !== undefined &&
                                    alert.asset.type_id.toString() ===
                                      filterFields.assetType.toString()
                                  ) {
                                    return true;
                                  }
                                  return false;
                                }),
                              );
                            }}
                          />
                        )}

                        {filterFields.thresholdType !== '' ||
                        filterFields.aggregation !== '' ||
                        filterFields.aggregationPeriod !== '' ||
                        filterFields.state !== '' ||
                        filterFields.enabled ||
                        filterFields.assetType !== '' ||
                        filterFields.measurementType !== '' ? (
                          <Chip
                            label={'Clear all'}
                            sx={{
                              color: 'primary.main',
                              border: 'unset',
                            }}
                            onClick={() => {
                              setFilterFields({
                                ...filterFields,
                                customerId: undefined,
                                measurementTag: undefined,
                                thresholdType: undefined,
                                condition: undefined,
                                aggregation: undefined,
                                aggregationPeriod: undefined,
                                numUsers: '',
                                enabled: undefined,
                                state: undefined,
                                showFilters: true, // Keep the filters panel open
                                assetType: undefined,
                                measurementType: undefined,
                              });
                            }}
                          />
                        ) : null}
                      </>
                    )}
                    {/* {hideInfo && (
                      <Alert
                        severity="info"
                        sx={{ mt: 2, mb: 2 }}
                        onClose={() => {
                          setHideInfo(false);
                        }}
                      >
                        For `EXCEEDED` state changed color to yellow
                      </Alert>
                    )} */}
                  </>
                }
                filterOptions={
                  <>
                    <Card sx={{ p: 1, height: 600 }}>
                      <Select
                        value={filterFields.thresholdType ?? ''} // Ensure it falls back to an empty value
                        onChange={(e) =>
                          setFilterFields({
                            ...filterFields,
                            thresholdType: e.target.value || undefined,
                          })
                        }
                        input={
                          <OutlinedInput
                            label="Threshold Type"
                            sx={{
                              '& legend': {
                                maxWidth: '100%',
                                height: 'fit-content',
                                '& span': {
                                  opacity: 1,
                                },
                              },
                            }}
                          />
                        }
                        variant="outlined"
                        fullWidth
                        label="Threshold Type"
                      >
                        {thresholds?.items.map((threshold) => (
                          <MenuItem value={threshold.id} key={threshold.id}>
                            {threshold.threshold}
                          </MenuItem>
                        ))}
                      </Select>

                      <Select
                        value={filterFields.aggregation ?? ''} // Fallback to empty string
                        onChange={(e) =>
                          setFilterFields({
                            ...filterFields,
                            aggregation: e.target.value || undefined,
                          })
                        }
                        input={
                          <OutlinedInput
                            label="Aggregation"
                            sx={{
                              mt: 2,
                              '& legend': {
                                maxWidth: '100%',
                                height: 'fit-content',
                                '& span': {
                                  opacity: 1,
                                },
                              },
                            }}
                          />
                        }
                        variant="outlined"
                        fullWidth
                        label="Aggregation"
                      >
                        {aggPeriods?.items
                          .filter((aggPeriod) => aggPeriod.label !== 'Total')
                          .map((agg) => (
                            <MenuItem value={agg.id} key={agg.id}>
                              {agg.label}
                            </MenuItem>
                          ))}
                      </Select>

                      <Select
                        value={filterFields.aggregationPeriod ?? ''} // Fallback to an empty value
                        onChange={(e) =>
                          setFilterFields({
                            ...filterFields,
                            aggregationPeriod: e.target.value || undefined, // Reset to undefined if empty
                          })
                        }
                        input={
                          <OutlinedInput
                            label="Aggregation Period"
                            sx={{
                              mt: 2,
                              '& legend': {
                                maxWidth: '100%',
                                height: 'fit-content',
                                '& span': {
                                  opacity: 1,
                                },
                              },
                            }}
                          />
                        }
                        variant="outlined"
                        fullWidth
                        label="Aggregation Period"
                      >
                        {periods?.items.map((period) => (
                          <MenuItem value={period.id} key={period.id}>
                            {period.label}
                          </MenuItem>
                        ))}
                      </Select>

                      <Select
                        value={filterFields.state ?? ''} // Fallback to an empty value
                        onChange={(e) =>
                          setFilterFields({
                            ...filterFields,
                            state: e.target.value || undefined, // Reset to undefined if empty
                          })
                        }
                        input={
                          <OutlinedInput
                            label="State"
                            sx={{
                              mt: 2,
                              '& legend': {
                                maxWidth: '100%',
                                height: 'fit-content',
                                '& span': {
                                  opacity: 1,
                                },
                              },
                            }}
                          />
                        }
                        variant="outlined"
                        fullWidth
                        label="State"
                      >
                        <MenuItem value="EXCEEDED">EXCEEDED</MenuItem>
                        <MenuItem value="NORMAL">NORMAL</MenuItem>
                      </Select>

                      <Select
                        value={
                          filterFields.enabled === undefined
                            ? ''
                            : filterFields.enabled
                            ? 'active'
                            : 'inactive'
                        }
                        onChange={(e) =>
                          setFilterFields({
                            ...filterFields,
                            enabled:
                              e.target.value === 'active'
                                ? true
                                : e.target.value === 'inactive'
                                ? false
                                : undefined,
                          })
                        }
                        input={
                          <OutlinedInput
                            label="Active/Inactive"
                            sx={{
                              mt: 2,
                              '& legend': {
                                maxWidth: '100%',
                                height: 'fit-content',
                                '& span': {
                                  opacity: 1,
                                },
                              },
                            }}
                          />
                        }
                        variant="outlined"
                        fullWidth
                        label="Active/Inactive"
                      >
                        <MenuItem value="active">Active</MenuItem>
                        <MenuItem value="inactive">Inactive</MenuItem>
                      </Select>
                      <Select
                        value={filterFields.assetType ?? ''} // Ensure it falls back to an empty value
                        onChange={(e) =>
                          setFilterFields({
                            ...filterFields,
                            assetType: e.target.value || undefined,
                          })
                        }
                        input={
                          <OutlinedInput
                            label="Asset Type"
                            sx={{
                              mt: 1,
                              '& legend': {
                                maxWidth: '100%',
                                height: 'fit-content',
                                '& span': {
                                  opacity: 1,
                                },
                              },
                            }}
                          />
                        }
                        variant="outlined"
                        fullWidth
                        label="Asset Type"
                      >
                        {assetTypesWithPath?.map((assetType, index) => (
                          <MenuItem value={assetType.value} key={index}>
                            {assetType.label}
                          </MenuItem>
                        ))}
                      </Select>
                      <Select
                        value={filterFields.measurementType ?? ''} // Ensure it falls back to an empty value
                        onChange={(e) =>
                          setFilterFields({
                            ...filterFields,
                            measurementType: e.target.value || undefined,
                          })
                        }
                        input={
                          <OutlinedInput
                            label="Measurement Type"
                            sx={{
                              mt: 1,
                              '& legend': {
                                maxWidth: '100%',
                                height: 'fit-content',
                                '& span': {
                                  opacity: 1,
                                },
                              },
                            }}
                          />
                        }
                        variant="outlined"
                        fullWidth
                        label="Measurement Type"
                      >
                        {measurementTypes?.map((threshold) => (
                          <MenuItem value={threshold.id} key={threshold.id}>
                            {threshold.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </Card>
                    <Box mt={2} display={'flex'} justifyContent={'space-between'}>
                      <Button
                        variant="contained"
                        color="primary"
                        fullWidth
                        onClick={async () => {
                          setFilteredData(
                            alerts.items.filter((alert) => {
                              if (
                                filterFields.customerId &&
                                filterFields.customerId?.toString() !== alert.customerId.toString()
                              ) {
                                return false;
                              }
                              if (filterFields.state && filterFields.state !== alert.state) {
                                return false;
                              }
                              if (
                                filterFields.aggregation &&
                                alert.agg.id.toString() !== filterFields.aggregation.toString()
                              ) {
                                return false;
                              }
                              if (
                                filterFields.aggregationPeriod &&
                                alert?.period?.id.toString() !==
                                  filterFields.aggregationPeriod.toString()
                              ) {
                                return false;
                              }
                              if (
                                filterFields.thresholdType &&
                                alert.thresholdType.id.toString() !==
                                  filterFields.thresholdType.toString()
                              ) {
                                return false;
                              }
                              if (
                                filterFields.enabled !== undefined &&
                                String(alert.enabled) !== String(filterFields.enabled)
                              ) {
                                return false;
                              }
                              if (
                                filterFields.measurementType !== undefined &&
                                alert.measurement.measurementType!.toString() !==
                                  filterFields.measurementType.toString()
                              ) {
                                return false;
                              }

                              if (
                                filterFields.assetType !== undefined &&
                                alert.asset.type_id?.toString() !==
                                  filterFields.assetType?.toString()
                              ) {
                                return false;
                              }
                              return true;
                            }),
                          );
                          setFilterFields({ ...filterFields, showFilters: true });
                          await refetch();
                        }}
                      >
                        Apply Filters
                      </Button>

                      <Button
                        variant="outlined"
                        fullWidth
                        color="error"
                        onClick={() => {
                          setFilterFields({
                            customerId: undefined,
                            measurementTag: undefined,
                            thresholdType: undefined,
                            condition: undefined,
                            aggregation: undefined,
                            aggregationPeriod: undefined,
                            numUsers: '',
                            enabled: undefined,
                            state: undefined,
                            showFilters: true, // Keep the filters panel open
                            assetType: undefined,
                            measurementType: undefined,
                          });
                          setFilteredData(alerts.items); // Reset to show all alerts
                        }}
                      >
                        Clear Filters
                      </Button>
                    </Box>
                  </>
                }
              />
            </Box>
          )}
        </>
      )}
      {deleteAlertId.confirm ? (
        <>
          <CustomDialog
            open={deleteAlertId.confirm}
            content={null}
            dialogActions={
              <>
                <Button
                  onClick={() => {
                    setDeleteAlertId({ id: undefined, confirm: false });
                  }}
                  variant="outlined"
                  startIcon={<CancelIcon />}
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => {
                    if (deleteAlertId.id)
                      deleteAlert({
                        alertId: deleteAlertId.id,
                      });
                    setDeleteAlertId({ id: undefined, confirm: false });
                  }}
                  variant="contained"
                  color="error"
                  startIcon={<DeleteIcon />}
                >
                  Delete
                </Button>
              </>
            }
            title={
              <>
                <Typography variant="h4">Delete Alert?</Typography>
                <Typography color="error">This action cannot be undone.</Typography>
              </>
            }
            onClose={() => {
              setDeleteAlertId({ id: undefined, confirm: false });
            }}
          />
        </>
      ) : null}
      {markEnabledData.id && (
        <CustomDialog
          open={markEnabledData.id !== undefined}
          content={null}
          dialogActions={
            <>
              <Button
                onClick={() => {
                  setMarkEnabledData({ id: undefined, enabled: false });
                }}
                variant="outlined"
                startIcon={<CancelIcon />}
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  markEnabled({
                    alertId: markEnabledData.id ?? 0,
                  });
                  setMarkEnabledData({ id: undefined, enabled: false });
                }}
                variant="contained"
                color={markEnabledData.enabled ? 'primary' : 'error'}
              >
                {markEnabledData.enabled ? 'Enable' : 'Disable'}
              </Button>
            </>
          }
          title={
            <>
              <Typography variant="h4">
                {markEnabledData.enabled ? 'Enable' : 'Disable'} Alert?
              </Typography>
            </>
          }
          onClose={() => {
            setMarkEnabledData({ id: undefined, enabled: false });
          }}
        />
      )}
    </Box>
  );
};

export default AlertsList;
