import { yupResolver } from '@hookform/resolvers/yup';
import {
  <PERSON>ert,
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormLabel,
  Grid,
  Radio,
  RadioGroup,
  Typography,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import { CustomError } from '~/errors/CustomerErrorResponse';
import {
  EditMeasureAlertsValidationData,
  editMeasureAlertsValidationSchema,
} from '~/measurements/domain/types';
import {
  useEditAlertMutation,
  useGetAggregationPeriodsQuery,
  useGetAlertByIdQuery,
  useGetConditionsQuery,
  useGetPeriodQuery,
  useGetThresholdTypesQuery,
} from '~/redux/api/alertApi';
import { useGetCustomerUsersByIdMutation } from '~/redux/api/customersApi';
import { ControlledAutocomplete } from '~/shared/forms/components/ControlledAutocomplete';
import { ControlledTextField } from '~/shared/forms/components/ControlledTextField';
import { AlertMessage } from '~/shared/forms/types';

const EditAlert = ({ alertId }: { alertId: number | undefined }) => {
  const { data: thresholds } = useGetThresholdTypesQuery();
  const { data: aggPeriods } = useGetAggregationPeriodsQuery();
  const { data: conditions } = useGetConditionsQuery();
  const { data: periods } = useGetPeriodQuery();
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const { data: alert, isFetching: isLoadingAlert } = useGetAlertByIdQuery(
    { alertId: alertId || 0 },
    { skip: !alertId || alertId === 0 || alertId === undefined, refetchOnMountOrArgChange: true },
  );
  const [getCustomerUser, { isLoading: usersLoading, data }] = useGetCustomerUsersByIdMutation();
  useEffect(() => {
    getCustomerUser({
      customer_id: alert?.customerId.toString() ?? '',
    });
  }, [alert]);
  const [editAlert, { isError, isLoading, isSuccess, error: editError }] = useEditAlertMutation();
  const { control, handleSubmit, setValue, watch, getValues } =
    useForm<EditMeasureAlertsValidationData>({
      defaultValues: {
        aggregate: undefined,
        customerId: undefined,
        aggregatePeriod: undefined,
        comparison: undefined,
        measurementId: undefined,
        resetDeadband: undefined,
        thresholdType: undefined,
        learningPeriod: undefined,
        includeVelocity: undefined,
        includeMomentum: undefined,
        thresholdValue: undefined,
        contactNumbers: [
          {
            user: '',
            notificationType: 'both',
          },
        ],
      },
      resolver: yupResolver(editMeasureAlertsValidationSchema),
    });
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'contactNumbers',
  });
  useEffect(() => {
    if (alert) {
      setValue('aggregate', alert.agg);
      setValue('aggregatePeriod', alert.period);
      setValue('comparison', alert.condition);
      setValue('customerId', alert?.customerId?.toString() ?? '');
      setValue('resetDeadband', alert.resetDeadband);
      setValue('thresholdType', alert.thresholdType);
      setValue('learningPeriod', alert.learningPeriod?.days ?? '');
      setValue('includeVelocity', alert.includeVelocity ?? false);
      setValue('includeMomentum', alert.includeMomentum ?? false);
      setValue('thresholdValue', alert.thresholdValue);
      setValue('measurementId', alert.measurement.id.toString() ?? '');
      setValue('description', alert.description ?? '');
      setValue(
        'contactNumbers',
        (alert.alertUsers?.length ?? 0) > 0
          ? alert.alertUsers?.map((user) => ({
              user: user.user.toString(),
              notificationType:
                user.notificationtype === 1
                  ? 'email'
                  : user.notificationtype === 2
                  ? 'sms'
                  : 'both',
            })) ?? [
              {
                user: '',
                notificationType: 'both',
              },
            ]
          : [
              {
                user: '',
                notificationType: 'both',
              },
            ],
      );
    }
  }, [alert]);
  useEffect(() => {
    if (isSuccess) {
      setAlertMessage({
        message: `Alert updated successfully!`,
        severity: 'success',
      });
    }
    if (isError && editError) {
      const err = editError as CustomError;
      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [editError, isError, isSuccess]);
  const thresholdValue = watch('thresholdValue');
  const resetDeadband = watch('resetDeadband');
  const comparison = watch('comparison');
  const thresholdType = watch('thresholdType');

  let alertMessageText = '';
  if (
    thresholdValue !== undefined &&
    resetDeadband !== undefined &&
    comparison &&
    comparison !== undefined
  ) {
    const condition = conditions?.items.find(
      (cond) => cond.id.toString() === comparison.toString(),
    )?.condition;
    switch (condition) {
      case 'LT':
        alertMessageText = `Alert will reset at threshold value reaches to ${
          Number(thresholdValue) + Number(resetDeadband)
        }`;
        break;
      case 'LE':
        alertMessageText = `Alert will reset at threshold value reaches to ${
          Number(thresholdValue) + Number(resetDeadband)
        }`;
        break;
      case 'GT':
        if (thresholdValue && resetDeadband) {
          alertMessageText = `Alert will reset at threshold value reaches to ${
            thresholdValue - resetDeadband
          }`;
        }
        break;
      case 'GE':
        if (thresholdValue && resetDeadband) {
          alertMessageText = `Alert will reset at threshold value reaches to ${
            thresholdValue - resetDeadband
          }`;
        }
        break;
      default:
        alertMessageText = ``;
        break;
    }
  }
  return (
    <Box>
      <Typography>Measurement : {alert?.measurement.tag}</Typography>
      <form
        onSubmit={handleSubmit(async (data) => {
          try {
            await editAlert({
              id: alertId ?? 0,
              agg: data.aggregate,
              period: data.aggregatePeriod,
              asset: alert?.asset ?? '',
              customerId: alert?.customerId.toString() ?? '',
              measurement: alert?.measurement.id.toString() ?? '',
              condition: data.comparison ?? 0,
              resetDeadband: data.resetDeadband ?? 0,
              thresholdType: data.thresholdType,
              learningPeriod: `${data.learningPeriod} days`,
              includeVelocity: data.includeVelocity,
              includeMomentum: data.includeMomentum,
              thresholdValue: data.thresholdValue ?? 0,
              description: data.description,
              users: data.contactNumbers.map((contact) => ({
                id: Number(contact.user),
                notificationType:
                  contact.notificationType === 'email'
                    ? 1
                    : contact.notificationType === 'sms'
                    ? 2
                    : 3,
              })),
            });
          } catch (error) {
            console.error(error);
          }
        })}
      >
        <ControlledTextField
          control={control}
          fieldName="description"
          label="Description"
          loading={isLoadingAlert}
        />
        <ControlledAutocomplete
          control={control}
          fieldName="aggregate"
          label="Aggregate"
          loading={isLoadingAlert}
          options={
            aggPeriods?.items
              .filter((aggPeriod) => aggPeriod.label !== 'Total')
              ?.map((agg) => ({
                id: agg.id.toString(),
                label: agg.label,
              })) || []
          }
        />
        <ControlledAutocomplete
          control={control}
          fieldName="aggregatePeriod"
          label="Aggregate Period"
          loading={isLoadingAlert}
          options={
            periods?.items?.map((period) => ({
              id: period.id.toString(),
              label: period.label,
            })) || []
          }
        />
        <ControlledAutocomplete
          control={control}
          fieldName="thresholdType"
          label="Threshold Type"
          loading={isLoadingAlert}
          helperText={
            String(thresholdType) === '3' ? (
              <Typography variant="caption" sx={{ fontStyle: 'italic' }}>
                Triggers if the measurement has not changed for the specified number of minutes.
              </Typography>
            ) : String(thresholdType) === '4' ? (
              <Typography variant="caption" sx={{ fontStyle: 'italic' }}>
                Triggers if no new value has been received for the specified number of minutes.
              </Typography>
            ) : null
          }
          disabled
          options={
            thresholds?.items?.map((type) => ({
              id: type.id.toString(),
              label: type.threshold,
            })) || []
          }
        />

        {String(thresholdType) === '2' && (
          <>
            <ControlledAutocomplete
              control={control}
              fieldName="learningPeriod"
              label="Learning Days"
              loading={isLoadingAlert}
              options={[
                { id: '7', label: '7' },
                { id: '30', label: '30' },
                { id: '60', label: '60' },
                { id: '90', label: '90' },
                { id: '180', label: '180' },
                { id: '365', label: '365' },
              ]}
            />

            {/* Include Velocity Checkbox */}
            <Controller
              name="includeVelocity"
              control={control}
              render={({ field }) => (
                <FormControlLabel
                  control={<Checkbox {...field} checked={!!field.value} />}
                  label="Include Velocity"
                />
              )}
            />

            {/* Include Momentum Checkbox */}
            <Controller
              name="includeMomentum"
              control={control}
              render={({ field }) => (
                <FormControlLabel
                  control={<Checkbox {...field} checked={!!field.value} />}
                  label="Include Momentum"
                />
              )}
            />
          </>
        )}
        {String(thresholdType) !== '2' && (
          <>
            <ControlledAutocomplete
              control={control}
              fieldName="comparison"
              label="Comparision condition"
              loading={isLoadingAlert}
              options={
                conditions?.items
                  ?.filter((cond) =>
                    String(thresholdType) === '3' || String(thresholdType) === '4'
                      ? cond.condition === 'GT' || cond.condition === 'GE'
                      : true,
                  )
                  ?.map((cond) => ({
                    id: cond.id.toString(),
                    label: cond.condition,
                  })) || []
              }
            />
            <ControlledTextField
              control={control}
              fieldName="thresholdValue"
              label={
                String(thresholdType) === '3' || String(thresholdType) === '4'
                  ? 'Duration in Minutes'
                  : 'Threshold Value'
              }
              type="number"
              loading={isLoadingAlert}
            />

            {String(thresholdType) !== '3' && String(thresholdType) !== '4' && (
              <ControlledTextField
                control={control}
                fieldName="resetDeadband"
                label="Reset Deadband Value"
                type="number"
                loading={isLoadingAlert}
              />
            )}
          </>
        )}
        {thresholdValue !== undefined &&
        resetDeadband !== undefined &&
        comparison !== undefined &&
        String(thresholdType) !== '2' &&
        String(thresholdType) !== '3' &&
        String(thresholdType) !== '4' ? (
          <Alert variant="outlined" severity="info">
            {alertMessageText}
          </Alert>
        ) : null}
        <Box display={'flex'} justifyContent={'end'} pr={3}>
          <Button
            variant="contained"
            onClick={() => append({ user: '', notificationType: 'both' })}
            sx={{ mb: 2, mt: 2 }}
          >
            Add Contact
          </Button>
        </Box>
        {fields.map((field, index) => (
          <Grid container spacing={2} key={field.id} alignItems="center">
            <Grid item xs={12} sm={6} md={4}>
              <ControlledAutocomplete
                control={control}
                fieldName={`contactNumbers.${index}.user`}
                label={`User ${index + 1}`}
                loading={isLoadingAlert || usersLoading}
                options={
                  data?.items?.map((item) => ({
                    id: item.id.toString(),
                    label: item.username,
                  })) || []
                }
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <FormControl component="fieldset">
                <FormLabel component="legend">Notification Type</FormLabel>
                <Controller
                  name={`contactNumbers.${index}.notificationType`}
                  control={control}
                  render={({ field }) => (
                    <RadioGroup row {...field}>
                      <FormControlLabel value="email" control={<Radio />} label="Email" />
                      <FormControlLabel value="sms" control={<Radio />} label="SMS" />
                      <FormControlLabel value="both" control={<Radio />} label="Both" />
                    </RadioGroup>
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={12} md={4}>
              <Button
                variant="outlined"
                color="error"
                disabled={fields.length === 1}
                onClick={() => remove(index)}
              >
                Remove
              </Button>
            </Grid>
          </Grid>
        ))}

        <Button
          type="submit"
          variant="contained"
          size="large"
          sx={{ mt: 2, width: 200 }}
          disabled={isLoadingAlert || isLoading}
        >
          Submit
        </Button>
      </form>
      {alertMessage && (
        <Alert severity={alertMessage.severity} sx={{ mt: 3 }}>
          {alertMessage.message}
        </Alert>
      )}
    </Box>
  );
};

export default EditAlert;
