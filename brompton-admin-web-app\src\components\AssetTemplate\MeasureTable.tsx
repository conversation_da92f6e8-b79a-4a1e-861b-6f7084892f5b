import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { Box } from '@mui/material';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { Dispatch, SetStateAction } from 'react';
import { UseFormSetValue } from 'react-hook-form';
import { measurementSchemaData, measurementSchemaDataDTO } from '~/measurements/domain/types';

type MeasureTableProps = {
  setCurrentMeasureIndex: Dispatch<SetStateAction<number>>;
  fields: measurementSchemaData[] | measurementSchemaDataDTO[];
  valueTypeOptions: {
    id: string;
    label: string;
  }[];
  datasourceOptions: {
    id: string;
    label: string;
  }[];
  locationsListOption: {
    id: string;
    label: string;
  }[];
  dataTypesListOptions: {
    id: string;
    label: string;
  }[];
  measurementTypeListOptions: {
    id: string;
    label: string;
  }[];
  assetTypeMetricsListOptions: {
    id: string;
    label: string;
  }[];
  remove: (index: number) => void;
  setMeasureValue?: UseFormSetValue<measurementSchemaData | measurementSchemaDataDTO>;
  isInstance?: boolean;
};
const MeasureTable = ({
  fields,
  valueTypeOptions,
  datasourceOptions,
  locationsListOption,
  measurementTypeListOptions,
  dataTypesListOptions,
  setCurrentMeasureIndex,
  assetTypeMetricsListOptions,
  remove,
  setMeasureValue,
  isInstance,
}: MeasureTableProps) => {
  return (
    <>
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label="simple table">
          <TableHead>
            <TableRow>
              <TableCell align="center">Measure No.</TableCell>
              {isInstance ? <TableCell align="center">Tag </TableCell> : null}
              <TableCell align="center">TypeId </TableCell>
              <TableCell align="center">Data Type</TableCell>
              <TableCell align="center">Value Type</TableCell>
              <TableCell align="center">Metric</TableCell>
              <TableCell align="center">Location</TableCell>
              <TableCell align="center">Data Source</TableCell>
              <TableCell align="center">Description</TableCell>
              <TableCell align="center">Meter Factor</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {fields.map((row, i) => (
              <TableRow key={i} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                <TableCell component="th" scope="row" align="center">
                  {i + 1}
                </TableCell>
                {isInstance ? (
                  <TableCell component="th" scope="row" align="center">
                    {isInstance && 'tag' in row ? row.tag : 'N/A'}
                  </TableCell>
                ) : null}

                <TableCell component="th" scope="row" align="center">
                  {measurementTypeListOptions.find(
                    (measure) => measure.id === row.type_id?.toString(),
                  )?.label ?? 'N/A'}
                </TableCell>
                <TableCell component="th" scope="row" align="center">
                  {dataTypesListOptions.find(
                    (dataType) => dataType.id === row.data_type_id?.toString(),
                  )?.label ?? 'N/A'}
                </TableCell>
                <TableCell component="th" scope="row" align="center">
                  {valueTypeOptions.find(
                    (valueType) => valueType.id === row.value_type_id?.toString(),
                  )?.label ?? 'N/A'}
                </TableCell>
                <TableCell component="th" scope="row" align="center">
                  {(() => {
                    const metric = row.metric_id;
                    if (!metric) return null;
                    const metricString = metric.toString();
                    const foundMetric = assetTypeMetricsListOptions.find(
                      (asset) => asset.id === metricString,
                    );
                    return foundMetric ? foundMetric.label : metricString;
                  })()}
                </TableCell>
                <TableCell component="th" scope="row" align="center">
                  {locationsListOption.find(
                    (location) => location.id === row.location_id?.toString(),
                  )?.label ?? 'N/A'}
                </TableCell>
                <TableCell component="th" scope="row" align="center">
                  {datasourceOptions.find((source) => source.id === row.datasource_id?.toString())
                    ?.label ?? 'N/A'}
                </TableCell>
                <TableCell component="th" scope="row" align="center">
                  {row.description ?? 'N/A'}
                </TableCell>
                <TableCell component="th" scope="row" align="center">
                  {row.meter_factor ?? 'N/A'}
                </TableCell>
                <TableCell component="th" scope="row" align="center">
                  <Box display={'flex'} justifyContent={'space-around'}>
                    <EditIcon
                      onClick={() => {
                        setCurrentMeasureIndex(i);
                        if (row.type_id !== null) setMeasureValue?.('type_id', row.type_id);
                        if (row.data_type_id !== null)
                          setMeasureValue?.('data_type_id', row.data_type_id);
                        if (row.value_type_id !== null)
                          setMeasureValue?.('value_type_id', row.value_type_id);
                        if (row.metric_id !== null) setMeasureValue?.('metric_id', row.metric_id);
                        if (row.description !== null)
                          setMeasureValue?.('description', row.description);
                        if (row.description === null) setMeasureValue?.('description', '');
                        if (row.location_id !== null)
                          setMeasureValue?.('location_id', row.location_id);
                        if (row.datasource_id !== null)
                          setMeasureValue?.('datasource_id', row.datasource_id);
                        if (row.meter_factor !== null)
                          setMeasureValue?.('meter_factor', row.meter_factor);
                        if ((row as measurementSchemaDataDTO).unit_of_measure_id !== null) {
                          setMeasureValue?.(
                            'unit_of_measure_id',
                            (row as measurementSchemaDataDTO).unit_of_measure_id,
                          );
                        }
                        if ((row as measurementSchemaDataDTO).tag !== null)
                          setMeasureValue?.('tag', (row as measurementSchemaDataDTO).tag);
                      }}
                      sx={{ cursor: 'pointer' }}
                    />
                    {!isInstance ? (
                      <DeleteIcon
                        color="error"
                        onClick={() => remove(i)}
                        sx={{ cursor: 'pointer' }}
                      />
                    ) : null}
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};

export default MeasureTable;
