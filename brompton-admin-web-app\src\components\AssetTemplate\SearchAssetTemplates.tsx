import { ContentCopy, Edit } from '@mui/icons-material';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import {
  Autocomplete,
  Box,
  IconButton,
  Radio,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  DataGrid,
  GridColDef,
  GridRenderCellParams,
  GridTreeNodeWithRender,
} from '@mui/x-data-grid';
import { useRouter } from 'next/router';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import {
  createAssetTemplateDataWithId,
  createAssetTemplateDataWIthMeasureId,
} from '~/measurements/domain/types';
import {
  useGetAllAssetTemplatedByAssetTypeQuery,
  useGetAllBackOfficeAssetTypesMetricsQuery,
  useGetAllBackOfficeAssetTypesQuery,
} from '~/redux/api/assetsApi';
import {
  useGetAllDatasourcesQuery,
  useGetAllDataTypesQuery,
  useGetAllLocationsQuery,
  useGetAllMeasureTypesQuery,
  useGetAllValueTypesQuery,
} from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { AssetDo, AssetTypeOption } from '~/types/asset';
import { assetTypePathMapperFilterTemplates } from '~/utils/mappers/asset-type-mapper';
import Loader from '../common/Loader';
import CloneAssetTemplateForm from './CloneAssetTemplateForm';

type ExtendedCreateAssetTemplateData = createAssetTemplateDataWIthMeasureId & {
  customer?: number | null;
  asset_type_id?: number;
  id?: number;
};
type SearchAssetTemplatesProps = {
  showMeasure?: boolean;
  selectedTemplate?: createAssetTemplateDataWithId | null;
  setSelectedTemplet?: Dispatch<SetStateAction<createAssetTemplateDataWithId | null>>;
  asset?: AssetDo;
};
const SearchAssetTemplates = ({
  selectedTemplate,
  showMeasure,
  setSelectedTemplet,
  asset,
}: SearchAssetTemplatesProps) => {
  const router = useRouter();
  const [openCloneDialog, setOpenCloneDialog] = useState<boolean>(false);
  const { globalAdmin, admin } = useHasAdminAccess();
  const activeCustomer = useSelector(getActiveCustomer);
  const [selectedAssetTemplate, setSelectedAssetTemplate] =
    useState<ExtendedCreateAssetTemplateData | null>(null);
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const [selectedRow, setSelectedRow] = useState<number | null>(null);
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const columns: GridColDef[] = [
    ...(setSelectedTemplet
      ? [
          {
            field: 'selection',
            headerName: '',
            flex: 1,
            sortable: false,
            filterable: false,
            disableColumnMenu: true,
            renderCell: (params: GridRenderCellParams<any, any, any, GridTreeNodeWithRender>) => (
              <Radio
                checked={selectedRow === params.id}
                onChange={() => setSelectedRow(params.id as number)}
              />
            ),
          },
        ]
      : []),
    { field: 'id', headerName: 'Number #', flex: 1 },
    {
      field: 'manufacturer',
      headerName: 'Manufacturer',
      flex: 1,
    },
    {
      field: 'model_number',
      headerName: 'Model number',
      flex: 1,
    },
    {
      field: 'customer',
      headerName: 'Global Template',
      flex: 1,
      renderCell: (params) => {
        return <>{params.row.customer === null ? 'Yes' : 'No'}</>;
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      renderCell: (params) => (
        <Box sx={{ width: '100%', display: 'flex' }}>
          <Tooltip title="Export">
            <IconButton onClick={() => exportAssetTemplate(params.row)} color="primary">
              <FileUploadIcon />
            </IconButton>
          </Tooltip>
          {!params.row?.customer && (globalAdmin || admin) && (
            <Tooltip title="Clone to Customer">
              <IconButton
                color="default"
                onClick={() => {
                  setOpenCloneDialog(true);
                  setSelectedAssetTemplate(params?.row);
                }}
              >
                <ContentCopy />
              </IconButton>
            </Tooltip>
          )}
          {!params.row?.customer && globalAdmin && (
            <Tooltip title="Edit Template">
              <IconButton
                color="secondary"
                onClick={() =>
                  router.push(
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    //@ts-ignore
                    `/asset-templates/edit?assetType=${params?.row?.asset_type_id}&assetTemplate=${params?.row?.id}`,
                  )
                }
              >
                <Edit />
              </IconButton>
            </Tooltip>
          )}
          {params.row?.customer && admin && (
            <Tooltip title="Edit Template">
              <IconButton
                color="secondary"
                onClick={() =>
                  router.push(
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    //@ts-ignore
                    `/asset-templates/edit?assetType=${params?.row?.asset_type_id}&assetTemplate=${params?.row?.id}`,
                  )
                }
              >
                <Edit />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      ),
    },
  ];
  const [selectedAssetType, setSelectedAssetType] = useState<{
    id: string;
    label: string;
  } | null>();
  const [defaultAssetType, setDefaultAssetType] = useState<AssetTypeOption | null>(null);
  const {
    data: assetTypeListData,
    isLoading: isAssetTypeLoading,
    isSuccess: isSuccessfullBackOffieAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery();
  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapperFilterTemplates(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes, status]);

  const handleChangeAssetType = (
    event: React.SyntheticEvent,
    value: {
      id: string;
      label: string;
    } | null,
  ) => {
    setSelectedAssetType(value);
    if (setSelectedTemplet !== undefined) {
      setSelectedTemplet(null);
    }
  };
  const { data: assetTemplates, isLoading } = useGetAllAssetTemplatedByAssetTypeQuery(
    {
      assetTypeId:
        selectedAssetType !== undefined && selectedAssetType !== null
          ? selectedAssetType?.id.toString()
          : '',
    },
    {
      refetchOnMountOrArgChange: true,
      skip: selectedAssetType === null || selectedAssetType === undefined,
    },
  );
  const { data: assetTypeMetrics } = useGetAllBackOfficeAssetTypesMetricsQuery(
    {
      assetId:
        selectedAssetType !== undefined && selectedAssetType !== null
          ? selectedAssetType?.id.toString()
          : '',
    },
    {
      refetchOnMountOrArgChange: true,
      skip: selectedAssetType === null || selectedAssetType === undefined,
    },
  );
  const { data: valueTypeList } = useGetAllValueTypesQuery();
  const { data: datasourceList } = useGetAllDatasourcesQuery({});
  const { data: measurementLocationsList } = useGetAllLocationsQuery();
  const { data: dataTypeList } = useGetAllDataTypesQuery();
  const { data: measurementTypeList } = useGetAllMeasureTypesQuery();
  const exportAssetTemplate = (assetTemplate: ExtendedCreateAssetTemplateData) => {
    if (!assetTemplate?.measurements) {
      console.warn('No measurements found for export.');
      return;
    }

    const relatedEntities = assetTemplate.measurements.map((measurement, i) => ({
      entityType: 'measurement',
      version: 1,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      entityId: measurement.id,
      measurement: {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        id: measurement?.id,
        metric: {
          name:
            assetTypeMetrics?.items?.find((metric) => metric.id === measurement.type_id)?.name ??
            'N/A',
          id: measurement.type_id ?? null,
        },
        type: {
          name: measurementTypeList?.find((type) => type.id === measurement.type_id)?.name ?? 'N/A',
          id: measurement.type_id ?? null,
        },
        data_type: {
          name: dataTypeList?.find((dt) => dt.id === measurement.data_type_id)?.name ?? 'N/A',
          id: measurement.data_type_id ?? null,
        },
        value_type: {
          name: valueTypeList?.find((vt) => vt.id === measurement.value_type_id)?.name ?? 'N/A',
          id: measurement.value_type_id ?? null,
        },
        datasource: {
          name:
            datasourceList?.items?.find((ds) => ds.id === measurement.datasource_id)?.name ?? 'N/A',
          id: measurement.datasource_id ?? null,
        },
        location: {
          name:
            measurementLocationsList?.items?.find((loc) => loc.id === measurement.location_id)
              ?.name ?? 'N/A',
          id: measurement.location_id ?? null,
        },
        meter_factor: measurement.meter_factor ?? null,
        description: measurement.description ?? '',
      },
    }));
    const exportData = {
      metaInfo: {
        assetTemplate: {
          manufacturer: assetTemplate.manufacturer,
          model_number: assetTemplate.model_number,
          selectedAsset: {
            id: selectedAssetType?.id,
            name: selectedAssetType?.label,
          },
        },
        ...(assetTemplate.customer
          ? {
              customer: {
                name: activeCustomer?.name,
                id: activeCustomer?.id,
                nameId: activeCustomer?.nameId,
              },
            }
          : {
              customer: null,
            }),
      },
      relatedEntities,
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `asset_template_export_${assetTemplate.id}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    showSuccessAlert('Asset template exported successfully');
  };
  useEffect(() => {
    if (asset && assetTypesWithPath.length > 0) {
      const assetType = assetTypesWithPath.find((item) => item.value === asset.type_id);
      if (assetType) {
        setSelectedAssetType({
          id: assetType?.value.toString(),
          label: assetType?.label,
        });
        setDefaultAssetType(assetType);
      }
    }
  }, [asset, assetTypesWithPath]);
  return (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
        <Autocomplete
          disablePortal
          id="combo-box-demo"
          value={
            selectedAssetType
              ? {
                  id: selectedAssetType.id,
                  label: selectedAssetType.label,
                }
              : null
          }
          options={assetTypesWithPath.map((item) => {
            return {
              id: item.value.toString(),
              label: item.label,
            };
          })}
          loading={isAssetTypeLoading}
          sx={{ width: 300 }}
          onChange={(event, value) => handleChangeAssetType(event, value)}
          renderInput={(params) => <TextField {...params} label="Asset Type" />}
        />
      </Box>
      {isLoading ? (
        <Loader />
      ) : (
        <>
          {assetTemplates?.total === 0 ? (
            <Typography mt={3}>
              No Asset Teamplates Found For <b>{selectedAssetType?.label}</b>
            </Typography>
          ) : null}
          {assetTemplates && assetTemplates?.items && assetTemplates?.items?.length > 0 ? (
            <Box sx={{ height: 'calc(90vh - 75px)', width: '100%', overflow: 'hidden' }}>
              <DataGrid
                rowSelectionModel={selectedRow ? [selectedRow] : []}
                rows={assetTemplates?.items || []}
                columns={columns}
                autoPageSize
                getRowId={(row) => row.id}
                sx={{
                  '& .MuiDataGrid-columnHeaderCheckbox .MuiDataGrid-columnHeaderTitleContainer': {
                    display: 'none', // Hides checkbox in header
                  },
                }}
                onRowSelectionModelChange={(newSelection) => {
                  if (setSelectedTemplet) {
                    if (newSelection.length > 0) {
                      const lastSelected = newSelection[newSelection.length - 1] as number;
                      setSelectedRow(lastSelected);
                      const template = assetTemplates?.items?.find(
                        (temp) => (temp as ExtendedCreateAssetTemplateData)?.id === lastSelected,
                      );
                      if (template) {
                        setSelectedTemplet(template);
                      }
                    } else {
                      setSelectedRow(null);
                      setSelectedTemplet(null);
                    }
                  }
                }}
              />
            </Box>
          ) : null}
          {selectedAssetTemplate ? (
            <CloneAssetTemplateForm
              open={openCloneDialog}
              setOpenCloneDialog={setOpenCloneDialog}
              assetTypeId={selectedAssetTemplate?.asset_type_id ?? 0}
              templateId={selectedAssetTemplate?.id ?? 0}
              defaultModelNumber={selectedAssetTemplate.model_number}
              defaultManufacturer={selectedAssetTemplate.manufacturer}
            />
          ) : null}
        </>
      )}
    </>
  );
};
export default SearchAssetTemplates;
