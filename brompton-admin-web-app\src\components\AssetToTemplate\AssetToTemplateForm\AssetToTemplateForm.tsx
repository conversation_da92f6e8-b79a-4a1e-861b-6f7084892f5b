import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import {
  Alert,
  Autocomplete,
  Box,
  Button,
  Card,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormHelperText,
  FormLabel,
  Grid,
  IconButton,
  Paper,
  Radio,
  RadioGroup,
  Stack,
  Step,
  StepLabel,
  Stepper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { Controller } from 'react-hook-form';
import ExpressionTemplateDetails from '~/components/CalcEngine/ExpressionTemplateDetails';
import { CustomError } from '~/errors/CustomerErrorResponse';
import useCreateTemplateFromAsset from '~/hooks/useCreateTemplateFromAsset';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import {
  AssetMeasurement,
  templateFromAssetmeasurementSchemaFrom,
} from '~/measurements/domain/types';
import { useCreateAssetTemplateMultiMutation, useEditAssetMutation } from '~/redux/api/assetsApi';
import { AlertMessage } from '~/shared/forms/types';
import { AssetDoDetails, AssetTypeOption } from '~/types/asset';
import { calc_engine_template, poll_period } from '~/types/calc_engine';
type AssetToTemplateFormProps = {
  asset: AssetDoDetails;
  measurements: AssetMeasurement[] | undefined;
  assetTypesList: AssetTypeOption[] | undefined;
};
const AssetToTemplateForm = ({ asset, assetTypesList, measurements }: AssetToTemplateFormProps) => {
  const [activeStep, setActiveStep] = useState(0);
  const [currentMeausreIndex, setCurrentMeasureIndex] = useState<number>(-1);
  const [measurementIds, setMeasurementIds] = useState<number[]>([]);
  const { globalAdmin, admin } = useHasAdminAccess();
  const [currentCalcMeasureIndex, setCurrentCalcMeasureIndex] = useState<number>(-1);
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const [expression, setExpression] = useState<calc_engine_template | null>(null);
  const [
    createAssetTemplateMulti,
    {
      isError: isMultiError,
      isSuccess: isMultiSuccess,
      data: createAssetTemplateMultiData,
      error: multiError,
      isLoading: isMultiLoading,
    },
  ] = useCreateAssetTemplateMultiMutation();
  const [editAsset] = useEditAssetMutation();

  const {
    steps,
    calculatedMeasures,
    calcMeasurements,
    setCalcMeasurements,
    existingMeasurementMetrics,
    setExistingMeasurementMetrics,
    hasDuplicates,
    assetTypeMetricsListOptions,
    dataTypesListOptions,
    datasourceOptions,
    locationsListOption,
    measurementTypeListOptions,
    valueTypeOptions,
    calculationSource,
    expressionTemplates,
    fetchingExpressionTemplates,
    append,
    createAssetTemplateFromAssets: {
      control,
      formState: { errors },
      getValues,
      handleSubmit,
      setValue,
      trigger,
    },
    measurementsAddEdit: {
      measureClearError,
      measureErrors,
      measurementGetValues,
      measurementSubmit,
      measurementsControl,
      resetMeasure,
      setMeasureValue,
    },
    calculationMeasurement: {
      calcMeasureError,
      calcMeasureGetValues,
      calcMeasureHandleSubmit,
      calcMeasureSetValue,
      calcMeasureWatch,
      calcMeasurementController,
      resetCalcMeasureValues,
      variableFields,
    },
    pollPeriods,
    fields,
    remove,
    update,
  } = useCreateTemplateFromAsset({
    activeStep,
    currentMeausreIndex,
    asset,
    measurements,
    measurementIds,
    setMeasurementIds,
  });
  const variableInputs = calcMeasureWatch('variable_inputs');
  const handleNext = async () => {
    // Trigger validation on first step before moving to second step
    const isValid = await trigger(['asset_type_id', 'manufacturer', 'model_number']);
    if (isValid) {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };
  const handleMeasure = measurementSubmit((data) => {
    const metricIdStr = data.metric_id?.toString() ?? '';
    const isCalcMeasurement = data.datasource_id === calculationSource?.id;
    const addCalcMeasurement = () => {
      // First, add the new calculated measurement
      setCalcMeasurements((prev) => [
        ...prev,
        {
          metric_id: metricIdStr,
          existing: false,
          calcMeasurementData: {
            expression_template_id: null,
            is_persisted: false,
            poll_period: null,
            writeback: false,
            variable_inputs: [],
          },
        },
      ]);
      //   if (currentMeausreIndex > -1) {
      //     const isMetricAssociated = calcMeasurements.some((calc) =>
      //       calc.calcMeasurementData.variable_inputs.some(
      //         (input) => input.metric_id?.toString() === metricIdStr,
      //       ),
      //     );

      //     if (isMetricAssociated) {
      //       setCalcMeasurements((prev) =>
      //         prev.map((calc) => {
      //           return {
      //             ...calc,
      //             calcMeasurementData: {
      //               ...calc.calcMeasurementData,
      //               variable_inputs: calc.calcMeasurementData.variable_inputs.map((input) =>
      //                 input.metric_id?.toString() === metricIdStr
      //                   ? {
      //                       ...input,
      //                       metric_id: null,
      //                       constant_value: null,
      //                       comment: '',
      //                     }
      //                   : input,
      //               ),
      //             },
      //           };
      //         }),
      //       );
      //     }
      //   }
    };

    if (currentMeausreIndex === -1) {
      // If currentMeausreIndex is -1, add a new measurement to the fields array
      append({
        type_id: data.type_id ?? 0,
        location_id: data.location_id ?? null,
        datasource_id: data.datasource_id ?? null,
        description: data.description ?? '',
        meter_factor: data.meter_factor ?? 0,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        metric_id: data.metric_id ?? 0,
        data_type_id: data.data_type_id ?? 0,
        value_type_id: data.value_type_id ?? 0,
        // id: undefined,
      });
      if (isCalcMeasurement) {
        addCalcMeasurement();
      } else {
        setCalcMeasurements((prev) => prev.filter((calc) => calc.metric_id !== metricIdStr));
      }
    } else {
      // Use the update method to replace the specific field at the index
      setValue(`measurements.${currentMeausreIndex}`, {
        type_id: data.type_id ?? 0,
        location_id: data.location_id ?? null,
        datasource_id: data.datasource_id ?? null,
        description: data.description ?? '',
        meter_factor: data.meter_factor ?? 0,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        metric_id: data.metric_id ?? 0,
        data_type_id: data.data_type_id ?? 0,
        value_type_id: data.value_type_id ?? 0,
        // id: data.id ?? undefined,
      });
      if (isCalcMeasurement) {
        const existingCalcMeasurement = calcMeasurements.find(
          (calc) => calc.metric_id?.toString() === metricIdStr,
        );
        if (existingCalcMeasurement) {
          // Update variable_inputs for existing
          setCalcMeasurements((prev) =>
            prev.map((calc) =>
              calc.metric_id === metricIdStr
                ? {
                    ...calc,
                    calcMeasurementData: {
                      ...calc.calcMeasurementData,
                      variable_inputs: [
                        ...calc.calcMeasurementData.variable_inputs,
                        {
                          variable: '',
                          type: 'measurement',
                          metric_id: null,
                          constant_value: null,
                          comment: '',
                        },
                      ],
                    },
                  }
                : calc,
            ),
          );
        } else {
          addCalcMeasurement();
        }
      } else {
        setCalcMeasurements((prev) => prev.filter((calc) => calc.metric_id !== metricIdStr));
      }
      update(currentMeausreIndex, {
        type_id: data.type_id ?? 0,
        location_id: data.location_id ?? null,
        datasource_id: data.datasource_id ?? null,
        description: data.description ?? '',
        meter_factor: data.meter_factor ?? null,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        metric_id: data.metric_id ?? 0,
        data_type_id: data.data_type_id ?? 0,
        value_type_id: data.value_type_id ?? 0,
        // id: data.id ?? undefined,
      });
    }
    // Reset the form and index after submitting the form
    resetMeasure();
    setCurrentMeasureIndex(-1);
  });
  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };
  const expressionTemplate = calcMeasureWatch('expression_template_id');
  useEffect(() => {
    if (!expressionTemplate || !expressionTemplates) {
      setExpression(null);
      calcMeasureSetValue('variable_inputs', []);
      return;
    }
    const expressionTemplateToFind = expressionTemplates.items.find(
      (template) => template.id === expressionTemplate,
    );
    if (!expressionTemplateToFind) {
      setExpression(null);
      calcMeasureSetValue('variable_inputs', []);
      return;
    }
    setExpression(expressionTemplateToFind);
    calcMeasureSetValue('expression_template_id', expressionTemplateToFind.id);

    const variableMatches = expressionTemplateToFind.expression.match(/\$[A-Za-z0-9_]+/g);
    const uniqueVariables = Array.from(new Set(variableMatches ?? []));
    const defaultVariableInputs = uniqueVariables.map((variable) => ({
      variable,
      type: 'measurement' as const,
      metric_id: null,
      constant_value: null,
      comment: '',
    }));

    const currentMetricId = fields[currentCalcMeasureIndex]?.metric_id?.toString();
    const matchedCalc = calcMeasurements.find((calc) => calc.metric_id === currentMetricId);
    const updatedVariableInputs = defaultVariableInputs.map((input) => {
      const existing = matchedCalc?.calcMeasurementData.variable_inputs.find(
        (v) => v.variable?.trim() === input.variable.trim(),
      );
      return {
        ...input,
        type: existing?.type ?? input.type,
        metric_id: existing?.metric_id?.toString() ?? null,
        constant_value: existing?.constant_value ?? null,
        comment: existing?.comment ?? '',
      };
    });

    // Set form state
    calcMeasureSetValue('variable_inputs', updatedVariableInputs);
    setCalcMeasurements((prev) =>
      prev.map((calc) => {
        if (calc.metric_id === currentMetricId) {
          return {
            ...calc,
            variable: updatedVariableInputs,
            calcMeasurementData: {
              ...calc.calcMeasurementData,
              variable_inputs: updatedVariableInputs,
            },
          };
        }
        return calc;
      }),
    );
  }, [expressionTemplate, expressionTemplates]);
  useEffect(() => {
    if (isMultiSuccess && createAssetTemplateMultiData) {
      setAlertMessage({
        message: `Asset Template "${createAssetTemplateMultiData.id}" created successfully!`,
        severity: 'success',
      });
      //   const timeout = setTimeout(() => {
      //     setActiveStep(0);
      //     // resetCalcMeasureValues();
      //     // reset();
      //     setMeasureValue('data_type_id', null);
      //     setMeasureValue('value_type_id', null);
      //     setMeasureValue('type_id', null);
      //     setMeasureValue('metric_id', null);
      //     setMeasureValue('description', '');
      //     setMeasureValue('location_id', null);
      //     setMeasureValue('datasource_id', null);
      //     setMeasureValue('meter_factor', undefined);
      //     // setMeasurements([]);
      //     // setCalcMeasurements([]);
      //     setCurrentMeasureIndex(-1);
      //     // setCurrentCalcMeasureIndex(-1);
      //     // setIsEdit(false);
      //     // setIsFirst(true);
      //     resetMeasure();
      //     // resetCalcMeasureValues();
      //   }, 5000);

      //   return () => clearTimeout(timeout);
    }

    if (isMultiError && multiError) {
      const err = multiError as CustomError;
      setAlertMessage({
        message: err.data?.exception ?? 'Server error',
        severity: 'error',
      });
    }
  }, [isMultiSuccess, createAssetTemplateMultiData, isMultiError, multiError]);
  let hasInvalidVariable = false;

  calcMeasurements.forEach((measure) => {
    if (measure.calcMeasurementData.variable_inputs.length === 0) {
      hasInvalidVariable = true;
    }
    measure.calcMeasurementData.variable_inputs.forEach((varis) => {
      if (varis.type === 'measurement') {
        if (varis.metric_id === null || varis.metric_id === undefined) {
          hasInvalidVariable = true;
        }
      } else if (varis.type === 'constant') {
        if (varis.constant_value === null || varis.constant_value === undefined) {
          hasInvalidVariable = true;
        }
      } else {
        hasInvalidVariable = true; // invalid type
      }
    });
  });

  const hasExistingTemplate = !!asset?.assetTemplate;

  return (
    <Box sx={{ pr: 3 }}>
      <Stepper
        activeStep={activeStep}
        alternativeLabel
        sx={{ position: 'sticky', top: 0, backgroundColor: 'white', zIndex: 1000, padding: '10px' }}
      >
        {steps.filter(Boolean).map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
      <form
        onSubmit={handleSubmit(async (data: templateFromAssetmeasurementSchemaFrom) => {
          if (calculatedMeasures.length > 0) {
            await handleNext();
          } else {
            const stringMetrics = fields.filter((measure) => isNaN(Number(measure.metric_id)));
            createAssetTemplateMulti({
              metrics: stringMetrics
                .map((measure) => measure.metric_id?.toString())
                .filter((id): id is string => id !== undefined),
              newTemplate: {
                ...getValues(),
                assetTypeId: getValues('asset_type_id') ?? 0,
                measurements: fields.map((measure) => ({
                  ...measure,
                  location_id:
                    measure.location_id !== undefined &&
                    measure.location_id !== null &&
                    measure.location_id !== 0
                      ? measure.location_id
                      : undefined,
                  datasource_id:
                    measure.datasource_id !== undefined &&
                    measure.datasource_id !== null &&
                    measure.datasource_id !== 0
                      ? measure.datasource_id
                      : undefined,
                })),
              },
              expressionInstance: undefined,
            })
              .unwrap()
              .then((data) => {
                const shouldLink = asset?.id
                  ? !asset.assetTemplate?.id
                    ? getValues('link_asset_to_template')
                    : getValues('override_existing_asset_template')
                  : false;

                const newTemplateId = data?.id;

                if (shouldLink && newTemplateId && asset?.id && asset?.customer_id) {
                  editAsset({
                    customerId: asset.customer_id,
                    assetId: asset.id.toString(),
                    editAsset: {
                      assetTemplate: newTemplateId,
                      tag: asset.tag,
                      type_id: asset.type_id,
                      parent_ids: asset.parent_ids ?? [],
                      childrenIds: asset.childrenIds ?? [],
                    },
                  });
                }
              });
          }
        })}
      >
        {activeStep === 0 && (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <Typography>Asset : {asset?.tag}</Typography>
            <Controller
              name="asset_type_id"
              control={control}
              render={({ field }) => (
                <Autocomplete
                  {...field}
                  //   disabled={isFetching}
                  disabled
                  options={assetTypesList ? assetTypesList : []}
                  getOptionLabel={(option) => option.label || ''}
                  value={
                    assetTypesList?.find((assetType) => assetType.value === field.value) ?? null
                  } // Use the full object as the value
                  onChange={(_, value) => {
                    if (value?.value) {
                      setValue('asset_type_id', value?.value ?? null);
                    }
                  }} // Set the selected option as the value
                  onBlur={field.onBlur}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Asset Type"
                      error={!!errors.asset_type_id}
                      helperText={errors.asset_type_id?.message}
                    />
                  )}
                />
              )}
            />
            <Controller
              name="manufacturer"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Manufacturer"
                  error={!!errors.manufacturer}
                  onBlur={field.onBlur}
                  helperText={errors.manufacturer?.message}
                  InputLabelProps={{ shrink: true }}
                />
              )}
            />
            <Controller
              name="model_number"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Model Number"
                  error={!!errors.model_number}
                  onBlur={field.onBlur}
                  helperText={errors.model_number?.message}
                  InputLabelProps={{ shrink: true }}
                />
              )}
            />
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Controller
                name="save_as_global_asset_template"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Checkbox
                        {...field}
                        disabled={!globalAdmin}
                        checked={field.value || false}
                        onChange={(e) => field.onChange(e.target.checked)}
                      />
                    }
                    label="Save as Global Asset Template"
                  />
                )}
              />

              <>
                {!hasExistingTemplate ? (
                  <Controller
                    name="link_asset_to_template"
                    control={control}
                    defaultValue={false}
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            checked={field.value || false}
                            onChange={(e) => field.onChange(e.target.checked)}
                          />
                        }
                        label="Link asset to asset template"
                      />
                    )}
                  />
                ) : (
                  <Controller
                    name="override_existing_asset_template"
                    control={control}
                    defaultValue={false}
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            {...field}
                            checked={field.value || false}
                            onChange={(e) => field.onChange(e.target.checked)}
                          />
                        }
                        label="Override existing asset template for this asset"
                      />
                    )}
                  />
                )}
              </>
            </Box>
            <Button onClick={handleNext} variant="contained">
              Next
            </Button>
          </Box>
        )}
        {activeStep === 1 && (
          <>
            <Box sx={{ mt: 2, mb: 2, p: 2 }} component={Card}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                {currentMeausreIndex === -1
                  ? 'Add Measurement'
                  : `Edit Measurement #${currentMeausreIndex + 1}`}
              </Typography>
              <form onSubmit={(e) => e.preventDefault()}>
                <Grid container spacing={2}>
                  {/* Type Field */}
                  <Grid item md={4}>
                    <Controller
                      name={`type_id`}
                      control={measurementsControl}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          options={measurementTypeListOptions}
                          getOptionLabel={(option) => option.label || ''}
                          value={
                            measurementTypeListOptions.find(
                              (item) => item.id === field.value?.toString(),
                            ) ?? null
                          }
                          onBlur={field.onBlur}
                          onChange={(_, value) => {
                            if (value?.id) {
                              setMeasureValue('type_id', Number(value?.id ?? null));
                              measureClearError('type_id');
                            }
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Type"
                              error={!!measureErrors?.type_id}
                              helperText={measureErrors?.type_id?.message}
                            />
                          )}
                        />
                      )}
                    />
                  </Grid>

                  {/* Data Type Field */}
                  <Grid item md={4}>
                    <Controller
                      name={`data_type_id`}
                      control={measurementsControl}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          options={dataTypesListOptions}
                          getOptionLabel={(option) => option.label || ''}
                          value={
                            dataTypesListOptions.find(
                              (item) => item.id === field.value?.toString(),
                            ) ?? null
                          }
                          onBlur={field.onBlur}
                          onChange={(_, value) => {
                            if (value?.id) {
                              setMeasureValue('data_type_id', Number(value?.id ?? null));
                              measureClearError('data_type_id');
                            }
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Data Type"
                              error={!!measureErrors?.data_type_id}
                              helperText={measureErrors?.data_type_id?.message}
                            />
                          )}
                        />
                      )}
                    />
                  </Grid>

                  {/* Value Type Field */}
                  <Grid item md={4}>
                    <Controller
                      name={`value_type_id`}
                      control={measurementsControl}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          options={valueTypeOptions}
                          getOptionLabel={(option) => option.label || ''}
                          value={
                            valueTypeOptions.find((item) => item.id === field.value?.toString()) ??
                            null
                          }
                          onBlur={field.onBlur}
                          onChange={(_, value) => {
                            if (value?.id) {
                              setMeasureValue('value_type_id', Number(value?.id ?? null));
                              measureClearError('value_type_id');
                            }
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Value Type"
                              error={!!measureErrors?.value_type_id}
                              helperText={measureErrors?.value_type_id?.message}
                            />
                          )}
                        />
                      )}
                    />
                  </Grid>

                  {/* Metric Field */}
                  <Grid item md={4}>
                    <Controller
                      name="metric_id"
                      control={measurementsControl}
                      render={({ field }) => {
                        const selectedOption = assetTypeMetricsListOptions.find(
                          (option) => String(option.id) === String(field.value),
                        );
                        return (
                          <Autocomplete
                            freeSolo
                            sx={{
                              '.MuiFormControl-root': {
                                mt: 0,
                              },
                              '*': { mt: 0, background: 'transparent' },
                            }}
                            options={assetTypeMetricsListOptions}
                            getOptionLabel={(option) =>
                              typeof option === 'string' ? option : String(option.label)
                            }
                            isOptionEqualToValue={(option, value) =>
                              typeof option === 'string'
                                ? option === value
                                : String(option.id) === String(value.id)
                            }
                            value={
                              selectedOption ||
                              (field.value ? { id: field.value, label: field.value } : null)
                            }
                            onChange={(_, newValue) => {
                              let updatedMetricId: string | null = null;
                              if (typeof newValue === 'string') {
                                field.onChange(newValue);
                              } else if (newValue && typeof newValue.id === 'string') {
                                field.onChange(newValue.id);
                                updatedMetricId = newValue.id;
                              } else {
                                field.onChange(null);
                              }
                              if (updatedMetricId !== null) {
                                setCalcMeasurements((prev) =>
                                  prev.map((entry) => {
                                    const shouldUpdateOutput =
                                      String(entry.metric_id) === String(field.value);
                                    const updatedInputs =
                                      entry.calcMeasurementData.variable_inputs.map((input) => {
                                        if (String(input.metric_id) === String(field.value)) {
                                          return {
                                            ...input,
                                            // metric_id: Number(updatedMetricId),
                                            metric_id: updatedMetricId?.toString(),
                                          };
                                        }
                                        return input;
                                      });
                                    if (
                                      shouldUpdateOutput ||
                                      updatedInputs.some(
                                        (input, i) =>
                                          input !== entry.calcMeasurementData.variable_inputs[i],
                                      )
                                    ) {
                                      return {
                                        ...entry,
                                        metric_id: shouldUpdateOutput
                                          ? String(updatedMetricId) // ✅ safely convert to string
                                          : entry.metric_id,
                                        calcMeasurementData: {
                                          ...entry.calcMeasurementData,
                                          variable_inputs: updatedInputs,
                                        },
                                      };
                                    }
                                    return entry;
                                  }),
                                );
                              }
                            }}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                label="Metric"
                                fullWidth
                                value={
                                  selectedOption ||
                                  (field.value ? { id: field.value, label: field.value } : null)
                                }
                                margin="normal"
                                onChange={(event) => {
                                  const newValue = event.target.value as
                                    | string
                                    | {
                                        id: string;
                                        label: string;
                                      };
                                  if (typeof newValue === 'string') {
                                    field.onChange(newValue);
                                  } else if (newValue && typeof newValue.id === 'string') {
                                    field.onChange(newValue.id);
                                  } else {
                                    field.onChange(null);
                                  }
                                }}
                                error={!!measureErrors?.metric_id}
                                helperText={measureErrors?.metric_id?.message || ''}
                                required
                              />
                            )}
                          />
                        );
                      }}
                    />
                  </Grid>

                  {/* Description Field */}
                  <Grid item md={4}>
                    <Controller
                      name={`description`}
                      control={measurementsControl}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          label="Description"
                          error={!!measureErrors?.description}
                          helperText={measureErrors?.description?.message}
                        />
                      )}
                    />
                  </Grid>

                  {/* Location Field */}
                  <Grid item md={4}>
                    <Controller
                      name={`location_id`}
                      control={measurementsControl}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          options={locationsListOption}
                          getOptionLabel={(option) => option.label || ''}
                          value={
                            locationsListOption.find(
                              (item) => item.id.toString() === field.value?.toString(),
                            ) ?? null
                          }
                          onBlur={field.onBlur}
                          onChange={(_, value) => {
                            if (value?.id) {
                              setMeasureValue('location_id', Number(value?.id ?? null));
                              measureClearError('location_id');
                            }
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Location"
                              error={!!measureErrors?.location_id}
                              helperText={measureErrors?.location_id?.message}
                            />
                          )}
                        />
                      )}
                    />
                  </Grid>

                  {/* DataSource Field */}
                  <Grid item md={4}>
                    <Controller
                      name={`datasource_id`}
                      control={measurementsControl}
                      render={({ field }) => (
                        <Autocomplete
                          {...field}
                          //   disabled={
                          //     measurementGetValues('metric_id') != null &&
                          //     existingMeasurementMetrics.includes(
                          //       measurementGetValues('metric_id')!.toString(),
                          //     )
                          //   }
                          options={datasourceOptions}
                          getOptionLabel={(option) => option.label || ''}
                          value={
                            datasourceOptions.find((item) => item.id === field.value?.toString()) ??
                            null
                          }
                          onBlur={field.onBlur}
                          onChange={(_, value) => {
                            setMeasureValue('datasource_id', Number(value?.id ?? null));
                            measureClearError('datasource_id');
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Datasource"
                              error={!!measureErrors?.datasource_id}
                              helperText={measureErrors?.datasource_id?.message}
                            />
                          )}
                        />
                      )}
                    />
                  </Grid>

                  {/* Meter Factor Field */}
                  <Grid item md={4}>
                    <Controller
                      name={`meter_factor`}
                      control={measurementsControl}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          fullWidth
                          label="Meter Factor"
                          error={!!measureErrors?.meter_factor}
                          helperText={measureErrors?.meter_factor?.message}
                          InputLabelProps={{ shrink: true }}
                        />
                      )}
                    />
                  </Grid>
                </Grid>

                <Button type="submit" sx={{ mt: 2 }} variant="contained" onClick={handleMeasure}>
                  {currentMeausreIndex === -1 ? 'Add' : 'Update'}
                </Button>
                <Button
                  type="reset"
                  sx={{ mt: 2, ml: 3 }}
                  variant="outlined"
                  onClick={() => {
                    setCurrentMeasureIndex(-1);
                    resetMeasure();
                  }}
                >
                  Cancel
                </Button>
              </form>
            </Box>
            <Card sx={{ p: 2 }}>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ padding: '4px 8px', maxWidth: 50 }}>ID</TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px', minHeight: '80px' }}>
                        Type
                      </TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px', minHeight: '80px' }}>
                        Data Type
                      </TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px', minHeight: '80px' }}>
                        Value Type
                      </TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px', minHeight: '80px' }}>
                        Metric
                      </TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px', minHeight: '80px' }}>
                        Description
                      </TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px', minHeight: '80px' }}>
                        Location
                      </TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px' }}>Datasource</TableCell>
                      <TableCell sx={{ padding: '4px 8px', width: '200px' }}>
                        Meter Factor
                      </TableCell>
                      <TableCell sx={{ padding: '4px 8px', maxWidth: 50 }}>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {fields.map((field, index) => (
                      <TableRow key={field.id}>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          <Typography variant="body1">{index + 1}</Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          {/* Show the selected value for Type */}
                          <Typography variant="body1">
                            {measurementTypeListOptions.find(
                              (item) => item.id === field.type_id?.toString(),
                            )?.label || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          {/* Show the selected value for Data Type */}
                          <Typography variant="body1">
                            {dataTypesListOptions.find(
                              (item) => item.id === field.data_type_id?.toString(),
                            )?.label || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          <Typography variant="body1">
                            {valueTypeOptions.find(
                              (item) => item.id === field.value_type_id?.toString(),
                            )?.label || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          <Typography variant="body1">
                            {(() => {
                              const isCustom = isNaN(Number(field.metric_id));
                              const label = isCustom
                                ? field.metric_id || 'N/A'
                                : assetTypeMetricsListOptions?.find(
                                    (item) => item.id?.toString() === field.metric_id?.toString(),
                                  )?.label || 'N/A';

                              return <Typography variant="body1">{String(label)}</Typography>;
                            })()}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          {/* Show the Description */}
                          <Typography variant="body1">{field.description || 'N/A'}</Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          {/* Show the Location */}
                          <Typography variant="body1">
                            {locationsListOption.find(
                              (item) => item.id.toString() === field.location_id?.toString(),
                            )?.label || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          {/* Show the Data Source */}
                          <Typography variant="body1">
                            {datasourceOptions.find(
                              (item) => item.id === field.datasource_id?.toString(),
                            )?.label || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          <Typography variant="body1">{field.meter_factor || 'N/A'}</Typography>
                        </TableCell>
                        <TableCell sx={{ padding: '4px 8px' }}>
                          <Box sx={{ width: '100%', display: 'flex' }}>
                            <IconButton
                              color="error"
                              onClick={() => {
                                remove(index);
                                setCalcMeasurements((prev) =>
                                  prev.filter(
                                    (calc) => calc.metric_id !== field.metric_id?.toString(),
                                  ),
                                );
                              }}
                            >
                              <DeleteIcon />
                            </IconButton>
                            <IconButton
                              color="primary"
                              onClick={() => {
                                resetMeasure();
                                setCurrentMeasureIndex(index);
                                setMeasureValue('type_id', field.type_id);
                                setMeasureValue('data_type_id', field.data_type_id);
                                setMeasureValue('value_type_id', field.value_type_id);
                                setMeasureValue('metric_id', field.metric_id);
                                setMeasureValue('description', field.description);
                                setMeasureValue('location_id', field.location_id);
                                setMeasureValue('datasource_id', field.datasource_id);
                                setMeasureValue('meter_factor', field.meter_factor);
                                // const measurementID = data?.measurements.at(index)?.id;
                                // setMeasureValue('id', measurementID ?? undefined);
                              }}
                            >
                              <EditIcon />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Card>
            {errors.measurements?.message && (
              <Alert severity="error">{errors.measurements?.message}</Alert>
            )}
            {fields.filter(
              (measure) => measure.metric_id === null || measure.metric_id === undefined,
            ).length > 0 ? (
              <Alert severity="error">Metric can not be empty</Alert>
            ) : (
              <>
                {hasDuplicates() ? (
                  <Alert severity="error">Metric can not be duplicate</Alert>
                ) : null}
              </>
            )}
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                position: 'sticky',
                bottom: 0,
                backgroundColor: 'white',
                zIndex: 1000,
                padding: '10px',
              }}
            >
              {/*  sx={{ position: 'sticky', top: 0, backgroundColor: 'white', zIndex: 1000, padding: '10px' }} */}
              <Button onClick={handleBack} variant="outlined">
                Back
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={
                  hasDuplicates() ||
                  fields.filter(
                    (measure) => measure.metric_id === null || measure.metric_id === undefined,
                  ).length > 0 ||
                  fields.length === 0 ||
                  isMultiLoading
                }
              >
                {calculatedMeasures.length > 0 ? 'Save & Next' : 'Submit'}
              </Button>
            </Box>
          </>
        )}
      </form>
      {activeStep === 2 && (
        <>
          <Typography variant="h6" my={2}>
            Calculated Measurements Mapping
          </Typography>
          <TableContainer component={Paper}>
            <Table sx={{ minWidth: 650 }} aria-label="simple table">
              <TableHead>
                <TableRow>
                  <TableCell align="center">Type </TableCell>
                  <TableCell align="center">Data Type</TableCell>
                  <TableCell align="center">Value Type</TableCell>
                  <TableCell align="center">Metric</TableCell>
                  <TableCell align="center">Location</TableCell>
                  <TableCell align="center">Data Source</TableCell>
                  <TableCell align="center">Description</TableCell>
                  <TableCell align="center">Meter Factor</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {fields.map((row, i) => {
                  if (
                    calculationSource === undefined ||
                    row.datasource_id !== calculationSource?.id
                  )
                    return null;
                  return (
                    <TableRow
                      key={i} // Use unique `id` if available from useFieldArray
                      sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                    >
                      <TableCell component="th" scope="row" align="center">
                        {measurementTypeListOptions.find(
                          (measure) => measure.id === row.type_id?.toString(),
                        )?.label ?? 'N/A'}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        {dataTypesListOptions.find(
                          (dataType) => dataType.id === row.data_type_id?.toString(),
                        )?.label ?? 'N/A'}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        {valueTypeOptions.find(
                          (valueType) => valueType.id === row.value_type_id?.toString(),
                        )?.label ?? 'N/A'}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        {(() => {
                          const metric = row.metric_id;
                          if (!metric) return null;
                          const metricString = metric.toString();
                          const foundMetric = assetTypeMetricsListOptions.find(
                            (asset) => asset.id === metricString,
                          );
                          return foundMetric ? foundMetric.label : metricString;
                        })()}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        {locationsListOption.find(
                          (location) => location.id === row.location_id?.toString(),
                        )?.label ?? 'N/A'}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        {datasourceOptions.find(
                          (source) => source.id === row.datasource_id?.toString(),
                        )?.label ?? 'N/A'}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        {row.description ?? 'N/A'}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        {row.meter_factor ?? 'N/A'}
                      </TableCell>
                      <TableCell component="th" scope="row" align="center">
                        <Box
                          display={'flex'}
                          justifyContent={'space-around'}
                          sx={{ cursor: 'pointer' }}
                        >
                          <EditIcon
                            onClick={() => {
                              if (currentCalcMeasureIndex !== i) {
                                setCurrentCalcMeasureIndex(i);
                                const findCalcMeasure = calcMeasurements.find(
                                  (calc) => calc.metric_id === row.metric_id?.toString(),
                                );
                                // console.log(findCalcMeasure, calcMeasurements);
                                if (findCalcMeasure) {
                                  calcMeasureSetValue(
                                    'expression_template_id',
                                    findCalcMeasure.calcMeasurementData.expression_template_id,
                                  );
                                  calcMeasureSetValue(
                                    'variable_inputs',
                                    findCalcMeasure.calcMeasurementData.variable_inputs,
                                  );
                                  calcMeasureSetValue(
                                    'poll_period',
                                    findCalcMeasure.calcMeasurementData.poll_period,
                                  );
                                  calcMeasureSetValue(
                                    'writeback',
                                    findCalcMeasure.calcMeasurementData.writeback,
                                  );
                                  calcMeasureSetValue(
                                    'is_persisted',
                                    findCalcMeasure.calcMeasurementData.is_persisted,
                                  );
                                } else {
                                  calcMeasureSetValue('expression_template_id', null);
                                  calcMeasureSetValue('variable_inputs', []);
                                  calcMeasureSetValue('poll_period', null);
                                  calcMeasureSetValue('writeback', false);
                                  calcMeasureSetValue('is_persisted', false);
                                }
                              } else {
                                setCurrentCalcMeasureIndex(-1);
                              }
                            }}
                          />
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
          {hasInvalidVariable && (
            <Alert color="error">Each variable must have a valid metric or constant value.</Alert>
          )}
          {currentCalcMeasureIndex !== -1 &&
            fields[currentCalcMeasureIndex] &&
            (() => {
              return (
                <form
                  onSubmit={calcMeasureHandleSubmit((data) => {
                    const measuremetToCalcMetric = fields[currentCalcMeasureIndex]?.metric_id;
                    if (measuremetToCalcMetric) {
                      setCalcMeasurements(
                        calcMeasurements.map((calc) => {
                          if (calc.metric_id === measuremetToCalcMetric?.toString()) {
                            return {
                              ...calc,
                              existing: false,
                              calcMeasurementData: {
                                expression_template_id: data.expression_template_id,
                                is_persisted: data.is_persisted,
                                poll_period: data.poll_period,
                                writeback: data.writeback,
                                variable_inputs: data.variable_inputs.map((vars) => ({
                                  ...vars,
                                  metric_id: vars?.metric_id,
                                  constant_value: vars?.constant_value,
                                  comment: vars?.comment,
                                })),
                              },
                            };
                          }
                          return calc;
                        }),
                      );
                      resetCalcMeasureValues();
                      setCurrentCalcMeasureIndex(-1);
                    }
                  })}
                >
                  <Box component={Card} sx={{ mt: 2, p: 3 }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Editing Calculation Measurement: {currentCalcMeasureIndex + 1}
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 2 }}>
                      Metric:{' '}
                      {(() => {
                        const metric = fields[currentCalcMeasureIndex]?.metric_id;
                        if (!metric) return null;
                        const metricString = metric.toString();
                        const foundMetric = assetTypeMetricsListOptions.find(
                          (asset) => asset.id === metricString,
                        );
                        return foundMetric ? foundMetric.label : metricString;
                      })()}
                    </Typography>

                    <Stack spacing={2}>
                      <Controller
                        name="expression_template_id"
                        control={calcMeasurementController}
                        render={({ field }) => {
                          const selectedTemplate =
                            expressionTemplates?.items?.find((item) => item.id === field.value) ??
                            null;
                          return (
                            <Autocomplete
                              fullWidth
                              loading={fetchingExpressionTemplates}
                              //   disabled={
                              //     calcMeasurements.find(
                              //       (calc) =>
                              //         calc.metric_id ===
                              //         fields[currentCalcMeasureIndex]?.metric_id?.toString(),
                              //     )?.existing ?? false
                              //   }
                              options={expressionTemplates?.items || []}
                              getOptionLabel={(option) => option.name || ''}
                              value={selectedTemplate}
                              onChange={(_, value) => field.onChange(value?.id ?? null)}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  label="Expression Template"
                                  error={!!calcMeasureError.expression_template_id}
                                  helperText={calcMeasureError.expression_template_id?.message}
                                  variant="outlined"
                                />
                              )}
                            />
                          );
                        }}
                      />

                      <ExpressionTemplateDetails selectedTemplate={expression} />
                      <Box sx={{ my: 2 }}>
                        {variableInputs && variableInputs.length > 0 && (
                          <Box>
                            <Typography variant="subtitle1" sx={{ mb: 2 }}>
                              Variable Inputs
                            </Typography>

                            {variableFields.map((item, index) => (
                              <Box
                                key={item.id}
                                sx={{
                                  mb: 3,
                                  p: 2,
                                  border: '1px solid #ccc',
                                  borderRadius: 1,
                                }}
                              >
                                <Typography variant="body2" sx={{ mb: 2 }}>
                                  Variable: <strong>{item.variable}</strong>
                                </Typography>

                                <Grid container spacing={2}>
                                  <Grid item xs={12} md={4}>
                                    <Controller
                                      name={`variable_inputs.${index}.type`}
                                      control={calcMeasurementController}
                                      render={({ field }) => (
                                        <FormControl
                                          component="fieldset"
                                          fullWidth
                                          error={!!calcMeasureError?.variable_inputs?.[index]?.type}
                                          onChange={(event) => {
                                            field.onChange(event);
                                            calcMeasureSetValue(
                                              `variable_inputs.${index}.metric_id`,
                                              null,
                                            );
                                            calcMeasureSetValue(
                                              `variable_inputs.${index}.constant_value`,
                                              null,
                                            );
                                          }}
                                        >
                                          <FormLabel component="legend">Type</FormLabel>
                                          <RadioGroup row {...field}>
                                            <FormControlLabel
                                              value="measurement"
                                              control={<Radio />}
                                              label="Measurement"
                                            />
                                            <FormControlLabel
                                              value="constant"
                                              control={<Radio />}
                                              label="Constant"
                                            />
                                          </RadioGroup>
                                          {calcMeasureError?.variable_inputs?.[index]?.type && (
                                            <FormHelperText>
                                              {calcMeasureError.variable_inputs[
                                                index
                                              ]?.type?.toString()}
                                            </FormHelperText>
                                          )}
                                        </FormControl>
                                      )}
                                    />
                                  </Grid>

                                  {/* METRIC ID or CONSTANT VALUE */}
                                  <Grid item xs={12} md={4}>
                                    {variableInputs[index]?.type === 'measurement' ? (
                                      <Controller
                                        name={`variable_inputs.${index}.metric_id`}
                                        control={calcMeasurementController}
                                        render={({ field }) => {
                                          const nonExist = fields
                                            .filter((measure) => {
                                              return (
                                                !Number.isNaN(measure.metric_id) &&
                                                measure.datasource_id !== calculationSource?.id
                                              );
                                            })
                                            .map((measure) => measure);
                                          const options = assetTypeMetricsListOptions.filter(
                                            (metric) =>
                                              !calculatedMeasures.some(
                                                (cm) => cm.metric_id?.toString() === metric.id,
                                              ) &&
                                              fields.some(
                                                (cm) => cm.metric_id?.toString() === metric.id,
                                              ),
                                          );
                                          nonExist.forEach((measure) => {
                                            // Check if the measure already exists in options
                                            const existsInOptions = options.some(
                                              (option) =>
                                                option.id === measure.metric_id?.toString(),
                                            );

                                            // If it doesn't exist in options, push it into options
                                            if (!existsInOptions) {
                                              options.push({
                                                id: measure.metric_id?.toString() ?? '',
                                                label: measure.metric_id?.toString() ?? '',
                                              }); // Or customize the structure as needed
                                            }
                                          });
                                          const selectedOption =
                                            options.find(
                                              (opt) =>
                                                opt.id?.toString() === field.value?.toString(),
                                            ) ?? null;
                                          //   console.log(options, selectedOption);
                                          return (
                                            <Autocomplete
                                              fullWidth
                                              options={options}
                                              getOptionLabel={(option) => option.label}
                                              isOptionEqualToValue={(option, value) =>
                                                option.id === value?.id
                                              }
                                              value={selectedOption}
                                              onChange={(_, newValue) =>
                                                field.onChange(newValue?.id ?? null)
                                              }
                                              renderInput={(params) => (
                                                <TextField
                                                  {...params}
                                                  label="Metric"
                                                  error={
                                                    !!calcMeasureError?.variable_inputs?.[index]
                                                      ?.metric_id
                                                  }
                                                  helperText={
                                                    calcMeasureError?.variable_inputs?.[index]
                                                      ?.metric_id?.message
                                                  }
                                                />
                                              )}
                                            />
                                          );
                                        }}
                                      />
                                    ) : (
                                      <Controller
                                        name={`variable_inputs.${index}.constant_value`}
                                        control={calcMeasurementController}
                                        render={({ field }) => (
                                          <TextField
                                            {...field}
                                            label="Constant Value"
                                            fullWidth
                                            error={
                                              !!calcMeasureError?.variable_inputs?.[index]
                                                ?.constant_value
                                            }
                                            helperText={
                                              calcMeasureError?.variable_inputs?.[index]
                                                ?.constant_value?.message
                                            }
                                          />
                                        )}
                                      />
                                    )}
                                  </Grid>

                                  {/* COMMENT */}
                                  <Grid item xs={12} md={4}>
                                    <Controller
                                      name={`variable_inputs.${index}.comment`}
                                      control={calcMeasurementController}
                                      render={({ field }) => (
                                        <TextField
                                          {...field}
                                          label="Comment"
                                          fullWidth
                                          error={
                                            !!calcMeasureError?.variable_inputs?.[index]?.comment
                                          }
                                          helperText={
                                            calcMeasureError?.variable_inputs?.[index]?.comment
                                              ?.message
                                          }
                                        />
                                      )}
                                    />
                                  </Grid>
                                </Grid>
                              </Box>
                            ))}
                          </Box>
                        )}
                      </Box>

                      <Controller
                        name="is_persisted"
                        control={calcMeasurementController}
                        render={({ field }) => (
                          <FormControlLabel
                            control={
                              <Checkbox {...field} checked={!!field.value} color="primary" />
                            }
                            label="Is Persisted"
                          />
                        )}
                      />
                      {calcMeasureWatch('is_persisted') ? (
                        <>
                          <Controller
                            name="writeback"
                            control={calcMeasurementController}
                            render={({ field }) => (
                              <FormControlLabel
                                control={
                                  <Checkbox {...field} checked={!!field.value} color="primary" />
                                }
                                label="Writeback"
                              />
                            )}
                          />

                          <Controller
                            name="poll_period"
                            control={calcMeasurementController}
                            render={({ field }) => (
                              <Autocomplete<poll_period>
                                fullWidth
                                options={(pollPeriods?.items || []) as poll_period[]}
                                getOptionLabel={(option) => String(option.value)}
                                isOptionEqualToValue={(option, value) => option.id === value?.id}
                                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                                //@ts-ignore
                                value={field.value ?? null}
                                onChange={(_, value) => field.onChange(value)}
                                renderInput={(params) => (
                                  <TextField
                                    {...params}
                                    label="Poll Period"
                                    error={!!calcMeasureError.poll_period}
                                    helperText={calcMeasureError.poll_period?.message}
                                    variant="outlined"
                                  />
                                )}
                              />
                            )}
                          />
                        </>
                      ) : null}
                    </Stack>
                    <Button type="submit" color="primary" variant="contained" sx={{ mt: 2 }}>
                      Save
                    </Button>
                    <Button
                      type="reset"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 2, ml: 1.5 }}
                      onClick={() => {
                        resetCalcMeasureValues();
                        setCurrentCalcMeasureIndex(-1);
                      }}
                    >
                      cancel
                    </Button>
                  </Box>
                </form>
              );
            })()}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              position: 'sticky',
              bottom: 0,
              backgroundColor: 'white',
              zIndex: 1000,
              padding: '10px',
            }}
          >
            <Button onClick={handleBack} variant="outlined">
              Back
            </Button>
            <Button
              onClick={async () => {
                const stringMetrics = fields.filter((measure) => isNaN(Number(measure.metric_id)));
                createAssetTemplateMulti({
                  metrics: stringMetrics
                    .map((measure) => measure.metric_id?.toString())
                    .filter((id): id is string => id !== undefined),
                  newTemplate: {
                    ...getValues(),
                    assetTypeId: getValues('asset_type_id') ?? 0,
                    measurements: fields.map((measure) => ({
                      ...measure,
                      location_id:
                        measure.location_id !== undefined &&
                        measure.location_id !== null &&
                        measure.location_id !== 0
                          ? measure.location_id
                          : undefined,
                      datasource_id:
                        measure.datasource_id !== undefined &&
                        measure.datasource_id !== null &&
                        measure.datasource_id !== 0
                          ? measure.datasource_id
                          : undefined,
                    })),
                  },
                  expressionInstance: undefined,
                })
                  .unwrap()
                  .then((data) => {
                    const shouldLink = asset?.id
                      ? !asset.assetTemplate?.id
                        ? getValues('link_asset_to_template')
                        : getValues('override_existing_asset_template')
                      : false;

                    const newTemplateId = data?.id;

                    if (shouldLink && newTemplateId && asset?.id && asset?.customer_id) {
                      editAsset({
                        customerId: asset.customer_id,
                        assetId: asset.id.toString(),
                        editAsset: {
                          assetTemplate: newTemplateId,
                          tag: asset.tag,
                          type_id: asset.type_id,
                          parent_ids: asset.parent_ids ?? [],
                          childrenIds: asset.childrenIds ?? [],
                        },
                      });
                    }
                  });
              }}
              variant="contained"
              disabled={isMultiLoading || hasInvalidVariable || fields.length === 0}
            >
              Save
            </Button>
          </Box>
        </>
      )}
      {alertMessage && (
        <Alert severity={alertMessage.severity} sx={{ mt: 3 }}>
          {alertMessage.message}
        </Alert>
      )}
    </Box>
  );
};
export default AssetToTemplateForm;
