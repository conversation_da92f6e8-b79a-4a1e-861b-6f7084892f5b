import { yupResolver } from '@hookform/resolvers/yup';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { Autocomplete, Box, Button, IconButton, TextField } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { useEffect, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { CustomError } from '~/errors/CustomerErrorResponse';
import {
  useCreateBackOfficeAssetTypeMutation,
  useGetAllBackOfficeAssetTypesQuery,
  useUpdateBackOfficeAssetTypeMutation,
} from '~/redux/api/assetsApi';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { AssetTypeOption, AssetTypeSchema } from '~/types/asset';
import { assetTypeNoCountsPathMapper } from '~/utils/mappers/asset-type-map-no-template-count';
import CustomDialog from '../common/CustomDialog';
import Loader from '../common/Loader';
import PageName from '../common/PageName/PageName';
const AssetTypesListPage = () => {
  const [createNew, setCreateNew] = useState<{
    isEditCreate: 'create' | 'edit';
    isOpen: boolean;
    id: number | undefined;
  }>({
    id: undefined,
    isEditCreate: 'create',
    isOpen: false,
  });
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const [
    createAssetType,
    { isLoading, isSuccess: isCreateSuccess, error: createError, isError: isCreateError },
  ] = useCreateBackOfficeAssetTypeMutation();
  const [
    editAssetType,
    { isLoading: editLoading, isSuccess: isEditSuccess, error: editError, isError: isEditError },
  ] = useUpdateBackOfficeAssetTypeMutation();
  const {
    data: assetTypeListData,
    isLoading: isAssetTypeLoading,
    isSuccess: isSuccessfullBackOffieAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery();

  useEffect(() => {
    if (isCreateSuccess) {
      showSuccessAlert('Asset type created successully!!');
    }
    if (isCreateError && createError) {
      const err = createError as CustomError;
      showErrorAlert(err.message ?? 'Asset type creation failed.');
    }
  }, [createError, isCreateError, isCreateSuccess]);

  useEffect(() => {
    if (isEditSuccess) {
      showSuccessAlert('Asset type update successully!!');
    }
    if (isEditError && editError) {
      const err = editError as CustomError;
      showErrorAlert(err.data.exception ?? err.data.message ?? 'Asset type updation failed.');
    }
  }, [editError, isEditError, isEditSuccess]);
  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypeNoCountsPathMapper(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes]);

  const {
    control,
    handleSubmit,
    formState: { errors },
    getValues,
    setValue,
    reset,
  } = useForm<{
    name: string;
    parent_type: {
      id: number;
      label: string;
    };
  }>({
    resolver: yupResolver(AssetTypeSchema),
  });
  useEffect(() => {
    if (createNew.id && assetTypeListData) {
      const selected = assetTypeListData?.find((item) => item.id === createNew.id);
      if (selected) {
        setValue('name', selected?.name);
        const parent = assetTypesWithPath?.find((item) => item.value === selected.parentType);
        if (parent) {
          setValue('parent_type.id', parent.value);
          setValue('parent_type.label', parent.label);
        }
      }
    }
  }, [createNew.id, createNew.isEditCreate, assetTypesWithPath, assetTypeListData]);
  const onSubmit: SubmitHandler<{
    name: string;
    parent_type: {
      id: number;
      label: string;
    };
  }> = (data) => {
    if (createNew.isEditCreate === 'edit' && data.parent_type.id === createNew.id) {
      showErrorAlert('Parent Type cannot be same as current asset type');
      return;
    }
    if (createNew.isEditCreate === 'create') {
      setCreateNew({
        id: undefined,
        isEditCreate: 'create',
        isOpen: false,
      });
      createAssetType({
        name: data.name,
        parent_type_id: data.parent_type?.id,
      });
    } else if (createNew.isEditCreate === 'edit') {
      editAssetType({
        id: createNew.id ?? 0,
        name: data.name,
        parent_type_id: data.parent_type?.id,
      });
      setCreateNew({
        id: undefined,
        isEditCreate: 'create',
        isOpen: false,
      });
    }
    reset();
  };

  return (
    <Box>
      <AlertSnackbar {...snackbarState} />
      <Box pb={0} pt={0} sx={{ display: 'flex' }}>
        <Box
          sx={{
            flexGrow: 1,
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <PageName name="Asset Types" />
        </Box>
        <Button
          color="primary"
          variant="contained"
          onClick={() => {
            setCreateNew({ id: undefined, isEditCreate: 'create', isOpen: true });
            reset();
          }}
        >
          Add new Asset type
        </Button>
      </Box>
      {isAssetTypeLoading ? (
        <Loader />
      ) : (
        <Box mt={2}>
          <DataGrid
            sx={{
              height: 'calc(100vh - 90px)',
              width: '100%',
              '& .MuiDataGrid-columnHeader': {
                background: '#F9FAFB',
              },
              '.MuiDataGrid-columnHeader': {
                width: 'fit-content !important',
                maxWidth: 'fit-content !important',
              },
              '.MuiDataGrid-overlayWrapper': {
                height: '270px',
              },
              '--DataGrid-overlayHeight': '270px',
            }}
            autoPageSize
            rows={
              assetTypeListData?.map((assetTypes) => {
                return {
                  ...assetTypes,
                  parentType: assetTypes.parentType
                    ? assetTypeListData?.find((type) => type.id === assetTypes.parentType) ?? null
                    : null,
                };
              }) ?? []
            }
            slotProps={{
              toolbar: {
                showQuickFilter: true,
                printOptions: { disableToolbarButton: true },
                csvOptions: { disableToolbarButton: true },
              },
            }}
            ignoreDiacritics
            columns={[
              {
                field: 'name',
                headerName: 'Asset Name',
                flex: 1,
              },
              {
                field: 'lower case',
                headerName: 'Lower Cases',
                flex: 1,
                renderCell(params) {
                  return <>{params.row.name.toLowerCase() ?? ''}</>;
                },
              },
              {
                field: 'Parent',
                flex: 1,
                headerName: 'Parent',
                renderCell(params) {
                  return <>{params.row.parentType?.name ?? 'N/A'}</>;
                },
              },
              {
                field: 'actions',
                flex: 1,
                headerName: 'Actions',
                renderCell(params) {
                  return (
                    <IconButton
                      onClick={() => {
                        setCreateNew({
                          id: params.row.id,
                          isEditCreate: 'edit',
                          isOpen: true,
                        });
                      }}
                    >
                      <ModeEditIcon />
                    </IconButton>
                  );
                },
              },
            ]}
            disableRowSelectionOnClick
          />
        </Box>
      )}
      <CustomDialog
        open={createNew.isOpen}
        title={
          createNew.id === undefined && createNew.isEditCreate === 'create'
            ? 'Create New Asset Type'
            : 'Edit Asset type'
        }
        content={
          <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
            <Box mb={2} mt={2}>
              <Controller
                name="name"
                control={control}
                defaultValue=""
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Asset Type Name"
                    fullWidth
                    error={!!errors.name}
                    helperText={errors.name ? String(errors.name.message) : null}
                  />
                )}
              />
            </Box>
            <Box mb={2}>
              <Controller
                name="parent_type"
                control={control}
                defaultValue={getValues('parent_type')}
                render={({ field }) => {
                  return (
                    <Autocomplete
                      {...field}
                      options={
                        assetTypesWithPath.map((item) => {
                          return {
                            id: item.value,
                            label: item.label,
                          };
                        }) ?? []
                      }
                      value={field.value ?? null}
                      getOptionLabel={(option) => option.label || ''}
                      onChange={(_, value) => {
                        field.onChange(value || null);
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Parent Type"
                          fullWidth
                          error={!!errors.parent_type}
                          helperText={
                            errors.parent_type ? String(errors.parent_type.message) : null
                          }
                        />
                      )}
                    />
                  );
                }}
              />
            </Box>
            <Box
              sx={{
                width: '100%',
                display: 'flex',
                gap: 2,
                mt: 3,
              }}
            >
              <Button
                variant="outlined"
                fullWidth
                onClick={() => {
                  setCreateNew({
                    id: undefined,
                    isEditCreate: 'create',
                    isOpen: false,
                  });
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                fullWidth
                color="primary"
                disabled={isLoading}
              >
                Save
              </Button>
            </Box>
          </Box>
        }
        dialogActions={<></>}
        onClose={() => {
          setCreateNew({
            id: undefined,
            isEditCreate: 'create',
            isOpen: false,
          });
        }}
      />
    </Box>
  );
};

export default AssetTypesListPage;
