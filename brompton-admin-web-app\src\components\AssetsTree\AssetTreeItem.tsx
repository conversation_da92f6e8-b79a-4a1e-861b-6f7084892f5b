import * as React from 'react';
import { SyntheticEvent } from 'react';
import {
  TreeItem,
  TreeItemContentProps,
  TreeItemProps,
  useTreeItem,
} from '@mui/x-tree-view/TreeItem';
import { AssetNode, NodeConfig } from './AssetNode';

type AssetTreeItemProps = TreeItemProps & {
  onExpandToggle: (nodeId: string) => void;
  nodeConfig: NodeConfig;
};

const AssetNodeContainer = (
  expandToggleHandler: (nodeId: string) => void,
  nodeConfig: NodeConfig,
) =>
  React.forwardRef(function AssetNodeContainer(props: TreeItemContentProps, ref) {
    const { nodeId } = props;

    const {
      disabled,
      expanded,
      selected,
      focused,
      handleExpansion,
      handleSelection,
      preventSelection,
    } = useTreeItem(nodeId);

    return (
      <AssetNode
        ref={ref}
        {...props}
        handleExpansion={handleExpansion as (event: SyntheticEvent<Element, Event>) => void}
        handleSelection={handleSelection as (event: SyntheticEvent<Element, Event>) => void}
        preventSelection={preventSelection as (event: SyntheticEvent<Element, Event>) => void}
        disabled={disabled}
        expanded={expanded}
        selected={selected}
        focused={focused}
        onExpandToggle={expandToggleHandler}
        nodeConfig={nodeConfig}
        isChecked={selected}
      />
    );
  });

export const AssetTreeItem = ({ onExpandToggle, nodeConfig, ...rest }: AssetTreeItemProps) => (
  <TreeItem
    ContentComponent={AssetNodeContainer(onExpandToggle, nodeConfig)}
    {...rest}
    sx={{
      '& .MuiTreeItem-content': {
        padding: '0',
        minHeight: (theme) => theme.spacing(3),
        paddingTop: (theme) => theme.spacing(1),
        paddingBottom: (theme) => theme.spacing(1),
        gap: (theme) => theme.spacing(0.5),
        px: 2,
      },
    }}
  />
);
