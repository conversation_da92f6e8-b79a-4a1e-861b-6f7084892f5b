import { TreeView } from '@mui/x-tree-view';
import '@mui/lab';
import React from 'react';
import { NodeType, Tree, TreeNode } from './tree';
import { NodeConfig } from './AssetNode';

import { AssetTreeItem } from './AssetTreeItem';
import { formatMetricLabelForTree } from '~/utils/utils';

type NodeTypeConfigs = {
  customer: NodeConfig;
  asset: NodeConfig;
  measurement: NodeConfig;
  metric: NodeConfig;
};

export type AssetsTreeProps = {
  tree: Tree;
  expandedNodeIds?: string[];
  selectedNodeIds?: string[];
  onNodeToggle: (nodes: string[]) => void;
  onSelect: (evenType: string, currentNode: TreeNode, selectedNodes: string[]) => void;
  nodeTypeConfigs: NodeTypeConfigs;
};

const mapType = (nodeType: NodeType): keyof NodeTypeConfigs => {
  if (nodeType === 'company') {
    return 'customer';
  } else if (nodeType === 'activo') {
    return 'asset';
  } else if (nodeType === 'medicion') {
    return 'measurement';
  } else {
    return 'metric';
  }
};

const renderTree = (
  node: Tree,
  nodeExpandToggleHandler: (nodeId: string) => void,
  nodeTypeConfigs: NodeTypeConfigs,
  selected: string[],
) => {
  if (node.type === 'metric') {
    // debugger;
  }
  return (
    <AssetTreeItem
      key={node.id}
      nodeId={node.id}
      label={node.type === 'metric' ? formatMetricLabelForTree(node.tag) : node.tag}
      onExpandToggle={nodeExpandToggleHandler}
      nodeConfig={nodeTypeConfigs[mapType(node.type)]}
    >
      {Array.isArray(node.children)
        ? node.children.map((node) =>
            renderTree(node, nodeExpandToggleHandler, nodeTypeConfigs, selected),
          )
        : null}
    </AssetTreeItem>
  );
};

const AssetsTree: React.FC<AssetsTreeProps> = (props: AssetsTreeProps) => {
  const {
    tree,
    expandedNodeIds = [],
    selectedNodeIds = [],
    onNodeToggle,
    onSelect,
    nodeTypeConfigs,
  } = props;

  const nodeExpandToggleHandler = (nodeId: string) => {
    let newExpandedNodeIds;
    if (expandedNodeIds.includes(nodeId)) {
      newExpandedNodeIds = expandedNodeIds.filter((id) => id !== nodeId);
    } else {
      newExpandedNodeIds = [...expandedNodeIds, nodeId];
    }
    onNodeToggle(newExpandedNodeIds);
  };

  const nodeSelectionHandler = (_: unknown, nodeIds: string[]) => {
    let newSelectedNodeIds = [] as string[];
    const nodeId = nodeIds[0];
    const currentNode = tree.find(nodeId);
    if (currentNode === null) return;

    let eventType: string;

    if (selectedNodeIds.includes(nodeId)) {
      eventType = 'deselect';
      newSelectedNodeIds = selectedNodeIds.filter((id) => id !== nodeId);
    } else {
      eventType = 'select';
      newSelectedNodeIds = [...selectedNodeIds, nodeId];
    }

    onSelect(
      eventType,
      currentNode,
      newSelectedNodeIds,
      // .map((id) => tree.find(id))
      // .filter((treeNode) => treeNode !== null) as TreeNode[], // We're filtering null values
    );
  };

  return (
    <>
      <TreeView
        multiSelect={true}
        expanded={expandedNodeIds}
        selected={selectedNodeIds}
        onNodeSelect={nodeSelectionHandler}
      >
        {renderTree(tree, nodeExpandToggleHandler, nodeTypeConfigs, selectedNodeIds)}
      </TreeView>
    </>
  );
};

export default AssetsTree;
