import {
  Box,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  MenuItem,
  OutlinedInput,
  Select,
  Stack,
  TextField,
} from '@mui/material';
import { SelectChangeEvent } from '@mui/material/Select';
import React from 'react';
import { setSingleMeasureWidgetSettings, StatsWidget } from '~/types/widgets';
import { fontWeights } from '~/utils/utils';
import DataWidgetSettingsContainer from '../common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import { setSettings } from '../common/OverRideGlobalSettings';
import SingleMeasureSelect from '../common/SingleMeasureSelect';
const SelectOutLinedInput = () => {
  return (
    <OutlinedInput
      label="Font Weight"
      sx={{
        p: 0.5,
        '& legend': {
          maxWidth: '100%',
          height: 'fit-content',
          '& span': {
            opacity: 1,
          },
        },
      }}
    />
  );
};
const StatsDivider = () => (
  <Divider
    sx={{
      mt: 2,
      backgroundColor: 'primary.main',
    }}
  />
);
type StatsWidgetSettingDialogProps = {
  settings: StatsWidget;
  handleSettingsChange: React.Dispatch<React.SetStateAction<StatsWidget>>;
};

export const StatsWidgetSettingDialog = ({
  settings: statsWidgetSettings,
  handleSettingsChange,
}: StatsWidgetSettingDialogProps): JSX.Element => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({ ...statsWidgetSettings, [event.target.name]: event.target.checked });
  };

  const onHandleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const [key, subKey] = event.target.name.split('.');
    if (subKey === 'fontSize' && Number(event.target.value) > 100) {
      return;
    }
    handleSettingsChange({
      ...statsWidgetSettings,
      [key]: {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        ...statsWidgetSettings[key],
        [subKey]: event.target.value,
      },
    });
  };
  const onFontWeightChange = (event: SelectChangeEvent<string>, child: React.ReactNode) => {
    const [key, subKey] = event.target.name.split('.');
    handleSettingsChange({
      ...statsWidgetSettings,
      [key]: {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        ...statsWidgetSettings[key],
        [subKey]: event.target.value,
      },
    });
  };
  return (
    <Stack display="flex" gap={1}>
      <DataWidgetSettingsContainer
        settings={statsWidgetSettings}
        setSettings={handleSettingsChange as setSettings}
        feelTabChidren={
          <>
            <Box width={'100%'}>
              <FormControlLabel
                sx={{
                  p: 2,
                  pb: 0,
                }}
                control={
                  <Checkbox
                    checked={statsWidgetSettings.showMin}
                    onChange={handleChange}
                    name="showMin"
                  />
                }
                label="Min"
              />
            </Box>
            {statsWidgetSettings.showMin ? (
              <Box pl={3}>
                <FormControlLabel
                  sx={{
                    p: 2,
                    pb: 0,
                  }}
                  control={
                    <Checkbox
                      checked={statsWidgetSettings.showMinLable}
                      onChange={handleChange}
                      name="showMinLable"
                    />
                  }
                  label="Show Min Label"
                />
                <FormControlLabel
                  sx={{
                    p: 2,
                    pb: 0,
                  }}
                  control={
                    <Checkbox
                      checked={statsWidgetSettings.minBorder}
                      onChange={handleChange}
                      name="minBorder"
                    />
                  }
                  label="Show Min Border"
                />
                {statsWidgetSettings.showMinLable && (
                  <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                    <TextField
                      name="min.label"
                      onChange={onHandleChange}
                      defaultValue={statsWidgetSettings.min.label ?? 'Min'}
                      value={statsWidgetSettings.min.label ?? 'Min'}
                      label="Label"
                      variant="outlined"
                      margin="normal"
                      fullWidth
                    />
                  </FormGroup>
                )}
                <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                  <TextField
                    name="min.fontSize"
                    type="number"
                    onChange={onHandleChange}
                    defaultValue={12}
                    value={statsWidgetSettings?.min?.fontSize}
                    inputProps={{
                      max: 100,
                    }}
                    error={statsWidgetSettings?.min?.fontSize > 100}
                    helperText={
                      statsWidgetSettings?.min?.fontSize > 100
                        ? `Font size cannot exceed ${100}`
                        : ''
                    }
                    label="Font Size"
                    variant="outlined"
                    margin="normal"
                    fullWidth
                  />
                </FormGroup>
                <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                  <TextField
                    name="min.color"
                    type="color"
                    label="Font Color"
                    onChange={onHandleChange}
                    value={statsWidgetSettings?.min?.color}
                    variant="outlined"
                    margin="normal"
                    fullWidth
                  />
                </FormGroup>
                <FormControl sx={{ p: 2, pt: 0, pb: 0, width: '100%' }}>
                  <Box display="flex" alignItems="center" width="100%" gap={1}>
                    <Select
                      labelId="y-select-lable"
                      id="tabel-series"
                      name="min.fontWeight"
                      defaultValue="bolder"
                      fullWidth
                      input={SelectOutLinedInput()}
                      value={statsWidgetSettings?.min?.fontWeight}
                      onChange={onFontWeightChange}
                    >
                      {fontWeights.map((fonts: string) => {
                        return (
                          <MenuItem key={fonts} value={fonts}>
                            {fonts}
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </Box>
                </FormControl>
                <StatsDivider />
              </Box>
            ) : null}
            <Box width={'100%'}>
              <FormControlLabel
                sx={{
                  p: 2,
                  pb: 0,
                }}
                control={
                  <Checkbox
                    checked={statsWidgetSettings.showMax}
                    onChange={handleChange}
                    name="showMax"
                  />
                }
                label="Max"
              />
            </Box>
            {statsWidgetSettings.showMax ? (
              <Box pl={3}>
                <FormControlLabel
                  sx={{
                    p: 2,
                    pb: 0,
                  }}
                  control={
                    <Checkbox
                      checked={statsWidgetSettings.showMaxLabel}
                      onChange={handleChange}
                      name="showMaxLabel"
                    />
                  }
                  label="Show Max Label"
                />
                <FormControlLabel
                  sx={{
                    p: 2,
                    pb: 0,
                  }}
                  control={
                    <Checkbox
                      checked={statsWidgetSettings.maxBorder}
                      onChange={handleChange}
                      name="maxBorder"
                    />
                  }
                  label="Show Max Border"
                />
                {statsWidgetSettings.showMaxLabel && (
                  <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                    <TextField
                      name="max.label"
                      onChange={onHandleChange}
                      defaultValue={statsWidgetSettings.max.label ?? 'Max'}
                      value={statsWidgetSettings.max.label ?? 'Max'}
                      label="Label"
                      variant="outlined"
                      margin="normal"
                      fullWidth
                    />
                  </FormGroup>
                )}
                <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                  <TextField
                    name="max.fontSize"
                    type="number"
                    onChange={onHandleChange}
                    defaultValue={12}
                    value={statsWidgetSettings?.max?.fontSize}
                    inputProps={{
                      max: 100,
                    }}
                    error={statsWidgetSettings?.max?.fontSize > 100}
                    helperText={
                      statsWidgetSettings?.max?.fontSize > 100
                        ? `Font size cannot exceed ${100}`
                        : ''
                    }
                    label="Font Size"
                    variant="outlined"
                    margin="normal"
                    fullWidth
                  />
                </FormGroup>
                <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                  <TextField
                    name="max.color"
                    type="color"
                    label="Font Color"
                    onChange={onHandleChange}
                    value={statsWidgetSettings?.max?.color}
                    variant="outlined"
                    margin="normal"
                    fullWidth
                  />
                </FormGroup>
                <FormControl sx={{ pt: 0, p: 2, pb: 0, width: '100%' }}>
                  <Box display="flex" alignItems="center" width="100%" gap={1}>
                    <Select
                      labelId="y-select-lable"
                      id="tabel-series"
                      name="max.fontWeight"
                      defaultValue="bolder"
                      input={SelectOutLinedInput()}
                      fullWidth
                      value={statsWidgetSettings?.max?.fontWeight}
                      onChange={onFontWeightChange}
                    >
                      {fontWeights.map((fonts: string) => {
                        return (
                          <MenuItem key={fonts} value={fonts}>
                            {fonts}
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </Box>
                </FormControl>
                <StatsDivider />
              </Box>
            ) : null}
            <Box width={'100%'}>
              <FormControlLabel
                sx={{
                  p: 2,
                  pb: 0,
                }}
                control={
                  <Checkbox
                    checked={statsWidgetSettings.showAvg}
                    onChange={handleChange}
                    name="showAvg"
                  />
                }
                label="Avg"
              />
            </Box>
            {statsWidgetSettings.showAvg ? (
              <Box pl={3}>
                <FormControlLabel
                  sx={{
                    p: 2,
                    pb: 0,
                  }}
                  control={
                    <Checkbox
                      checked={statsWidgetSettings.showAvgLabel}
                      onChange={handleChange}
                      name="showAvgLabel"
                    />
                  }
                  label="Show Avg Label"
                />
                <FormControlLabel
                  sx={{
                    p: 2,
                    pb: 0,
                  }}
                  control={
                    <Checkbox
                      checked={statsWidgetSettings.avgBorder}
                      onChange={handleChange}
                      name="avgBorder"
                    />
                  }
                  label="Show Avg Border"
                />
                {statsWidgetSettings.showAvgLabel && (
                  <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                    <TextField
                      name="avg.label"
                      onChange={onHandleChange}
                      defaultValue={statsWidgetSettings.avg.label ?? 'Avg'}
                      value={statsWidgetSettings.avg.label ?? 'Avg'}
                      label="Label"
                      variant="outlined"
                      margin="normal"
                      fullWidth
                    />
                  </FormGroup>
                )}
                <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                  <TextField
                    name="avg.fontSize"
                    type="number"
                    onChange={onHandleChange}
                    defaultValue={12}
                    value={statsWidgetSettings?.avg?.fontSize}
                    inputProps={{
                      max: 100,
                    }}
                    error={statsWidgetSettings?.avg?.fontSize > 100}
                    helperText={
                      statsWidgetSettings?.avg?.fontSize > 100
                        ? `Font size cannot exceed ${100}`
                        : ''
                    }
                    label="Font Size"
                    variant="outlined"
                    margin="normal"
                    fullWidth
                  />
                </FormGroup>
                <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                  <TextField
                    name="avg.color"
                    type="color"
                    label="Font Color"
                    onChange={onHandleChange}
                    value={statsWidgetSettings?.avg?.color}
                    variant="outlined"
                    margin="normal"
                    fullWidth
                  />
                </FormGroup>
                <FormControl sx={{ mb: 2, p: 2, pb: 0, width: '100%' }}>
                  <Box display="flex" alignItems="center" width="100%" gap={1}>
                    <Select
                      labelId="y-select-lable"
                      id="tabel-series"
                      name="avg.fontWeight"
                      defaultValue="bolder"
                      input={SelectOutLinedInput()}
                      fullWidth
                      value={statsWidgetSettings?.avg?.fontWeight}
                      onChange={onFontWeightChange}
                    >
                      {fontWeights.map((fonts: string) => {
                        return (
                          <MenuItem key={fonts} value={fonts}>
                            {fonts}
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </Box>
                </FormControl>
                <StatsDivider />
              </Box>
            ) : null}
            <Box width={'100%'}>
              <FormControlLabel
                sx={{
                  p: 2,
                  pb: 0,
                }}
                control={
                  <Checkbox
                    checked={statsWidgetSettings.showDelta}
                    onChange={handleChange}
                    name="showDelta"
                  />
                }
                label="Delta"
              />
            </Box>
            {statsWidgetSettings.showDelta ? (
              <Box pl={3}>
                <FormControlLabel
                  sx={{
                    p: 2,
                    pb: 0,
                  }}
                  control={
                    <Checkbox
                      checked={statsWidgetSettings.showDeltaLabel}
                      onChange={handleChange}
                      name="showDeltaLabel"
                    />
                  }
                  label="Show Delta Label"
                />
                <FormControlLabel
                  sx={{
                    p: 2,
                    pb: 0,
                  }}
                  control={
                    <Checkbox
                      checked={statsWidgetSettings.deltaBorder}
                      onChange={handleChange}
                      name="deltaBorder"
                    />
                  }
                  label="Show Delta Border"
                />
                {statsWidgetSettings.showDeltaLabel && (
                  <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                    <TextField
                      name="delta.label"
                      onChange={onHandleChange}
                      defaultValue={statsWidgetSettings.delta.label ?? 'Delta'}
                      value={statsWidgetSettings.delta.label ?? 'Delta'}
                      label="Label"
                      variant="outlined"
                      margin="normal"
                      fullWidth
                    />
                  </FormGroup>
                )}
                <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                  <TextField
                    name="delta.fontSize"
                    type="number"
                    onChange={onHandleChange}
                    defaultValue={12}
                    value={statsWidgetSettings?.delta?.fontSize}
                    inputProps={{
                      max: 100,
                    }}
                    error={statsWidgetSettings?.delta?.fontSize > 100}
                    helperText={
                      statsWidgetSettings?.delta?.fontSize > 100
                        ? `Font size cannot exceed ${100}`
                        : ''
                    }
                    label="Font Size"
                    variant="outlined"
                    margin="normal"
                    fullWidth
                  />
                </FormGroup>
                <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                  <TextField
                    name="delta.color"
                    type="color"
                    label="Font Color"
                    onChange={onHandleChange}
                    value={statsWidgetSettings?.delta?.color}
                    variant="outlined"
                    margin="normal"
                    fullWidth
                  />
                </FormGroup>
                <FormControl sx={{ mb: 2, p: 2, pb: 0, width: '100%' }}>
                  <Box display="flex" alignItems="center" width="100%" gap={1}>
                    <Select
                      labelId="y-select-lable"
                      id="tabel-series"
                      name="delta.fontWeight"
                      input={SelectOutLinedInput()}
                      defaultValue="bolder"
                      fullWidth
                      value={statsWidgetSettings?.delta?.fontWeight}
                      onChange={onFontWeightChange}
                    >
                      {fontWeights.map((fonts: string) => {
                        return (
                          <MenuItem key={fonts} value={fonts}>
                            {fonts}
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </Box>
                </FormControl>
                <StatsDivider />
              </Box>
            ) : null}
            <Box width={'100%'}>
              <FormControlLabel
                sx={{
                  p: 2,
                  pb: 0,
                }}
                control={
                  <Checkbox
                    checked={statsWidgetSettings.showSum}
                    onChange={handleChange}
                    name="showSum"
                  />
                }
                label="Sum"
              />
            </Box>
            {statsWidgetSettings.showSum ? (
              <Box pl={3}>
                <FormControlLabel
                  sx={{
                    p: 2,
                    pb: 0,
                  }}
                  control={
                    <Checkbox
                      checked={statsWidgetSettings.showSumLabel}
                      onChange={handleChange}
                      name="showSumLabel"
                    />
                  }
                  label="Show Sum Label"
                />
                <FormControlLabel
                  sx={{
                    p: 2,
                    pb: 0,
                  }}
                  control={
                    <Checkbox
                      checked={statsWidgetSettings.sumBorder}
                      onChange={handleChange}
                      name="sumBorder"
                    />
                  }
                  label="Show Sum Border"
                />
                {statsWidgetSettings.showSumLabel && (
                  <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                    <TextField
                      name="sum.label"
                      onChange={onHandleChange}
                      defaultValue={'Sum'}
                      value={statsWidgetSettings.sum.label ?? 'Sum'}
                      label="Label"
                      variant="outlined"
                      margin="normal"
                      fullWidth
                    />
                  </FormGroup>
                )}
                <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                  <TextField
                    name="sum.fontSize"
                    type="number"
                    onChange={onHandleChange}
                    defaultValue={12}
                    value={statsWidgetSettings.sum.fontSize}
                    inputProps={{
                      max: 100,
                    }}
                    error={statsWidgetSettings?.delta?.fontSize > 100}
                    helperText={
                      statsWidgetSettings?.delta?.fontSize > 100
                        ? `Font size cannot exceed ${100}`
                        : ''
                    }
                    label="Font Size"
                    variant="outlined"
                    margin="normal"
                    fullWidth
                  />
                </FormGroup>
                <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                  <TextField
                    name="sum.color"
                    type="color"
                    label="Font Color"
                    onChange={onHandleChange}
                    value={statsWidgetSettings.sum.color}
                    variant="outlined"
                    margin="normal"
                    fullWidth
                  />
                </FormGroup>
                <FormControl sx={{ mb: 2, p: 2, pb: 0, width: '100%' }}>
                  <Box display="flex" alignItems="center" width="100%" gap={1}>
                    <Select
                      labelId="y-select-lable"
                      id="tabel-series"
                      name="delta.fontWeight"
                      input={SelectOutLinedInput()}
                      defaultValue="bolder"
                      fullWidth
                      value={statsWidgetSettings.delta.fontWeight}
                      onChange={onFontWeightChange}
                    >
                      {fontWeights.map((fonts: string) => {
                        return (
                          <MenuItem key={fonts} value={fonts}>
                            {fonts}
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </Box>
                </FormControl>
                <StatsDivider />
              </Box>
            ) : null}
            <Box width={'100%'}>
              <FormControlLabel
                sx={{
                  p: 2,
                  pb: 0,
                }}
                control={
                  <Checkbox
                    checked={statsWidgetSettings.showCurrent}
                    onChange={handleChange}
                    name="showCurrent"
                  />
                }
                label="Last Value"
              />
            </Box>
            {statsWidgetSettings.showCurrent ? (
              <>
                <Box pl={3}>
                  <FormControlLabel
                    sx={{
                      p: 2,
                      pb: 0,
                    }}
                    control={
                      <Checkbox
                        checked={statsWidgetSettings.showCurrentLabel}
                        onChange={handleChange}
                        name="showCurrentLabel"
                      />
                    }
                    label="Show Last Value Label"
                  />
                  <FormControlLabel
                    sx={{
                      p: 2,
                      pb: 0,
                    }}
                    control={
                      <Checkbox
                        checked={statsWidgetSettings.showCurrentBorder}
                        onChange={handleChange}
                        name="showCurrentBorder"
                      />
                    }
                    label="Show Last Value Border"
                  />
                  {statsWidgetSettings.showCurrentLabel && (
                    <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                      <TextField
                        name="current.label"
                        onChange={onHandleChange}
                        defaultValue={statsWidgetSettings.current.label ?? 'Current'}
                        value={statsWidgetSettings.current.label ?? 'Current'}
                        label="Label"
                        variant="outlined"
                        margin="normal"
                        fullWidth
                      />
                    </FormGroup>
                  )}
                  <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                    <TextField
                      name="current.fontSize"
                      type="number"
                      onChange={onHandleChange}
                      defaultValue={12}
                      value={statsWidgetSettings?.current?.fontSize}
                      inputProps={{
                        max: 100,
                      }}
                      error={statsWidgetSettings?.current?.fontSize > 100}
                      helperText={
                        statsWidgetSettings?.current?.fontSize > 100
                          ? `Font size cannot exceed ${100}`
                          : ''
                      }
                      label="Font Size"
                      variant="outlined"
                      margin="normal"
                      fullWidth
                    />
                  </FormGroup>
                  <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
                    <TextField
                      name="current.color"
                      type="color"
                      label="Font Color"
                      onChange={onHandleChange}
                      value={statsWidgetSettings?.current?.color}
                      variant="outlined"
                      margin="normal"
                      fullWidth
                    />
                  </FormGroup>
                  <FormControl sx={{ mb: 2, p: 2, pb: 0, width: '100%' }}>
                    <Box display="flex" alignItems="center" width="100%" gap={1}>
                      <Select
                        labelId="y-select-lable"
                        id="tabel-series"
                        name="current.fontWeight"
                        defaultValue="bolder"
                        input={SelectOutLinedInput()}
                        fullWidth
                        value={statsWidgetSettings?.current?.fontWeight}
                        onChange={onFontWeightChange}
                      >
                        {fontWeights.map((fonts: string) => {
                          return (
                            <MenuItem key={fonts} value={fonts}>
                              {fonts}
                            </MenuItem>
                          );
                        })}
                      </Select>
                    </Box>
                  </FormControl>
                </Box>
              </>
            ) : null}
          </>
        }
        dataTabChildren={
          <Box width="100%" sx={{ mt: 2 }}>
            <SingleMeasureSelect
              id={'stats-widget-settings'}
              settings={statsWidgetSettings}
              setSettings={handleSettingsChange as setSingleMeasureWidgetSettings}
            />
          </Box>
        }
      />
      {/* <DataWidgetContainer
        id={'stats-widget-settings'}
        settings={statsWidgetSettings}
        setSettings={handleSettingsChange as setSettings}
      >
        <FormControl
          fullWidth
          sx={{
            p: 2,
          }}
        >
          <Box width="100%">
            <SingleMeasureSelect
              id={'stats-widget-settings'}
              settings={statsWidgetSettings}
              setSettings={handleSettingsChange as setSingleMeasureWidgetSettings}
            />
          </Box>
        </FormControl>
      </DataWidgetContainer> */}
    </Stack>
  );
};
