import { dia, shapes } from '@joint/core';
import { Box, Button } from '@mui/material';
import React, { useEffect, useRef } from 'react';
import { DraggableLabel } from './DraggableLabel';
import LiquidTank from './Tank';

const cellNamespace = {
  LiquidTank,
  DraggableLabel,
  standard: shapes.standard,
  devs: shapes.devs,
};

const CreateElementPage: React.FC = () => {
  const canvasRef = useRef<HTMLDivElement | null>(null);
  const graph = useRef(new dia.Graph({}, { cellNamespace })).current;

  useEffect(() => {
    const paper = new dia.Paper({
      el: canvasRef.current as HTMLDivElement,
      model: graph,
      width: '100vw',
      height: '100vh',
      gridSize: 12,
      drawGrid: true,
      cellNamespace,
    });

    const liquidTank = new LiquidTank({}, {}, { barSetting: { direction: 'vertical' }, level: 50 });
    liquidTank.position(200, 200);
    // graph.addCell(liquidTank);

    // Create draggable labels
    const inletLabel = new DraggableLabel('inlet: 98', 150, 150);
    inletLabel.position(150, 150); // Position near the tank
    // graph.addCell(inletLabel);

    const outletLabel = new DraggableLabel('outlet: 67', 300, 400);
    outletLabel.position(300, 400); // Position near the tank
    // graph.addCell(outletLabel);
  }, [graph]);

  const addNew = () => {
    // Create a temporary graph and paper to hold the available shapes for display in the dialog
    const tempGraph = new dia.Graph({}, { cellNamespace });
    const tempPaperRef = document.createElement('div');
    tempPaperRef.style.width = '400px';
    tempPaperRef.style.height = '400px';
    tempPaperRef.style.border = '1px solid #ccc';
    tempPaperRef.style.overflow = 'auto';
    document.body.appendChild(tempPaperRef);

    const tempPaper = new dia.Paper({
      el: tempPaperRef,
      model: tempGraph,
      width: 400,
      height: 400,
      gridSize: 10,
      drawGrid: true,
      interactive: true, // Enable interaction in this paper
      cellNamespace,
    });

    // Create instances of each shape and add them to the temporary graph
    let yOffset = 10;
    Object.keys(cellNamespace).forEach((shapeName) => {
      const ShapeClass = cellNamespace[shapeName as keyof typeof cellNamespace];
      if (ShapeClass && typeof ShapeClass === 'function') {
        let shapeInstance;
        try {
          // Attempt to create the shape with an empty options object.
          // If the shape requires different parameters, provide them accordingly.
          if (shapeName === 'LiquidTank') {
            shapeInstance = new LiquidTank(
              {},
              {},
              { barSetting: { direction: 'vertical' }, level: 50 },
            );
          } else {
            // shapeInstance = new ShapeClass({});
            shapeInstance = new DraggableLabel('inlet: 1000', 150, 150);
            // const inletLabel = new DraggableLabel('inlet: 98');
            shapeInstance.position(150, 150); // Position near the tank
            // graph.addCell(inletLabel);
          }
        } catch (error) {
          // If default constructor fails, log the error.
          console.error(`Failed to create instance of ${shapeName}`, error);
          return;
        }

        shapeInstance.position(10, yOffset);
        yOffset += 100; // Update yOffset to position the next shape below the previous one
        tempGraph.addCell(shapeInstance);
      }
    });

    // Create the dialog and add the temporary paper inside
    // const dialog = new ui.Dialog({
    //   width: '450px',
    //   className: 'dialog',
    //   title: 'Add New Element',
    //   content: tempPaperRef,
    //   buttons: [
    //     {
    //       action: 'add-to-canvas',
    //       content: 'Add to Canvas',
    //       position: 'left',
    //     },
    //     {
    //       action: 'cancel',
    //       content: 'Close',
    //       position: 'right',
    //     },
    //   ],
    //   modal: true,
    //   draggable: true,
    // }).render();

    // dialog.on('action:cancel', function () {
    //   dialog.close();
    //   tempPaper.remove();
    //   tempPaperRef.remove();
    // });

    // dialog.on('action:add-to-canvas', function () {
    //   // Iterate over all elements in the tempGraph and add them to the main graph
    //   tempGraph.getCells().forEach((cell) => {
    //     graph.addCell(cell);
    //   });

    //   // Close the dialog after adding the elements to the canvas
    //   dialog.close();
    //   tempPaper.remove(); // Clean up when dialog is closed
    //   tempPaperRef.remove();
    // });

    // dialog.open();
  };
  return (
    <Box>
      <Button color="primary" variant="contained" onClick={addNew}>
        Add
      </Button>
      <Button
        color="primary"
        variant="contained"
        onClick={() => {
          const json = graph.toJSON();
          const dataStr = `data:text/json;charset=utf-8,${encodeURIComponent(
            JSON.stringify(json),
          )}`;
          const downloadAnchor = document.createElement('a');
          downloadAnchor.setAttribute('href', dataStr);
          downloadAnchor.setAttribute('download', 'diagram.json');
          document.body.appendChild(downloadAnchor);
          downloadAnchor.click();
          document.body.removeChild(downloadAnchor);
        }}
      >
        Download as JSON
      </Button>
      <Box ref={canvasRef} style={{ width: '100%', height: '100%' }} />
    </Box>
  );
};

export default CreateElementPage;
