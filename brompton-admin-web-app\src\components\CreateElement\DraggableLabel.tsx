import { dia, shapes } from '@joint/core';

export class DraggableLabel extends shapes.standard.TextBlock {
  constructor(text: string, x: number, y: number) {
    super();
    this.position(x, y);
    this.resize(120, 50);
    this.attr({
      body: {
        fill: 'transparent',
        stroke: '#000',
        strokeWidth: 1,
        rx: 5,
        ry: 5,
      },
      label: {
        text,
        textAnchor: 'middle',
        fontSize: 12,
        fontFamily: 'sans-serif',
        fill: '#000',
        cursor: 'pointer',
      },
    });
    this.set('elementType', 'draggableLabel');
  }

  makeDraggable(paper: dia.Paper) {
    const labelView = this.findView(paper);
    if (labelView) {
      let isDragging = false;
      let offsetX = 0;
      let offsetY = 0;

      labelView.on('pointerdown', (evt: dia.Event, x: number, y: number) => {
        isDragging = true;
        offsetX = x - this.position().x;
        offsetY = y - this.position().y;
      });

      paper.on('cell:pointermove', (cellView, evt, x, y) => {
        if (isDragging && cellView === labelView) {
          this.position(x - offsetX, y - offsetY);
        }
      });

      paper.on('cell:pointerup', () => {
        isDragging = false;
      });
    }
  }
}
