import { dia, util } from '@joint/core';

function generateUniqueId(prefix = 'selector') {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
}

export type BatteryConfig = {
  blockCount?: number; // Number of rectangular blocks
  level: number;
  colors?: BatteryColors;
  maxCapacity?: number;
  colorToShow?: string;
  style?: 'bar' | 'solid'; // New style property
};

export type BatteryColors = {
  low: string;
  mid: string;
  high: string;
};

export default class Progress extends dia.Element {
  blockCount = 5;
  maxCapacity = 100;

  constructor(
    attributes = {},
    options = {},
    config: BatteryConfig = {
      blockCount: 5, // Default to 5 blocks if not provided
      level: 0,
      maxCapacity: 100,
      colorToShow: '#009900',
      colors: {
        low: '#ff0000', // Default red for low
        mid: '#ffff00', // Default yellow for mid
        high: '#009900', // Default green for high
      },
      style: 'bar', // Default style is solid
    },
  ) {
    super(attributes, options);
    this.blockCount = config.blockCount || 5;
    this.level = config.level;
    this.maxCapacity = config.maxCapacity ?? 100;

    // Set style and colors
    this.set('style', config.style ?? 'solid');
    this.style = config.style ?? 'bar';
    this.set(
      'colors',
      config.colors || {
        low: '#ff0000',
        mid: '#ffff00',
        high: '#009900',
      },
    );

    // Set orientation and markup
    this.set('orientation', 'vertical');
    this.updateMarkup(); // Generate the initial markup

    // Add a listener for size changes
    this.on('change:size', this.updateMarkup.bind(this));
  }

  defaults(): Partial<dia.Element.Attributes> {
    return {
      ...super.defaults,
      type: 'Progress',
      size: { width: 80, height: 160 },
      level: 0,
      maxCapacity: 100,
      style: 'bar',
      orientation: 'vertical',
      colors: {
        low: '#ff0000',
        mid: '#ffff00',
        high: '#009900',
      },
      attrs: {
        root: {
          magnetSelector: 'body',
        },
        body: {
          x: 0,
          y: 0,
          width: 'calc(w)',
          height: 'calc(h)',
          stroke: 'black',
          strokeWidth: 1,
          fill: 'none',
        },
      },
    };
  }

  preinitialize() {
    this.markup = util.svg/* xml */ `
    <rect @selector="body"/>`;
  }

  get maxCapacityValue(): number {
    return this.maxCapacity;
  }

  set maxCapacityValue(maxCapacity: number) {
    if (maxCapacity > 0) {
      this.maxCapacity = maxCapacity;
      this.updateMarkup();
    }
  }

  applyOrientation() {
    const orientation = this.orientation;

    if (orientation === 'vertical') {
      this.rotate(0, true, this.getBBox().center());
    } else if (orientation === 'horizontal') {
      this.rotate(90, true, this.getBBox().center());
    }
  }

  get level() {
    return this.get('level') || 0;
  }

  set level(level: number) {
    const newLevel = Math.max(0, Math.min(100, level));
    this.set('level', newLevel);
    this.updateMarkup();
  }

  get orientation(): 'vertical' | 'horizontal' {
    return this.get('orientation') || 'vertical'; // Default to 'vertical'
  }

  set orientation(orientation: 'vertical' | 'horizontal') {
    if (this.get('orientation') !== orientation) {
      this.set('orientation', orientation);
      this.applyOrientation();
    }
  }

  get style(): 'bar' | 'solid' {
    return this.get('style') || 'solid';
  }

  set style(style: 'bar' | 'solid') {
    if (this.get('style') !== style) {
      this.set('style', style);
      this.updateMarkup();
    }
  }

  get colors(): BatteryColors {
    return this.get('colors');
  }

  set colors(colors: BatteryColors) {
    this.set('colors', colors);
    this.updateMarkup();
  }

  get colorToShow(): string {
    return this.get('colorToShow');
  }
  set colorToShow(color: string) {
    this.set('colorToShow', color);
    this.updateMarkup();
  }
  updateMarkup() {
    const style = this.style;

    if (style === 'bar') {
      this.createBarBlocks();
    } else if (style == 'solid') {
      this.createSolidFill();
    }
  }

  createBarBlocks() {
    const bodyHeight = this.size().height;
    const bodyWidth = this.size().width;
    const totalSpacing = 10;
    const availableHeight = bodyHeight - totalSpacing;

    // Dynamically adjust block count based on maxCapacity
    // const blockCount = Math.min(5, Math.floor(this.maxCapacity / 20)); // At least 5 blocks, increase as needed
    const blockCount = 5;
    const blockHeight = availableHeight / blockCount;
    const blockWidth = bodyWidth - 10;
    const blockX = 5;
    const spacing = (bodyHeight - blockCount * blockHeight) / (blockCount + 10);

    // Determine how many blocks should be filled based on level
    const filledBlockCount = Math.round((this.level / 100) * blockCount);

    const blocksMarkup = [];

    for (let i = 0; i < blockCount; i++) {
      const blockY = bodyHeight - (i + 1) * (blockHeight + spacing);
      const isFilled = i < filledBlockCount;
      const blockColor = isFilled ? this.colorToShow : 'lightgrey';
      // Generate a unique selector for each block
      const uniqueSelector = generateUniqueId(`block-${i}`);

      blocksMarkup.push({
        tagName: 'rect',
        selector: uniqueSelector,
        attributes: {
          x: blockX,
          y: blockY,
          width: blockWidth,
          height: blockHeight * 0.8,
          fill: blockColor,
          opacity: 1,
          stroke: 'black',
          'stroke-width': 1,
        },
      });
    }

    this.prop('markup', [
      {
        tagName: 'rect',
        selector: 'body', // Keep the selector consistent to reference attributes correctly
      },
      {
        tagName: 'g',
        selector: generateUniqueId('blocks'),
        children: blocksMarkup,
      },
    ]);
  }

  // createSolidFill() {
  //   const bodyHeight = this.size().height;
  //   const bodyWidth = this.size().width;
  //   const levelHeight = (this.level / 100) * bodyHeight;
  //   const levelY = bodyHeight - levelHeight;

  //   const colors = this.colors;
  //   const fillColor = this.level <= 30 ? colors.low : this.level <= 50 ? colors.mid : colors.high;

  //   this.prop('markup', [
  //     {
  //       tagName: 'rect',
  //       selector: 'body', // Keep the selector consistent to reference attributes correctly
  //     },
  //     {
  //       tagName: 'rect',
  //       selector: generateUniqueId('emptyFill'),
  //       attributes: {
  //         x: 0,
  //         y: 0,
  //         width: bodyWidth,
  //         height: bodyHeight,
  //         fill: 'lightgrey',
  //         opacity: 1,
  //       },
  //     },
  //     {
  //       tagName: 'rect',
  //       selector: generateUniqueId('levelFill'),
  //       attributes: {
  //         x: 0,
  //         y: levelY,
  //         width: bodyWidth,
  //         height: levelHeight,
  //         fill: fillColor,
  //         opacity: 1,
  //       },
  //     },
  //   ]);
  // }
  // createSolidFill() {
  //   const bodyHeight = this.size().height;
  //   const bodyWidth = this.size().width;
  //   const filledHeight = (this.level / 100) * bodyHeight;
  //   const filledY = bodyHeight - filledHeight;
  //   const fillColor = this.colorToShow;
  //   this.prop('markup', [
  //     { tagName: 'rect', selector: 'body' },
  //     {
  //       tagName: 'rect',
  //       selector: generateUniqueId('emptyFill'),
  //       attributes: { x: 0, y: 0, width: bodyWidth, height: bodyHeight, fill: 'lightgrey' },
  //     },
  //     {
  //       tagName: 'rect',
  //       selector: generateUniqueId('levelFill'),
  //       attributes: { x: 0, y: filledY, width: bodyWidth, height: filledHeight, fill: fillColor },
  //     },
  //   ]);
  // }
  createSolidFill() {
    const bodyHeight = this.size().height;
    const bodyWidth = this.size().width;
    const maxFillHeight = Math.min(bodyHeight, this.maxCapacity);
    const filledHeight = (this.level / 100) * maxFillHeight;
    const filledY = bodyHeight - filledHeight;
    const fillColor = this.colorToShow;
    this.prop('markup', [
      { tagName: 'rect', selector: 'body' },
      {
        tagName: 'rect',
        selector: generateUniqueId('emptyFill'),
        attributes: { x: 0, y: 0, width: bodyWidth, height: bodyHeight, fill: 'lightgrey' },
      },
      {
        tagName: 'rect',
        selector: generateUniqueId('levelFill'),
        attributes: { x: 0, y: filledY, width: bodyWidth, height: filledHeight, fill: fillColor },
      },
    ]);
  }
}
