import { yupResolver } from '@hookform/resolvers/yup';
import {
  Alert,
  Autocomplete,
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  TextField,
} from '@mui/material';
import { useEffect, useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useGetCustomerUserDetailsByIdQuery } from '~/redux/api/customersApi';
import { AlertMessage } from '~/shared/forms/types';
import { Customer } from '~/types/customers';
import { EditUser, UserDto, userDtoSchema } from '~/types/users';
import { mapListToOptions } from '~/utils/utils';

type EditUserPageProps = {
  user: UserDto | undefined;
  customers: Customer[];
  onSaveSuccess: () => void;
  onCanceled: () => void;
  onValidSubmit: (data: any) => Promise<unknown>;
};

const EditUserPage = ({
  user,
  customers,
  onCanceled,
  onSaveSuccess,
  onValidSubmit,
}: EditUserPageProps) => {
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const { data, isFetching } = useGetCustomerUserDetailsByIdQuery(
    {
      id: user?.id ?? 0,
    },
    {
      skip: !user,
      refetchOnMountOrArgChange: true,
    },
  );
  const { control, handleSubmit, reset, getValues, setValue } = useForm<EditUser>({
    defaultValues: {
      username: '',
      first_name: '',
      last_name: '',
      email: '',
      customer_ids_user: [],
      customer_ids_admin: [],
      customer_ids_power_user: [],
      enabled: true,
      country_code: undefined,
      phone_no: undefined,
      ...user,
    },
    resolver: yupResolver(userDtoSchema),
  });
  useEffect(() => {
    if (data) {
      setValue('username', data.username);
      setValue('first_name', data.first_name);
      setValue('last_name', data.last_name);
      setValue('email', data.email);
      setValue(
        'customer_ids_user',
        data.scoped_roles
          .find((role) => role.role === 'USER')
          ?.customer_ids.map((ids) => ids.toString()) ?? [],
      );
      setValue(
        'customer_ids_admin',
        data.scoped_roles
          .find((role) => role.role === 'ADMIN')
          ?.customer_ids.map((ids) => ids.toString()) ?? [],
      );
      setValue(
        'customer_ids_power_user',
        data.scoped_roles
          .find((role) => role.role === 'POWER_USER')
          ?.customer_ids.map((ids) => ids.toString()) ?? [],
      );
      setValue('enabled', data.enabled ?? false);
      setValue('country_code', data.country_code);
      setValue('phone_no', data.phone_no);
    }
  }, [data, user?.id]);
  const onSubmit = (data: EditUser) => {
    const admin_customers = data.customer_ids_admin;
    const user_customers = data.customer_ids_user;
    const power_user_customers = data.customer_ids_power_user;
    onValidSubmit({
      userId: data?.id ?? 0,
      userDetails: {
        id: data?.id ?? 0,
        username: data.username,
        first_name: data.first_name,
        last_name: data.last_name,
        email: data.email,
        phone_number: data.phone_no,
        country_code: data.country_code,
        scoped_roles: [
          {
            role: 'ADMIN',
            customer_ids: admin_customers,
          },
          {
            role: 'USER',
            customer_ids: user_customers,
          },
          {
            role: 'POWER_USER',
            customer_ids: power_user_customers,
          },
        ],
        enabled: data.enabled ?? false,
      },
    });
  };
  const customerListOptions = useMemo(() => mapListToOptions(customers ?? []), [customers]);
  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Controller
          name="username"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              disabled
              onBlur={onBlur}
              value={value}
              label="Username"
              variant="outlined"
              margin="normal"
              fullWidth
              required
            />
          )}
        />
        <Controller
          name="first_name"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              disabled={isFetching}
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              onBlur={onBlur}
              value={value}
              label="First Name"
              variant="outlined"
              margin="normal"
              fullWidth
              required
            />
          )}
        />

        <Controller
          name="last_name"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              disabled={isFetching}
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              onBlur={onBlur}
              value={value}
              label="Last Name"
              variant="outlined"
              margin="normal"
              fullWidth
              required
            />
          )}
        />
        <Box sx={{ display: 'flex', gap: 2, width: '100%' }}>
          <Controller
            name="country_code"
            control={control}
            render={({ field: { onChange, onBlur, value }, fieldState }) => (
              <TextField
                sx={{ width: '30%' }}
                disabled={isFetching}
                error={!!fieldState.error}
                helperText={fieldState.error?.message}
                onChange={onChange}
                onBlur={onBlur}
                value={value}
                type="text"
                label="Country Code"
                variant="outlined"
                margin="normal"
                fullWidth
                required
              />
            )}
          />

          <Controller
            name="phone_no"
            control={control}
            render={({ field: { onChange, onBlur, value }, fieldState }) => (
              <TextField
                sx={{ width: '70%' }}
                error={!!fieldState.error}
                disabled={isFetching}
                helperText={fieldState.error?.message}
                onChange={onChange}
                onBlur={onBlur}
                value={value}
                type="text"
                label="Phone Number"
                variant="outlined"
                margin="normal"
                fullWidth
                required
              />
            )}
          />
        </Box>
        <Controller
          name="email"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              sx={{ mt: 2 }}
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              disabled
              onBlur={onBlur}
              value={value}
              type="email"
              label="Email"
              variant="outlined"
              margin="normal"
              fullWidth
              required
            />
          )}
        />
        <Controller
          name="customer_ids_user"
          control={control}
          render={({ field }) => {
            const value = customerListOptions.filter((customer) =>
              getValues('customer_ids_user').includes(customer.id),
            );
            return (
              <Autocomplete
                sx={{ mt: 2 }}
                fullWidth
                multiple
                disabled={isFetching}
                options={customerListOptions}
                getOptionLabel={(option) => option.label}
                onChange={(_, value) => {
                  field.onChange(value.map((item) => item.id));
                  const adminCustomers = getValues('customer_ids_admin');
                  const powerUserCustomers = getValues('customer_ids_power_user');
                  const filteredAdminCustomers = adminCustomers.filter(
                    (customer) => !value.find((item) => item.id === customer),
                  );
                  const filteredPowerUserCustomers = powerUserCustomers.filter(
                    (customer) => !value.find((item) => item.id === customer),
                  );
                  setValue('customer_ids_admin', filteredAdminCustomers);
                  setValue('customer_ids_power_user', filteredPowerUserCustomers);
                }}
                value={value}
                renderInput={(params) => <TextField {...params} label="Select Customer for User" />}
                isOptionEqualToValue={(option, value) => option.id === value.id}
              />
            );
          }}
        />
        <Controller
          name="customer_ids_power_user"
          control={control}
          render={({ field }) => {
            const value = customerListOptions.filter((customer) =>
              getValues('customer_ids_power_user').includes(customer.id),
            );

            return (
              <Autocomplete
                sx={{
                  mt: 2,
                }}
                fullWidth
                multiple
                disabled={isFetching}
                options={customerListOptions}
                getOptionLabel={(option) => option.label}
                value={value}
                onChange={(_, value) => {
                  field.onChange(value.map((item) => item.id));
                  const userCustomers = getValues('customer_ids_user');
                  const adminCustomers = getValues('customer_ids_admin');
                  const filteredUserCustomers = userCustomers.filter(
                    (customer) => !value.find((item) => item.id === customer),
                  );
                  const filteredAdminCustomers = adminCustomers.filter(
                    (customer) => !value.find((item) => item.id === customer),
                  );
                  setValue('customer_ids_user', filteredUserCustomers, {
                    shouldValidate: true,
                  });
                  setValue('customer_ids_admin', filteredAdminCustomers, {
                    shouldValidate: true,
                  });
                }}
                renderInput={(params) => (
                  <TextField {...params} label="Select Customer for Power User" />
                )}
                isOptionEqualToValue={(option, value) => option.id === value.id}
              />
            );
          }}
        />
        <Controller
          name="customer_ids_admin"
          control={control}
          render={({ field: { onChange } }) => {
            const value = customerListOptions.filter((customer) =>
              getValues('customer_ids_admin').includes(customer.id),
            );
            return (
              <Autocomplete
                sx={{
                  mt: 2,
                }}
                fullWidth
                multiple
                disabled={isFetching}
                options={customerListOptions}
                getOptionLabel={(option) => option.label}
                value={value}
                onChange={(_, value) => {
                  onChange(value.map((item) => item.id));
                  const userCustomers = getValues('customer_ids_user');
                  const powerUserCustomers = getValues('customer_ids_power_user');
                  const filteredUserCustomers = userCustomers.filter(
                    (customer) => !value.find((item) => item.id === customer),
                  );
                  const filteredPowerUserCustomers = powerUserCustomers.filter(
                    (customer) => !value.find((item) => item.id === customer),
                  );
                  setValue('customer_ids_user', filteredUserCustomers);
                  setValue('customer_ids_power_user', filteredPowerUserCustomers);
                }}
                renderInput={(params) => (
                  <TextField {...params} label="Select Customer for Admin" />
                )}
                isOptionEqualToValue={(option, value) => option.id === value.id}
              />
            );
          }}
        />

        <Controller
          name="enabled"
          control={control}
          render={({ field: { onChange, value } }) => {
            return (
              <FormControlLabel
                control={
                  <Checkbox
                    checked={getValues('enabled')}
                    name="enabled"
                    onChange={(event, checked) => {
                      onChange(checked);
                    }}
                  />
                }
                label="is Enabled?"
              />
            );
          }}
        />
        <Box sx={{ width: '100%', display: 'flex', gap: 2, mt: 3 }}>
          <Button type="submit" variant="contained" color="primary" fullWidth disabled={isFetching}>
            Update
          </Button>
          <Button
            fullWidth
            variant="outlined"
            color="primary"
            onClick={() => {
              onCanceled();
              reset();
            }}
          >
            Cancel
          </Button>
        </Box>
      </form>
      {alertMessage && (
        <Alert severity={alertMessage.severity} sx={{ mt: 3 }}>
          {alertMessage.message}
        </Alert>
      )}
    </>
  );
};

export default EditUserPage;
