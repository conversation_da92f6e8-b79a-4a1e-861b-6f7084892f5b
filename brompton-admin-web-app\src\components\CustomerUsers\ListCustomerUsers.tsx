import AddIcon from '@mui/icons-material/Add';
import { Box, Button, Container } from '@mui/material';
import { useState } from 'react';
import { useRolePermission } from '~/hooks/useRolePermission';
import PageName from '../common/PageName/PageName';
import SearchCustomerUsers from './SearchCustomerUsers';

const ListCustomerUsers = () => {
  const { hasPermission } = useRolePermission();
  const [addUser, setAddUser] = useState<boolean>(false);

  return (
    <Container
      sx={{
        padding: 'unset !important',
        maxWidth: '100%',
        width: '100%',
        '@media (min-width: 1200px)': { width: '100%', maxWidth: '100%' },
      }}
    >
      <Box py={2} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <PageName name="Users" />

        {hasPermission('user.create') ? (
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => setAddUser(true)}
          >
            Add User
          </Button>
        ) : null}
      </Box>

      <SearchCustomerUsers addUser={addUser} setAddUser={setAddUser} />
    </Container>
  );
};
export default ListCustomerUsers;
