import {
  FlagOutlined,
  Flag,
  Delete,
  StarBorderPurple500Outlined,
  StarOutlined,
  Cancel,
} from '@mui/icons-material';
import {
  Box,
  Button,
  Card,
  CircularProgress,
  Tooltip,
  Typography,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useHasPowerUserAccess } from '~/hooks/useHasPowerUserAccess';
import {
  useDeleteDashboardMutation,
  useGetDashboardByCustomerIdQuery,
  useMarkDefaultDashboardMutation,
  useMarkFavoriteDashboardMutation,
} from '~/redux/api/dashboardApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getSearchedValue } from '~/redux/selectors/dashboardListSelector';
import { getIsUserLoggedIn } from '~/redux/selectors/dashboardSelectors';
import { dashboardListSlice } from '~/redux/slices/dashboardListSlice';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { DashboardDTO } from '~/types/dashboard';
import { NoDataFound } from '../common/NoDataFound';
import SearchDashbaord from './SearchDashboard';
import dayjs from 'dayjs';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';

export default function DashboardsTable() {
  const { globalAdmin, admin } = useHasAdminAccess();
  const hasPowerUserAccess = useHasPowerUserAccess();
  const dispatch = useDispatch();
  const router = useRouter();
  const currentCustomer = useSelector(getActiveCustomer);
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const isUserLoggedIn = useSelector(getIsUserLoggedIn);
  const searchValue = useSelector(getSearchedValue);
  const [favorite, setFavorite] = useState<DashboardDTO | null>(null);
  const [deleteDashboard, setDeleteDashboard] = useState({ open: false, dashboardId: 0 });
  const [marksState, setMarksState] = useState<boolean | null>(null);

  const [markAsDefault] = useMarkDefaultDashboardMutation();
  const [markFavorite] = useMarkFavoriteDashboardMutation();

  const dateTimeFormat = useSelector(getDateTimeFormat);
  const dateFormat = dateTimeFormat.split(' ')[0];

  const {
    data: dashboardList,
    isLoading: isLoadingDashboards,
    refetch,
  } = useGetDashboardByCustomerIdQuery(
    { customerId: currentCustomer?.id ?? 0, search: searchValue },
    { skip: !currentCustomer?.id },
  );
  const [deleteDashboardAction, { isSuccess, isError }] = useDeleteDashboardMutation();

  useEffect(() => {
    if (isSuccess) {
      showSuccessAlert('Dashboard deleted successfully');
      refetch();
    }
    if (isError) {
      showErrorAlert('Error deleting dashboard');
    }
  }, [isSuccess, isError]);

  const handleDelete = async () => {
    await deleteDashboardAction({
      customerId: currentCustomer?.id ?? 0,
      dashboardId: deleteDashboard.dashboardId,
    });
    setDeleteDashboard({ dashboardId: 0, open: false });
  };

  const columns: GridColDef[] = [
    {
      field: 'title',
      headerName: 'Title',
      flex: 2,
      renderCell: (params) => (
        <Typography
          sx={{ cursor: 'pointer', color: 'primary.main', textDecoration: 'underline' }}
          onClick={() => {
            const dashboard = params.row;
            dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
            dispatch(dashboardSlice.actions.setActiveCustomer(currentCustomer!));
            dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
            dispatch(dashboardListSlice.actions.setCurrentDashboardId(dashboard.id));
            router.push(`/customer/${currentCustomer?.id}/dashboard/${dashboard.id}`);
          }}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'assetDashboardTemplate',
      headerName: 'Asset Dashboard Template',
      flex: 1.5,
      valueGetter: (params) =>
        params.row.asset?.tag && params.row.dashboardTemplate?.title
          ? `${params.row.asset?.tag} / ${params.row.dashboardTemplate?.title}`
          : 'N/A',
    },
    {
      field: 'createdBy',
      headerName: 'Created By',
      flex: 1,
      valueGetter: (params) => params.row.createdBy ?? 'N/A',
    },
    {
      field: 'updatedAt',
      headerName: 'Last Modified On',
      flex: 1,
      valueGetter: (params) =>
        params.row.updatedAt ? dayjs(params.row.updatedAt).format(dateFormat) : 'N/A',
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      sortable: false,
      renderCell: (params) => {
        const dashboard = params.row;
        return (
          <Box display="flex" gap={1}>
            <Tooltip title={dashboard.default ? 'Unmark as Default' : 'Mark as Default'}>
              <Button
                onClick={async () => {
                  try {
                    setMarksState(!dashboard.default);
                    await markAsDefault({
                      customerId: currentCustomer?.id ?? 0,
                      dashboard_id: dashboard.id,
                      user_id: isUserLoggedIn?.id ?? 0,
                      status: !dashboard.default,
                    });
                    showSuccessAlert(
                      !dashboard.default
                        ? 'Dashboard marked as default'
                        : 'Dashboard removed from marked as default',
                    );
                  } catch (e) {
                    showErrorAlert('Error updating for default dashboard');
                  }
                }}
                sx={{ minWidth: 'auto' }}
              >
                {dashboard.default ? <Flag /> : <FlagOutlined />}
              </Button>
            </Tooltip>
            <Tooltip title={dashboard.favorite ? 'Remove from Favorites' : 'Mark as Favorite'}>
              <Button
                onClick={() => {
                  setFavorite({ ...dashboard, favorite: !dashboard.favorite });
                  markFavorite({
                    customerId: currentCustomer?.id ?? 0,
                    dashboard_id: dashboard.id,
                    user_id: isUserLoggedIn?.id ?? 0,
                    status: !dashboard.favorite,
                  });
                }}
                sx={{ minWidth: 'auto' }}
              >
                {dashboard.favorite ? <StarOutlined /> : <StarBorderPurple500Outlined />}
              </Button>
            </Tooltip>
            {(globalAdmin || admin || hasPowerUserAccess) && (
              <Tooltip title="Delete Dashboard">
                <Button
                  color="error"
                  onClick={() => setDeleteDashboard({ dashboardId: dashboard.id, open: true })}
                  sx={{ minWidth: 'auto' }}
                >
                  <Delete />
                </Button>
              </Tooltip>
            )}
          </Box>
        );
      },
    },
  ];

  return (
    <Box>
      <AlertSnackbar {...snackbarState} />

      <SearchDashbaord />

      {isLoadingDashboards ? (
        <Box display="flex" justifyContent="center" alignItems="center" height={200}>
          <CircularProgress />
        </Box>
      ) : dashboardList?.items.length ? (
        <Box sx={{ height: 'calc(100vh - 165px)' }}>
          <DataGrid
            rows={dashboardList.items}
            columns={columns}
            getRowId={(row) => row.id}
            autoPageSize
            pagination
            rowSelection={false}
            sx={{
              width: '100%',
              '& .MuiDataGrid-columnHeader': {
                background: '#F9FAFB',
              },
            }}
            slotProps={{
              toolbar: {
                showQuickFilter: true,
                printOptions: { disableToolbarButton: true },
                csvOptions: { disableToolbarButton: true },
              },
            }}
          />
        </Box>
      ) : (
        <NoDataFound
          message={
            <span>
              No dashboards found for customer <b>{currentCustomer?.name}</b>
            </span>
          }
        />
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDashboard.open}
        onClose={() => setDeleteDashboard({ open: false, dashboardId: 0 })}
      >
        <DialogTitle>
          <Typography variant="h4">Delete Dashboard?</Typography>
        </DialogTitle>
        <DialogContent>
          <Typography color="error">This action cannot be undone.</Typography>
        </DialogContent>
        <DialogActions>
          <Button
            variant="outlined"
            startIcon={<Cancel />}
            onClick={() => setDeleteDashboard({ open: false, dashboardId: 0 })}
          >
            Cancel
          </Button>
          <Button variant="contained" color="error" startIcon={<Delete />} onClick={handleDelete}>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
