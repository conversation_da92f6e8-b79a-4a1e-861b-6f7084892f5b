import { Box, Checkbox, List, ListItem, ListItemText, Typography } from '@mui/material';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  createAssetTemplateDataWithMetricName,
  Datasource,
  DataType,
  MeasurementType,
} from '~/measurements/domain/types';
import { getMetrics } from '~/redux/selectors/dashboardSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { AssetTypeMetrics } from '~/types/asset';

type MetricsListProps = {
  assetTemplate: createAssetTemplateDataWithMetricName | undefined;
  dataSourceList: Datasource[];
  dataTypeList: DataType[];
  measurementTypeList: MeasurementType[];
  assetTypeMetrics: AssetTypeMetrics[];
};
const MetricsList = ({
  assetTemplate,
  assetTypeMetrics,
  dataSourceList,
  dataTypeList,
  measurementTypeList,
}: MetricsListProps) => {
  const dispatch = useDispatch();
  const metrics = useSelector(getMetrics);
  const { setMetrics, setMetricsIdToName, unsetMetricsIdToName } = dashboardSlice.actions;

  useEffect(() => {
    if (assetTemplate) {
      const initialMetrics = assetTemplate.measurements.map((measurement) =>
        Number(measurement.metric_id),
      );
      dispatch(setMetrics(initialMetrics));
      assetTemplate.measurements.forEach(async (measurement) => {
        if (!measurement.metric_id) return;
        await dispatch(
          setMetricsIdToName({
            metricId: measurement.metric_id?.toString(),
            //eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            metricName: measurement.name ?? 'N/A',
          }),
        );
      });
    }
  }, [assetTemplate, assetTypeMetrics, dispatch]);
  return (
    <>
      {assetTemplate && assetTemplate.measurements ? (
        <>
          {assetTemplate.measurements.map((measurement, index) => (
            <List key={index} sx={{ overflowWrap: 'break-word', pl: 1, pt: 0, pb: 0 }}>
              <ListItem disablePadding sx={{ pt: 0, pb: 0 }}>
                <ListItemText
                  primary={
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        maxWidth: 'inherit',
                        overflowWrap: 'anywhere',
                        '& .MuiSvgIcon-root': {
                          display: 'none', // Hides the checkmark
                        },
                      }}
                    >
                      <Checkbox
                        sx={{
                          pt: 0,
                          pb: 0,
                        }}
                        checked={metrics.includes(Number(measurement.metric_id) ?? 0)}
                        onChange={(
                          _event: React.ChangeEvent<HTMLInputElement>,
                          checked: boolean,
                        ) => {
                          if (checked) {
                            dispatch(
                              setMetrics(
                                metrics.includes(Number(measurement.metric_id) ?? 0)
                                  ? metrics.filter((i) => i !== Number(measurement.metric_id))
                                  : [...metrics, Number(measurement.metric_id) ?? 0],
                              ),
                            );
                            dispatch(
                              setMetricsIdToName({
                                metricId: (Number(measurement) ?? 0).toString(),
                                metricName:
                                  assetTypeMetrics.find(
                                    (assetTypeMetric) =>
                                      assetTypeMetric.id === Number(measurement.metric_id),
                                  )?.name ?? 'N/A',
                              }),
                            );
                          } else {
                            dispatch(
                              setMetrics(
                                metrics.includes(Number(measurement.metric_id) ?? 0)
                                  ? metrics.filter((i) => i !== Number(measurement.metric_id))
                                  : [...metrics, Number(measurement.metric_id) ?? 0],
                              ),
                            );
                            dispatch(
                              unsetMetricsIdToName({
                                metricId: (Number(measurement.metric_id) ?? 0).toString(),
                              }),
                            );
                          }
                        }}
                      />
                      <Typography fontWeight="bold">
                        {
                          //eslint-disable-next-line @typescript-eslint/ban-ts-comment
                          //@ts-ignore
                          measurement.name
                        }
                      </Typography>
                    </Box>
                  }
                />
              </ListItem>
            </List>
          ))}
        </>
      ) : null}
    </>
  );
};
export default MetricsList;
