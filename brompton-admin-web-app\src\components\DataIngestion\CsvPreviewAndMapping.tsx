import {
  Box,
  Divider,
  FormControl,
  FormControlLabel,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
  Radio,
  RadioGroup,
  FormLabel,
} from '@mui/material';
import React, { useEffect, useState } from 'react';

interface CsvPreviewAndMappingProps {
  columns: string[];
  previewRows: any[][];
  initialMapping: Record<string, string>;
  onMappingChange: (mapping: Record<string, string>) => void;
  onTimestampFormatChange?: (format: 'UTC' | 'IST' | 'SEPARATE') => void;
}

const CsvPreviewAndMapping: React.FC<CsvPreviewAndMappingProps> = ({
  columns,
  previewRows,
  initialMapping = {},
  onMappingChange,
  onTimestampFormatChange,
}) => {
  const [mapping, setMapping] = useState<Record<string, string>>(initialMapping);
  const [timestampFormat, setTimestampFormat] = useState<'UTC' | 'IST' | 'SEPARATE'>(
    initialMapping.date && initialMapping.time ? 'SEPARATE' : 'UTC',
  );

  useEffect(() => {
    onMappingChange(mapping);
  }, [mapping, onMappingChange]);

  const handleChange = (field: string, value: string) => {
    setMapping((prev) => ({ ...prev, [field]: value }));
  };

  const requiredKeys = ['asset', 'measure', 'value'];
  const timestampReady = timestampFormat !== 'SEPARATE' && mapping.timestamp;
  const dateTimeReady = timestampFormat === 'SEPARATE' && mapping.date && mapping.time;

  const allMapped = requiredKeys.every((key) => mapping[key]) && (timestampReady || dateTimeReady);

  const handleTimestampFormatChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newFormat = event.target.value as 'UTC' | 'IST' | 'SEPARATE';
    setTimestampFormat(newFormat);

    // Update mapping based on the selected format
    const updatedMapping = { ...mapping };

    if (newFormat === 'SEPARATE') {
      // Remove timestamp field when switching to separate date/time
      delete updatedMapping.timestamp;
    } else {
      // Remove date and time fields when switching to timestamp
      delete updatedMapping.date;
      delete updatedMapping.time;
    }

    setMapping(updatedMapping);

    // Notify parent component of format change
    if (onTimestampFormatChange) {
      onTimestampFormatChange(newFormat);
    }
  };

  useEffect(() => {
    if (onTimestampFormatChange) {
      onTimestampFormatChange(timestampFormat);
    }
  }, []);

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        CSV Preview
      </Typography>
      <Box sx={{ overflowX: 'auto', mb: 3 }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              {columns.map((col) => (
                <TableCell key={col} sx={{ fontWeight: 'bold', backgroundColor: '#CAEAD8' }}>
                  {col}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {previewRows.slice(0, 2).map((row, idx) => (
              <TableRow key={idx} hover>
                {row.map((cell, ci) => (
                  <TableCell key={ci}>{cell}</TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Box>

      <Divider sx={{ mb: 3 }} />

      <Typography variant="h5" gutterBottom>
        CSV Mapping
      </Typography>

      <Stack spacing={3}>
        <Grid container spacing={2}>
          {['asset', 'measure', 'value'].map((key) => (
            <Grid item xs={12} sm={3} key={key}>
              <FormControl fullWidth>
                <InputLabel>{`${key[0].toUpperCase()}${key.slice(1)} Column`}</InputLabel>
                <Select
                  value={mapping[key] || ''}
                  onChange={(e) => handleChange(key, e.target.value)}
                  label={`${key[0].toUpperCase()}${key.slice(1)} Column`}
                  displayEmpty
                >
                  {columns.map((col) => (
                    <MenuItem key={col} value={col}>
                      {col}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          ))}
        </Grid>

        <FormControl component="fieldset" sx={{ mt: 2 }}>
          <RadioGroup row value={timestampFormat} onChange={handleTimestampFormatChange}>
            <FormControlLabel value="UTC" control={<Radio />} label="UTC Timestamp" />
            <FormControlLabel value="IST" control={<Radio />} label="IST Timestamp (India)" />
            <FormControlLabel value="SEPARATE" control={<Radio />} label="Separate Date and Time" />
          </RadioGroup>
        </FormControl>

        {/* Timestamp fields based on selected format */}
        <Grid container spacing={2}>
          {timestampFormat !== 'SEPARATE' && (
            <Grid item xs={12} sm={3}>
              <FormControl fullWidth>
                <InputLabel>{`Timestamp ${timestampFormat}`}</InputLabel>
                <Select
                  value={mapping.timestamp || ''}
                  onChange={(e) => handleChange('timestamp', e.target.value)}
                  label={`Timestamp ${timestampFormat}`}
                  displayEmpty
                >
                  {columns.map((col) => (
                    <MenuItem key={col} value={col}>
                      {col}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          )}

          {timestampFormat === 'SEPARATE' && (
            <>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth>
                  <InputLabel>Date Column</InputLabel>
                  <Select
                    value={mapping.date || ''}
                    onChange={(e) => handleChange('date', e.target.value)}
                    label="Date Column"
                    displayEmpty
                  >
                    {columns.map((col) => (
                      <MenuItem key={col} value={col}>
                        {col}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={3}>
                <FormControl fullWidth>
                  <InputLabel>Time Column</InputLabel>
                  <Select
                    value={mapping.time || ''}
                    onChange={(e) => handleChange('time', e.target.value)}
                    label="Time Column"
                    displayEmpty
                  >
                    {columns.map((col) => (
                      <MenuItem key={col} value={col}>
                        {col}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </>
          )}
        </Grid>
      </Stack>
    </Box>
  );
};

export default CsvPreviewAndMapping;
