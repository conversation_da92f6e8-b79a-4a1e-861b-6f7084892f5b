import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';
import { alpha, Box, Chip, Typography } from '@mui/material';
import <PERSON> from 'papaparse';
import React, { useRef, useState } from 'react';
import { theme } from '~/pages/_app';

interface Props {
  onParsed: (data: any, file: File) => void;
  isLoading?: boolean;
}

const CsvUploadForm: React.FC<Props> = ({ onParsed, isLoading = false }) => {
  const [file, setFile] = useState<File | null>(null);
  const [parseError, setParseError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const selectedFile = event.target.files[0];
      setFile(selectedFile);
      setParseError(null);

      // Automatically parse the file when selected
      parseFile(selectedFile);
    }
  };

  const handleBrowseClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const parseFile = (file: File) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        if (results.errors.length > 0) {
          setParseError(`Error parsing CSV: ${results.errors[0].message}`);
          return;
        }

        // Get column names
        const columns = results.meta.fields || [];

        // Get a preview of the data (first 5 rows)
        const previewRows = results.data
          .slice(0, 5)
          .map((row: any) => columns.map((col) => row[col]));

        onParsed(
          {
            columns,
            previewRows,
            originalFile: file,
          },
          file,
        );
      },
      error: (error) => {
        setParseError(`Error parsing CSV: ${error.message}`);
      },
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      const droppedFile = files[0];
      if (droppedFile.type === 'text/csv' || droppedFile.name.endsWith('.csv')) {
        setFile(droppedFile);
        setParseError(null);

        // Automatically parse the dropped file
        parseFile(droppedFile);
      } else {
        setParseError('Please upload a CSV file');
      }
    }
  };

  return (
    <Box>
      <Typography variant="h5" mb={3}>
        Upload CSV File
      </Typography>

      <Box
        sx={{
          border: `2px dashed ${
            isDragging ? theme.palette.primary.main : alpha(theme.palette.text.primary, 0.2)
          }`,
          borderRadius: 2,
          p: 4,
          textAlign: 'center',
          mb: 4,
          cursor: 'pointer',
          backgroundColor: isDragging
            ? alpha(theme.palette.primary.main, 0.05)
            : alpha(theme.palette.background.default, 0.5),
          transition: 'all 0.2s ease',
          '&:hover': {
            backgroundColor: alpha(theme.palette.primary.main, 0.05),
            borderColor: theme.palette.primary.main,
          },
        }}
        onClick={handleBrowseClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          accept=".csv"
          style={{ display: 'none' }}
          ref={fileInputRef}
          onChange={handleFileChange}
        />
        {file ? (
          <Box display="flex" flexDirection="column" alignItems="center">
            <InsertDriveFileOutlinedIcon sx={{ fontSize: 56, color: 'primary.main', mb: 2 }} />
            <Typography variant="h6" fontWeight="500" color="primary.main">
              {file.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" mt={1}>
              {(file.size / 1024).toFixed(2)} KB
            </Typography>
            <Chip
              icon={<CheckCircleOutlineIcon />}
              label="File Selected"
              color="success"
              variant="outlined"
              sx={{ mt: 2 }}
            />
          </Box>
        ) : (
          <Box>
            <CloudUploadIcon
              sx={{
                fontSize: 64,
                color: isDragging ? 'primary.main' : 'text.secondary',
                mb: 2,
                transition: 'all 0.2s ease',
              }}
            />
            <Typography variant="h6" fontWeight="500" mb={1}>
              Drag & Drop CSV File Here
            </Typography>
            <Typography variant="body2" color="text.secondary">
              or click to browse files
            </Typography>
          </Box>
        )}
      </Box>

      {parseError && (
        <Box mb={3} color="error.main">
          <Typography variant="body2">{parseError}</Typography>
        </Box>
      )}
    </Box>
  );
};

export default CsvUploadForm;
