import {
  Autocomplete,
  Box,
  FormControl,
  FormLabel,
  MenuItem,
  OutlinedInput,
  Select,
  SelectChangeEvent,
  TextField,
  Typography,
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import dynamic from 'next/dynamic';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import useFetchDynamicChart from '~/hooks/useFetchDynamicChart';
import { useGetAllMeasurementsByCustomerQuery } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';
import { AggByOptions, SamplePeriodOptions } from '~/types/dashboard';
import IOSSwitch from '../Switch/Switch';
import HomeButton from '../common/Home/HomeButton';
import Loader from '../common/Loader';
import PageName from '../common/PageName/PageName';
import { useRouter } from 'next/router';
const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });

const today = new Date();

const DynamicCharts = () => {
  const router = useRouter();
  const dateTimeFormat = useSelector(getDateTimeFormat);
  const activeCustomer = useSelector(getActiveCustomer);
  const [dateError, setDateError] = useState<string | null>(null);
  const [unitAndTag, setUnitAndTag] = useState<{
    unit: string | null;
    tag: string | null;
  }>({
    tag: null,
    unit: null,
  });
  const [measurement, setMeasurement] = useState<number | null>(null);
  const [chartType, setChartType] = useState<'bar' | 'scatter'>('scatter');
  const [startDate, setStartDate] = useState<Date>(
    new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
      today.getHours(),
      today.getMinutes() - 10,
    ),
  );
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [samplePeriod, setSamplePeriod] = useState<number>(1);
  const [aggregation, setAggregation] = useState<number>(1);
  const { data: measurementsList } = useGetAllMeasurementsByCustomerQuery(
    { customerId: activeCustomer?.id ?? 0 },
    {
      skip: !activeCustomer || activeCustomer.id === 0,
      refetchOnMountOrArgChange: true,
    },
  );
  const handleStartDateChange = (newValue: dayjs.Dayjs | null) => {
    if (newValue) {
      const newDate = newValue.toDate();
      if (newDate > endDate) {
        setDateError('Start date cannot be greater than the end date.');
      } else {
        setDateError(null); // Clear error if valid
        setStartDate(newDate);
      }
    }
  };

  const handleEndDateChange = (newValue: dayjs.Dayjs | null) => {
    if (newValue) {
      const newDate = newValue.toDate();
      if (newDate < startDate) {
        setDateError('End date cannot be earlier than the start date.');
      } else {
        setDateError(null); // Clear error if valid
        setEndDate(newDate);
      }
    }
  };

  // Handlers
  const handleChartTypeChange = () => {
    setChartType((prev) => (prev === 'scatter' ? 'bar' : 'scatter'));
  };

  const handleSamplePeriodChange = (event: SelectChangeEvent<number | null>) => {
    setSamplePeriod(Number(event.target.value));
  };

  const handleAggregationChange = (event: SelectChangeEvent<number | null>) => {
    setAggregation(Number(event.target.value));
  };
  useEffect(() => {
    if (measurementsList && measurementsList.items) {
      const measureFind = measurementsList?.items?.find((measure) => measure.id === measurement);
      setUnitAndTag({
        tag: measureFind?.tag ?? null,
        unit: measureFind?.units ?? null,
      });
    } else {
      setUnitAndTag({
        tag: null,
        unit: null,
      });
    }
  }, [measurementsList, measurement]);
  useEffect(() => {
    if (!router.isReady) return;

    const { measurement_id, start_date, end_date } = router.query;

    if (measurement_id) {
      const measureParam = Array.isArray(measurement_id) ? measurement_id[0] : measurement_id;
      const measureIds = measureParam.split(',').map(Number); // Convert strings to numbers
      setMeasurement(measureIds.length === 1 ? measureIds[0] : null); // Ensure type consistency
    }

    if (start_date) {
      const startTimestamp = Array.isArray(start_date) ? start_date[0] : start_date;
      const parsedStart = new Date(Number(startTimestamp));
      setStartDate(parsedStart);
    }

    if (end_date) {
      const endTimestamp = Array.isArray(end_date) ? end_date[0] : end_date;
      const parsedEnd = new Date(Number(endTimestamp));
      setEndDate(parsedEnd);
    }
  }, [router.isReady, router.query]);
  const { chartData, isError, isLoading, layoutData } = useFetchDynamicChart({
    measurement,
    startDate: startDate.getTime(),
    endDate: endDate.getTime(),
    chartType,
    aggBy: aggregation,
    samplePeriod: samplePeriod,
    unit: unitAndTag.unit,
    tag: unitAndTag.tag,
  });

  return (
    <Box>
      {/* Header */}

      {/* Form Fields */}
      <Box display="grid" gridTemplateColumns="repeat(3, 1fr)" gap={2} mt={3}>
        {/* Measurements Dropdown (Autocomplete) */}
        <FormControl
          sx={{ width: '100%', backgroundColor: (theme) => theme.palette.background.paper }}
        >
          <Autocomplete
            id="measurements-autocomplete"
            options={measurementsList?.items || []}
            getOptionLabel={(option) => option.tag}
            isOptionEqualToValue={(option, value) => option.id === value?.id}
            value={measurementsList?.items.find((item) => item.id === measurement) || null}
            onChange={(_, newValue) => setMeasurement(newValue ? newValue.id : null)}
            renderInput={(params) => (
              <TextField {...params} label="Measurements" variant="outlined" />
            )}
            renderOption={(props, option, { index }) => (
              <li {...props} key={`${option.id}-${index}`}>
                {option.tag}
              </li>
            )}
          />
        </FormControl>

        {/* Sample Period Dropdown */}

        {/* Start Date Picker */}
        <DateTimePicker
          label="Start Date"
          sx={{ width: '100%' }}
          value={dayjs(startDate)}
          onAccept={handleStartDateChange}
          closeOnSelect={false}
          ampm={false}
          disableFuture
          format={dateTimeFormat}
          slotProps={{
            textField: {
              helperText: dateError,
              error: Boolean(dateError),
            },
          }}
        />

        <DateTimePicker
          label="End Date"
          sx={{ width: '100%' }}
          value={dayjs(endDate)}
          onAccept={handleEndDateChange}
          closeOnSelect={false}
          disableFuture
          ampm={false}
          format={dateTimeFormat}
          slotProps={{
            textField: {
              helperText: dateError,
              error: Boolean(dateError),
            },
          }}
        />

        <FormControl
          sx={{ width: '100%', backgroundColor: (theme) => theme.palette.background.paper }}
        >
          <Select
            labelId="sample-period-select-label"
            id="sample-period-range-select"
            value={samplePeriod}
            onChange={handleSamplePeriodChange}
            input={
              <OutlinedInput
                label="Sample Period"
                sx={{
                  p: 0.2,
                  '& legend': {
                    maxWidth: '100%',
                    height: 'fit-content',
                    '& span': {
                      opacity: 1,
                    },
                  },
                }}
              />
            }
          >
            {SamplePeriodOptions.map((option, index) =>
              index > 0 ? (
                <MenuItem key={option.value} value={index}>
                  {option.label}
                </MenuItem>
              ) : null,
            )}
          </Select>
        </FormControl>
        {/* Aggregation Dropdown */}
        <FormControl
          sx={{ width: '100%', backgroundColor: (theme) => theme.palette.background.paper }}
        >
          <Select
            labelId="aggregation-select-label"
            id="aggregation-select"
            value={aggregation}
            onChange={handleAggregationChange}
            input={
              <OutlinedInput
                label="Aggregation"
                sx={{
                  p: 0.2,
                  '& legend': {
                    maxWidth: '100%',
                    height: 'fit-content',
                    '& span': {
                      opacity: 1,
                    },
                  },
                }}
              />
            }
          >
            {AggByOptions.map((option, index) => (
              <MenuItem key={index} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'baseline' }}>
          <FormLabel>Chart Type : </FormLabel>
          <Box display="flex" alignItems="center" gap={1} mt={2}>
            <Typography variant="body1">Scatter </Typography>
            <IOSSwitch
              sx={{ m: 1 }}
              checked={chartType === 'bar'}
              onChange={handleChartTypeChange}
            />
            <Typography variant="body1">Bar</Typography>
          </Box>
        </Box>
      </Box>

      <Box>
        {measurement === null ? (
          <Box sx={{ height: 400 }}>
            {/* <Typography variant="h6" color="error">
              Please select measurement{' '}
            </Typography> */}
          </Box>
        ) : (
          <>
            {isLoading ? (
              // Show loader when loading
              <Box display="flex" justifyContent="center" alignItems="center" height="100%" mt={10}>
                <Loader />
              </Box>
            ) : (
              <>
                {isError ? (
                  <Box
                    sx={{
                      height: 400,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography variant="h6" color="error">
                      Error while fetching measurement data
                    </Typography>
                  </Box>
                ) : (
                  <>
                    <Plot
                      data={chartData}
                      useResizeHandler={true}
                      style={{ width: '100%', height: '100%' }}
                      layout={{
                        margin: {
                          ...layoutData.margin,
                          l: layoutData?.margin?.l ?? undefined,
                          r: layoutData?.margin?.r ?? undefined,
                        },
                        legend: {
                          x: 0, // Position legend at the left
                          y: -0.1, // Position legend slightly below the x-axis
                          xanchor: 'left', // Anchor the legend to the right side of the x position
                          yanchor: 'top', // Anchor the legend to the top side of the y position
                        },
                        autosize: true,
                        ...layoutData,
                        yaxis: {
                          title: 'Value',
                          position: 0,
                        },
                        xaxis: {
                          title: 'Time',
                          ...layoutData.xaxis,
                        },
                      }}
                      config={{
                        responsive: true,
                        displaylogo: false,
                        displayModeBar: false, // Hide the entire mode bar
                        modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
                      }}
                    />
                  </>
                )}
              </>
            )}
          </>
        )}
      </Box>
    </Box>
  );
};

export default DynamicCharts;
