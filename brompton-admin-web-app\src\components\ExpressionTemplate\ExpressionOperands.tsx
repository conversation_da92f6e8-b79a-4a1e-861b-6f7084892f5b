import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import { Box, Button, FormHelperText, Grid, TextField, Typography } from '@mui/material';
import { Dispatch, SetStateAction } from 'react';
type ExpresionOperandsProps = {
  operands: string[];
  setOperands: Dispatch<SetStateAction<string[]>>;
  handleAddOperand: () => void;
  handleRemoveOperand: (index: number) => void;
  handleOperandChange: (index: number, value: string) => void;
};
const ExpresionOperands = ({
  operands,
  handleAddOperand,
  handleRemoveOperand,
  handleOperandChange,
}: ExpresionOperandsProps) => {
  return (
    <Box mt={3}>
      <Typography>Operands</Typography>
      <Grid container spacing={2}>
        {operands.map((operand, index) => (
          <Grid item xs={12} sm={3} key={index}>
            <Box mt={1}>
              <TextField
                value={operand}
                label={`Operand ${index + 1}`}
                onChange={(e) => handleOperandChange(index, e.target.value)}
                inputProps={{
                  pattern: '[a-zA-Z][a-zA-Z0-9_]*',
                  title:
                    'Variable name must start with a letter and can only contain letters, numbers, and underscores',
                }}
                error={!operand.match(/^[a-zA-Z][a-zA-Z0-9_]*$/)}
                helperText={!operand.match(/^[a-zA-Z][a-zA-Z0-9_]*$/) && 'Invalid input'}
              />
              <Button startIcon={<DeleteIcon />} onClick={() => handleRemoveOperand(index)}>
                Remove
              </Button>
            </Box>
          </Grid>
        ))}
      </Grid>
      <Button onClick={handleAddOperand} startIcon={<AddIcon />} sx={{ mt: 2 }}>
        Add Operand
      </Button>
    </Box>
  );
};
export default ExpresionOperands;
