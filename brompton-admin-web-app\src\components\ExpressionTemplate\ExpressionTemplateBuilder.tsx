import {
  <PERSON>ert,
  Alert<PERSON><PERSON>le,
  Box,
  Button,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { compile, evaluate, isInteger, isString } from 'mathjs';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { CustomError } from '~/errors/CustomerErrorResponse';
import InfoIcon from '@mui/icons-material/Info';
import {
  useCreateCalculationEngineTemplateMutation,
  useDataTypesQuery,
  useEditCalculationEngineTemplateMutation,
  useGetCalculationEngineTemplatesQuery,
} from '~/redux/api/calculationEngine';
import { AlertMessage } from '~/shared/forms/types';

const ExpressionTemplateBuilder: React.FC = () => {
  const router = useRouter();
  const { templateId } = router.query;
  const [expressionName, setExpressionName] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [dataType, setDataType] = useState<number | undefined>(undefined);
  const [expression, setExpression] = useState<string>('');
  const [expressionValues, setExpressionValue] = useState<string>('');
  const [output, setOutput] = useState<string>('');
  const [outputError, setOutputError] = useState<string>('');
  const [variables, setVariables] = useState<
    {
      variable_name: string;
      variable_value: string;
    }[]
  >([]);
  const { data: dataTypes } = useDataTypesQuery();
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const [editAlertMessage, setEditAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const [isValidSample, setIsValidSample] = useState<boolean>(false);
  const { data: engines, isLoading: loadingTemplates } = useGetCalculationEngineTemplatesQuery(
    undefined,
    {
      skip: !templateId,
    },
  );
  useEffect(() => {
    if (templateId && engines?.items.find((engine) => engine.id === Number(templateId))) {
      const template = engines.items.find((engine) => engine.id === Number(templateId));
      if (template) {
        setExpressionName(template.name);
        setDescription(template.description);
        setDataType(template.dataType as number);
        setExpression(template.expression);
      }
    }
  }, [templateId, engines, loadingTemplates]);
  const [createCalcEngineTemplate, { isError, isLoading, isSuccess, error }] =
    useCreateCalculationEngineTemplateMutation();

  const [
    editCalculation,
    {
      isError: updateError,
      error: updateErrorMessage,
      isLoading: updateLoading,
      isSuccess: updateSuccess,
    },
  ] = useEditCalculationEngineTemplateMutation();

  const isValidExpression = (expression: string): boolean => {
    if (expression === '') {
      return true;
    }
    try {
      // Extract variable names from the expression
      const variablePattern = /\$\b[a-zA-Z]\w*\b/g;
      const variables = expression.match(variablePattern) || [];
      // Check if all variables start with $
      const allVariablesValid = variables.every((variable) => variable.startsWith('$'));
      if (!allVariablesValid) {
        return false;
      }
      return true;
    } catch (error) {
      return false;
    }
  };
  const handleSave = () => {
    if (!isValidExpression(expression)) {
      return;
    }
    createCalcEngineTemplate({
      name: expressionName,
      description,
      dataType: dataType ?? 0,
      expression,
    });
  };
  const handleEdit = () => {
    if (!isValidExpression(expression)) {
      return;
    }
    editCalculation({
      id: Number(templateId),
      name: expressionName,
      description,
      dataType: dataType ?? 0,
      expression,
    });
  };
  useEffect(() => {
    if (!templateId) {
      if (isSuccess) {
        setAlertMessage({
          message: `Expression created successfully!`,
          severity: 'success',
        });
      }
      if (isError && error) {
        const err = error as CustomError;
        setAlertMessage({ message: err.data.exception ?? 'Server error', severity: 'error' });
      }
    }
  }, [error, isError, isSuccess, isLoading]);

  useEffect(() => {
    if (templateId) {
      if (updateSuccess) {
        setEditAlertMessage({
          message: `Expression updated successfully!`,
          severity: 'success',
        });
      }
      if (updateError && updateErrorMessage) {
        const err = updateErrorMessage as CustomError;
        setEditAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
      }
    }
  }, [updateError, updateErrorMessage, updateLoading, updateSuccess, templateId]);

  useEffect(() => {
    if (expression === '') {
      setOutput('');
      setOutputError('');
    }
    if (expression) {
      const variables = expression.match(/\$\w+/g);
      const uniqueVariables = new Set(variables);
      const vars = Array.from(uniqueVariables)?.map((variable) => {
        return {
          variable_name: variable,
          variable_value: '',
        };
      });
      setVariables(vars ?? []);
    }
  }, [expression]);
  useEffect(() => {
    if (expression && variables.filter((vars) => vars.variable_value === '').length === 0) {
      const targetDataType = dataTypes?.items?.find((type) => type.id === dataType);
      const updatedExpression = expression.replace(/\$\w+/g, (match) => {
        const variable = variables.find((v) => v.variable_name === match);
        return variable
          ? targetDataType?.name === 'STRING'
            ? `'${variable.variable_value}'`
            : variable.variable_value
          : match;
      });
      // this code is to evaluate the expression to varaibles
      try {
        const variableObjects: { [key: string]: string } = variables.reduce((acc, variable) => {
          acc[variable.variable_name] = variable.variable_value;
          return acc;
        }, {} as { [key: string]: string });

        switch (targetDataType?.name) {
          case 'INT':
            if (variables.every((variable) => isInteger(Number(variable.variable_value)))) {
              // setOutput(evaluate(`number(${updatedExpression})`, variableObjects).toString());
              setOutput(eval(updatedExpression).toString().toUpperCase());
            } else {
              setOutput('Invalid expression');
            }
            break;
          case 'REAL':
            if (variables.every((variable) => !isNaN(Number(variable.variable_value)))) {
              // setOutput(evaluate(`number(${updatedExpression})`, variableObjects).toString());
              setOutput(eval(updatedExpression).toString().toUpperCase());
            } else {
              setOutput('Invalid expression');
            }
            break;
          case 'BOOLEAN':
            if (variables.every((variable) => Boolean(variable.variable_value))) {
              setOutput(eval(updatedExpression).toString().toUpperCase());
              // setOutput(
              //   evaluate(`boolean(${updatedExpression})`, variableObjects).toString().toUpperCase(),
              // );
            } else {
              setOutput('Invalid expression');
            }
            break;
          case 'STRING':
            setOutput(eval(updatedExpression));
            // setOutput(evaluate(`string(${updatedExpression})`, variableObjects).toString());
            break;
          case 'TIME':
            setOutput(evaluate(`${updatedExpression}`, variableObjects).toString());
            break;
          default:
            setOutput('Invalid expression');
        }
      } catch (e) {
        setOutputError((e as Error).message);
        setOutput('Invalid expression');
      }
      setExpressionValue(updatedExpression);
    } else {
      setOutput('');
      setOutputError('');
    }
  }, [variables, expression, dataType]);
  useEffect(() => {
    const targetDataType = dataTypes?.items?.find((type) => type.id === dataType);
    // console.log(targetDataType);
    if (targetDataType) {
      // dataTypes is  "INT", "REAL", "BOOLEAN", "STRING","TIME"]
      // variable's sample value must be from the same data type only
      // generate code to check if the variable's sample value is from the same data type
      switch (targetDataType?.name) {
        case 'INT':
          setIsValidSample(
            variables.every((variable) => isInteger(Number(variable.variable_value))),
          );
          break;
        case 'REAL':
          setIsValidSample(variables.every((variable) => !isNaN(Number(variable.variable_value))));
          break;
        case 'BOOLEAN':
          setIsValidSample(variables.every((variable) => Boolean(variable.variable_value)));
          break;
        case 'STRING':
          setIsValidSample(variables.every((variable) => isString(variable.variable_value)));
          break;
        case 'TIME':
          setIsValidSample(
            variables.every((variable) =>
              /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(variable.variable_value),
            ),
          );
          break;
        default:
          setIsValidSample(false);
      }
    }
  }, [variables, dataType]);

  if (
    templateId &&
    !engines?.items.find((engine) => engine.id === Number(templateId))?.expression &&
    !loadingTemplates
  ) {
    return <Alert severity="error">No Expression Template found</Alert>;
  }

  return (
    <Box>
      <FormControl fullWidth>
        <TextField
          label="Expression Name"
          value={expressionName}
          onChange={(e) => setExpressionName(e.target.value)}
        />
      </FormControl>
      <FormControl fullWidth sx={{ mt: 1 }}>
        <TextField
          label="Description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
        />
      </FormControl>
      <Box sx={{ display: 'flex' }}>
        <FormControl fullWidth sx={{ mt: 1 }}>
          <Select
            label="Data type"
            value={dataType ?? ''}
            defaultValue={dataType ?? ''}
            onChange={(e) => setDataType(e.target.value as number)}
            sx={{
              p: 0.3,
              '& fieldset': {
                '& legend': {
                  maxWidth: '100%',
                  height: 'auto',
                  '& span': {
                    opacity: 1,
                  },
                },
              },
            }}
          >
            {dataTypes?.items.map((dataType) => (
              <MenuItem key={dataType.id} value={dataType.id}>
                {dataType.name}
              </MenuItem>
            )) ?? null}
          </Select>
        </FormControl>
        {dataTypes?.items?.find((type) => type.id === dataType && type.name === 'TIME') !==
          undefined && (
          <Tooltip title="For Time Must need to add Hours,mins, seconds, miliseconds in sample output">
            <InfoIcon sx={{ m: 'auto', ml: 2 }} />
          </Tooltip>
        )}
      </Box>
      <Alert severity="info" sx={{ mt: 2 }}>
        <AlertTitle>Note: Prepend $ symbol to declare a variable.</AlertTitle>
        Use the variables in the expression by prefixing the variable name with a dollar sign ($).
        For example, if you have a variable named &apos;amount&apos;, you can use it in the
        expression as $amount.
      </Alert>
      <FormControl fullWidth sx={{ mt: 1 }}>
        <TextField
          label="Expression"
          multiline
          rows={4}
          onChange={(e) => setExpression(e.target.value)}
          value={expression}
          error={!isValidExpression(expression)}
          helperText={!isValidExpression(expression) ? 'Invalid expression' : undefined}
        />
      </FormControl>
      {variables.length > 0 ? (
        <Box mt={3}>
          <Typography>Variables sample inputs</Typography>
          <Grid container spacing={2}>
            {variables.map((variable, index) => (
              <Grid item xs={12} sm={3} key={index}>
                <Box mt={1}>
                  <TextField
                    onChange={(e) => {
                      const updatedVariables = [...variables];
                      updatedVariables[index].variable_value = e.target.value;
                      setVariables(updatedVariables);
                    }}
                    value={variable.variable_value}
                    label={`Variable ${variable.variable_name} sample value`}
                  />
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>
      ) : null}

      <Box>
        {output !== 'Invalid expression' ? (
          <Typography>Output : {output}</Typography>
        ) : (
          <FormHelperText error={output === 'Invalid expression'}>
            Invalid sample values for the variables
            <br />
            {outputError}
          </FormHelperText>
        )}
      </Box>
      {!templateId && (
        <Button
          variant="contained"
          sx={{ mt: 2 }}
          disabled={
            isLoading ||
            !isValidExpression(expression) ||
            !expressionName ||
            !dataType ||
            !description ||
            !expression ||
            isSuccess ||
            output === 'Invalid expression'
          }
          onClick={() => {
            handleSave();
          }}
        >
          Save
        </Button>
      )}
      {templateId && (
        <Button
          variant="contained"
          sx={{ mt: 2 }}
          disabled={
            updateLoading ||
            !isValidExpression(expression) ||
            !expressionName ||
            !dataType ||
            !description ||
            !expression ||
            output === 'Invalid expression'
          }
          onClick={() => {
            handleEdit();
          }}
        >
          Save
        </Button>
      )}
      {alertMessage && (
        <Alert severity={alertMessage.severity} sx={{ mt: 3 }}>
          {alertMessage.message}
        </Alert>
      )}
      {editAlertMessage && (
        <Alert severity={editAlertMessage.severity} sx={{ mt: 3 }}>
          {editAlertMessage.message}
        </Alert>
      )}
    </Box>
  );
};
export default ExpressionTemplateBuilder;
