import { Box, Button, ListItemText, MenuItem, Select, TextField, Typography } from '@mui/material';
import { Dispatch, SetStateAction } from 'react';

type MathExpressionBuilderProps = {
  operands: string[];
  expressionBuilder: {
    expression: string;
    operands: number[];
  }[];
  setExpressionBuilder: Dispatch<
    SetStateAction<
      {
        expression: string;
        operands: number[];
      }[]
    >
  >;
};
const MathExpressionBuilder = ({
  operands,
  expressionBuilder,
  setExpressionBuilder,
}: MathExpressionBuilderProps) => {
  const handleAddExpressionBuilder = () => {
    setExpressionBuilder([...expressionBuilder, { expression: '', operands: [-1, -1] }]);
  };
  return (
    <Box sx={{ mt: 2 }}>
      <Typography>Mathematical Expression builder with operands </Typography>
      <Box>
        {expressionBuilder.map((expression, index) => {
          return (
            <Box key={index}>
              <Typography>Expression {index + 1}</Typography>
              <Box>
                <Typography>Expression</Typography>
                <TextField
                  value={expression.expression}
                  onChange={(e) => {
                    const updatedExpressionBuilder = [...expressionBuilder];
                    updatedExpressionBuilder[index].expression = e.target.value;
                    setExpressionBuilder(updatedExpressionBuilder);
                  }}
                />
              </Box>
              <Box>
                <Typography>Operands</Typography>
              </Box>
              <Box display={'flex'}>
                <Box>
                  <Typography>Select 1st Operands</Typography>
                  <Select
                    value={expression.operands[0] === -1 ? '' : operands[expression.operands[0]]}
                    onChange={(e) => {
                      const updatedExpressionBuilder = [...expressionBuilder];
                      updatedExpressionBuilder[index].operands[0] = Number(e.target.value);
                      setExpressionBuilder(updatedExpressionBuilder);
                    }}
                    renderValue={(selected) => selected}
                  >
                    {operands.map((operand, index) => (
                      <MenuItem key={operand} value={index}>
                        <ListItemText primary={operand} />
                      </MenuItem>
                    ))}
                  </Select>
                </Box>
                <Box>
                  <Typography>Select 2nd Operands</Typography>
                  <Select
                    value={expression.operands[1] === -1 ? '' : operands[expression.operands[1]]}
                    onChange={(e) => {
                      const updatedExpressionBuilder = [...expressionBuilder];
                      updatedExpressionBuilder[index].operands[1] = Number(e.target.value);
                      setExpressionBuilder(updatedExpressionBuilder);
                    }}
                    renderValue={(selected) => selected}
                  >
                    {operands.map((operand, index) => (
                      <MenuItem key={operand} value={index}>
                        <ListItemText primary={operand} />
                      </MenuItem>
                    ))}
                  </Select>
                </Box>
              </Box>
            </Box>
          );
        })}
      </Box>
      <Button variant="contained" onClick={handleAddExpressionBuilder}>
        Add Expression Builder
      </Button>
    </Box>
  );
};

export default MathExpressionBuilder;
