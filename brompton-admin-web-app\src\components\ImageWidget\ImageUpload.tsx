import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import DeleteIcon from '@mui/icons-material/Delete';
import { Box, Button, IconButton, Tooltip, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useCreateCustomerImagesMutation } from '~/redux/api/customersApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { ImageWidget } from '~/types/widgets';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

const ImagePreviewContainer = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: '10px',
});

const ImageUpload = ({
  onImageUpload,
  refetch,
  setSettings,
}: {
  onImageUpload: (imageUrl: string | null) => void;
  refetch: () => any;
  setSettings: (value: React.SetStateAction<ImageWidget>) => void;
}) => {
  const [preview, setPreview] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const activeCustomer = useSelector(getActiveCustomer);
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();

  const [
    createCustomerImage,
    { isError, error: uploadError, isLoading, isSuccess: success, data: imageUplaod },
  ] = useCreateCustomerImagesMutation();

  useEffect(() => {
    const handleUploadStatus = async () => {
      if (isLoading) {
        showSuccessAlert('Uploading image...');
      }

      if (success && imageUplaod) {
        await refetch(); // Await inside an async function
        setSettings((prev) => ({
          ...prev,
          image: imageUplaod?.id ? imageUplaod.id.toString() : prev.image,
          uploadedImage: null,
        }));
        showSuccessAlert('Image uploaded successfully!');
        setError(null);
        setPreview(null);
      }

      if (isError) {
        showErrorAlert('Failed to upload image.');
      }
    };

    handleUploadStatus(); // Call the async function
  }, [isLoading, isError, uploadError, success]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setError('Invalid file type. Please upload an image file.');
        setPreview(null);
        onImageUpload(null);
        return;
      }

      // Validate file size (max 2 MB)
      const maxSizeInBytes = 2 * 1024 * 1024; // 2MB
      if (file.size > maxSizeInBytes) {
        setError('File size exceeds 2 MB. Please upload a smaller image.');
        setPreview(null);
        onImageUpload(null);
        return;
      }

      setError(null); // Clear any previous errors

      // Read and preview the image
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        setPreview(imageUrl);
        onImageUpload(imageUrl); // Pass image URL to parent component
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = () => {
    setPreview(null);
    onImageUpload(null);
  };

  return (
    <>
      <AlertSnackbar {...snackbarState} />
      <Box mt={2} mb={2} display="flex" alignItems="center" gap={2}>
        {/* Upload Button */}
        <Button component="label" variant="contained" startIcon={<CloudUploadIcon />}>
          Upload Image
          <VisuallyHiddenInput type="file" accept="image/*" onChange={handleFileChange} />
        </Button>

        {/* Image Preview with Delete Button Outside */}
        {preview && (
          <ImagePreviewContainer>
            <img
              src={preview}
              alt="Uploaded preview"
              style={{ width: '80px', height: '80px', borderRadius: '5px' }}
            />
            <Tooltip title="Remove Image">
              <IconButton onClick={handleRemoveImage} size="small">
                <DeleteIcon fontSize="small" color="error" />
              </IconButton>
            </Tooltip>
          </ImagePreviewContainer>
        )}
        {preview && (
          <Button
            color="primary"
            variant="contained"
            disabled={isLoading}
            onClick={() => {
              createCustomerImage({
                id: activeCustomer?.id ?? 0,
                image: preview,
              });
            }}
          >
            Save
          </Button>
        )}
        {/* Error Message */}
        {error && (
          <Typography color="error" mt={1}>
            {error}
          </Typography>
        )}
      </Box>
    </>
  );
};

export default ImageUpload;
