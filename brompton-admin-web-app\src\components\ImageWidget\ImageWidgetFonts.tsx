import {
  Box,
  Card,
  Divider,
  FormControl,
  FormGroup,
  MenuItem,
  OutlinedInput,
  Select,
  SelectChangeEvent,
  TextField,
  Typography,
} from '@mui/material';
import React, { ReactNode } from 'react';
import { ImageWidgetFontsProps, ImageWidgetSettings } from '~/types/widgets';
import { fontWeights } from '~/utils/utils';

export const ImageWidgetFonts = ({
  settings,
  setSettings,
}: ImageWidgetFontsProps<ImageWidgetSettings>) => {
  const handleFontSize = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings((prevState) => ({
      ...prevState,
      font: {
        ...prevState.font,
        size: Number(event.target.value),
      },
    }));
  };
  const handleColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings((prevState) => ({
      ...prevState,
      font: {
        ...prevState.font,
        color: event.target.value,
      },
    }));
  };
  const handleFontWeight = (event: SelectChangeEvent<string>, child: ReactNode) => {
    setSettings((prevState) => ({
      ...prevState,
      font: {
        ...prevState.font,
        weight: event.target.value,
      },
    }));
  };
  return (
    <>
      <Box mb={2} component={Card} p={2}>
        <Typography variant="h5" mb={1.5}>
          Label Font Settings
        </Typography>
        <Divider />
        <FormGroup>
          <TextField
            name="fontSize"
            type="number"
            onChange={handleFontSize}
            defaultValue={12}
            value={settings.font?.size}
            label="Font Size"
            variant="outlined"
            margin="normal"
            fullWidth
          />
        </FormGroup>
        <FormGroup>
          <TextField
            name="color"
            type="color"
            label="Font Color"
            onChange={handleColorChange}
            value={settings.font?.color}
            variant="outlined"
            margin="normal"
            fullWidth
          />
        </FormGroup>
        <FormControl fullWidth>
          <Select
            labelId="y-select-lable"
            id="tabel-series"
            fullWidth
            defaultValue="bolder"
            value={settings.font?.weight}
            onChange={handleFontWeight}
            label={'Font Weight'}
            input={
              <OutlinedInput
                label="Font Weight"
                sx={{
                  p: 0.5,
                  '& legend': {
                    maxWidth: '100%',
                    height: 'fit-content',
                    '& span': {
                      opacity: 1,
                    },
                  },
                }}
              />
            }
          >
            {fontWeights.map((fonts: string) => {
              return (
                <MenuItem key={fonts} value={fonts}>
                  {fonts}
                </MenuItem>
              );
            })}
          </Select>
        </FormControl>
      </Box>
    </>
  );
};
