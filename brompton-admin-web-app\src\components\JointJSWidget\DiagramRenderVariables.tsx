import { dia } from '@joint/core';
import { Card } from '@material-ui/core';
import { Box } from '@mui/material';
import { MutableRefObject, SetStateAction, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { getEndDate, getStartDate } from '~/redux/selectors/chartSelectors';
import { TimeRangeOptions } from '~/types/dashboard';
import { elementVariable } from '~/types/diagram';
import { DiagramWidget } from '~/types/widgets';
import { getPreviousDate } from '~/utils/utils';
import DiagramMeasureSelect from './DiagramMeasureSelect';
import DiagramMetricSelect from './DiagramMetricSelect';

type DiagramRenderVariablesProps = {
  variables: elementVariable[];
  cellId: dia.Cell.ID;
  graphRef: MutableRefObject<dia.Graph<dia.Graph.Attributes, dia.ModelSetOptions> | null>;
  handleSettingsChange: (value: SetStateAction<DiagramWidget>) => void;
  setGrpahVersion: (value: SetStateAction<number>) => void;
  settings: DiagramWidget;
  graphVersion: number;
};
const DiagramRenderVariables = ({
  cellId,
  graphRef,
  variables,
  handleSettingsChange,
  setGrpahVersion,
  settings,
  graphVersion,
}: DiagramRenderVariablesProps) => {
  const globalStartDate = useSelector(getStartDate);
  const globalendDate = useSelector(getEndDate);
  const [error, setError] = useState<
    Record<number, { showWarningMessage: boolean; showNoneError: boolean }>
  >({});

  const handleVariablesChange = (updatedVariable: elementVariable, variableIndex: number) => {
    const cell = graphRef.current!.getCell(cellId);
    if (!cell) return;
    const currentVariables = (cell.get('data')?.variables ?? []) as elementVariable[];
    currentVariables[variableIndex] = updatedVariable;
    cell.prop('data/variables', currentVariables);

    const updatedSettingsElementIdVariables = { ...settings.elementIdVariabels };
    let cellVariables = [...(updatedSettingsElementIdVariables[cellId.toString()] ?? [])];

    cellVariables[variableIndex] = {
      ...cellVariables[variableIndex],
      ...updatedVariable,
    };

    const timeRange = 8;
    // 🔧 Properly update each variable with defaults and computed values
    cellVariables = cellVariables.map((v) => {
      const tr = v?.timeRange === undefined ? timeRange : v.timeRange;
      if (tr !== 0) {
        return {
          ...v,
          aggBy: v?.aggBy,
          samplePeriod: v?.samplePeriod ?? 1,
          timeRange: tr,
          startDate: getPreviousDate(TimeRangeOptions[tr].serverValue),
          endDate: Date.now(),
          isRelativeToGlboalEndTime: v?.isRelativeToGlboalEndTime ?? false,
          globalSamplePeriod: v?.globalSamplePeriod ?? false,
        };
      }
      return {
        ...v,
        aggBy: v?.aggBy,
        samplePeriod: v?.samplePeriod ?? 1,
        timeRange: tr,
        isRelativeToGlboalEndTime: v?.isRelativeToGlboalEndTime ?? false,
        globalSamplePeriod: v?.globalSamplePeriod ?? false,
      };
    });

    updatedSettingsElementIdVariables[cellId.toString()] = cellVariables;

    // 🔄 Sync updated variables with all relevant cells
    graphRef.current!.getCells().forEach((graphCell) => {
      const graphCellData = graphCell.get('data');
      const variablesForCell = updatedSettingsElementIdVariables[graphCell.id.toString()];
      if (variablesForCell) {
        graphCell.set('data', {
          ...graphCellData,
          variables: variablesForCell,
        });
      }
    });

    // 💾 Save updated settings + JSON graph
    handleSettingsChange((prevSettings) => ({
      ...prevSettings,
      elementIdVariabels: updatedSettingsElementIdVariables,
      jsonFile: JSON.stringify({ graph: graphRef.current!.toJSON() }, null),
    }));

    setGrpahVersion((prev) => prev + 1);
  };
  useEffect(() => {
    let showNoneError = false;
    let isInvalid = false;
    let showWarningMessage = false;
    const variableErrors: Record<number, { showWarningMessage: boolean; showNoneError: boolean }> =
      {};
    if (settings.isRealTime === false) {
      variables.forEach((variable, index) => {
        if (variable?.aggBy === 5 && settings.globalSamplePeriod && settings.samplePeriod === 14) {
          isInvalid = true;
          showWarningMessage = true;
          variableErrors[index] = {
            showWarningMessage,
            showNoneError: false,
          };
          return;
        }
        if (variable?.aggBy === 0) {
          const start = settings.overrideGlobalSettings ? settings.startDate : globalStartDate;
          const end = settings.overrideGlobalSettings ? settings.endDate : globalendDate;
          const timeDifferenceHours = (end - start) / (1000 * 60 * 60);

          if (timeDifferenceHours > 6) {
            isInvalid = true;
            showNoneError = true;
            variableErrors[index] = {
              showWarningMessage: false,
              showNoneError,
            };
            return;
          }
        }
        if (!variableErrors[index]) {
          variableErrors[index] = {
            showWarningMessage: false,
            showNoneError: false,
          };
        }
      });
      setError(variableErrors);
    }
  }, [
    globalStartDate,
    globalendDate,
    settings.overrideGlobalSettings,
    variables,
    settings.timeRange,
    settings.elementIdVariabels,
    settings.overrideAssetTzValue,
    graphVersion,
    settings.jsonFile,
    settings.startDate,
    settings.endDate,
    cellId,
    settings.globalSamplePeriod,
    settings.samplePeriod,
    settings.isRelativeToGlboalEndTime,
    settings.elementVariable,
    settings.isRealTime,
  ]);
  return variables.length > 0 ? (
    <>
      {variables.map((variable, index) => (
        <Box
          key={index}
          sx={{ display: 'flex', flexDirection: 'column', gap: 0, mt: 0.5, p: 0.5 }}
          component={Card}
        >
          {settings.mode === 'dashboard' && (
            <DiagramMeasureSelect
              key={index}
              variables={variable}
              showNoneWarning={error[index]?.showNoneError}
              showWarningMessage={error[index]?.showWarningMessage}
              onVariablesChange={(updatedVariable) => handleVariablesChange(updatedVariable, index)}
              settings={settings}
            />
          )}
          {settings.mode === 'template' && (
            <DiagramMetricSelect
              key={index}
              variables={variable}
              onVariablesChange={(updatedVariable) => handleVariablesChange(updatedVariable, index)}
            />
          )}
        </Box>
      ))}
    </>
  ) : null;
};
export default DiagramRenderVariables;
