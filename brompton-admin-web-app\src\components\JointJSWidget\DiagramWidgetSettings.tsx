import { dia } from '@joint/core';
import {
  Autocomplete,
  Box,
  Card,
  Checkbox,
  FormControlLabel,
  Tab,
  Tabs,
  TextField,
} from '@mui/material';
import { MutableRefObject, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import useDiagramWidgetSettingsHelper from '~/hooks/useDiagramWidgetSettingsHelper';
import { getEndDate, getStartDate } from '~/redux/selectors/chartSelectors';
import { diagram } from '~/types/diagram';
import { DiagramWidget } from '~/types/widgets';
import WidgetTimeContext from '../common/DataWidgetSettingsContainer/WidgetTimeContext';
import Loader from '../common/Loader';
import DiagramRenderVariables from './DiagramRenderVariables';

export type DiagramWidgetSettingsProps = {
  settings: DiagramWidget;
  graphRef: MutableRefObject<dia.Graph<dia.Graph.Attributes, dia.ModelSetOptions> | null>;
  handleSettingsChange: React.Dispatch<React.SetStateAction<DiagramWidget>>;
  isFetching: boolean;
  diagramData: diagram | undefined;
};

const DiagramWidgetSettings = ({
  diagramData: data,
  graphRef,
  isFetching,
  settings,
  handleSettingsChange,
}: DiagramWidgetSettingsProps) => {
  const globalStartDate = useSelector(getStartDate);
  const globalendDate = useSelector(getEndDate);
  const [graphVersion, setGrpahVersion] = useState(0);
  const [showTimeRangeError, setShowTimeRangeError] = useState<boolean>(false);
  const [tabIndex, setTabIndex] = useState<number>(0);
  const {
    isLoadingDashboards,
    fetchingDiagrams,
    dashboardOptions,
    diagramOptions,
    fetchNewData,
    handleChangeDashboard,
    handleChangeDiagramDropdown,
  } = useDiagramWidgetSettingsHelper({
    settings,
    graphRef,
    handleSettingsChange,
    graphVersion,
    setGrpahVersion,
  });
  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
  };

  useEffect(() => {
    // 1. Custom time range validation
    const isCustomRange = settings.timeRange === 0;
    const hasInvalidTimeRange =
      isCustomRange &&
      settings.startDate &&
      settings.endDate &&
      settings.startDate >= settings.endDate;

    // 2. Variable validation across all cells
    let isAnyInvalidVariable = false;
    if (settings.isRealTime === false) {
      Object.entries(settings.elementIdVariabels ?? {}).forEach(([_, variables]) => {
        for (const variable of variables) {
          const isWarnInvalid =
            variable?.aggBy === 5 && settings.globalSamplePeriod && settings.samplePeriod === 14;
          // variable?.samplePeriod === 14
          //  && variable?.globalSamplePeriod;

          const isNoneInvalid = (() => {
            if (variable?.aggBy !== 0 || variable?.aggBy === undefined) return false;

            const start = settings.overrideGlobalSettings ? settings.startDate : globalStartDate;
            const end = settings.overrideGlobalSettings ? settings.endDate : globalendDate;
            const timeDifferenceHours = (end - start) / (1000 * 60 * 60);
            return timeDifferenceHours > 6;
          })();

          if (isWarnInvalid || isNoneInvalid) {
            isAnyInvalidVariable = true;
            break;
          }
        }
      });

      // 5. Update settings validity
    }
    // 3. Final validation result
    const finalIsValid = !(hasInvalidTimeRange || isAnyInvalidVariable);

    // 4. Show UI error for invalid time range only (optional)
    setShowTimeRangeError(!!hasInvalidTimeRange);
    handleSettingsChange((prev) => ({
      ...prev,
      isValid: finalIsValid,
    }));
  }, [
    settings.timeRange,
    settings.startDate,
    settings.endDate,
    settings.overrideGlobalSettings,
    settings.overrideAssetTz,
    settings.overrideAssetTzValue,
    settings.isRelativeToGlboalEndTime,
    settings.elementIdVariabels,
    globalStartDate,
    globalendDate,
    settings.globalSamplePeriod,
    settings.samplePeriod,
    settings.isRealTime,
  ]);

  const renderActiveTab = () => {
    switch (tabIndex) {
      case 0:
        return (
          <Box sx={{ mt: 2 }}>
            <Autocomplete
              id="diagrams-combo-box"
              options={diagramOptions}
              value={
                settings.selectedDiagram
                  ? {
                      id: settings.selectedDiagram.id,
                      label: settings.selectedDiagram.name,
                    }
                  : null
              }
              loading={fetchingDiagrams}
              sx={{ width: '100%', p: 2 }}
              onChange={(_, value) => handleChangeDiagramDropdown(value)}
              renderInput={(params) => <TextField {...params} label="Diagram" />}
            />
            {isFetching || fetchNewData ? (
              <Loader />
            ) : (
              <>
                {Object.keys(settings.elementIdVariabels ?? {}).map((cellId) => {
                  const cell = graphRef.current!.getCell(cellId); // Get the cell from the graph using the id
                  if (!cell) return null; // In case the cell is not found in the graph
                  const variables = settings.elementIdVariabels[cellId] || [];
                  return variables.length === 0 ? null : (
                    <Box mt={2} mb={2} key={cellId}>
                      <DiagramRenderVariables
                        cellId={cellId}
                        graphRef={graphRef}
                        graphVersion={graphVersion}
                        handleSettingsChange={handleSettingsChange}
                        setGrpahVersion={setGrpahVersion}
                        settings={settings}
                        variables={variables}
                        key={cellId}
                      />
                    </Box>
                  );
                })}
              </>
            )}
          </Box>
        );
      case 1:
        return (
          <Box component={Card}>
            <FormControlLabel
              control={
                <Checkbox
                  sx={{ p: 2 }}
                  checked={settings.disableZoom}
                  onChange={() => {
                    handleSettingsChange((prevState) => ({
                      ...prevState,
                      disableZoom: !prevState.disableZoom,
                    }));
                  }}
                  name="disableZoom"
                />
              }
              label="Disable Zoom"
            />
          </Box>
        );
      case 2:
        return (
          <WidgetTimeContext
            settings={settings}
            setSettings={handleSettingsChange}
            showTimeRangeError={showTimeRangeError}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Tabs value={tabIndex} onChange={handleChange} aria-label="settings tabs" variant="fullWidth">
        <Tab label="Data" />
        <Tab label="Look & Feel" />
        <Tab label="Time Context" />
      </Tabs>

      {renderActiveTab()}
    </Box>
  );
};

export default DiagramWidgetSettings;
