import { dia, shapes as DiagramShapes } from '@joint/core';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import DeleteIcon from '@mui/icons-material/Delete';
import SplitIcon from '@mui/icons-material/Splitscreen';
import { Box, Button, Grid, Paper, Tooltip, Typography } from '@mui/material';
import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useCreateElementHook } from '~/hooks/useCreateElementHook';
import { getIconPosition, getIconVisible } from '~/redux/selectors/diagramSelector';
import { diagramSlice } from '~/redux/slices/diagramSlice';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { CATEGORY_E, generalShapesArray } from '../Palette/Palette';
import CreateElementCommonSettings from './CreateElementCommonSettings';

type CreateElementProps = {
  graph: dia.Graph<dia.Graph.Attributes, dia.ModelSetOptions>;
};

const CreateElement = ({ graph }: CreateElementProps) => {
  const dispatch = useDispatch();
  const resizeDotsRef = useRef<HTMLDivElement[]>([]);
  const rotationDotsRef = useRef<HTMLDivElement[]>([]);
  const hideIconTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const paperRef = useRef<HTMLDivElement | null>(null);
  const iconVisible = useSelector(getIconVisible);
  const iconPosition = useSelector(getIconPosition);
  const {
    elementsVariables,
    addNewVariableToElement,
    currentSelectedElement,
    deleteSelectedElements,
    handleDeleteVariable,
    handleVariableChange,
    onDragOver,
    onDragStart,
    onDrop,
    selectedElements,
    setCurrentSelectedElement,
    setSelectedElements,
    snackbarState,
    ungroupSelectedElement,
    setCurrentBattery,
    batteryModalOpen,
    setBatteryModalOpen,
    currentBattery,
  } = useCreateElementHook({ graph, hideIconTimeoutRef, resizeDotsRef, rotationDotsRef, paperRef });

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Delete' && currentSelectedElement) {
        deleteSelectedElements();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentSelectedElement, deleteSelectedElements]);
  return (
    <Box>
      <AlertSnackbar {...snackbarState} />
      <Grid container spacing={2}>
        <Grid item xs={12} sm={3}>
          <Box component={Paper} height={'100%'} p={2}>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 2 }}>
              {currentSelectedElement && currentSelectedElement.get('type') === 'groupElement' && (
                <Tooltip title="Ungroup Selected Element" placement="top">
                  <Button variant="outlined" color="primary" onClick={ungroupSelectedElement}>
                    <SplitIcon />
                  </Button>
                </Tooltip>
              )}

              {currentSelectedElement ? (
                <>
                  <Tooltip title="Delete Selected Elements" placement="top">
                    <Button variant="outlined" color="error" onClick={deleteSelectedElements}>
                      <DeleteIcon />
                    </Button>
                  </Tooltip>

                  <Tooltip title="Bring to Front" placement="top">
                    <Button
                      variant="outlined"
                      color="primary"
                      onClick={() => currentSelectedElement.toFront()}
                    >
                      <ArrowUpwardIcon />
                    </Button>
                  </Tooltip>

                  <Tooltip title="Send to Back" placement="top">
                    <Button
                      variant="outlined"
                      color="primary"
                      onClick={() => currentSelectedElement.toBack()}
                    >
                      <ArrowDownwardIcon />
                    </Button>
                  </Tooltip>
                </>
              ) : null}
            </Box>
            <Box>
              <Typography variant="subtitle1" color="textSecondary">
                Basic Elements
              </Typography>
              <Grid container columnSpacing={1}>
                {generalShapesArray
                  .filter((shape) => shape.category === CATEGORY_E.BASIC)
                  .map((element: any) => (
                    <Grid item xs={12} md={6} key={element.type}>
                      <Paper
                        elevation={1}
                        className="shape"
                        draggable
                        onDragStart={(e) => onDragStart(e, element.type)}
                        sx={{
                          marginBottom: '10px',
                          textAlign: 'center',
                          cursor: 'grab',
                          border: '1px solid #010101',
                        }}
                      >
                        <Typography variant="caption">{element.name}</Typography>
                      </Paper>
                    </Grid>
                  ))}
              </Grid>
              <Typography variant="subtitle1" color="textSecondary">
                Domain Elements
              </Typography>
              <Grid container columnSpacing={1}>
                {generalShapesArray
                  .filter((shape) => shape.category === CATEGORY_E.DOMAIN)
                  .map((element) => (
                    <Grid item xs={12} md={6} key={element.type}>
                      <Paper
                        elevation={1}
                        className="shape"
                        draggable
                        onDragStart={(e) => onDragStart(e, element.type)}
                        sx={{
                          marginBottom: '10px',
                          textAlign: 'center',
                          cursor: 'grab',
                          border: '1px solid #010101',
                        }}
                      >
                        <Typography variant="caption">{element.name}</Typography>
                      </Paper>
                    </Grid>
                  ))}
              </Grid>
              <Typography variant="subtitle1" color="textSecondary">
                Dynamic Elements
              </Typography>
              <Grid container columnSpacing={1}>
                {generalShapesArray
                  .filter((shape) => shape.category === CATEGORY_E.DYNAMIC)
                  .map((element) => (
                    <Grid item xs={12} md={6} key={element.type}>
                      <Paper
                        elevation={1}
                        className="shape"
                        draggable
                        onDragStart={(e) => onDragStart(e, element.type)}
                        sx={{
                          marginBottom: '10px',
                          textAlign: 'center',
                          cursor: 'grab',
                          border: '1px solid #010101',
                        }}
                      >
                        <Typography variant="caption">{element.name}</Typography>
                      </Paper>
                    </Grid>
                  ))}
              </Grid>
            </Box>
          </Box>
        </Grid>
        <Grid item xs={12} sm={graph.getCells().length > 0 ? 6 : 9}>
          <Paper elevation={3}>
            <Box
              ref={paperRef}
              onDrop={onDrop}
              onDragOver={onDragOver}
              style={{ width: '100%', height: '100%' }}
            >
              {currentSelectedElement &&
                currentSelectedElement.get('position') &&
                currentSelectedElement.get('size') &&
                iconVisible && (
                  <Box
                    display="flex"
                    flexDirection="row"
                    style={{
                      position: 'absolute',
                      top: iconPosition.top - 30, // Adjust the position to prevent overlap
                      left: iconPosition.left + 10, // Optional: adjust horizontally if needed
                      zIndex: 1001, // Higher z-index for modal icon
                    }}
                    onMouseEnter={() => {
                      clearTimeout(hideIconTimeoutRef.current!);
                      resizeDotsRef.current.forEach((dot) => (dot.style.zIndex = '1000')); // Lower z-index on hover
                    }}
                    onMouseLeave={() => {
                      dispatch(diagramSlice.actions.setIconVisible(false));
                      resizeDotsRef.current.forEach((dot) => (dot.style.zIndex = '999')); // Restore original z-index
                    }}
                  />
                )}
            </Box>
          </Paper>
        </Grid>
        {graph.getCells().length > 0 ? (
          <Grid item xs={12} sm={3}>
            <Box component={Paper} sx={{ height: '100%', p: 2 }}>
              {currentSelectedElement &&
              !(currentSelectedElement instanceof DiagramShapes.standard.TextBlock) ? (
                <>
                  <CreateElementCommonSettings
                    selectedElement={currentSelectedElement}
                    graph={graph}
                  />

                  {/* <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="h6">Variables</Typography>
                    <Tooltip title="Add a new variable to the selected element" placement="bottom">
                      <IconButton
                        color="primary"
                        sx={{
                          backgroundColor: 'primary.main',
                          color: 'white',
                          '&:hover': {
                            backgroundColor: 'primary.dark',
                          },
                        }}
                        onClick={addNewVariableToElement}
                        aria-label="add variable"
                      >
                        <AddIcon />
                      </IconButton>
                    </Tooltip>
                  </Box> */}
                  {/* {!elementsVariables[currentSelectedElement.id] ||
                  elementsVariables[currentSelectedElement.id]?.length === 0 ? (
                    <Box
                      sx={{
                        display: 'flex',
                        height: '100%',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                    >
                      <Typography>Press `+` icon to add new variables</Typography>
                    </Box>
                  ) : null} */}
                  {/* {elementsVariables[currentSelectedElement.id]?.map((variable, index) => (
                    <Box key={index} sx={{ display: 'flex', gap: 2, mt: 1, alignItems: 'center' }}>
                      <TextField
                        label="Variable"
                        value={variable.variable} // Updated from variable.value
                        onChange={(e) =>
                          handleVariableChange(
                            currentSelectedElement,
                            index,
                            'variable', // Updated key
                            e.target.value,
                          )
                        }
                        fullWidth
                      />
                      <TextField
                        label="Label"
                        value={variable.label} // Updated from variable.name
                        onChange={(e) =>
                          handleVariableChange(
                            currentSelectedElement,
                            index,
                            'label', // Updated key
                            e.target.value,
                          )
                        }
                        fullWidth
                      />
                      <Tooltip title="Delete variable" placement="bottom">
                        <IconButton
                          sx={{
                            backgroundColor: 'error.main',
                            color: 'white',
                            '&:hover': {
                              backgroundColor: 'error.main',
                            },
                          }}
                          color="error"
                          onClick={() => handleDeleteVariable(currentSelectedElement, index)}
                          aria-label="delete variable"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  ))} */}
                </>
              ) : (
                <>
                  {graph.getCells().length > 0 ? (
                    <Typography variant="h6">Select an element to add variables</Typography>
                  ) : null}
                </>
              )}
            </Box>
          </Grid>
        ) : null}
      </Grid>
    </Box>
  );
};

export default CreateElement;
