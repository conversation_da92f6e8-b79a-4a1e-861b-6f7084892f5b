import { dia, util } from '@joint/core';

export class ControlValve extends dia.Element {
  valLevel = 0;
  colors: { low: string; medium: string; high: string; full: string };
  constructor(
    attributes = {},
    options = {},
    level = 0,
    color = { low: '#ffa500', medium: '#ffff00', high: '#008000', full: '#ff0000' },
  ) {
    super(attributes, options);
    this.valLevel = level;
    this.colors = {
      ...color,
    };
  }
  flowRatio = 50; // Default flow ratio value

  defaults(): Partial<dia.Element.Attributes> {
    return {
      ...super.defaults,
      type: 'ControlValve',
      size: {
        width: 60,
        height: 60,
      },
      open: 1,
      attrs: {
        root: {
          magnetSelector: 'body',
        },
        body: {
          cx: 'calc(w / 2)', // ✅ Center dynamically
          cy: 'calc(h / 2)',
          rx: 'calc(w / 3)', // ✅ Dynamic radius
          ry: 'calc(h / 3)',
          stroke: 'gray',
          strokeWidth: 2,
          fill: {
            type: 'radialGradient',
            stops: [
              { offset: '80%', color: 'white' },
              { offset: '100%', color: 'gray' },
            ],
          },
        },
        stem: {
          width: 'calc(w / 6)',
          height: 'calc(h / 3)',
          x: 'calc(w / 2 - calc(w / 11))',
          y: 'calc(h / 39)',
          stroke: '#333',
          strokeWidth: 2,
          fill: '#555',
        },
        control: {
          d: 'M 0 0 C 0 calc(-0.25*h) calc(0.5*w) calc(-0.25*h) calc(0.5*w) 0 Z',
          transform: 'translate(calc(w / 2 - calc(0.25*w)), calc(h / 30))',
          stroke: '#333',
          strokeWidth: 2,
          rx: 5,
          ry: 5,
          fill: '#666',
        },
        label: {
          textAnchor: 'middle',
          textVerticalAnchor: 'top',
          x: 'calc(0.5*w)',
          y: 'calc(h+10)',
          fontSize: 14,
          fontFamily: 'sans-serif',
          fill: '#350100',
        },
      },
    };
  }

  get level() {
    return this.get('level') || 0;
  }

  set level(level) {
    const newLevel = Math.max(0, Math.min(100, level));
    this.set('level', newLevel);
    // Update border color based on the level
    // Update background color based on the level
    let backgroundColor;
    if (newLevel > 20 && newLevel <= 40) {
      backgroundColor = this.colors.medium;
    } else if (newLevel > 40 && newLevel <= 80) {
      backgroundColor = this.colors.high;
    } else if (newLevel > 80) {
      backgroundColor = this.colors.full;
    }

    // Update the fill attribute dynamically
    this.attr('body/fill', backgroundColor);
  }
  preinitialize() {
    this.markup = util.svg/* xml */ `
        <rect @selector="stem" />
        <path @selector="control" />
        <ellipse @selector="body" />
        <rect @selector="coverFrame" />
        <rect @selector="cover" />
        <text @selector="label" />
      `;
  }

  // Method to set the flow ratio and update the visual representation
  setFlowRatio(flowRatio: number) {
    // Ensure flowRatio is between 0 and 100 for safety
    if (flowRatio < 0) {
      flowRatio = 0;
    } else if (flowRatio > 50) {
      flowRatio = 50;
    }

    // Set the flowRatio value
    this.flowRatio = flowRatio;
  }
}
