import { dia } from '@joint/core';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { Grid, IconButton, Paper } from '@mui/material';
import Box from '@mui/material/Box';
import React, { useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useDiagramComponentHook } from '~/hooks/useDiagramComponentHook';
import { getCurrentGraph, getSelectedElements } from '~/redux/selectors/diagramSelector';
import { diagramSlice } from '~/redux/slices/diagramSlice';
import DiagramCommonButtons from './DiagramCommonButtons';
import CommonElementPropEditDialog from './DiagramComponentDialoge/CommonElementPropEditDialog';
import ImageUploadModal from './DiagramComponentDialoge/ImageUploadModal';
import LinkConfigModal from './DiagramComponentDialoge/LinkConfigModal';

const DiagramComponent: React.FC = () => {
  const dispatch = useDispatch();
  const selectedElementsList = useSelector(getSelectedElements);
  const graphState = useSelector(getCurrentGraph);
  const graphRef = useRef<dia.Graph | null>(graphState);
  const paperRef = useRef<HTMLDivElement | null>(null);
  const rotateIconRef = useRef<HTMLButtonElement | null>(null);
  const deleteIconPositionRef = useRef({ x: 0, y: 0 });
  const resizeDotsRef = useRef<HTMLDivElement[]>([]);
  const hideIconTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Store the timeout ID for hiding the icon

  const {
    onDrop,
    onDragOver,
    selectedElement,
    handleOpenEditDialog,
    elementName,
    handleAttrChangeElement,
    elementAttrs,
    elementType,
    base64Image,
    handleUpdateElement,
    editLinkDialogOpen,
    linkAttrs,
    deleteSelectedElement,
    removeAllLinks,
    imageUploaded,
    opacity,
    handleImageUploadInternal,
    handleOpacityChange,
    iconVisible,
    iconPosition,
    setIconVisible,
    handleRemoveImage,
    imageUploadModalOpen,
    applyImageToElement,
    resetZoom,
    zoomIn,
    zoomOut,
    setFieldErrors,
    fieldErrors,
    setEditedLabels,
    editedLabels,
    handleOpenLinkEditDialog,
    errors,
    handlePredefinedImageSelect,
    removeAllVertexTools,
    handleAddPort,
    selectedPortId,
    visiblePorts,
    x,
    y,
    handleEditPort,
    handleDeletePort,
    handlePortSliderChange,
  } = useDiagramComponentHook({
    paperRef,
    rotateIconRef,
    deleteIconPositionRef,
    resizeDotsRef,
    hideIconTimeoutRef,
    graphRef,
  });

  return (
    <Box sx={{ width: '100%' }}>
      <Grid container>
        <Grid item xs={selectedElement && selectedElementsList.length === 0 ? 9 : 12}>
          <Paper elevation={3} sx={{ boxShadow: 'unset' }}>
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              sx={{
                width: '100%',
                height: '100vh',
              }}
            >
              <Box
                ref={paperRef}
                style={{ width: '100%', height: '100%' }}
                onDrop={onDrop}
                onDragOver={onDragOver}
              >
                {selectedElement &&
                  selectedElement.get('position') &&
                  selectedElement.get('size') &&
                  iconVisible && (
                    <Box
                      display="flex"
                      flexDirection="row"
                      style={{
                        position: 'absolute',
                        top: iconPosition.top - 30, // Adjust the position to prevent overlap
                        left: iconPosition.left + 10, // Optional: adjust horizontally if needed
                        zIndex: 1001, // Higher z-index for modal icon
                      }}
                      onMouseEnter={() => {
                        clearTimeout(hideIconTimeoutRef.current!);
                        resizeDotsRef.current.forEach((dot) => (dot.style.zIndex = '1000')); // Lower z-index on hover
                      }}
                      onMouseLeave={() => {
                        setIconVisible(false);
                        resizeDotsRef.current.forEach((dot) => (dot.style.zIndex = '999')); // Restore original z-index
                      }}
                    >
                      {selectedElement?.get('type') !== 'standard.Image' && (
                        <IconButton onClick={() => handleOpenEditDialog(selectedElement!)}>
                          <MoreVertIcon />
                        </IconButton>
                      )}
                    </Box>
                  )}
              </Box>

              <LinkConfigModal
                open={editLinkDialogOpen}
                close={() => dispatch(diagramSlice.actions.setEditLinkDialogOpen(false))}
              />

              {/* <CommonLinkPropEditDialog
                editLinkDialogOpen={editLinkDialogOpen}
                linkAttrs={linkAttrs}
                handleAttrChange={handleAttrChange}
                handleUpdateLink={handleUpdateLink}
                errors={errors}
              /> */}

              <ImageUploadModal
                open={imageUploadModalOpen}
                onClose={() => dispatch(diagramSlice.actions.setImageUploadModalOpen(false))}
                onApply={applyImageToElement}
              />

              <DiagramCommonButtons
                graphRef={graphRef}
                deleteSelectedElement={deleteSelectedElement}
                removeAllLinks={removeAllLinks}
                selectedElement={selectedElement}
                zoomIn={zoomIn}
                zoomOut={zoomOut}
                removeAllVertexTools={removeAllVertexTools}
              />
            </Box>
          </Paper>
        </Grid>
        {selectedElement && selectedElementsList.length === 0 ? (
          <Grid item xs={3} sx={{ maxHeight: '100vh', overflow: 'auto' }}>
            <Box sx={{ maxHeight: '100vh', overflow: 'auto' }}>
              <CommonElementPropEditDialog
                graphRef={graphRef}
                selectedElement={selectedElement}
                elementName={elementName}
                handleAttrChangeElement={handleAttrChangeElement}
                elementAttrs={elementAttrs}
                elementType={elementType}
                base64Image={base64Image}
                handleUpdateElement={handleUpdateElement}
                imageUploaded={imageUploaded}
                opacity={opacity}
                handleImageUploadInternal={handleImageUploadInternal}
                handleOpacityChange={handleOpacityChange}
                handleRemoveImage={handleRemoveImage}
                setFieldErrors={setFieldErrors}
                fieldErrors={fieldErrors}
                editedLabels={editedLabels}
                setEditedLabels={setEditedLabels}
                handlePredefinedImageSelect={handlePredefinedImageSelect}
                handleAddPort={handleAddPort}
                selectedPortId={selectedPortId}
                visiblePorts={visiblePorts}
                portX={x}
                portY={y}
                handleEditPort={handleEditPort}
                handleDeletePort={handleDeletePort}
                handlePortSliderChange={handlePortSliderChange}
              />
            </Box>
          </Grid>
        ) : null}
      </Grid>
    </Box>
  );
};

export default DiagramComponent;
