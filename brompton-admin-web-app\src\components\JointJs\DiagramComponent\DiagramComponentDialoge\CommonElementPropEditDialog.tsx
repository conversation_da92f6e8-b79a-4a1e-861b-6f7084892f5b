import { dia, shapes } from '@joint/core';
import CancelIcon from '@mui/icons-material/Cancel';
import DeleteIcon from '@mui/icons-material/Delete';
import RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline';
import {
  Alert,
  Box,
  Button,
  Divider,
  IconButton,
  Paper,
  Slider,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import React, { FC, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Battery from '~/components/CreateElement/Battery';
import Progress from '~/components/CreateElement/Progress';
import { images } from '~/components/ImageWidget/ImageWidgetDialog';
import { getElementsVariables } from '~/redux/selectors/diagramSelector';
import { diagramSlice } from '~/redux/slices/diagramSlice';
import { formatDiagramElementName } from '~/utils/utils';
import { CATEGORY_E } from '../../Palette/Palette';
import { ConicTank } from '../ConicalTank';
import LiquidTank from '../LiquidTank';
import Pump from '../Pump';
import BatterySettings from './BatterySettings';
import CommonSettings from './CommonSettings';
import ConicalTankSettings from './ConicalTankSettings';
import LabelSettings from './LabelSettings';
import LiquidTankSettings from './LiquidTankSettings';
import ProgressSettings from './ProgressSettings';
import { elementSettings } from '~/types/diagram';
import EditIcon from '@mui/icons-material/Edit';

interface IElementAttrs {
  name: string;
  color: string;
  borderColor: string;
  borderWidth: string;
  borderStyle: 'solid' | 'dotted' | 'dashed';
}

interface ICommonElementPropEditDialog {
  selectedElement: dia.Element<dia.Element.Attributes, dia.ModelSetOptions> | null;
  elementName: string;
  handleAttrChangeElement: (attr: string, value: string) => void;
  elementAttrs: IElementAttrs;
  elementType: string;
  base64Image: string | null;
  handleUpdateElement: (opacity: number) => void;
  imageUploaded: boolean;
  opacity: number;
  handleImageUploadInternal: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleOpacityChange: (event: Event, newValue: number | number[]) => void;
  handleRemoveImage: () => void;
  fieldErrors: {
    [key: number]: string | null;
  };
  setFieldErrors: React.Dispatch<
    React.SetStateAction<{
      [key: number]: string | null;
    }>
  >;
  graphRef: React.MutableRefObject<dia.Graph<dia.Graph.Attributes, dia.ModelSetOptions> | null>;
  setEditedLabels: React.Dispatch<React.SetStateAction<string[]>>;
  editedLabels: string[];
  handlePredefinedImageSelect: (src: string, elementID: string) => void;
  handleAddPort: () => void;
  selectedPortId: string | null;
  visiblePorts: dia.Element.Port[];
  portX: number;
  portY: number;
  handleEditPort: (portId: string) => void;
  handleDeletePort: (portId: string) => void;
  handlePortSliderChange: (axis: 'x' | 'y', value: number) => void;
}

const CommonElementPropEditDialog: FC<ICommonElementPropEditDialog> = ({
  selectedElement,
  elementName,
  handleAttrChangeElement,
  elementAttrs,
  elementType,
  base64Image,
  handleUpdateElement,
  imageUploaded,
  opacity,
  handleImageUploadInternal,
  handleOpacityChange,
  handleRemoveImage,
  fieldErrors,
  graphRef,
  setFieldErrors,
  setEditedLabels,
  editedLabels,
  handlePredefinedImageSelect,
  handleAddPort,
  selectedPortId,
  visiblePorts,
  portX: x,
  portY: y,
  handleEditPort,
  handleDeletePort,
  handlePortSliderChange,
}) => {
  const dispatch = useDispatch();
  const { setElementsVariables, setSelectedElement } = diagramSlice.actions;
  const elementsVariables = useSelector(getElementsVariables);

  // State for managing opacity and image
  const [currentOpacity, setCurrentOpacity] = useState(1);
  const [currentBase64Image, setCurrentBase64Image] = useState<string | null>(base64Image);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Update state when a new element is selected
  useEffect(() => {
    if (selectedElement && selectedElement instanceof shapes.standard.BorderedImage) {
      const existingOpacity = selectedElement.attr('image/opacity') ?? 1;
      setCurrentOpacity(existingOpacity);
      const existingImage = selectedElement.attr('image/xlinkHref') ?? null;
      setCurrentBase64Image(existingImage);
    }

    if (selectedElement) {
      const borderColor = selectedElement.attr('body/stroke') ?? '#000000';
      const borderWidth = selectedElement.attr('body/strokeWidth') ?? '0';

      handleAttrChangeElement('borderColor', borderColor);
      handleAttrChangeElement('borderWidth', borderWidth.toString());
    }
  }, [selectedElement]);

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && selectedElement) {
      const reader = new FileReader();
      reader.onloadend = () => {
        if (reader.result) {
          const resultString = reader.result.toString();
          selectedElement.attr('image/xlinkHref', resultString); // Set the image to the selected element
          selectedElement.attr('label/text', 'Image');
          setCurrentBase64Image(resultString); // Update the state to hold the image data
          handleUpdateElement(currentOpacity); // Update the element with any other changes if needed
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSliderChange = (event: Event, newValue: number | number[]) => {
    const newOpacity = typeof newValue === 'number' ? newValue : newValue[0];
    setCurrentOpacity(newOpacity);
    if (selectedElement) {
      selectedElement.attr('image/opacity', newOpacity);
      selectedElement.attr('label/text', 'Image');
    }
    handleOpacityChange(event, newOpacity);
  };

  const handleRemoveImageInternal = () => {
    if (selectedElement) {
      selectedElement.removeAttr('image/xlinkHref'); // Remove the image from the selected element
      selectedElement.attr('label/text', 'Upload Image');
    }
    setCurrentBase64Image(null); // Clear the image state
    handleRemoveImage(); // Update state to reflect the removal of the image
  };
  const elementLabels =
    selectedElement instanceof shapes.standard.TextBlock &&
    graphRef.current
      ?.getElements()
      .filter(
        (element) =>
          element.id !== selectedElement?.id &&
          (element.get('data') as elementSettings)?.variableLabels?.length > 0,
      )
      .flatMap((element) => (element.get('data') as elementSettings).variableLabels || [])
      .includes(selectedElement!.id);

  return (
    <Paper sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'end' }}>
        <Tooltip title="Close" arrow>
          <IconButton onClick={() => dispatch(setSelectedElement(null))}>
            <CancelIcon />
          </IconButton>
        </Tooltip>
      </Box>
      <Typography gutterBottom variant="h5">
        Edit the properties of the selected {elementName} element.
      </Typography>
      {selectedElement && selectedElement?.attributes?.elementType !== 'draggableLabel' && (
        <>
          <Button
            disabled={selectedElement.getPorts().length >= 8}
            variant="contained"
            fullWidth
            sx={{ my: 2 }}
            onClick={handleAddPort}
          >
            Add Port
          </Button>

          {selectedElement.getPorts().length >= 8 && (
            <Alert sx={{ mb: 2 }} severity="warning">
              You can only add up to 6 custom ports per element.
            </Alert>
          )}

          {selectedPortId && (
            <Paper sx={{ mt: 3, p: 2 }} elevation={2}>
              <Typography variant="subtitle1" gutterBottom>
                Adjust Port Position
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Typography gutterBottom>X Position</Typography>
              <Slider
                value={x}
                min={0}
                max={selectedElement.size().width}
                step={1}
                onChange={(_, val) => handlePortSliderChange('x', val as number)}
                valueLabelDisplay="auto"
              />
              <Typography gutterBottom>Y Position</Typography>
              <Slider
                value={y}
                min={0}
                max={selectedElement.size().height}
                step={1}
                onChange={(_, val) => handlePortSliderChange('y', val as number)}
                valueLabelDisplay="auto"
              />
            </Paper>
          )}

          {visiblePorts.length > 0 && (
            <Paper sx={{ my: 3, p: 2, maxHeight: '200px', overflowY: 'auto' }} elevation={2}>
              <Typography variant="subtitle1" gutterBottom>
                All Ports
              </Typography>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {visiblePorts.map((port) => (
                  <Box
                    key={port.id}
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <Box sx={{ overflow: 'hidden' }}>
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 500,
                          maxWidth: 160,
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                        }}
                      >
                        {port.id}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Group: {port.group}
                      </Typography>
                    </Box>
                    <Box>
                      <Tooltip title="Edit Port" arrow>
                        <IconButton
                          size="small"
                          sx={{ mr: 1 }}
                          onClick={() => handleEditPort(String(port.id))}
                          title="Edit Port"
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>

                      <Tooltip
                        title={`${
                          port.id === 'in' || port.id === 'out'
                            ? 'You cannot delete default port'
                            : 'Delete Port'
                        }`}
                        arrow
                      >
                        <span>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeletePort(String(port.id))}
                            title="Delete Port"
                            disabled={port.id === 'in' || port.id === 'out'}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </span>
                      </Tooltip>
                    </Box>
                  </Box>
                ))}
              </Box>
            </Paper>
          )}
        </>
      )}

      {selectedElement?.attributes?.elementType !== CATEGORY_E.DOMAIN ? (
        <>
          {!elementLabels ? (
            <Box mb={2}>
              <TextField
                label="Title"
                name="name"
                type="text"
                onChange={(e) => {
                  const inputValue = e.target.value;
                  if (inputValue.length <= 50) {
                    handleAttrChangeElement('name', inputValue);
                  }
                }}
                value={formatDiagramElementName(
                  selectedElement?.attributes.attrs?.label?.text ?? '',
                )}
                variant="outlined"
                fullWidth
                inputProps={{
                  maxLength: 50,
                }}
              />
              {(!elementAttrs.name || elementAttrs.name.trim() === '') && (
                <Typography sx={{ mt: 1 }} variant="body2" color="textSecondary">
                  No label set. Please enter a label.
                </Typography>
              )}
              {elementAttrs.name?.length >= 50 && (
                <Typography sx={{ mt: 1 }} variant="body2" color="error">
                  Title cannot exceed 50 characters.
                </Typography>
              )}
            </Box>
          ) : (
            <Box mb={2}>
              <TextField
                label="Title"
                name="name"
                type="text"
                disabled
                onChange={(e) => {
                  const inputValue = e.target.value;
                  if (inputValue.length <= 50) {
                    handleAttrChangeElement('name', inputValue);
                  }
                }}
                value={formatDiagramElementName(
                  selectedElement?.attributes.attrs?.label?.text ?? '',
                )}
                variant="outlined"
                fullWidth
                inputProps={{
                  maxLength: 50,
                }}
              />
            </Box>
          )}
        </>
      ) : null}

      {(selectedElement?.attributes?.elementType === CATEGORY_E.BASIC ||
        selectedElement?.attributes?.elementType === 'draggableLabel') &&
        selectedElement?.attributes?.type !== 'standard.BorderedImage' && (
          <Box mb={2}>
            <TextField
              margin="normal"
              label={`${
                selectedElement?.attributes?.attrs?.label?.text
                  ? selectedElement.attributes.attrs.label.text.charAt(0).toUpperCase() +
                    selectedElement.attributes.attrs.label.text.slice(1)
                  : ''
              } Border Color`}
              name="borderColor"
              type="color"
              onChange={(e) => handleAttrChangeElement('borderColor', e.target.value)}
              value={selectedElement?.attr('body/stroke') || '#000000'}
              variant="outlined"
              fullWidth
            />

            <TextField
              margin="normal"
              label={`${
                selectedElement?.attributes?.attrs?.label?.text
                  ? selectedElement.attributes.attrs.label.text.charAt(0).toUpperCase() +
                    selectedElement.attributes.attrs.label.text.slice(1)
                  : ''
              } Border Width`}
              name="borderWidth"
              type="number"
              onChange={(e) => {
                const value = e.target.value;
                if (value === '' || (parseInt(value, 10) >= 0 && parseInt(value, 10) <= 10)) {
                  handleAttrChangeElement('borderWidth', value);
                }
              }}
              value={elementAttrs.borderWidth ?? '1'}
              variant="outlined"
              fullWidth
              inputProps={{
                min: 0,
                max: 10,
                step: 1,
              }}
              helperText="Border width must be between 0 and 10."
            />
          </Box>
        )}

      {selectedElement && selectedElement instanceof Progress && (
        <ProgressSettings
          selectedElement={selectedElement}
          handleAttrChangeElement={handleAttrChangeElement}
        />
      )}
      {/* {selectedElement &&
        selectedElement.getEmbeddedCells().length > 0 &&
        isGroupWithConicTankAndProgressOnly(selectedElement) && (
          <GroupedConicalProgressSettings
            selectedElement={selectedElement}
            handleAttrChangeElement={handleAttrChangeElement}
          />
        )}
      {selectedElement &&
        selectedElement.getEmbeddedCells().length > 0 &&
        isGroupWithLiquidTankAndProgressOnly(selectedElement) && (
          <GroupedLiquidProgressSettings
            selectedElement={selectedElement}
            handleAttrChangeElement={handleAttrChangeElement}
          />
        )} */}
      {selectedElement && selectedElement instanceof ConicTank && (
        <ConicalTankSettings
          selectedElement={selectedElement}
          handleAttrChangeElement={handleAttrChangeElement}
        />
      )}
      {selectedElement && selectedElement instanceof Battery && (
        <BatterySettings
          selectedElement={selectedElement}
          handleAttrChangeElement={handleAttrChangeElement}
        />
      )}
      {selectedElement && selectedElement instanceof LiquidTank && (
        <LiquidTankSettings
          selectedElement={selectedElement}
          handleAttrChangeElement={handleAttrChangeElement}
        />
      )}
      {selectedElement &&
        selectedElement?.getEmbeddedCells().length === 0 &&
        graphRef.current !== null && (
          <Box mb={2}>
            <CommonSettings
              graph={graphRef.current}
              selectedElement={selectedElement}
              setEditedLabels={setEditedLabels}
              editedLabels={editedLabels}
            />
          </Box>
        )}
      {selectedElement &&
        graphRef.current !== null &&
        selectedElement instanceof shapes.standard.TextBlock && (
          <LabelSettings graph={graphRef.current} selectedElement={selectedElement} />
        )}

      {elementType === CATEGORY_E.DOMAIN && selectedElement && selectedElement instanceof Pump && (
        <>
          <Box mb={2}>
            <Typography gutterBottom>Rotation Speed ({selectedElement.rotationSpeed})</Typography>
            <Slider
              name="rotationSpeed"
              value={selectedElement.rotationSpeed}
              min={0}
              max={5}
              step={0.1}
              onChange={(event, newValue) => {
                if (typeof newValue === 'number') {
                  selectedElement.setRotationSpeed(newValue);
                  handleAttrChangeElement('rotationSpeed', newValue.toString());
                }
              }}
              aria-labelledby="rotation-speed-slider"
              valueLabelDisplay="auto"
            />
          </Box>
        </>
      )}
      {selectedElement instanceof shapes.standard.BorderedImage && (
        <>
          <Box mb={2}>
            <Typography gutterBottom>Choose from Predefined Images</Typography>
            <Box
              sx={{
                display: 'flex',
                gap: 2,
                justifyContent: 'start',
                flexWrap: 'wrap',
              }}
            >
              {images.map((image, index) => (
                <Box
                  key={index}
                  sx={{
                    width: 60,
                    height: 60,
                    border: (theme) =>
                      selectedImage === image
                        ? `2px solid ${theme.palette.primary.main}`
                        : '1px solid #010101',
                    cursor: 'pointer',
                    backgroundImage: `url(${image})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    objectFit: 'contain',
                    opacity: selectedImage === image ? currentOpacity : 1,
                  }}
                  onClick={() => {
                    handlePredefinedImageSelect(image, String(selectedElement.id));
                    setSelectedImage(image);
                  }}
                />
              ))}
            </Box>
          </Box>

          <Box mb={2} display="flex" alignItems="center">
            <Typography gutterBottom>Upload Background Image</Typography>
          </Box>

          <input
            accept="image/*"
            style={{ display: 'none' }}
            id="upload-button-file"
            type="file"
            onChange={handleImageUpload}
          />

          <Box mb={2} display="flex" alignItems="center">
            <label htmlFor="upload-button-file">
              <Button variant="contained" component="span" color="primary" size="small">
                Upload File
              </Button>
            </label>

            {currentBase64Image ? (
              <Box ml={2} sx={{ position: 'relative' }}>
                <img
                  src={currentBase64Image}
                  alt="Background Preview"
                  style={{
                    width: '50px',
                    height: '50px',
                    objectFit: 'cover',
                    border: '1px solid #ccc',
                    opacity: currentOpacity,
                  }}
                />

                <IconButton
                  sx={{ position: 'absolute', top: -10, right: -10 }}
                  onClick={handleRemoveImageInternal}
                  color="secondary"
                  size="small"
                >
                  <RemoveCircleOutlineIcon sx={{ fontSize: '18px', color: '#010101' }} />
                </IconButton>
              </Box>
            ) : (
              <Typography sx={{ ml: 2 }} variant="body2" color="textSecondary">
                No image selected. Please upload an image.
              </Typography>
            )}
          </Box>

          {(currentBase64Image || selectedImage) && (
            <Box mb={2} sx={{ px: 1 }}>
              <Typography gutterBottom>Adjust Image Opacity:</Typography>
              <Slider
                value={currentOpacity}
                min={0}
                max={1}
                step={0.01}
                onChange={handleSliderChange}
                aria-labelledby="opacity-slider"
                sx={{ width: '100%' }}
              />
            </Box>
          )}
        </>
      )}

      {elementsVariables && selectedElement && elementsVariables[selectedElement.id as string] && (
        <>
          {elementsVariables[selectedElement.id as string].map((variable: any, index) => (
            <Box key={index} mt={2}>
              <Box
                sx={{
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'space-between',
                }}
              >
                <TextField
                  label={`Variable ${index + 1}`}
                  name={variable.name}
                  type="text"
                  onChange={(e) => {
                    const updatedName = e.target.value.trim();
                    const elementId = selectedElement?.id as string;
                    const existingVariables = elementsVariables[elementId] || [];

                    const updatedVariables = [...existingVariables];
                    updatedVariables[index] = { ...variable, label: updatedName };

                    dispatch(
                      setElementsVariables({
                        key: elementId,
                        value: updatedVariables,
                      }),
                    );

                    // Clear the error for this field
                    setFieldErrors((prevErrors) => ({
                      ...prevErrors,
                      [index]: null,
                    }));
                  }}
                  value={variable.title}
                  variant="outlined"
                  fullWidth
                  error={!!fieldErrors[index]} // Show error state
                  helperText={fieldErrors[index]} // Display error message
                />

                <Tooltip title="Delete">
                  <IconButton
                    disableFocusRipple
                    disableTouchRipple
                    disableRipple
                    color="error"
                    onClick={() => {
                      dispatch(
                        diagramSlice.actions.removeElementsVariables({
                          key: selectedElement?.id as string,
                          index: index,
                        }),
                      );

                      setFieldErrors((prevErrors) => {
                        const updatedErrors = { ...prevErrors };
                        delete updatedErrors[index];
                        return updatedErrors;
                      });
                    }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
          ))}
        </>
      )}
    </Paper>
  );
};

export default CommonElementPropEditDialog;
