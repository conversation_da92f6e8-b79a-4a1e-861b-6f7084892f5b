import { dia, shapes } from '@joint/core';
import { Delete as DeleteIcon } from '@mui/icons-material';
import {
  <PERSON><PERSON>,
  Box,
  Button,
  Card,
  Checkbox,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import _ from 'lodash';
import { ChangeEvent, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { DraggableLabel } from '~/components/CreateElement/DraggableLabel';
import { RootState } from '~/redux/store';
import {
  borderRule,
  colorRule,
  conditionalRule,
  elementSettings,
  elementVariable,
  rotationRule,
  titleRule,
} from '~/types/diagram';
import Pump from '../Pump';
import { CATEGORY_E } from '../../Palette/Palette';

type CircleSettingsProps = {
  selectedElement: dia.Element<dia.Element.Attributes, dia.ModelSetOptions>;
  graph: dia.Graph;
  editedLabels: string[];
  setEditedLabels: React.Dispatch<React.SetStateAction<string[]>>;
};
const dropDownfixSxs = {
  width: '100%',
  p: 0.3,
  '& fieldset': {
    '& legend': {
      maxWidth: '100%',
      height: 'auto',
      '& span': {
        opacity: 1,
      },
    },
  },
};
const CommonSettings = ({
  selectedElement,
  graph,
  setEditedLabels,
  editedLabels,
}: CircleSettingsProps) => {
  const { variables = [], conditionalRule = [] } =
    (selectedElement.get('data') as elementSettings) || {};
  const customElementData = useSelector((state: RootState) => state.diagram.customElementData);
  const [elementVariables, setElementsVariables] = useState<elementVariable[]>(variables);
  const [conditionalRules, setConditionalRules] = useState<conditionalRule[]>(conditionalRule);

  useEffect(() => {
    if (!selectedElement) return;
    const elementData = selectedElement.get('data') || {};
    const variables = elementData.variables || [];
    const conditionalRules = elementData.conditionalRule || [];

    setElementsVariables(variables);
    setConditionalRules(conditionalRules);
  }, [selectedElement]);

  const handleAddConditionalRule = () => {
    const newRule: conditionalRule = {
      variable: '',
      conditionalOperator: '==',
      value: '',
      applicableTo: 'border',
      rules: {
        color: undefined,
        borderWidth: undefined,
        borderStyle: undefined,
        borderColor: undefined,
        titleColor: undefined,
        fontSize: undefined,
      },
    };
    const updatedRules = [...conditionalRules, newRule];
    setConditionalRules(updatedRules);
    selectedElement.prop('data/conditionalRule', updatedRules);
  };

  const handleUpdateConditionalRule = (
    index: number,
    field: keyof conditionalRule,
    value: string | number | object,
  ) => {
    const updatedRules = conditionalRules.map((rule, i) =>
      i === index ? { ...rule, [field]: value } : rule,
    );
    setConditionalRules(updatedRules);
    selectedElement.prop('data/conditionalRule', updatedRules);
  };

  const handleDeleteConditionalRule = (index: number) => {
    // Remove the conditional rule from the state
    const updatedRules = conditionalRules.filter((_, i) => i !== index);
    setConditionalRules(updatedRules);

    // Update the 'conditionalRule' in the selected element in the graph
    selectedElement.prop('data/conditionalRule', updatedRules);

    // Update the graph with the new conditional rules, variables, and labels
    graph.getCells().forEach((cell) => {
      if (cell.id === selectedElement.id) {
        // Re-apply the updated data to the selected cell
        const updatedCellData = cell.get('data');

        // Ensure that the data for the conditional rule is updated
        cell.set('data', {
          ...updatedCellData,
          conditionalRule: updatedRules, // Update conditional rule
        });
      }
    });
  };

  const addNewVariable = () => {
    const newVariable: elementVariable = {
      variableName: '',
      label: '',
      measurementId: '',
      assetId: '',
      value: '',
    };

    const updatedVariables = [...elementVariables, newVariable];
    setElementsVariables(updatedVariables);
    selectedElement.prop('data/variables', updatedVariables);

    const basePosition = selectedElement.position();
    const offsetY = elementVariables.length * 40;

    const uomList = ['kg', 'm', 's', 'L', '°C', 'Pa', 'W', 'J', '%'];
    const dummyUOM = uomList[Math.floor(Math.random() * uomList.length)];

    const draggableLabel = new DraggableLabel(
      `Label ${updatedVariables.length} (${dummyUOM})`,
      basePosition.x + 100,
      basePosition.y + offsetY,
    );
    draggableLabel.addTo(graph);

    const variableLabels = selectedElement.get('data')?.variableLabels || [];

    const updatedVariableLabels = [
      ...variableLabels,
      { elementId: selectedElement.id, labelId: draggableLabel.id },
    ];
    selectedElement.prop('data/variableLabels', updatedVariableLabels);
  };

  const debouncedUpdateLabel = (labelText: string, index: number) => {
    const labelRef = selectedElement.get('data')?.variableLabels?.[index];
    const uomList = ['kg', 'm', 's', 'L', '°C', 'Pa', 'W', 'J', '%'];
    const dummyUOM = uomList[Math.floor(Math.random() * uomList.length)];

    if (labelRef?.elementId === selectedElement.id) {
      const label = graph.getCell(labelRef.labelId);
      label?.attr('label/text', `${labelText} (${dummyUOM})`);
    }
  };

  const updateVariableName = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    index: number,
  ) => {
    const newVariableName = e.target.value.trim();

    // Get all cells except the currently selected one
    const elements = graph.getCells().filter((cell) => cell.id !== selectedElement.id);

    // Collect all variable names from other cells
    const existingVariableNames = elements.reduce<string[]>((acc, cell) => {
      const cellVariables: elementVariable[] = cell.get('data')?.variables || [];
      const cellVariableNames = cellVariables.map((variable) => variable.variableName);
      return [...acc, ...cellVariableNames];
    }, []);

    const isDuplicate = existingVariableNames.includes(newVariableName);

    // Update the variable in local state
    const updatedVariables = elementVariables.map((variable, i) =>
      i === index
        ? {
            ...variable,
            variableName: newVariableName,
            error: isDuplicate,
            errorMsg: isDuplicate ? 'Variable name must be unique.' : '',
          }
        : variable,
    );

    setElementsVariables(updatedVariables);

    if (!isDuplicate) {
      selectedElement.prop('data/variables', updatedVariables);

      if (customElementData[selectedElement.id]) {
        const updatedData = {
          ...customElementData[selectedElement.id],
          relatedVariables: updatedVariables.map((v) => v.variableName),
        };
      }
    }
  };

  const updateLabelName = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    index: number,
  ) => {
    const newLabelName = e.target.value.trim();
    if (index < 0 || index >= elementVariables.length) return;

    const updatedVariables = [...elementVariables];
    updatedVariables[index].label = newLabelName;
    setElementsVariables(updatedVariables);
    selectedElement.prop('data/variables', updatedVariables);

    // ✅ Update label on canvas
    const labelRef = selectedElement.get('data')?.variableLabels?.[index];
    if (labelRef?.elementId === selectedElement.id) {
      const label = graph.getCell(labelRef.labelId);
      label?.attr('label/text', newLabelName);
    }
  };

  const handleDeleteVariable = (index: number) => {
    const currentVariables = selectedElement.get('data')?.variables || [];
    const currentVariableLabels = selectedElement.get('data')?.variableLabels || [];

    if (index < 0 || index >= currentVariables.length) return;

    const updatedVariables: elementVariable[] = currentVariables.filter(
      (_: string, i: number) => i !== index,
    );
    setElementsVariables(updatedVariables);
    selectedElement.prop('data/variables', updatedVariables);

    const updatedVariableLabels = currentVariableLabels.filter(
      (_: string, i: number) => i !== index,
    );
    selectedElement.prop('data/variableLabels', updatedVariableLabels);

    const labelRefToRemove = currentVariableLabels[index];
    if (labelRefToRemove?.elementId === selectedElement.id) {
      const labelElement = graph.getCell(labelRefToRemove.labelId);
      labelElement?.remove();
    }

    const updatedLabels = editedLabels.filter((_, i) => i !== index);
    setEditedLabels(updatedLabels);

    graph.getCells().forEach((cell) => {
      if (cell.id === selectedElement.id) {
        const updatedCellData = cell.get('data') || {};
        cell.set('data', {
          ...updatedCellData,
          variables: updatedVariables,
          variableLabels: updatedVariableLabels,
        });
      }
    });

    handleDeleteConditionalRule(index);
  };

  if (
    // selectedElement instanceof LiquidTank ||
    selectedElement instanceof shapes.standard.TextBlock
  ) {
    return null;
  }

  const RenderRules = ({ rule, index }: { rule: conditionalRule; index: number }) => {
    switch (rule.applicableTo) {
      case 'color':
        return (
          <Grid item xs={6}>
            <TextField
              label="Color"
              type="color"
              value={(rule.rules as colorRule).color || null}
              onChange={(e) =>
                handleUpdateConditionalRule(index, 'rules', {
                  ...rule.rules,
                  color: e.target.value,
                })
              }
              fullWidth
            />
          </Grid>
        );
      case 'rotation': {
        return (
          <FormControlLabel
            control={
              <Checkbox
                checked={(rule.rules as rotationRule).rotate}
                onChange={(e) => {
                  handleUpdateConditionalRule(index, 'rules', {
                    ...rule.rules,
                    rotate: e.target.checked, // ✅ FIXED: Corrected property name
                  });

                  // If it's a Pump element, apply the rotation directly
                  if (selectedElement instanceof Pump) {
                    selectedElement.toggleRotation();
                  }
                }}
                color="primary"
              />
            }
            label="Enable rotation"
          />
        );
      }
      case 'background':
        return (
          <Grid item xs={6}>
            <TextField
              label="Background Color"
              type="color"
              value={(rule.rules as colorRule).color || null}
              onChange={(e) =>
                handleUpdateConditionalRule(index, 'rules', {
                  ...rule.rules,
                  color: e.target.value,
                })
              }
              fullWidth
            />
          </Grid>
        );
      case 'border':
        return (
          <>
            <Grid item xs={4} md={6}>
              <TextField
                label="Border Width"
                type="number"
                value={(rule.rules as borderRule).borderWidth || 1}
                sx={{ py: 0.3 }}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || (parseInt(value, 10) >= 0 && parseInt(value, 10) <= 10)) {
                    handleUpdateConditionalRule(index, 'rules', {
                      ...rule.rules,
                      borderWidth: Number(value),
                    });
                  }
                }}
                fullWidth
                inputProps={{
                  min: 0,
                  max: 10,
                  step: 1,
                }}
              />
            </Grid>
            <Grid item xs={4} md={6}>
              <Select
                label="Border Style"
                sx={{ ...dropDownfixSxs }}
                value={(rule.rules as borderRule).borderStyle || 'solid'}
                onChange={(e) =>
                  handleUpdateConditionalRule(index, 'rules', {
                    ...rule.rules,
                    borderStyle: e.target.value as borderRule['borderStyle'],
                  })
                }
                fullWidth
              >
                {['solid', 'dotted', 'dashed', 'none'].map((style) => (
                  <MenuItem key={style} value={style}>
                    {style}
                  </MenuItem>
                ))}
              </Select>
            </Grid>
            <Grid item xs={4} md={12}>
              <TextField
                label="Border Color"
                type="color"
                value={(rule.rules as borderRule).borderColor || '#010101'}
                onChange={(e) =>
                  handleUpdateConditionalRule(index, 'rules', {
                    ...rule.rules,
                    borderColor: e.target.value,
                  })
                }
                fullWidth
              />
            </Grid>
          </>
        );
      case 'title':
        return (
          <>
            <Grid item xs={6}>
              <TextField
                label="Title Color"
                type="color"
                value={(rule.rules as titleRule).titleColor || null}
                onChange={(e) =>
                  handleUpdateConditionalRule(index, 'rules', {
                    ...rule.rules,
                    titleColor: e.target.value,
                  })
                }
                fullWidth
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Font Size"
                type="number"
                value={(rule.rules as titleRule).fontSize || null}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '' || (parseInt(value, 10) >= 1 && parseInt(value, 10) <= 25)) {
                    handleUpdateConditionalRule(index, 'rules', {
                      ...rule.rules,
                      fontSize: Number(value),
                    });
                  }
                }}
                fullWidth
                inputProps={{
                  min: 1,
                  max: 25,
                  step: 1,
                  onKeyDown: (e) => {
                    if (e.key === '0' && e.currentTarget.value === '') {
                      e.preventDefault();
                    }
                  },
                }}
              />
            </Grid>
          </>
        );
      default:
        return null;
    }
  };
  return (
    <Box mb={2}>
      <Box>
        <Button variant="contained" color="primary" onClick={addNewVariable}>
          Add Variable
        </Button>
      </Box>
      <Box mt={2}>
        {elementVariables.map((variable, index) => (
          <Card key={index} sx={{ mb: 2, p: 2 }}>
            <Grid container spacing={2} alignItems="center">
              {/* Variable Name Input */}
              <Grid item xs={12} md={5}>
                <TextField
                  name="variableName"
                  fullWidth
                  label={`Variable ${index + 1}`}
                  value={variable.variableName || null}
                  onChange={(e) => {
                    updateVariableName(e, index);
                  }}
                  error={!!variable.error}
                  helperText={variable.error ? variable.errorMsg : ''}
                />
              </Grid>

              {/* Label Name Input */}
              <Grid item xs={12} md={5}>
                <TextField
                  name="labelName"
                  fullWidth
                  label={`Label ${index + 1}`}
                  value={variable.label || ''}
                  onChange={(e) => {
                    updateLabelName(e, index);
                  }}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  onBlur={() => debouncedUpdateLabel(variable.label || '', index)}
                />
              </Grid>

              {/* Delete Button */}
              <Grid item xs={12} md={2}>
                <Tooltip title="Delete Variable & Label">
                  <IconButton color="error" onClick={() => handleDeleteVariable(index)}>
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              </Grid>
            </Grid>
          </Card>
        ))}
      </Box>

      {/* Conditional Rules */}
      <Box mb={2}>
        <Button
          variant="contained"
          color="primary"
          onClick={handleAddConditionalRule}
          disabled={elementVariables.length === 0}
        >
          Add Conditional Rule
        </Button>
        {elementVariables.length === 0 && (
          <Alert severity="error" sx={{ mt: 2 }}>
            You need to add at least one variable before adding conditional rules.
          </Alert>
        )}
        {conditionalRules.map((rule, index) => (
          <Card sx={{ mt: 2, p: 2 }} key={index}>
            <Box
              mt={2}
              sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
            >
              <Typography variant="h5">Conditional Rule #{index + 1}</Typography>
              <Tooltip title={`Delete Rule ${index + 1}`}>
                <IconButton color="error" onClick={() => handleDeleteConditionalRule(index)}>
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
            </Box>
            <Grid container spacing={2} mt={1}>
              <Grid item xs={6}>
                <Select
                  label="Variable"
                  name="variable"
                  sx={dropDownfixSxs}
                  value={rule.variable}
                  onChange={(e) =>
                    handleUpdateConditionalRule(index, 'variable', e.target.value as string)
                  }
                >
                  {elementVariables
                    .filter(({ variableName }) => variableName && variableName !== '')
                    .map((variable, idx) => (
                      <MenuItem key={idx} value={idx}>
                        {variable.variableName}
                      </MenuItem>
                    ))}
                </Select>
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth>
                  <Select
                    label="Operator"
                    name="conditionalOperator"
                    sx={dropDownfixSxs}
                    value={rule.conditionalOperator}
                    onChange={(e) =>
                      handleUpdateConditionalRule(
                        index,
                        'conditionalOperator',
                        e.target.value as string,
                      )
                    }
                  >
                    {['==', '!=', '>', '<', '>=', '<='].map((op) => (
                      <MenuItem key={op} value={op}>
                        {op}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <Grid container spacing={2} mt={1}>
              <Grid item xs={6}>
                <TextField
                  label="Value"
                  value={rule.value}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (/^\d{0,4}$/.test(value)) {
                      handleUpdateConditionalRule(index, 'value', value);
                    }
                  }}
                  fullWidth
                  inputProps={{ maxLength: 4 }}
                />
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth>
                  <Select
                    label="Apply to"
                    name="applicableTo"
                    sx={dropDownfixSxs}
                    value={rule.applicableTo}
                    onChange={(e) =>
                      handleUpdateConditionalRule(index, 'applicableTo', e.target.value as string)
                    }
                  >
                    {[
                      'border',
                      selectedElement.attributes?.elementType !== CATEGORY_E.DOMAIN
                        ? 'title'
                        : undefined,
                      'background',
                      selectedElement instanceof Pump ? 'rotation' : undefined,
                    ]
                      .filter((target): target is string => target !== undefined)
                      .map((target) => (
                        <MenuItem key={target} value={target}>
                          {target.charAt(0).toUpperCase() + target.slice(1)}
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>
              </Grid>
              <Box width={'100%'} sx={{ pl: 2, mt: 2 }}>
                <Typography variant="h6">Conditional Formatting Rules</Typography>
              </Box>
              <RenderRules rule={rule} index={index} />
            </Grid>
          </Card>
        ))}
      </Box>
    </Box>
  );
};

export default CommonSettings;
