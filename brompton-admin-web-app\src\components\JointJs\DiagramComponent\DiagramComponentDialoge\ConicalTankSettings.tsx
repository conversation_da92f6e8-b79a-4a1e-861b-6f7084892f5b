import { dia } from '@joint/core';
import { Box, TextField } from '@mui/material';
import React from 'react';
import { ConicTank } from '../ConicalTank';

type ConicalTankSettingsProps = {
  selectedElement: dia.Element<dia.Element.Attributes, dia.ModelSetOptions>;
  handleAttrChangeElement: (attr: string, value: string) => void;
};

const ConicalTankSettings = ({
  selectedElement,
  handleAttrChangeElement,
}: ConicalTankSettingsProps) => {
  // Retrieve current maxCapacity and level from the selected element
  const { maxCapacity = 100 } = selectedElement.get('data') ?? {};
  if (!(selectedElement instanceof ConicTank)) {
    return null;
  }
  //   const maxCapacity = selectedElement?.maxCapacity ?? 100; // Default to 100 if not set
  //   const level = selectedElement.get('level') || 0; // Default to 0 if not set

  const handleMaxCapacityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Ensure the value is a number and does not exceed 100000
    const value = Math.min(Number(e.target.value) || 0, 100000);
    if (value !== maxCapacity) {
      handleAttrChangeElement('maxCapacity', value.toString());
      // Update the element's max capacity property
      selectedElement.maxCapacity = value;
      // Ensure maxCapacity is updated in the element data
      selectedElement.prop('data/maxCapacity', value);
      // Ensure the update method exists before calling
      selectedElement.updateMaxCapacity(value);
    }
  };

  return (
    <Box mb={2} display="flex" flexDirection="column" gap={2}>
      <TextField
        label="Maximum Capacity"
        type="number"
        value={maxCapacity ?? selectedElement.maxCapacity}
        onChange={handleMaxCapacityChange}
        inputProps={{ min: 0, max: 100000 }}
        fullWidth
      />
    </Box>
  );
};

export default ConicalTankSettings;
