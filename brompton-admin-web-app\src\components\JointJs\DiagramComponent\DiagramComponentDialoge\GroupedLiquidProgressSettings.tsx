import { dia } from '@joint/core';
import { Box, FormControlLabel, Radio, RadioGroup, TextField, Typography } from '@mui/material';
import Progress from '~/components/CreateElement/Progress';
import LiquidTank from '../LiquidTank';

export type GroupedLiquidProgressSettingsProps = {
  selectedElement: dia.Element<dia.Element.Attributes, dia.ModelSetOptions>;
  handleAttrChangeElement: (attr: string, value: string) => void;
};
const GroupedLiquidProgressSettings = ({
  selectedElement,
  handleAttrChangeElement,
}: GroupedLiquidProgressSettingsProps) => {
  const embeddedCells = selectedElement.getEmbeddedCells();

  // Extract all ConicTank and Progress elements from the group
  const conicTanks = embeddedCells.filter((cell) => cell instanceof LiquidTank) as LiquidTank[];
  const progressElements = embeddedCells.filter((cell) => cell instanceof Progress) as Progress[];

  const handleOrientationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newOrientation = e.target.value as 'vertical' | 'horizontal';
    progressElements.forEach((element, index) => {
      element.orientation = newOrientation; // Update each progress element's orientation

      if (newOrientation === 'horizontal' && conicTanks[index]) {
        // Center progress horizontally relative to the corresponding ConicTank
        const conicTank = conicTanks[index];
        const conicBBox = conicTank.getBBox();
        const progressBBox = element.getBBox();

        // Calculate new position to center the Progress element in the middle of the ConicTank's width
        const newX = conicBBox.x + conicBBox.width / 2 - progressBBox.width / 2;
        const newY = conicBBox.y + conicBBox.height / 2 - progressBBox.height / 2;

        element.position(newX, newY);
      }
    });

    handleAttrChangeElement('orientation', newOrientation);
  };

  const handleStyleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newStyle = e.target.value as 'bar' | 'solid';
    progressElements.forEach((element) => {
      element.style = newStyle; // Update each progress element's fill style
    });
    handleAttrChangeElement('style', newStyle);
  };

  const handleMaxCapacityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMaxCapacity = Math.max(0, parseInt(e.target.value) || 0);
    progressElements.forEach((element) => {
      element.maxCapacityValue = newMaxCapacity; // Update max capacity for each progress element
    });
    handleAttrChangeElement('maxCapacity', newMaxCapacity.toString());
  };

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <Typography variant="h6" gutterBottom>
        Grouped Conical Tank and Progress Elements Settings
      </Typography>
      <Box>
        <Typography gutterBottom>Orientation</Typography>
        <RadioGroup
          name="orientation"
          value={progressElements.length > 0 ? progressElements[0].orientation : 'vertical'}
          onChange={handleOrientationChange}
          row
        >
          <FormControlLabel value="vertical" control={<Radio />} label="Vertical" />
          <FormControlLabel value="horizontal" control={<Radio />} label="Horizontal" />
        </RadioGroup>
      </Box>
      <Box>
        <Typography gutterBottom>Fill Style</Typography>
        <RadioGroup
          name="style"
          value={progressElements.length > 0 ? progressElements[0].style : 'solid'}
          onChange={handleStyleChange}
          row
        >
          <FormControlLabel value="bar" control={<Radio />} label="Bar" />
          <FormControlLabel value="solid" control={<Radio />} label="Solid" />
        </RadioGroup>
      </Box>

      <Box>
        <TextField
          label="Maximum Capacity"
          type="number"
          value={progressElements.length > 0 ? progressElements[0].maxCapacity : 100}
          onChange={handleMaxCapacityChange}
          inputProps={{ min: 0 }}
          fullWidth
        />
      </Box>
    </Box>
  );
};

export default GroupedLiquidProgressSettings;
