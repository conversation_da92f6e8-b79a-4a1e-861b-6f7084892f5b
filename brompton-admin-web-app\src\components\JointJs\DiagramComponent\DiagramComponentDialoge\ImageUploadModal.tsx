import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
} from '@mui/material';
import React, { FC, useState } from 'react';

interface IImageUploadModal {
  open: boolean;
  onClose: () => void;
  onApply: (imageSrc: string) => void;
}

const ImageUploadModal: FC<IImageUploadModal> = ({ onApply, onClose, open }) => {
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.[0]) {
      const file = e.target.files[0];
      setImageFile(file);

      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleApply = () => {
    if (imageFile && imagePreview) {
      onApply(imagePreview);
      setImagePreview(null);
      onClose();
    }
  };

  const removeImage = () => {
    setImageFile(null);
    setImagePreview(null);
  };

  const handleCloseModal = () => {
    onClose();
    setImagePreview(null);
  };

  return (
    <Dialog open={open} onClose={handleCloseModal}>
      <DialogTitle>Upload Image</DialogTitle>
      <DialogContent>
        {imagePreview && (
          <Box
            mt={2}
            sx={{
              position: 'relative',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <img
              src={imagePreview}
              alt="Image Preview"
              style={{
                width: '100%',
                maxHeight: '200px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                objectFit: 'contain',
                padding: 10,
              }}
            />
            <IconButton
              size="small"
              onClick={removeImage}
              sx={{
                position: 'absolute',
                top: 0,
                right: 0,
              }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        )}

        <Box my={2}>
          <input type="file" accept="image/*" onChange={handleFileChange} />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCloseModal}>Cancel</Button>
        <Button onClick={handleApply} color="primary" disabled={!imageFile}>
          Apply
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ImageUploadModal;
