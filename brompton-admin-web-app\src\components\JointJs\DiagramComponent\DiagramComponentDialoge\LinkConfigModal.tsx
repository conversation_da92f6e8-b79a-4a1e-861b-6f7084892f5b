import { dia } from '@joint/core';
import {
  <PERSON>ton,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Slider,
  Switch,
  TextField,
  Typography,
} from '@mui/material';
import { FC, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import CustomDialog from '~/components/common/CustomDialog';
import { getSelectedLink } from '~/redux/selectors/diagramSelector';

interface ILinkConfigModal {
  open: boolean;
  close: () => void;
}

const LinkConfigModal: FC<ILinkConfigModal> = ({ open, close }) => {
  const animationRef = useRef<number | null>(null);
  const selectedLink = useSelector(getSelectedLink);

  const [linkConfig, setLinkConfig] = useState({
    color: '#000000',
    strokeWidth: 2,
    animated: false,
    animationSpeed: 50,
    flowBackgroundColor: '#000000',
    flowBackgroundWidth: 5,
    endStyle: 'arrow',
    lineStyle: 'solid' as keyof typeof lineStyles,
  });

  const linkEndStyles: dia.Link.Attributes = {
    arrow: {
      type: 'path',
      d: 'M 10 -5 L 0 0 L 10 5 Z',
      fill: linkConfig.color ?? 'black',
      stroke: linkConfig.color ?? 'black',
      strokeWidth: 2,
      strokeLinejoin: 'miter',
    },
    dot: { type: 'circle', r: 3, fill: linkConfig.color ?? 'black' },
    triangle: {
      type: 'path',
      d: 'M 10 -5 L 0 0 L 10 5 Z',
      fill: 'white',
      stroke: linkConfig.color ?? 'black',
      strokeWidth: 2,
      strokeLinejoin: 'miter',
    },
    none: { type: 'none' },
  };

  const lineStyles = {
    solid: 'none', // Solid line
    dashed: '8,5', // Dashed line
    dashedDot: '8,5,2,5', // Dashed-dot pattern
  };

  const applyLinkSettings = () => {
    if (selectedLink) {
      // Clear existing animation interval
      if (animationRef.current) {
        clearInterval(animationRef.current);
        animationRef.current = null;
      }

      const linkAttributes: any = {
        line: {
          stroke: linkConfig.color,
          strokeWidth: linkConfig.strokeWidth,
          strokeDasharray: lineStyles[linkConfig.lineStyle as keyof typeof lineStyles],
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
          targetMarker: linkEndStyles[linkConfig.endStyle as keyof typeof linkEndStyles] || {
            type: 'path',
            d: 'M 10 -5 0 0 10 5 z',
          },
        },
      };

      if (linkConfig.animated) {
        linkAttributes.wrapper = {
          stroke: linkConfig.flowBackgroundColor,
          strokeWidth: linkConfig.flowBackgroundWidth,
          strokeOpacity: 1,
          strokeLinecap: 'round',
          strokeLinejoin: 'round',
        };

        // Start animation
        let dashOffset = 0;
        const intervalTime = 1000 - linkConfig.animationSpeed * 12;
        animationRef.current = window.setInterval(() => {
          if (selectedLink) {
            dashOffset -= 2;
            selectedLink.attr('line/strokeDashoffset', dashOffset.toString());
          }
        }, intervalTime);
      }

      selectedLink.attr(linkAttributes);
      close();
    }
  };

  return (
    <CustomDialog
      maxWidth="md"
      open={open}
      onClose={() => {
        close();
        setLinkConfig({
          color: '#000000',
          strokeWidth: 2,
          animated: false,
          animationSpeed: 50,
          flowBackgroundColor: '#000000',
          flowBackgroundWidth: 5,
          endStyle: 'arrow',
          lineStyle: 'solid' as keyof typeof lineStyles,
        });
      }}
      title={<>Link Configuration</>}
      content={
        <Grid container spacing={2}>
          {/* Enable Animation */}
          {/* <Grid item xs={12} display="flex" justifyContent="space-between" alignItems="center">
            <Typography>Enable Animation:</Typography>
            <Switch
              checked={linkConfig.animated}
              onChange={(e) => setLinkConfig({ ...linkConfig, animated: e.target.checked })}
            />
          </Grid> */}

          {/* Animation-related settings */}
          {linkConfig.animated && (
            <>
              <Grid item xs={12}>
                <Typography>Animation Speed: {linkConfig.animationSpeed}</Typography>
                <Slider
                  value={linkConfig.animationSpeed}
                  min={0}
                  max={100}
                  onChange={(e, value) =>
                    setLinkConfig({ ...linkConfig, animationSpeed: value as number })
                  }
                />
              </Grid>

              <Grid item xs={6}>
                <TextField
                  label="Flow Background Color"
                  type="color"
                  value={linkConfig.flowBackgroundColor}
                  onChange={(e) =>
                    setLinkConfig({ ...linkConfig, flowBackgroundColor: e.target.value })
                  }
                  fullWidth
                  margin="dense"
                />
              </Grid>

              <Grid item xs={6}>
                <TextField
                  label="Flow Background Width"
                  type="number"
                  value={linkConfig.flowBackgroundWidth}
                  onChange={(e) =>
                    setLinkConfig({ ...linkConfig, flowBackgroundWidth: Number(e.target.value) })
                  }
                  fullWidth
                  margin="dense"
                />
              </Grid>
            </>
          )}

          {/* General link properties */}
          <Grid item xs={6}>
            <TextField
              label="Dash Stroke Color"
              type="color"
              value={linkConfig.color}
              onChange={(e) => setLinkConfig({ ...linkConfig, color: e.target.value })}
              fullWidth
              margin="dense"
            />
          </Grid>

          <Grid item xs={6}>
            <TextField
              label="Dashed Stroke Width"
              type="number"
              value={linkConfig.strokeWidth}
              onChange={(e) => {
                const value = Number(e.target.value);
                if (value >= 1 && value <= 10) {
                  setLinkConfig({ ...linkConfig, strokeWidth: value });
                }
              }}
              inputProps={{ min: 0, max: 10 }}
              fullWidth
              margin="dense"
            />
          </Grid>

          {/* Link End Style */}
          <Grid item xs={6}>
            <FormControl fullWidth>
              <InputLabel id="end-style">End Style</InputLabel>
              <Select
                labelId="end-style"
                id="end-style-select"
                value={linkConfig.endStyle}
                label="End Style"
                onChange={(e) =>
                  setLinkConfig({
                    ...linkConfig,
                    endStyle: e.target.value as 'arrow' | 'none' | 'dot' | 'triangle',
                  })
                }
              >
                <MenuItem value="arrow">Arrow</MenuItem>
                <MenuItem value="dot">Dot</MenuItem>
                <MenuItem value="triangle">Triangle</MenuItem>
                <MenuItem value="none">None</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Line Style */}
          <Grid item xs={6}>
            <FormControl fullWidth>
              <InputLabel id="line">Line Style</InputLabel>
              <Select
                labelId="line"
                id="line-select"
                value={linkConfig.lineStyle}
                label="Line Style"
                onChange={(e) =>
                  setLinkConfig({
                    ...linkConfig,
                    lineStyle: e.target.value as 'dashed' | 'solid' | 'dashedDot',
                  })
                }
              >
                <MenuItem value="solid">Solid</MenuItem>
                <MenuItem value="dashed">Dashed</MenuItem>
                <MenuItem value="dashedDot">Dashed Dot</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      }
      dialogActions={
        <>
          <Button
            onClick={() => {
              close();
              setLinkConfig({
                color: '#000000',
                strokeWidth: 2,
                animated: false,
                animationSpeed: 50,
                flowBackgroundColor: '#000000',
                flowBackgroundWidth: 5,
                endStyle: 'arrow',
                lineStyle: 'solid' as keyof typeof lineStyles,
              });
            }}
            variant="outlined"
          >
            Cancel
          </Button>
          <Button onClick={applyLinkSettings} variant="contained" color="primary">
            Apply
          </Button>
        </>
      }
    />
  );
};

export default LinkConfigModal;
