import { dia } from '@joint/core';
import {
  Box,
  FormControl,
  FormControlLabel,
  FormLabel,
  Radio,
  RadioGroup,
  TextField,
} from '@mui/material';
import { ChangeEvent, useState } from 'react';
import LiquidTank from '../LiquidTank';

type LiquidTankSettingsProps = {
  selectedElement: dia.Element<dia.Element.Attributes, dia.ModelSetOptions>;
  handleAttrChangeElement: (attr: string, value: string) => void;
};

const LiquidTankSettings = ({
  selectedElement,
  handleAttrChangeElement,
}: LiquidTankSettingsProps) => {
  const [rangePicker, setRangePicker] = useState(() => {
    if (selectedElement instanceof LiquidTank) {
      return selectedElement.rangePicker;
    }
    return [];
  });

  const [maxCapacity, setMaxCapacity] = useState(() => {
    if (selectedElement instanceof LiquidTank) {
      return selectedElement.maxCapacity;
    }
    return 0;
  });

  // Early return if the selected element is not a LiquidTank
  if (!(selectedElement instanceof LiquidTank)) {
    return null;
  }

  const updateRangePicker = (
    e: ChangeEvent<HTMLInputElement | { name?: string | undefined; value: unknown }>,
    index: number,
    field: 'value' | 'operator',
  ) => {
    const updatedRangePicker = rangePicker.map((range, i) =>
      i === index
        ? { ...range, [field]: field === 'value' ? Number(e.target.value) : e.target.value }
        : range,
    );

    setRangePicker(updatedRangePicker);
    selectedElement.updateRangePicker(updatedRangePicker);
    selectedElement.prop('data/range', updatedRangePicker);
    selectedElement.level = selectedElement.level; // Update the level to reflect the new range settings in the SVG
  };

  const handleAddRangePicker = () => {
    const newRange: {
      value: number;
      color: string;
      operator: '>' | '>=' | '<' | '<=' | '==' | '!=';
    } = {
      value: 0,
      color: '#ffa500',
      operator: '<=',
    };
    const updatedRangePicker = [...rangePicker, newRange];
    setRangePicker(updatedRangePicker);
    selectedElement.updateRangePicker(updatedRangePicker);
    selectedElement.prop('data/range', updatedRangePicker);
    selectedElement.level = selectedElement.level; // Update the level to reflect the new range settings in the SVG
  };

  const handleDeleteRangePicker = (index: number) => {
    const updatedRangePicker = rangePicker.filter((_, i) => i !== index);
    setRangePicker(updatedRangePicker);
    selectedElement.updateRangePicker(updatedRangePicker);
    selectedElement.prop('data/range', updatedRangePicker);
    selectedElement.level = selectedElement.level; // Update the level to reflect the new range settings in the SVG
  };

  const handleColorChange = (e: ChangeEvent<HTMLInputElement>, rangeValue: number) => {
    const updatedRangePicker = rangePicker.map((r) =>
      r.value === rangeValue ? { ...r, color: e.target.value } : r,
    );

    setRangePicker(updatedRangePicker);
    selectedElement.updateRangePicker(updatedRangePicker);
    selectedElement.prop('data/range', updatedRangePicker);
    selectedElement.prop('data/color', {
      ...updatedRangePicker.reduce((acc, r) => ({ ...acc, [r.value]: r.color }), {}),
    });
    selectedElement.level = selectedElement.level; // Update the level to reflect the new color settings in the SVG
  };

  const handleMaxCapacityChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newMaxCapacity = Math.min(Number(e.target.value) || 0, 100000);
    if (newMaxCapacity !== maxCapacity) {
      setMaxCapacity(newMaxCapacity);
      handleAttrChangeElement('maxCapacity', newMaxCapacity.toString());
      selectedElement.updateMaxCapacity(newMaxCapacity);
      selectedElement.prop('data/maxCapacity', newMaxCapacity);
      // Ensure the level is updated
      selectedElement.level = selectedElement.level;
    }
  };

  return (
    <>
      <Box mb={2}>
        <FormControl component="fieldset" fullWidth>
          <FormLabel component="legend">Direction</FormLabel>
          <RadioGroup
            name="direction"
            value={selectedElement.barSettings.direction ?? 'horizontal'}
            onChange={(e) => {
              handleAttrChangeElement('direction', e.target.value);
              selectedElement.updateDirection(e.target.value as 'horizontal' | 'vertical');
              selectedElement.prop('data/direction', e.target.value);
              selectedElement.level = selectedElement.level; // Update the level to reflect the new direction in the SVG
            }}
            row // Aligns radio buttons horizontally, remove this for vertical alignment
          >
            <FormControlLabel value="vertical" control={<Radio />} label="Vertical" />
            <FormControlLabel value="horizontal" control={<Radio />} label="Horizontal" />
          </RadioGroup>
        </FormControl>
      </Box>
      <Box mt={2} mb={2}>
        <FormControl component="fieldset" fullWidth>
          <FormLabel component="legend">Maximum Capacity</FormLabel>
          <TextField
            name="maxCapacity"
            type="number"
            value={maxCapacity}
            inputProps={{ min: 0, max: 100000 }}
            onChange={handleMaxCapacityChange}
          />
        </FormControl>
      </Box>

      {/* <Box mt={2} mb={2}>
        <Button variant="contained" color="primary" onClick={handleAddRangePicker}>
          Add Range Picker
        </Button>
      </Box> */}
      {/* <Grid container spacing={2} mb={2}>
        {rangePicker.map((range, index) => (
          <Fragment key={index}>
            <Grid item xs={4}>
              <FormControl component="fieldset" fullWidth>
                <FormLabel component="legend">{`Limit ${index + 1}`}</FormLabel>
                <TextField
                  name={`limit-${index}`}
                  type="number"
                  value={range.value}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => {
                    handleAttrChangeElement(`range-value-${index}`, e.target.value);
                    updateRangePicker(e, index, 'value');
                  }}
                />
              </FormControl>
            </Grid>
            <Grid item xs={3}>
              <FormLabel component="legend">{'Cond.'}</FormLabel>
              <Select
                name={`operator-${index}`}
                value={range.operator}
                onChange={(e) => {
                  updateRangePicker(
                    e as ChangeEvent<{ name?: string; value: unknown }>,
                    index,
                    'operator',
                  );
                }}
                fullWidth
              >
                <MenuItem value="<=">{'<='}</MenuItem>
                <MenuItem value="<">{'<'}</MenuItem>
                <MenuItem value=">=">{'>='}</MenuItem>
                <MenuItem value=">">{'>'}</MenuItem>
                <MenuItem value="==">{'=='}</MenuItem>
                <MenuItem value="!=">{'!='}</MenuItem>
              </Select>
            </Grid>
            <Grid item xs={4}>
              <FormLabel component="legend">{`Limit ${index + 1} Color`}</FormLabel>
              <TextField
                name={`color-${index}`}
                fullWidth
                type="color"
                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                  handleAttrChangeElement(`range-color-${index}`, e.target.value);
                  handleColorChange(e, range.value);
                }}
                value={range.color ?? '#ffa500'}
                defaultValue={range.color}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={1} display="flex" justifyContent={'center'} alignItems="center" p={0}>
              <IconButton color="error" onClick={() => handleDeleteRangePicker(index)}>
                <DeleteIcon />
              </IconButton>
            </Grid>
          </Fragment>
        ))}
      </Grid> */}
    </>
  );
};

export default LiquidTankSettings;
