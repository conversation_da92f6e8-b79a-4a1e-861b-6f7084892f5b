import { dia, shapes as DiagramShapes } from '@joint/core';
import { Box, Button } from '@mui/material';
import { useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import CustomDialog from '~/components/common/CustomDialog';
import { getCurrentGraph } from '~/redux/selectors/diagramSelector';
import { diagramSlice } from '~/redux/slices/diagramSlice';
import { cellNamespace } from '~/types/diagram';
import CreateElement from '../../CreateElement';
import SaveDiagramNameModal from './SaveDiagramNameModal';
import { CATEGORY_E } from '../../Palette/Palette';

type NewElementDialogProps = {
  createNewElement: boolean;
  setCreateNewElement: React.Dispatch<React.SetStateAction<boolean>>;
};
const NewElementDialog = ({ createNewElement, setCreateNewElement }: NewElementDialogProps) => {
  const dispatch = useDispatch();
  const graphState = useSelector(getCurrentGraph);
  const graphRef = useRef<dia.Graph | null>(graphState);
  const graph = useRef(new dia.Graph({}, { cellNamespace: cellNamespace })).current;
  const [openSaveDiagramNameModal, setOpenSaveDiagramNameModal] = useState(false);
  const [diagramName, setDiagramName] = useState('');

  const handleSaveDiagram = () => {
    if (!diagramName) {
      console.warn('Diagram name is required to save.');
      return;
    }

    const groupElement = new DiagramShapes.standard.Rectangle();
    groupElement.attr({
      body: {
        fill: 'transparent',
        stroke: '#e1e1e1',
        strokeWidth: 1,
      },
      label: {
        text: diagramName,
        fontSize: 12,
        fill: '#000',
        textAnchor: 'middle',
        refY: -10, // Position the label above the rectangle
        yAlignment: 'middle',
      },
    });
    groupElement.set('type', 'groupElement');

    // Add the group to the graph
    graph.addCell(groupElement);

    // Embed only top-level elements into the group
    const topLevelElements = graph.getElements().filter((element) => !element.getParentCell());
    topLevelElements.forEach((element) => {
      if (element !== groupElement) {
        groupElement.embed(element);
      }
    });

    // Adjust the group's size to fit its children
    groupElement.fitEmbeds({ padding: 10 });

    // Serialize the graph to JSON
    const json = graph.toJSON();

    // Dispatch to Redux to add to Custom Elements
    dispatch(
      diagramSlice.actions.addCustomElement({
        name: diagramName, // Diagram name from user input
        type: `uploadedDiagram-${Date.now()}`, // Unique identifier
        category: CATEGORY_E.CUSTOM, // Add to Custom Elements
        data: json, // Serialized graph data
      }),
    );

    // Close the modal
    graph.clear();
    setOpenSaveDiagramNameModal(false);
    setCreateNewElement(false);
    setDiagramName('');
  };

  return (
    <>
      <CustomDialog
        open={createNewElement}
        title={<>Create Element</>}
        fullScreen
        onClose={() => {
          setCreateNewElement(false);
        }}
        dialogActions={
          <Box sx={{ display: 'flex', width: '100%', gap: 2 }}>
            <Button
              color="primary"
              variant="contained"
              fullWidth
              onClick={() => {
                if (!graphRef.current) {
                  const graph = new dia.Graph(
                    {},
                    {
                      cellNamespace: cellNamespace,
                    },
                  );
                  graphRef.current = graph;
                }
                graphRef.current?.addCells(
                  graph.getCells().filter((cell) => cell.get('type') !== 'selectionHighlight'),
                );
                dispatch(diagramSlice.actions.setDiagram(graphRef.current!));
                setOpenSaveDiagramNameModal(true);
              }}
            >
              Add Element
            </Button>

            <Button
              color="error"
              variant="outlined"
              fullWidth
              onClick={() => {
                setCreateNewElement(false);
                graph.clear();
              }}
            >
              Cancel
            </Button>
          </Box>
        }
        content={<CreateElement graph={graph} />}
      />

      <SaveDiagramNameModal
        open={openSaveDiagramNameModal}
        close={() => setOpenSaveDiagramNameModal(false)}
        diagramName={diagramName}
        setDiagramName={setDiagramName}
        handleSaveDiagram={handleSaveDiagram}
      />
    </>
  );
};

export default NewElementDialog;
