import { dia } from '@joint/core';
import { Box, FormControlLabel, Radio, RadioGroup, TextField, Typography } from '@mui/material';
import Progress from '~/components/CreateElement/Progress';
export type ProgressSettingsProps = {
  selectedElement: dia.Element<dia.Element.Attributes, dia.ModelSetOptions>;
  handleAttrChangeElement: (attr: string, value: string) => void;
};

const ProgressSettings = ({ selectedElement, handleAttrChangeElement }: ProgressSettingsProps) => {
  if (!(selectedElement instanceof Progress)) {
    return null;
  }
  return (
    <>
      <Box mb={2}>
        <Typography gutterBottom>Orientation</Typography>
        <RadioGroup
          name="orientation"
          value={selectedElement.orientation}
          onChange={(e) => {
            selectedElement.orientation = e.target.value as 'vertical' | 'horizontal';
            handleAttrChangeElement('orientation', e.target.value);
          }}
          row
        >
          <FormControlLabel value="vertical" control={<Radio />} label="Vertical" />
          <FormControlLabel value="horizontal" control={<Radio />} label="Horizontal" />
        </RadioGroup>
      </Box>
      <Box mb={2}>
        <Typography gutterBottom>Fill Style</Typography>
        <RadioGroup
          name="style"
          value={selectedElement.style}
          onChange={(e) => {
            handleAttrChangeElement('style', e.target.value);
            selectedElement.style = e.target.value as 'bar' | 'solid';
            selectedElement.prop('data/style', e.target.value as 'bar' | 'solid');
          }}
          row
        >
          <FormControlLabel value="bar" control={<Radio />} label="Bar" />
          <FormControlLabel value="solid" control={<Radio />} label="Solid" />
        </RadioGroup>
      </Box>
      <Box mb={2}>
        <TextField
          label="Maximum Capacity"
          type="number"
          value={selectedElement?.maxCapacity ?? 100}
          name="maxCapacity"
          fullWidth
          inputProps={{
            min: 1,
            max: 100000,
          }}
          onChange={(e) => {
            const value = Math.min(Number(e.target.value), 100000); // Ensure value does not exceed 100000
            selectedElement.maxCapacityValue = value;
            handleAttrChangeElement('maxCapacity', value.toString());
          }}
        />
      </Box>
    </>
  );
};

export default ProgressSettings;
