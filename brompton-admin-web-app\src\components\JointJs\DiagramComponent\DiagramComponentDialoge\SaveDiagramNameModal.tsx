import {
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  TextField,
} from '@mui/material';
import React, { <PERSON><PERSON>atch, FC, SetStateAction } from 'react';

interface ISaveDiagramNameModal {
  open: boolean;
  close: () => void;
  diagramName: string;
  setDiagramName: Dispatch<SetStateAction<string>>;
  handleSaveDiagram: () => void;
}

const SaveDiagramNameModal: FC<ISaveDiagramNameModal> = ({
  open,
  close,
  diagramName,
  setDiagramName,
  handleSaveDiagram,
}) => {
  return (
    <Dialog
      open={open}
      onClose={() => {
        setDiagramName('');
        close();
      }}
      disableEnforceFocus
    >
      <DialogTitle>Save Element</DialogTitle>
      <DialogContent>
        <TextField
          required
          autoFocus
          margin="dense"
          label="Diagram Name"
          type="text"
          fullWidth
          value={diagramName}
          onChange={(e) => setDiagramName(e.target.value)}
        />
      </DialogContent>
      <DialogActions>
        <Button
          disabled={!diagramName}
          onClick={() => {
            handleSaveDiagram();
            close();
          }} // Save and download the diagram, then close the modal
          color="primary"
          variant="contained"
        >
          Save
        </Button>
        <Button
          onClick={() => {
            setDiagramName('');
            close();
          }}
          color="error"
          variant="outlined"
        >
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SaveDiagramNameModal;
