// import { shapes } from '@joint/core';

// // Define a mixin type that includes rangePicker and the related methods
// type RangePickerMixin = {
//   rangePicker: Array<{ value: number; color: string }>;
//   addRange(value: number, color: string): void;
//   getRanges(): Array<{ value: number; color: string }>;
// };

// // The mixin object containing the methods and initializing `rangePicker`
// const rangePickerMixin: Partial<RangePickerMixin> = {
//   addRange(value, color) {
//     if (!(this as any).rangePicker) {
//       (this as any).rangePicker = [];
//     }
//     (this as any).rangePicker.push({ value, color });
//   },

//   getRanges() {
//     if (!(this as any).rangePicker) {
//       (this as any).rangePicker = [];
//     }
//     return (this as any).rangePicker;
//   },
// };

// // Function to extend all standard shapes to include rangePicker functionality
// export default function extendStandardShapes() {
//   // List of standard shapes that you want to extend
//   const standardShapes = [
//     shapes.standard.Rectangle,
//     shapes.standard.Ellipse,
//     shapes.standard.Polygon,
//     shapes.standard.Circle,
//     shapes.standard.Link,
//     // Add other standard shapes you want to extend here
//   ];

//   // Iterate over each shape class and extend it with the mixin
//   standardShapes.forEach((ShapeClass) => {
//     // Store the original initialize method
//     const originalInitialize = ShapeClass.prototype.initialize;

//     // Override the initialize method to add the rangePicker property
//     ShapeClass.prototype.initialize = function (...args: any[]) {
//       // Call the original initialize method
//       originalInitialize.call(this, ...args);

//       // Initialize the rangePicker array
//       (this as any).rangePicker = [];
//     };

//     // Use Object.assign to add the mixin methods to the prototype
//     Object.assign(ShapeClass.prototype, rangePickerMixin);
//   });
// }

// // // Usage example
// // extendStandardShapes();

// // // Create an instance of a standard shape and use the range picker functionality
// // const rect = new shapes.standard.Rectangle();

// // // Using type assertion to access custom properties
// // (rect as any).addRange(50, 'red');
// // (rect as any).addRange(100, 'blue');

// // const circle = new shapes.standard.Circle();

// // console.log((rect as any).getRanges()); // Output: [ { value: 50, color: 'red' }, { value: 100, color: 'blue' } ]
// import { dia, shapes } from '@joint/core';

// export default class ExtendedStandard extends dia.Element<RectangleAttributes | CircleAttributes> {}
import { dia, shapes, util } from '@joint/core';

// Define the rangePicker type.
interface RangePickerAttribute {
  rangePicker: Array<{
    value: number;
    color: string;
  }>;
}

// Define attributes that extend existing rectangle, circle, ellipse, and cylinder shapes.
type RectangleAttributes = shapes.standard.Rectangle['attributes'] & RangePickerAttribute;
type CircleAttributes = shapes.standard.Circle['attributes'] & RangePickerAttribute;
type EllipseAttributes = shapes.standard.Ellipse['attributes'] & RangePickerAttribute;
type CylinderAttributes = shapes.standard.Cylinder['attributes'] & RangePickerAttribute;

// Base class for adding RangePicker functionality
class ShapeWithRangePicker<T extends dia.Element> extends dia.Element {
  rangePicker: Array<{
    value: number;
    color: string;
  }>;

  constructor(attributes?: any, options?: dia.Graph.Options) {
    super(attributes, options);
    this.rangePicker = attributes?.rangePicker ?? [];
  }
}

// Separate classes for each shape
export class ExtendedRectangle extends ShapeWithRangePicker<shapes.standard.Rectangle> {
  constructor(attributes: RectangleAttributes, options?: dia.Graph.Options) {
    super(
      {
        ...shapes.standard.Rectangle.prototype.defaults,
        ...attributes,
      },
      options,
    );
  }
}

export class ExtendedCircle extends ShapeWithRangePicker<shapes.standard.Circle> {
  constructor(attributes: CircleAttributes, options?: dia.Graph.Options) {
    super(
      {
        ...shapes.standard.Circle.prototype.defaults,
        ...attributes,
        attrs: {
          body: {
            fill: 'lightgreen',
            stroke: 'black',
            strokeWidth: 2,
          },
          label: {
            text: 'Circle',
            fill: 'black',
          },
        },
      },
      options,
    );
  }
  defaults(): Partial<dia.Element.Attributes> {
    return {
      ...super.defaults,
      type: 'standard.Circle',
    };
  }
  preinitialize(attributes?: dia.Element.Attributes | undefined, options?: any): void {
    this.markup = util.svg/**/ ``;
  }
}

export class ExtendedEllipse extends ShapeWithRangePicker<shapes.standard.Ellipse> {
  constructor(attributes: EllipseAttributes, options?: dia.Graph.Options) {
    super(
      {
        ...shapes.standard.Ellipse.prototype.defaults,
        ...attributes,
      },
      options,
    );
  }
}

export class ExtendedCylinder extends ShapeWithRangePicker<shapes.standard.Cylinder> {
  constructor(attributes: CylinderAttributes, options?: dia.Graph.Options) {
    super(
      {
        ...shapes.standard.Cylinder.prototype.defaults,
        ...attributes,
      },
      options,
    );
  }
}
