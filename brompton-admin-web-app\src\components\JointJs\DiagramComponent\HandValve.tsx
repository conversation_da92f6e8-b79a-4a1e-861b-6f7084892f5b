import { dia, util } from '@joint/core';

export class HandValve extends dia.Element {
  valLevel = 0;
  colors: { low: string; medium: string; high: string; full: string };
  constructor(
    attributes = {},
    options = {},
    level = 0,
    color = { low: '#ffa500', medium: '#ffff00', high: '#008000', full: '#ff0000' },
  ) {
    super(attributes, options);
    this.valLevel = level;
    this.colors = {
      ...color,
    };
  }
  defaults(): Partial<dia.Element.Attributes> {
    return {
      ...super.defaults,
      type: 'HandValve',
      size: {
        width: 120,
        height: 80,
      },
      power: 0,
      attrs: {
        root: {
          magnetSelector: 'body',
        },
        body: {
          cx: 'calc(w / 2)', // ✅ Dynamically center the body
          cy: 'calc(h / 2)',
          rx: 'calc(w / 4)', // ✅ Resize dynamically
          ry: 'calc(h / 4)',
          stroke: 'gray',
          strokeWidth: 2,
          fill: {
            type: 'radialGradient',
            stops: [
              { offset: '70%', color: 'white' },
              { offset: '100%', color: 'gray' },
            ],
          },
        },
        stem: {
          width: 'calc(w / 12)', // ✅ Relative to width
          height: 'calc(h / 4)',
          x: 'calc(w / 2 - calc(w / 24))',
          y: 'calc(h / 39)',
          stroke: '#333',
          strokeWidth: 2,
          fill: '#555',
        },
        handwheel: {
          width: 'calc(w / 2.5)', // ✅ Scales with width
          height: 'calc(h / 10)',
          x: 'calc(w / 2 - calc(w / 5))',
          y: 'calc(h / 44)',
          stroke: '#333',
          strokeWidth: 2,
          rx: 5,
          ry: 5,
          fill: '#666',
        },
        label: {
          textAnchor: 'middle',
          textVerticalAnchor: 'top',
          x: 'calc(0.5*w)',
          y: 'calc(h-25)',
          fontSize: '14',
          fontFamily: 'sans-serif',
          fill: '#350100',
        },
      },
    };
  }

  get level() {
    return this.get('level') || 0;
  }

  set level(level) {
    const newLevel = Math.max(0, Math.min(100, level));
    this.set('level', newLevel);
    // Update border color based on the level
    let borderColor;
    if (newLevel > 20 && newLevel <= 40) {
      borderColor = this.colors.medium;
    } else if (newLevel > 40 && newLevel <= 80) {
      borderColor = this.colors.high;
    } else if (newLevel > 80) {
      borderColor = this.colors.full;
    }

    // Set the stroke attribute dynamically
    this.attr('body/stroke', borderColor);
    this.attr('stem/stroke', borderColor);
    this.attr('handwheel/stroke', borderColor);
  }

  preinitialize() {
    this.markup = util.svg/* xml */ `
        <ellipse @selector="body" />
        <rect @selector="stem" />
        <rect @selector="handwheel" />
        <text @selector="label" />
      `;
  }
}
