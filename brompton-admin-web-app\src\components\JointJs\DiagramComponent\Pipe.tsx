import { dia, util, linkTools } from '@joint/core';

class <PERSON>pe extends dia.Link {
  private paperInstance?: dia.Paper;
  defaults(): Partial<dia.Element.Attributes> {
    return {
      ...super.defaults,
      type: 'Pipe',
      z: -1,
      router: { name: 'manhattan' },
      flow: 1,
      animation: {
        enabled: false,
        speed: 1,
      },
      attrs: {
        liquid: {
          connection: true,
          strokeLinejoin: 'normal',
          strokeLinecap: 'square',
        },
        connector: { name: 'normal' },
        line: {
          connection: true,
          strokeLinejoin: 'normal',
          strokeLinecap: 'normal',
        },
        outline: {
          connection: true,
          strokeLinejoin: 'normal',
          strokeLinecap: 'normal',
        },
      },
    };
  }

  preinitialize() {
    this.markup = util.svg/* xml */ `
              <path @selector="outline" fill="none"/>
              <path @selector="line" fill="none"/>
              <path @selector="liquid" fill="none"/>
          `;
  }

  setPaper(paper: dia.Paper) {
    this.paperInstance = paper;
    this.addTools();
  }

  initialize(attributes: any, options: any) {
    super.initialize(attributes, options);

    this.addTools();
  }

  addTools() {
    if (!this.paperInstance) return;

    const vertexTool = new linkTools.Vertices({
      snapRadius: 10,
    });

    const toolsView = new dia.ToolsView({
      tools: [vertexTool],
    });

    const linkView = this.findView(this.paperInstance);
    linkView?.addTools(toolsView);
  }

  startAnimation() {
    const liquid = this.attr('liquid');
    const animation = this.get('animation');

    if (animation?.enabled) {
      const speed = animation.speed || 1;
      const direction = this.get('direction') || 'forward';

      const step = () => {
        if (animation?.enabled) {
          const offset = parseFloat(liquid.strokeDashoffset) || 0;

          if (direction === 'forward') {
            this.attr('liquid/strokeDashoffset', offset - speed);
          } else if (direction === 'backward') {
            this.attr('liquid/strokeDashoffset', offset + speed);
          } else if (direction === 'bidirectional') {
            // Sinusoidal motion for bidirectional flow
            const sinusoidalOffset = Math.sin(Date.now() / 100) * 20;
            this.attr('liquid/strokeDashoffset', sinusoidalOffset);
          }

          requestAnimationFrame(step);
        }
      };
      requestAnimationFrame(step);
    }
  }

  stopAnimation() {
    this.attr('liquid/strokeDashoffset', 0); // Reset offset
    this.set('animation', { ...this.get('animation'), enabled: false }); // Disable animation
  }
}

export default Pipe;
