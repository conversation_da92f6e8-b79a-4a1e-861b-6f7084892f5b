import { dia, util } from '@joint/core';

const r = 30;
const d = 10;
const l = (3 * r) / 4;
const step = 20;

export default class Pump extends dia.Element {
  private rotationAngle = 0;
  rotationAnimation: 'on' | 'off' = 'on';
  rotationSpeed = 1.0; // Rotation speed multiplier
  private animationFrameId: number | null = null; // Track the animation frame for stopping

  constructor(attributes = {}, options = {}) {
    super(attributes, options);
    this.updateIndicator(); // Initialize indicator color
    this.startRotation(); // Automatically start rotation if the animation is 'on'
  }

  defaults(): Partial<dia.Element.Attributes> {
    return {
      ...super.defaults,
      type: 'Pump',
      size: {
        width: 100,
        height: 100,
      },
      power: 0,
      attrs: {
        root: {
          magnetSelector: 'body',
        },
        body: {
          rx: 'calc(w / 2)',
          ry: 'calc(h / 2)',
          cx: 'calc(w / 2)',
          cy: 'calc(h / 2)',
          stroke: 'gray',
          strokeWidth: 2,
          fill: 'lightgray',
        },
        label: {
          // text: 'Pump',
          textAnchor: 'middle',
          textVerticalAnchor: 'top',
          x: 'calc(0.5*w)',
          y: 'calc(h+10)',
          fontSize: 14,
          fontFamily: 'sans-serif',
          fill: '#350100',
        },
        rotorGroup: {
          transform: 'translate(calc(w / 2), calc(h / 2)) rotate(0)',
          event: 'element:power:click',
          cursor: 'pointer',
        },
        rotorFrame: {
          r: 'calc(w / 2.5)',
          fill: '#eee',
          stroke: '#666',
          strokeWidth: 2,
        },
        rotorBackground: {
          r: 'calc(w / 3)',
          fill: '#777',
          stroke: '#222',
          strokeWidth: 1,
          style: {
            transition: 'fill 0.5s ease-in-out',
          },
        },
        rotor: {
          d: `
            M 0 0 V calc(h / 3.33) l calc(-1 * w / 10) calc(-1 * h / 4) Z 
            M 0 0 V calc(-1 * h / 3.33) l calc(w / 10) calc(h / 4) Z
            M 0 0 H calc(w / 3.33) l calc(-1 * h / 4) calc(w / 10) Z 
            M 0 0 H calc(-1 * w / 3.33) l calc(h / 4) calc(-1 * w / 10) Z
          `,
          stroke: '#222',
          strokeWidth: 3,
          fill: '#bbb',
        },
        indicator: {
          r: 'calc(w / 20)',
          cx: 'calc(w - calc(w / 10))',
          cy: 'calc(h - calc(h / 10))',
          fill: 'green',
        },
      },
    };
  }

  preinitialize() {
    this.markup = util.svg/* xml */ `
      <ellipse @selector="body" />
      <g @selector="rotorGroup">
          <circle @selector="rotorFrame" />
          <circle @selector="rotorBackground" />
          <path @selector="rotor" />
      </g>
      <text @selector="label" />
      <circle @selector="indicator" /> <!-- Added indicator circle -->
    `;
  }

  get power() {
    return this.get('power') || 0;
  }

  set power(value) {
    this.set('power', value);
  }

  private startRotation() {
    // Cancel any existing animation before starting a new one
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }

    const rotateRotor = () => {
      if (this.rotationAnimation === 'on') {
        // Increase the rotation angle, considering rotationSpeed
        this.rotationAngle = (this.rotationAngle + 5 * this.rotationSpeed) % 360;

        // Apply the rotation to rotorGroup
        this.attr(
          'rotorGroup/transform',
          `translate(calc(w/2),calc(h/2)) rotate(${this.rotationAngle})`,
        );

        // Use requestAnimationFrame to create a smooth animation
        this.animationFrameId = requestAnimationFrame(rotateRotor);
      }
    };

    // Start the animation only if rotationAnimation is 'on'
    if (this.rotationAnimation === 'on') {
      rotateRotor();
    }
  }

  changeRotation() {
    // Update the rotationAnimation state
    this.rotationAnimation = this.rotationAnimation === 'on' ? 'off' : 'on';
    // Update the indicator color accordingly
    this.updateIndicator();

    // If turning on, start the rotation
    if (this.rotationAnimation === 'on') {
      this.startRotation();
      this.rotationAnimation = 'on';
    } else if (this.rotationAnimation === 'off' && this.animationFrameId) {
      // Cancel the animation if turning off
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
      this.rotationAnimation = 'off';
    }
  }

  toggleRotation(on_off?: 'on' | 'off') {
    // Determine the new rotation state based on the argument or toggle behavior
    if (on_off) {
      this.rotationAnimation = on_off;
    } else {
      this.rotationAnimation = this.rotationAnimation === 'on' ? 'off' : 'on';
    }

    // Update indicator UI
    this.updateIndicator();

    if (this.rotationAnimation === 'on') {
      this.startRotation();
    } else if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  setRotationSpeed(speed: number) {
    // Allow changing the speed dynamically
    if (speed > 0) {
      this.rotationSpeed = speed;
    }
  }

  private updateIndicator() {
    // Update the color of the indicator based on rotationAnimation state
    const color = this.rotationAnimation === 'on' ? 'green' : 'red';
    this.attr('indicator/fill', color);
  }
}
