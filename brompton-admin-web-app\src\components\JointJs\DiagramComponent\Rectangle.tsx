import { shapes } from '@joint/core';

export default class Rectangle extends shapes.standard.Rectangle {
  rangePicker: Array<{ value: number; color: string }>;

  constructor(attributes = {}, options = {}) {
    // Call the base class constructor
    super(attributes, options);

    // Initialize the rangePicker property
    this.rangePicker = [];
  }

  // Methods to interact with rangePicker
  addRange(value: number, color: string) {
    this.rangePicker.push({ value, color });
  }

  getRanges() {
    return this.rangePicker;
  }
}
