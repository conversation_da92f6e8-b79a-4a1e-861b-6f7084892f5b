import ReactDOM from 'react-dom/client';
import React from 'react';
import Plot from 'react-plotly.js';
import { dia } from '@joint/core';
import { Box, Card, CardContent, Typography } from '@mui/material';

export const StatsChartElement = (label: string): dia.Element => {
  const BarChartElement = dia.Element.define(
    'custom.BarChartElement',
    {
      attrs: {
        body: {
          refWidth: '100%',
          refHeight: '100%',
        },
      },
    },
    {
      markup: `
        <g class="rotatable">
          <rect class="body" />
          <foreignObject width="100%" height="100%">
            <div xmlns="http://www.w3.org/1999/xhtml" class="bar-chart-container">
              <div id="bar-chart" style="width: 100%; height: 100%;"></div>
            </div>
          </foreignObject>
        </g>
      `,
    },
  );

  const element = new BarChartElement({
    size: { width: 300, height: 200 },
    attrs: {
      body: { fill: 'transparent' },
    },
  });

  // Use ReactDOM.createRoot to render the Plotly chart inside the container
  setTimeout(() => {
    const container = document.querySelector('.bar-chart-container #bar-chart');
    if (container) {
      const root = ReactDOM.createRoot(container); // Create a root
      root.render(
        <>
          <Box
            id={'stats-widget'}
            sx={{
              display: 'flex',
              height: '100%',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                height: '100%',
                width: '100%',
                overflowX: 'auto',
                overflowY: 'hidden',
              }}
            >
              <Card
                variant="outlined"
                sx={{
                  marginRight: '10px',
                  padding: 0,
                  border: '1px solid gray',
                  minWidth: 60,
                }}
              >
                <CardContent
                  sx={{
                    padding: '8px',
                    paddingBottom: '8px !important',
                    color: 'green',
                  }}
                >
                  <Typography
                    variant="h5"
                    component="div"
                    sx={{
                      color: 'green',
                      fontSize: '20px',
                      fontWeight: 'bold',
                      textAlign: 'center',
                    }}
                  >
                    15
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: 14,
                      textAlign: 'center',
                      display: 'block',
                      color: 'green',
                    }}
                    color="text.secondary"
                    gutterBottom
                  >
                    Min
                  </Typography>
                </CardContent>
              </Card>

              <Card
                variant="outlined"
                sx={{
                  marginRight: '10px',
                  padding: 0,
                  border: '1px solid gray',
                  minWidth: 60,
                }}
              >
                <CardContent
                  sx={{
                    padding: '8px',
                    paddingBottom: '8px !important',
                    color: 'red',
                  }}
                >
                  <Typography
                    variant="h5"
                    component="div"
                    sx={{
                      color: 'red',
                      fontSize: '20px',
                      fontWeight: 'bold',
                      textAlign: 'center',
                    }}
                  >
                    45
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: 14,
                      textAlign: 'center',
                      display: 'block',
                      color: 'red',
                    }}
                    color="text.secondary"
                    gutterBottom
                  >
                    Max
                  </Typography>
                </CardContent>
              </Card>

              <Card
                variant="outlined"
                sx={{
                  marginRight: '10px',
                  padding: 0,
                  border: '1px solid gray',
                  minWidth: 60,
                }}
              >
                <CardContent
                  sx={{
                    padding: '8px',
                    paddingBottom: '8px !important',
                    color: 'purple',
                  }}
                >
                  <Typography
                    variant="h5"
                    component="div"
                    sx={{
                      color: 'purple',
                      fontSize: '20px',
                      fontWeight: 'bold',
                      textAlign: 'center',
                    }}
                  >
                    30
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: 14,
                      textAlign: 'center',
                      display: 'block',
                      color: 'purple',
                    }}
                    color="text.secondary"
                    gutterBottom
                  >
                    Avg
                  </Typography>
                </CardContent>
              </Card>

              <Card
                variant="outlined"
                sx={{
                  marginRight: '10px',
                  padding: 0,
                  border: '1px solid gray',
                  minWidth: 60,
                }}
              >
                <CardContent
                  sx={{
                    padding: '8px',
                    paddingBottom: '8px !important',
                    color: 'orange',
                  }}
                >
                  <Typography
                    variant="h5"
                    component="div"
                    sx={{
                      color: 'orange',
                      fontSize: '20px',
                      fontWeight: 'bold',
                      textAlign: 'center',
                    }}
                  >
                    30
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: 14,
                      textAlign: 'center',
                      display: 'block',
                      color: 'orange',
                    }}
                    color="text.secondary"
                    gutterBottom
                  >
                    Delta
                  </Typography>
                </CardContent>
              </Card>

              <Card
                variant="outlined"
                sx={{
                  marginRight: '10px',
                  padding: 0,
                  border: '1px solid gray',
                  minWidth: 60,
                  textAlign: 'center',
                }}
              >
                <CardContent
                  sx={{
                    padding: '8px',
                    paddingBottom: '8px !important',
                    color: 'blue',
                  }}
                >
                  <Typography
                    variant="h5"
                    component="div"
                    sx={{
                      color: 'blue',
                      fontSize: '20px',
                      fontWeight: 'bold',
                      textAlign: 'center',
                    }}
                  >
                    100
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: 14,
                      textAlign: 'center',
                      display: 'block',
                      color: 'blue',
                    }}
                    color="text.secondary"
                    gutterBottom
                  >
                    Sum
                  </Typography>
                </CardContent>
              </Card>

              <Card
                variant="outlined"
                sx={{
                  marginRight: '10px',
                  padding: 0,
                  border: '1px solid gray',
                  minWidth: 60,
                }}
              >
                <CardContent
                  sx={{
                    padding: '8px',
                    paddingBottom: '8px !important',
                    color: 'brown',
                  }}
                >
                  <Typography
                    variant="h5"
                    component="div"
                    sx={{
                      color: 'brown',
                      fontSize: '20px',
                      fontWeight: 'bold',
                      textAlign: 'center',
                    }}
                  >
                    50
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: 14,
                      textAlign: 'center',
                      display: 'block',
                      color: 'brown',
                    }}
                    color="text.secondary"
                    gutterBottom
                  >
                    Current
                  </Typography>
                </CardContent>
              </Card>
            </Box>
          </Box>
        </>,
      );
    }
  }, 500);

  return element;
};
