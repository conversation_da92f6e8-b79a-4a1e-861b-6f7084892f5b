import { dia } from '@joint/core';
import ReactDOM from 'react-dom/client';
import Plot from 'react-plotly.js';

export const TrendChartElement = (label: string): dia.Element => {
  const TrendChartElement = dia.Element.define(
    'custom.TrendChartElement',
    {
      attrs: {
        body: {
          refWidth: '100%',
          refHeight: '100%',
        },
      },
    },
    {
      markup: `
        <g class="rotatable">
          <rect class="body" />
          <foreignObject width="100%" height="100%">
            <div xmlns="http://www.w3.org/1999/xhtml" class="trend-chart-container">
              <div id="trend-chart" style="width: 100%; height: 100%;"></div>
            </div>
          </foreignObject>
        </g>
      `,
    },
  );

  const element = new TrendChartElement({
    size: { width: 300, height: 200 },
    attrs: {
      body: { fill: 'transparent' },
    },
  });

  // Use ReactDOM.createRoot to render the Plotly trend chart inside the container
  setTimeout(() => {
    const container = document.querySelector('.trend-chart-container #trend-chart');
    if (container) {
      const root = ReactDOM.createRoot(container); // Create a root
      root.render(
        <Plot
          data={[
            {
              x: ['Jan', 'Feb', 'Mar', 'Apr', 'May'], // Example x-axis values (e.g., months)
              y: [10, 15, 13, 17, 21], // Example y-axis values
              type: 'scatter', // Trend chart type
              mode: 'lines+markers', // Line with markers
              line: { shape: 'spline', color: 'blue' }, // Smooth line with custom color
              marker: { size: 8, color: 'blue' }, // Marker style
            },
          ]}
          config={{
            displayModeBar: false,
          }}
          layout={{
            width: 300,
            height: 200,
            title: label, // Dynamic chart title
            xaxis: {
              title: 'Months', // Label for x-axis
              showgrid: false, // Hide grid for x-axis
            },
            yaxis: {
              title: 'Values', // Label for y-axis
              showgrid: true, // Show grid for y-axis
            },
            margin: {
              l: 40,
              r: 10,
              t: 40,
              b: 40,
            },
          }}
        />,
      );
    }
  }, 500);

  return element;
};
