import { yupResolver } from '@hookform/resolvers/yup';
import { Al<PERSON>, Box, Button } from '@mui/material';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { useResetPasswordMutation } from '~/redux/api/usersApi';
import { ControlledTextField } from '~/shared/forms/components/ControlledTextField';
import { AlertMessage } from '~/shared/forms/types';
import { ResetPassword, resetPasswordSchema } from '~/types/users';

const ResetPasswordContainer = () => {
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const [resetPassword, { isError, isLoading, isSuccess, data, error }] =
    useResetPasswordMutation();

  useEffect(() => {
    if (isSuccess) {
      setAlertMessage({
        message: `Password reset successfully!`,
        severity: 'success',
      });
    }
    if (isError && error) {
      const err = error as CustomError;
      setAlertMessage({ message: err.data.exception ?? 'Server error', severity: 'error' });
    }
  }, [data, error, isError, isSuccess]);
  const { control, handleSubmit, reset } = useForm<ResetPassword>({
    defaultValues: {
      current_password: '',
      new_password: '',
      confirm_password: '',
    },
    resolver: yupResolver(resetPasswordSchema),
  });
  return (
    <Box>
      <form
        onSubmit={handleSubmit(async (data) => {
          try {
            // await onValidSubmit(data);
            await resetPassword(data);
            reset();
          } catch (err) {}
        })}
        noValidate
      >
        <ControlledTextField
          control={control}
          type="password"
          fieldName="current_password"
          label="Current Password"
          loading={isLoading}
          required
        />
        <ControlledTextField
          control={control}
          type="password"
          fieldName="new_password"
          label="New Password"
          loading={isLoading}
          required
        />
        <ControlledTextField
          control={control}
          type="password"
          fieldName="confirm_password"
          label="Confirm Password"
          loading={isLoading}
          required
        />
        <Button
          type="submit"
          variant="contained"
          size="large"
          sx={{ mt: 2, width: 200 }}
          disabled={isLoading}
        >
          Submit
        </Button>
        {alertMessage ? (
          <Alert severity={alertMessage.severity} sx={{ mt: 2 }}>
            {alertMessage.message}
          </Alert>
        ) : null}
      </form>
    </Box>
  );
};

export default ResetPasswordContainer;
