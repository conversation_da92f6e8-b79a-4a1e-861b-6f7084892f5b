import {
  Box,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  TextField,
  Typography,
} from '@mui/material';
import { SumDeltaWidgets, setSumDeltaWidgetSettings } from '~/types/widgets';

type SumDeltaProps = {
  selectedTitles: string[];
  settings: SumDeltaWidgets;
  setSettings: setSumDeltaWidgetSettings;
};
const SumDeltaSettings = ({ selectedTitles, settings, setSettings }: SumDeltaProps) => {
  const handleShowSum = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings((prevState) => ({
      ...prevState,
      showSum: event.target.checked,
    }));
  };
  const handleShowDelta = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings((prevState) => ({
      ...prevState,
      showDelta: event.target.checked,
    }));
  };
  const handleLabelChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings((prevState) => ({
      ...prevState,
      [event.target.name]: event.target.value,
    }));
  };
  return (
    <>
      {selectedTitles.length === 1 ? (
        <>
          <Divider />
          <Typography variant="h5" mt={2} mb={1}>
            Sum / Delta Configurations
          </Typography>
          <Box>
            <FormControlLabel
              control={
                <Checkbox checked={settings?.showSum} onChange={handleShowSum} name="showSum" />
              }
              label="Show Sum"
            />
          </Box>
          {settings?.showSum && (
            <FormControl fullWidth>
              <TextField
                name="sumLabel"
                onChange={handleLabelChange}
                defaultValue={settings.sumLabel ?? 'Sum'}
                value={settings.sumLabel}
                label="Sum Label"
                variant="outlined"
                margin="normal"
                fullWidth
              />
            </FormControl>
          )}
          <Box>
            <FormControlLabel
              control={
                <Checkbox
                  checked={settings?.showDelta}
                  onChange={handleShowDelta}
                  name="showDelta"
                />
              }
              label="Show Delta"
            />
          </Box>
          {settings?.showDelta && (
            <FormControl fullWidth>
              <TextField
                name="deltaLabel"
                onChange={handleLabelChange}
                defaultValue={settings.deltaLabel ?? 'Delta'}
                value={settings.deltaLabel}
                label="Delta Label"
                variant="outlined"
                margin="normal"
                fullWidth
              />
            </FormControl>
          )}
        </>
      ) : null}
    </>
  );
};

export default SumDeltaSettings;
