import { Box } from '@mui/material';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { getDbMeasureIdToName } from '~/redux/selectors/treeSelectors';
import { setMultiMeasureWidgetSettings, TableWidget } from '~/types/widgets';
import DataWidgetSettingsContainer from '../common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import MultiMeasureSelection from '../common/MultiMeasureSelection';
import MultiMeasureSelectionMenu from '../common/MultiMeasureSelectionMenu';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';

type TableSettingDialogProps = {
  settings: TableWidget;
  handleSettingsChange: (value: ((prevState: TableWidget) => TableWidget) | TableWidget) => void;
};
const TableSettingDialog = ({ settings, handleSettingsChange }: TableSettingDialogProps) => {
  const [title, setTitle] = useState(settings.title);
  const metricsIdToName = useSelector(getMetricsIdToName);

  const selectedDbMeasureIdToName = useSelector(getDbMeasureIdToName);
  const [selected, setSelected] = useState<any>([
    settings.selectedTitles ? [...settings.selectedTitles] : [],
  ]);
  useEffect(() => {
    setTitle(settings.title);
  }, [settings]);
  useEffect(() => {
    setSelected(settings.selectedTitles);
  }, [settings]);
  useEffect(() => {
    if (settings.mode === 'template') {
      const titles: Record<string, string> = {};
      settings.selectedTitles.forEach((title) => {
        titles[title] = metricsIdToName[title];
      });
      handleSettingsChange((prevState) => ({
        ...prevState,
        dbMeasureIdToName: titles,
        title: {
          ...prevState.title,
          value: prevState.title.isVisible
            ? prevState.title.value
            : Object.values(titles).join(' Vs.'),
        },
      }));
    }
  }, [settings.mode, selectedDbMeasureIdToName, settings.selectedTitles, metricsIdToName]);
  const handleSelectedTitles = (e: string[]) => {
    handleSettingsChange((prevState) => {
      const { selectedTitles, ...rest } = prevState;
      const updatedTitles = Array.from(new Set([...selectedTitles, ...e])).filter((title) =>
        e.includes(title),
      );
      return {
        ...rest,
        selectedTitles: updatedTitles,
      };
    });
  };

  return (
    <DataWidgetSettingsContainer
      settings={settings}
      setSettings={handleSettingsChange}
      dataTabChildren={
        <Box sx={{ mt: 2 }}>
          {settings.mode === 'dashboard' ? (
            <MultiMeasureSelectionMenu
              mode={settings.mode}
              settings={settings}
              setSettings={handleSettingsChange as setMultiMeasureWidgetSettings}
            />
          ) : (
            <MultiMeasureSelection
              mode={settings.mode}
              selectedMeasureNames={settings.selectedTitles ? [...settings.selectedTitles] : []}
              handleChangeMeasure={(e) => {
                setSelected(e.map((item) => item));
                handleSelectedTitles(e.map((item) => item));
              }}
            />
          )}
        </Box>
      }
    />
  );
};

export default TableSettingDialog;
