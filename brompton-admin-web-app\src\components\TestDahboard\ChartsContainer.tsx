import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { Box, IconButton, Tooltip } from '@mui/material';
import dynamic from 'next/dynamic';
import { Data, Datum, PlotData } from 'plotly.js';
import * as XLSX from 'xlsx';
import { useFetchScatterTest } from '~/hooks/useFetchScatterTest';
import Loader from '../common/Loader';
const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });

type ChartsContainerProps = {
  index: number;
  measure: string;
  customerId: string;
  assetId: string;
  startDate: Date;
  endDate: Date;
  selectedMeasure: string[];
  assetToMeasureList: Record<string, string[]>;
  color: 'blue' | 'red' | 'black' | 'green';
  aggregation: number;
  samplePeriod: number;
  chartType: 'scatter' | 'bar';
  rangeSlider: boolean;
};
const ChartsContainer = ({
  index,
  assetId,
  customerId,
  endDate,
  measure,
  startDate,
  color,
  aggregation,
  samplePeriod,
  chartType,
  rangeSlider,
}: ChartsContainerProps) => {
  const { chartData, tsData, isLoading, layoutData } = useFetchScatterTest({
    customerId,
    assetId,
    measureId: measure,
    startDate: startDate.getTime(),
    endDate: endDate.getTime(),
    samplePeriod: samplePeriod,
    aggBy: aggregation,
    timeRangeType: 1,
    color: color,
    chartType: chartType,
    rangeSlider,
  });
  const createXLSXFile = (): void => {
    // if (chartType !== 'indicator' && chartType !== ('bullet' as ChartType)) {
    const data = chartData as Data[];
    const workbook = XLSX.utils.book_new();
    data.forEach((dataItem1, index) => {
      // Convert each Data object to a worksheet
      const worksheetData = [];
      const dataItem = dataItem1 as PlotData;
      for (let i = 0; i < dataItem.x.length; i++) {
        const rowData: {
          Time: string;
          [y: string]: Datum | Datum[];
          z?: any;
        } = {
          Time: new Date((dataItem.x[i] as string) + ' UTC')
            .toISOString()
            .replace(/T/, ' ')
            .replace(/\..+/, ''),
          [`${dataItem.name}`]: dataItem.y[i],
        };
        // }
        worksheetData.push(rowData);
      }
      const worksheet = XLSX.utils.json_to_sheet(worksheetData);
      XLSX.utils.book_append_sheet(workbook, worksheet, `Data ${index + 1}`);
    });
    // Write the workbook to a file
    XLSX.writeFile(workbook, 'ChartData.xlsx');
  };
  return (
    <>
      {isLoading ? (
        <Loader />
      ) : (
        <>
          <Box sx={{ width: '100%', justifyContent: 'end', display: 'flex', mb: 1 }}>
            {chartData ? (
              <Tooltip title="Export to Excel" placement="bottom">
                <IconButton>
                  <FileDownloadIcon
                    color="primary"
                    onClick={() => {
                      createXLSXFile();
                    }}
                  />
                </IconButton>
              </Tooltip>
            ) : null}
          </Box>

          <Box height={500}>
            <Plot
              data={chartData}
              useResizeHandler={true}
              style={{ width: '100%', height: '100%' }}
              layout={{
                margin: {
                  // ...chartLayout.margin,
                  // l: mobile ? 50 : chartLayout.margin?.l ?? undefined,
                  // r: mobile ? 0 : chartLayout.margin?.r ?? undefined,
                },
                legend: {
                  x: 0, // Position legend at the left
                  y: -0.1, // Position legend slightly below the x-axis
                  xanchor: 'left', // Anchor the legend to the right side of the x position
                  yanchor: 'top', // Anchor the legend to the top side of the y position,
                },
                autosize: true,
                ...layoutData,
                yaxis: {
                  title: 'Value',
                  position: 0,
                  // fixedrange: enabledZoom ? true : undefined,
                  ...layoutData.yaxis,
                },
                xaxis: {
                  title: 'Time',
                  rangeslider: {
                    visible: rangeSlider,
                  },
                  // position:
                  //   (chartType === 'bar' || chartType === 'scatter') &&
                  //   'showRangeSlider' in settings &&
                  //   settings.showRangeSlider
                  //     ? undefined
                  //     : 0,
                  // fixedrange: enabledZoom ? true : undefined,
                  // ...chartLayout.xaxis,
                  // rangeslider:
                  //   (chartType === 'bar' || chartType === 'scatter') &&
                  //   'showRangeSlider' in settings &&
                  //   settings.showRangeSlider
                  //     ? { visible: true }
                  //     : undefined,
                },
              }}
              config={{
                responsive: true,
                displaylogo: false,
                displayModeBar: false, // This will hide the entire mode bar
                modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
              }}
            />
          </Box>
        </>
      )}
    </>
  );
};

export default ChartsContainer;
