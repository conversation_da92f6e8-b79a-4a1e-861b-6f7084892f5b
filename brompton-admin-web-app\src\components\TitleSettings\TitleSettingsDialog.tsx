import CancelIcon from '@mui/icons-material/Cancel';
import {
  Alert,
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  FormControl,
  FormControlLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  SelectChangeEvent,
  Snackbar,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import { ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useGetAllAssetQuery } from '~/redux/api/assetsApi';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getCurrentAssetType, getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { RootState } from '~/redux/store';
import { TitleWidget } from '~/types/widgets';
import { assetsPathMapper, formatMetricLabel, fontWeights } from '~/utils/utils';
import CustomDialog from '../common/CustomDialog';
import DataWidgetSettingsContainer from '../common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';

type TitleSettingsDialogProps = {
  settings: TitleWidget;
  handleSettingsChange: (value: ((prevState: TitleWidget) => TitleWidget) | TitleWidget) => void;
};
export const TitleSettingsDialog = ({
  settings,
  handleSettingsChange,
}: TitleSettingsDialogProps) => {
  const router = useRouter();
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  // handle local states
  const [confirm, setConfirm] = useState<boolean>(false);
  const [openSnackBar, setOpenSnackBar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });
  // handling redux state selectors
  const assetTypeTemplate = useSelector(getCurrentAssetType);
  const activeCustomer = useSelector(getActiveCustomer);
  const metricsIdToName = useSelector(getMetricsIdToName);

  const { data: assetData, isFetching: isAssetReloading } = useGetAllAssetQuery(
    { customerId: activeCustomer?.id ?? 0, parentIds: [] },
    {
      skip: !activeCustomer || settings.mode === 'template',
      refetchOnMountOrArgChange: true,
    },
  );
  const assetsWithPath = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);

  const { data: assetMeasurements, isLoading: isMeasurementLoading } = useGetAllMeasurementsQuery(
    { customerId: activeCustomer?.id ?? 0, assetId: settings.assetId ?? 0 },
    {
      skip: !settings.assetId || !activeCustomer?.id,
      refetchOnMountOrArgChange: true,
    },
  );

  // Add this effect to synchronize title value when measurement is selected
  useEffect(() => {
    if (settings.valueMode === 'measurement' && settings.measurementId && assetMeasurements) {
      const selectedMeasurement = assetMeasurements.find((m) => m.id === settings.measurementId);
      // Remove the code that updates title.value
    }
  }, [settings.measurementId, assetMeasurements]);

  // Add this effect to update title when fixed value changes
  useEffect(() => {
    if (settings.valueMode === 'fixed' && settings.fixedValue) {
      // Remove the code that updates title.value
    }
  }, [settings.fixedValue, settings.valueMode]);

  // Add this effect to handle existing widgets without valueMode
  useEffect(() => {
    // If valueMode is undefined, set it to 'fixed' by default
    if (settings.valueMode === undefined) {
      handleSettingsChange((prev) => ({
        ...prev,
        valueMode: 'fixed',
        fixedValue: prev.title?.value || 'Title',
      }));
    }
  }, []);

  useEffect(() => {
    if (settings.valueMode === 'measurement' && !settings.measurementId) {
      handleSettingsChange((prev) => ({
        ...prev,
        isValid: false,
      }));
    } else {
      handleSettingsChange((prev) => ({
        ...prev,
        isValid: true,
      }));
    }
  }, [settings.valueMode, settings.measurementId]);

  // Add handlers for fixed value formatting
  const handleFixedFontSize = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value, 10);
    handleSettingsChange((prev) => ({
      ...prev,
      fixedFontSize: value,
      isDirty: true,
    }));
  };

  const handleFixedFontWeight = (event: any) => {
    const value = event.target.value;
    handleSettingsChange((prev) => ({
      ...prev,
      fixedFontWeight: value,
      isDirty: true,
    }));
  };

  const handleFixedColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    handleSettingsChange((prev) => ({
      ...prev,
      fixedColor: value,
      isDirty: true,
    }));
  };

  // Initialize fixed formatting values if they don't exist
  useEffect(() => {
    if (
      settings.fixedFontSize === undefined ||
      settings.fixedFontWeight === undefined ||
      settings.fixedColor === undefined
    ) {
      handleSettingsChange((prev) => ({
        ...prev,
        fixedFontSize: prev.fixedFontSize ?? prev.title?.fontSize ?? 12,
        fixedFontWeight: prev.fixedFontWeight ?? prev.title?.fontWeight ?? 'normal',
        fixedColor: prev.fixedColor ?? prev.title?.color ?? '#000000',
      }));
    }
  }, []);

  const handleMeasureChange = (event: SelectChangeEvent<string>) => {
    const selectedDbMeasureId = event.target.value as string;
    handleSettingsChange({
      ...settings,
      selectedDbMeasureId,
    });
  };

  return (
    <Stack display="flex" gap={1}>
      <DataWidgetSettingsContainer
        hideSettings={{
          timeContext: settings.valueMode !== 'measurement',
          aggBy: settings.valueMode !== 'measurement',
        }}
        settings={settings}
        setSettings={handleSettingsChange}
        dataTabChildren={
          <>
            <RadioGroup
              row
              value={settings.valueMode || 'fixed'} // Default to 'fixed' if undefined
              onChange={(e) => {
                const mode = e.target.value as 'fixed' | 'measurement';
                handleSettingsChange((prev) => ({
                  ...prev,
                  valueMode: mode,
                  isDirty: true,
                  ...(mode === 'fixed'
                    ? {
                        assetId: undefined,
                        measurementId: undefined,
                        timeContext: undefined,
                      }
                    : {}),
                }));
              }}
            >
              <FormControlLabel value="fixed" control={<Radio />} label="Value" />
              <FormControlLabel value="measurement" control={<Radio />} label="Measurement" />
            </RadioGroup>

            {settings.valueMode === 'fixed' && (
              <TextField
                fullWidth
                label="Fixed Value"
                value={settings.fixedValue ?? ''}
                onChange={(e) =>
                  handleSettingsChange((prev) => ({
                    ...prev,
                    fixedValue: e.target.value,
                    isDirty: true,
                  }))
                }
                sx={{ mt: 2 }}
              />
            )}

            {settings.valueMode === 'measurement' &&
              (settings.mode === 'dashboard' ? (
                <>
                  <Box
                    display={'flex'}
                    sx={{ mt: 2 }}
                    alignItems={'center'}
                    justifyContent={'center'}
                    gap={2}
                  >
                    <FormControl fullWidth>
                      <Autocomplete
                        fullWidth
                        id="asset-autocomplete-title"
                        loading={isAssetReloading}
                        options={assetsWithPath}
                        getOptionLabel={(option) => option.label}
                        value={
                          assetsWithPath.find((asset) => asset.id === settings.assetId) ?? null
                        }
                        onChange={(e, value) =>
                          handleSettingsChange((prev) => ({
                            ...prev,
                            assetId: value?.id ?? undefined,
                            measurementId: undefined,
                            isDirty: true,
                          }))
                        }
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Asset"
                            variant="outlined"
                            InputProps={{
                              ...params.InputProps,
                              endAdornment: (
                                <>
                                  {isAssetReloading ? (
                                    <CircularProgress color="inherit" size={20} />
                                  ) : null}
                                  {params.InputProps.endAdornment}
                                </>
                              ),
                            }}
                          />
                        )}
                      />
                    </FormControl>

                    {settings.assetId && (
                      <FormControl fullWidth>
                        <Autocomplete
                          fullWidth
                          id="measurement-autocomplete-title"
                          loading={isMeasurementLoading}
                          disabled={isMeasurementLoading}
                          options={
                            assetMeasurements?.map((m) => ({
                              label: formatMetricLabel(m.tag),
                              id: m.id,
                            })) ?? []
                          }
                          getOptionLabel={(option) => option.label}
                          value={
                            assetMeasurements
                              ?.map((m) => ({ label: formatMetricLabel(m.tag), id: m.id }))
                              .find((m) => m.id === settings.measurementId) ?? null
                          }
                          onChange={(e, value) =>
                            handleSettingsChange((prev) => ({
                              ...prev,
                              measurementId: value?.id ?? undefined,
                              isDirty: true,
                            }))
                          }
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Measurement"
                              variant="outlined"
                              InputProps={{
                                ...params.InputProps,
                                endAdornment: (
                                  <>
                                    {isMeasurementLoading ? (
                                      <CircularProgress color="inherit" size={20} />
                                    ) : null}
                                    {params.InputProps.endAdornment}
                                  </>
                                ),
                              }}
                            />
                          )}
                        />
                      </FormControl>
                    )}
                  </Box>
                </>
              ) : (
                <FormControl sx={{ pt: 2, pb: 2 }} fullWidth>
                  <Select
                    sx={{
                      width: '100%',
                      p: 0.3,
                      '& fieldset': {
                        '& legend': {
                          maxWidth: '100%',
                          height: 'auto',
                          '& span': {
                            opacity: 1,
                          },
                        },
                      },
                    }}
                    value={settings.selectedDbMeasureId}
                    onChange={handleMeasureChange}
                    label="Metric"
                  >
                    {Object.keys(metricsIdToName).map((dbMeasureId) => {
                      return (
                        <MenuItem key={dbMeasureId} value={dbMeasureId}>
                          {metricsIdToName[dbMeasureId]}
                        </MenuItem>
                      );
                    })}
                  </Select>
                </FormControl>
              ))}

            <Snackbar
              open={openSnackBar.open}
              onClose={() => {
                setOpenSnackBar({ open: false, message: '', severity: 'info' });
              }}
              autoHideDuration={3000}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
            >
              <Alert severity={openSnackBar.severity}>{openSnackBar.message}</Alert>
            </Snackbar>
          </>
        }
        feelTabChidren={
          <>
            {/* Add Fixed Value Formatting in the Look and Feel tab */}
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Value Formatting
              </Typography>

              <FormControl fullWidth>
                <TextField
                  name="fixedFontSize"
                  type="number"
                  onChange={handleFixedFontSize}
                  value={settings.fixedFontSize ?? 12}
                  label="Font Size"
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormControl>

              <FormControl fullWidth sx={{ mb: 2 }}>
                <TextField
                  label="Font Color"
                  name="fixedColor"
                  type="color"
                  onChange={handleFixedColorChange}
                  value={settings.fixedColor ?? '#000000'}
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormControl>

              <FormControl fullWidth>
                <TextField
                  select
                  label="Font Weight"
                  value={settings.fixedFontWeight ?? 'normal'}
                  onChange={handleFixedFontWeight}
                  fullWidth
                  SelectProps={{
                    native: true,
                  }}
                >
                  {fontWeights.map((weight) => (
                    <option key={weight} value={weight}>
                      {weight}
                    </option>
                  ))}
                </TextField>
              </FormControl>
            </Box>
          </>
        }
      />

      <CustomDialog
        title="You have unsaved changes."
        content={<Typography color={'error'}>Do you want to still proceed?</Typography>}
        dialogActions={
          <>
            <Button
              onClick={() => {
                setConfirm(false);
              }}
              variant="outlined"
              startIcon={<CancelIcon />}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setConfirm(false);
                dispatch(
                  dashboardSlice.actions.setDashboardCrumb({
                    dashboardId: settings?.dashboard?.id ?? 0,
                    title: settings?.dashboard?.title ?? '',
                  }),
                );
                if (settings.openDashboardInNewTab) {
                  window.open(
                    `${router.basePath}/customer/${activeCustomer?.id}/dashboard/${settings.dashboard?.id}`,
                    '_blank',
                  );
                  return;
                }
                router.push(
                  `/customer/${activeCustomer?.id}/dashboard/${settings.dashboard?.id ?? 0}`,
                );
                dispatch(dashboardSlice.actions.setCurrentDashboardId(settings.dashboard?.id ?? 0));
                dispatch(
                  dashboardSlice.actions.setCurrentDashboardTitle(settings.dashboard?.title ?? ''),
                );
              }}
              variant="contained"
              color="error"
            >
              Proceed
            </Button>
          </>
        }
        onClose={() => setConfirm(false)}
        open={confirm}
      />
    </Stack>
  );
};
