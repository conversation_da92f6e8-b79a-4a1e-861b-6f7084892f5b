import { yupResolver } from '@hookform/resolvers/yup';
import EditIcon from '@mui/icons-material/Edit';
import { Autocomplete, Box, Button, IconButton, TextField } from '@mui/material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import React, { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import CustomDialog from '~/components/common/CustomDialog';
import Loader from '~/components/common/Loader';

import {
  useCreateUnitOfMeasureByMeasuresMutation,
  useGetAllMeasureTypesQuery,
  useGetAllUnitsOfMeasureWithMeasureTypeQuery,
  useUpdateUnitOfMeasureByMeasuresMutation,
} from '~/redux/api/measuresApi';

import { CustomError } from '~/errors/CustomerErrorResponse';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { unitOfMeasureValidationSchema } from '../domain';

type FormData = {
  name: string;
  measurement_type_id: number | undefined;
};

type UnitOfMeasuresDataProps = {
  createOrEdit: 'create' | 'edit' | null;
  setCreateOrEdit: (state: 'create' | 'edit' | null) => void;
  selectedRow: any;
  setSelectedRow: (row: any) => void;
};
const UnitOfMeasuresData: React.FC<UnitOfMeasuresDataProps> = ({
  createOrEdit,
  selectedRow,
  setCreateOrEdit,
  setSelectedRow,
}) => {
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();

  const {
    data: unitofMeasures,
    isFetching: loadingUOM,
    refetch,
  } = useGetAllUnitsOfMeasureWithMeasureTypeQuery();

  const { data: measureTypes, isFetching: loadingMeasures } = useGetAllMeasureTypesQuery();

  const [createUnitOfMeasure, { isLoading: isCreating, status, isError, error }] =
    useCreateUnitOfMeasureByMeasuresMutation();
  const [
    updateUnitOfMeasure,
    { isLoading: isUpdating, status: updateStatus, isError: isUpdateError, error: updateError },
  ] = useUpdateUnitOfMeasureByMeasuresMutation();
  useEffect(() => {
    if (status === 'fulfilled') {
      showSuccessAlert('Unit of Measure created successfully');
      refetch();
    } else if (isError && error) {
      const errorData = error as CustomError;
      showErrorAlert(errorData.data.exception ?? 'Error Creating Unit of Measure');
    }
  }, [status, isError, error]);
  useEffect(() => {
    if (updateStatus === 'fulfilled') {
      showSuccessAlert('Unit of Measure updated successfully');
      refetch();
    } else if (isUpdateError && updateError) {
      const errorData = updateError as CustomError;
      console.log('errorData', errorData, updateError);
      showErrorAlert(errorData.data.exception ?? 'Error Creating Unit of Measure');
    }
  }, [updateStatus, isUpdateError, updateError]);
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      name: '',
      measurement_type_id: undefined,
    },
    resolver: yupResolver(unitOfMeasureValidationSchema),
  });

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', flex: 1 },
    { field: 'name', headerName: 'Unit of Measure', flex: 1 },
    {
      field: 'measurementTypeName',
      headerName: 'Measurement Type',
      flex: 1,
      valueGetter: (params) => params.row.measurement_type_id?.name,
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      renderCell: (params) => {
        return (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton
              color="primary"
              size="small"
              onClick={() => {
                setCreateOrEdit('edit');
                setSelectedRow(params.row);
                reset({
                  name: params.row.name,
                  measurement_type_id: params.row.measurement_type_id?.id,
                });
              }}
            >
              <EditIcon />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  const onSubmit = async (data: FormData) => {
    try {
      if (createOrEdit === 'create') {
        await createUnitOfMeasure({
          name: data.name,
          measurementTypeId: data.measurement_type_id ?? 0,
        }).unwrap();
      } else if (createOrEdit === 'edit' && selectedRow) {
        await updateUnitOfMeasure({
          unitOfMeasureId: selectedRow.id,
          name: data.name,
          measurementTypeId: data.measurement_type_id ?? 0,
        }).unwrap();
      }
      setCreateOrEdit(null);
      setSelectedRow(null);
      reset();
    } catch (error) {
      console.error('Error creating/updating unit of measure:', error);
    }
  };

  if (loadingUOM) {
    return <Loader />;
  }

  return (
    <Box pt={1}>
      <AlertSnackbar {...snackbarState} />
      <DataGrid
        sx={{
          height: 'calc(100vh - 70px)',
          '& .MuiDataGrid-columnHeader': {
            background: '#F9FAFB',
          },
        }}
        rows={unitofMeasures?.items ?? []}
        columns={columns}
        autoPageSize
      />

      <CustomDialog
        open={createOrEdit !== null}
        onClose={() => {
          reset({
            name: '',
            measurement_type_id: undefined,
          });
          setCreateOrEdit(null);
          setSelectedRow(null);
        }}
        title={createOrEdit === 'create' ? 'Add Unit of Measure' : 'Edit Unit of Measure'}
        content={
          <Box component="form" onSubmit={handleSubmit(onSubmit)}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Unit of measure"
                  fullWidth
                  margin="normal"
                  error={!!errors.name}
                  helperText={errors.name?.message?.toString()}
                />
              )}
            />

            <Controller
              name="measurement_type_id"
              control={control}
              render={({ field }) => {
                const selectedOption =
                  field.value && measureTypes
                    ? measureTypes.find((type) => type.id === field.value)
                    : undefined;

                return (
                  <Autocomplete
                    loading={loadingMeasures}
                    options={
                      measureTypes?.map((type) => ({
                        label: type.name,
                        value: type.id,
                      })) ?? []
                    }
                    value={
                      selectedOption
                        ? { label: selectedOption.name, value: selectedOption.id }
                        : null
                    }
                    isOptionEqualToValue={(option, value) => option.value === value?.value}
                    onChange={(_, newValue) => {
                      field.onChange(newValue?.value ?? undefined);
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Measurement Type"
                        fullWidth
                        margin="normal"
                        error={!!errors.measurement_type_id}
                        helperText={errors.measurement_type_id?.message?.toString()}
                      />
                    )}
                  />
                );
              }}
            />

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
              <Button
                onClick={() => {
                  setCreateOrEdit(null);
                  setSelectedRow(null);
                  reset();
                }}
                variant="outlined"
                color="primary"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={isCreating || isUpdating}
              >
                {createOrEdit === 'create'
                  ? isCreating
                    ? 'Adding...'
                    : 'Add'
                  : isUpdating
                  ? 'Saving...'
                  : 'Save'}
              </Button>
            </Box>
          </Box>
        }
        dialogActions={null}
        maxWidth="md"
      />
    </Box>
  );
};

export default UnitOfMeasuresData;
