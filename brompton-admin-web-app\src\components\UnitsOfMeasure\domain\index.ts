import * as yup from 'yup';
export interface UnitOfMeasureDTO {
  total: number;
  items: UnitOfMeasure[];
}

export interface UnitOfMeasure {
  id: number;
  name: string;
  measurement_type_id: MeasurementType;
}

export interface MeasurementType {
  id: number;
  name: string;
}

export const unitOfMeasureValidationSchema = yup.object().shape({
  name: yup.string().required('Unit of measure is required.'),
  measurement_type_id: yup.number().required('Measurement Type is required.'),
});
