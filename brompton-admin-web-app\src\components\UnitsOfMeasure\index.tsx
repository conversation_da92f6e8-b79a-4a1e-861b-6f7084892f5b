import { Box, Button, Container } from '@mui/material';
import { useState } from 'react';
import PageName from '../common/PageName/PageName';
import UnitOfMeasuresData from './UnitOfMeasuresData';

const UnitsOfMeasure = () => {
  const [createOrEdit, setCreateOrEdit] = useState<'create' | 'edit' | null>(null);

  const [selectedRow, setSelectedRow] = useState<any>(null);
  return (
    <Container
      sx={{
        maxWidth: '100%',
        width: '100%',
        '@media (min-width: 1200px)': { width: '100%', maxWidth: '100%' },
      }}
    >
      <Box pl={0} pb={0} pt={0} sx={{ display: 'flex' }}>
        <Box
          sx={{
            flexGrow: 1,
            mt: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <PageName name="Units of Measures" />
          <Box sx={{ display: 'flex' }}>
            <Button
              variant="contained"
              onClick={() => {
                setCreateOrEdit('create');
                setSelectedRow(null);
              }}
            >
              Add Unit of Measure
            </Button>
          </Box>
        </Box>
      </Box>
      <UnitOfMeasuresData
        createOrEdit={createOrEdit}
        setCreateOrEdit={setCreateOrEdit}
        selectedRow={selectedRow}
        setSelectedRow={setSelectedRow}
      />
    </Container>
  );
};

export default UnitsOfMeasure;
