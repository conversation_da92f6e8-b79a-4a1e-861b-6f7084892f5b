import DeleteIcon from '@mui/icons-material/Delete';
import { Autocomplete, Box, IconButton, TextField, Tooltip } from '@mui/material';
import { AnyAction, ThunkDispatch } from '@reduxjs/toolkit';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AssetMeasurement } from '~/measurements/domain/types';
import { useGetAllAssetQuery } from '~/redux/api/assetsApi';
import { measuresApi } from '~/redux/api/measuresApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { RootState } from '~/redux/store';
import {
  AssetMeasureOptions,
  BarChartWidget,
  ChartMeasureSetting,
  ImageTextDetails,
  ImageWidget,
  MultiMeasureWidgets,
  ScatterChartWidget,
} from '~/types/widgets';
import { assetsPathMapper, formatMetricLabel } from '~/utils/utils';

type AssetMeasureProps<T extends MultiMeasureWidgets> = {
  assetMesures: AssetMeasureOptions;
  index: number;
  onDelete: (index: number) => void;
  onUpdate: (index: number, updatedMeasure: AssetMeasureOptions) => void; // Added onUpdate prop
  setSettings: (value: ((prevState: T) => T) | T) => void;
  required?: boolean;
};

const AssetMeasure = ({
  assetMesures,
  index,
  onDelete,
  onUpdate,
  setSettings,
  required,
}: AssetMeasureProps<MultiMeasureWidgets>) => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, AnyAction>>();
  const customerId = useSelector(getCustomerId);
  const [selectedAsset, setSelectedAsset] = useState<{ label: string; id: number } | null>(null);
  const [measureData, setMeasureData] = useState<AssetMeasurement[]>([]);
  const [loadingMeasure, setLoadingMeasure] = useState(false);

  const { data: assetData, isFetching: isAssetReloading } = useGetAllAssetQuery(
    { customerId, parentIds: [] },
    {
      skip: !customerId,
      refetchOnMountOrArgChange: true,
    },
  );

  const assetTypesWithPath = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);

  // Initialize selectedAsset based on assetId using assetTypesWithPath
  useEffect(() => {
    if (assetMesures.assetId && assetTypesWithPath) {
      const asset = assetTypesWithPath.find((a) => String(a.id) === assetMesures.assetId);
      if (asset) {
        setSelectedAsset({ label: asset.label, id: asset.id });
      } else {
        setSelectedAsset(null);
      }
    } else {
      setSelectedAsset(null);
    }
  }, [assetMesures.assetId, assetTypesWithPath]);

  // Fetch measurements when selectedAsset changes
  useEffect(() => {
    const fetchMeasure = async () => {
      if (!selectedAsset) {
        setMeasureData([]);
        return;
      }
      setLoadingMeasure(true);
      try {
        const response = await dispatch(
          measuresApi.endpoints.getAllMeasurements.initiate({
            customerId,
            assetId: selectedAsset.id,
          }),
        ).unwrap();

        // Assuming the API response structure is an array of measurements
        if (response && Array.isArray(response)) {
          setMeasureData(response);
        } else {
          setMeasureData([]);
        }
      } catch (error) {
        console.error('Failed to fetch measurements:', error);
        setMeasureData([]);
      } finally {
        setLoadingMeasure(false);
      }
    };
    fetchMeasure();
  }, [selectedAsset, dispatch, customerId]);

  // Handle asset change
  const handleAssetChange = (event: any, newValue: { label: string; id: number } | null) => {
    setSelectedAsset(newValue);
    onUpdate(index, {
      assetId: newValue ? String(newValue.id) : '',
      measureId: [], // Reset measurements when asset changes
    });
  };

  // Handle measurements change
  const handleMeasurementsChange = (event: any, newValues: { label: string; id: number }[]) => {
    onUpdate(index, {
      assetId: assetMesures.assetId, // Keep the current assetId
      measureId: newValues.map((measure) => String(measure.id)),
    });
    const newMeasureMapping = newValues.reduce((acc, measure) => {
      acc[String(measure.id)] = measure.label;
      return acc;
    }, {} as Record<string, string>);
    setSettings((prevSettings) => {
      const updatedSettings = {
        ...prevSettings,
        dbMeasureIdToName: {
          ...prevSettings.dbMeasureIdToName, // Retain existing mappings
          ...newMeasureMapping, // Add/update with new mappings
        },
      };
      if ('dbMeasureIdToSetting' in prevSettings) {
        (updatedSettings as ScatterChartWidget | BarChartWidget).dbMeasureIdToSetting = {
          ...prevSettings.dbMeasureIdToSetting,
          ...Object.keys(newMeasureMapping).reduce((acc, id) => {
            if (!prevSettings.dbMeasureIdToSetting?.[id]) {
              acc[id] = {
                showSum: false,
                yAxisSide: 'left',
              };
            }
            return acc;
          }, {} as Record<string, ChartMeasureSetting>),
        };
      }
      if ('measureIdToImageTextDetails' in prevSettings) {
        (updatedSettings as ImageWidget).measureIdToImageTextDetails = {
          ...prevSettings.measureIdToImageTextDetails,
          ...Object.keys(newMeasureMapping).reduce((acc, id) => {
            if (!prevSettings.measureIdToImageTextDetails?.[id]) {
              acc[id] = {
                label: newMeasureMapping[id],
                unit: '',
                id,
                positionX: 100,
                positionY: 100,
                value: '',
                dashboard: null,
                openDashboardInNewTab: false,
                dashboardOrTemplate: 'template',
                assetOrAssetType: null,
              };
            }
            return acc;
          }, {} as Record<string, ImageTextDetails>),
        };
      }

      return updatedSettings;
    });
  };

  return (
    <Box sx={{ width: '100%', display: 'flex', mb: 1, gap: 1 }}>
      {/* Asset Selection */}
      <Autocomplete
        fullWidth
        id={`asset-autocomplete-${index}`}
        loading={isAssetReloading}
        options={
          !isAssetReloading
            ? assetTypesWithPath.map((asset) => ({
                label: asset.label,
                id: asset.id,
              }))
            : []
        }
        getOptionLabel={(option) => option?.label ?? ''}
        onChange={handleAssetChange}
        value={selectedAsset}
        renderInput={(params) => (
          <TextField
            {...params}
            label="Asset"
            required={required}
            error={required}
            helperText={required ? 'Asset is required.' : ''}
            variant="outlined"
          />
        )}
      />

      {/* Measurement Selection (Multiple) */}
      <Autocomplete
        fullWidth
        multiple
        id={`measure-autocomplete-${index}`}
        loading={loadingMeasure}
        options={
          !loadingMeasure
            ? measureData.map((measure) => ({
                label: formatMetricLabel(measure.tag), // Adjust based on your actual measure data structure
                id: measure.id,
              }))
            : []
        }
        getOptionLabel={(option) => option?.label ?? ''}
        value={
          measureData
            ? measureData
                .filter((measure) => assetMesures.measureId.includes(String(measure.id)))
                .map((measure) => ({
                  label: formatMetricLabel(measure.tag), // Adjust based on your actual measure data structure
                  id: measure.id,
                }))
            : []
        }
        onChange={handleMeasurementsChange}
        renderInput={(params) => <TextField {...params} label="Measurements" variant="outlined" />}
        disabled={!selectedAsset} // Disable if no asset is selected
      />

      {/* Delete Button */}
      <Tooltip title="Delete asset measurement">
        <IconButton onClick={() => onDelete(index)}>
          <DeleteIcon color="error" />
        </IconButton>
      </Tooltip>
    </Box>
  );
};

export default AssetMeasure;
