import { DragIndicator } from '@mui/icons-material';
import CancelIcon from '@mui/icons-material/Cancel';
import CloseFullscreenOutlinedIcon from '@mui/icons-material/CloseFullscreenOutlined';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DeleteIcon from '@mui/icons-material/Delete';
import LaunchIcon from '@mui/icons-material/Launch';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import OpenInFullOutlinedIcon from '@mui/icons-material/OpenInFullOutlined';
import PushPinIcon from '@mui/icons-material/PushPin';
import PushPinOutlinedIcon from '@mui/icons-material/PushPinOutlined';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import SettingsIcon from '@mui/icons-material/Settings';
import {
  Badge,
  Box,
  Button,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
  useTheme,
} from '@mui/material';
import { useRouter } from 'next/router';
import React, { ReactNode, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import useCommonWidgetHelper from '~/hooks/useCommonWidgetHelper';
import { useHasPowerUserAccess } from '~/hooks/useHasPowerUserAccess';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';
import { getWidgetLayoutById } from '~/redux/selectors/widgetSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import {
  AssetMeasurementDetailsWithLastFetchAndSucess,
  AssetMeasurementDetailsWithLastFetchTime,
} from '~/types/timeseries';
import { Widget, WidgetCommonSettings, WidgetName, WidgetType } from '~/types/widgets';
import { formatDate } from '~/utils/utils';
import CustomDialog from './CustomDialog';
import DeleteWidgetDialog from './DeleteWidgetDialog';

type CommonWidgetContainerProps<T extends WidgetCommonSettings> = {
  id: string;
  widgetContent: ReactNode;
  widgetType: WidgetType;
  widgetName: WidgetName;
  settings: T;
  settingsDialog?: (props: {
    settings: T;
    handleSettingsChange: React.Dispatch<React.SetStateAction<T>>;
  }) => ReactNode;
  customSettingsDialog?: ReactNode;
  customSettingsDialogOpen?: boolean;
  setCustomSettingsDialogOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  menuOptions?: ReactNode;
  options?: { method: () => Promise<void> | void; content: ReactNode }[];
  setLoading?: React.Dispatch<React.SetStateAction<boolean>>;
  removedResults?: AssetMeasurementDetailsWithLastFetchTime[];
  successfulResults?: AssetMeasurementDetailsWithLastFetchTime[];
  successAndFailedMeasurements?: AssetMeasurementDetailsWithLastFetchAndSucess[];
};
const CommonWidgetContainer = <T extends WidgetCommonSettings>({
  id,
  widgetContent,
  widgetType,
  widgetName,
  settings,
  settingsDialog,
  customSettingsDialog,
  setCustomSettingsDialogOpen,
  menuOptions,
  options,
  setLoading,
  removedResults,
  successfulResults,
  successAndFailedMeasurements,
}: CommonWidgetContainerProps<T>) => {
  const dispatch = useDispatch();
  const theme = useTheme();
  const dateFormats = useSelector(getDateTimeFormat);
  const router = useRouter();
  const [open, setOpen] = useState<boolean>(false);
  const [currentSettings, setCurrentSettings] = useState<T>(settings);
  const hasPowerUserAccess = useHasPowerUserAccess();
  const widgetLayout = useSelector(getWidgetLayoutById(id));
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [deleteDialog, setDeleteDialog] = useState<boolean>(false);
  const [widgetExpanded, setWidgetExpanded] = useState<boolean>(false);
  const allSuccess = successAndFailedMeasurements?.filter((measurement) => measurement.isSuccess);
  const allFailed = successAndFailedMeasurements?.filter((measurement) => !measurement.isSuccess);
  const partiallyFailed = !allSuccess && !allFailed;

  const hasOnlySuccess =
    allSuccess && allSuccess.length > 0 && (!allFailed || allFailed.length === 0);
  const hasOnlyFailed =
    allFailed && allFailed.length > 0 && (!allSuccess || allSuccess.length === 0);
  const hasPartialFailures =
    allSuccess && allFailed && allSuccess.length > 0 && allFailed.length > 0;
  let color = theme.palette.warning.main;

  if (hasOnlySuccess) {
    color = theme.palette.success.main;
  } else if (hasOnlyFailed) {
    color = theme.palette.error.main;
  } else if (hasPartialFailures) {
    color = theme.palette.warning.main;
  }

  const dashboardLink = currentSettings as T & {
    dashboard: { id: number; title: string } | null;
  };
  const isOverriddenAssetTz =
    'overrideAssetTz' in settings && settings?.overrideAssetTz ? true : false;
  const isOverrdenGlobalSettings =
    'overrideGlobalSettings' in settings && settings?.overrideGlobalSettings;
  const isOverRiddenSamplePeriod = 'globalSamplePeriod' in settings && settings?.globalSamplePeriod;
  const hasOverriddenSettings =
    isOverriddenAssetTz || isOverrdenGlobalSettings || isOverRiddenSamplePeriod;
  const overriddenSettingsArray = hasOverriddenSettings
    ? [
        {
          overridenSetting: isOverriddenAssetTz,
          settingType: 'Asset Timezone',
        },
        {
          overridenSetting: isOverrdenGlobalSettings,
          settingType: 'Time Range',
        },
        {
          overridenSetting: isOverRiddenSamplePeriod,
          settingType: 'Sample Period',
        },
      ]
    : [];
  useEffect(() => {
    setCurrentSettings(settings);
  }, [settings, open]);
  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const makePin = () => {
    dispatch(dashboardSlice.actions.setStaticLayout(id));
    dispatch(
      dashboardSlice.actions.setSpecificWidgetDirty({
        widgetId: id,
        isDirty: true,
      }),
    );
  };
  const onDelete = (id: string) => {
    handleClose();
    setDeleteDialog(true);
  };
  const handleDialogOpen = () => setOpen(true);

  const handleCloseDialog = () => {
    setOpen(false);
  };
  const cloneWidget = () => {
    dispatch(dashboardSlice.actions.cloneWidget(id));
  };
  const handleExpandWidget = () => {
    setWidgetExpanded(!widgetExpanded);
  };
  const onUpdateClick = (updatedSetting: T) => {
    setLoading?.(true);
    dispatch(
      dashboardSlice.actions.setCurrentWidgetSettings({
        id: id,
        type: widgetType,
        settings: updatedSetting,
      } as unknown as Widget),
    );
    setLoading?.(false);
  };

  const {
    setDashboard,
    confirm,
    setConfirm,
    openDashboard,
    isLoadingDashboardTemplates,
    isMeasurementsFetching,
    isTemplateError,
    isLoadingDashboardTemplatesList,
    dashboardTemplateExists,
  } = useCommonWidgetHelper({
    id,
    settings,
  });
  const ToolTipContent = () => {
    const renderTable = (
      title: string,
      rows: typeof successAndFailedMeasurements,
      titleColor: string | undefined = undefined,
      timeLabel = 'Last Fetch',
    ) => (
      <>
        <Typography
          variant="subtitle2"
          gutterBottom
          sx={{ fontWeight: 600, color: titleColor || 'text.primary', mt: 1 }}
        >
          {title}
        </Typography>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 500, pl: 0 }}>Tag</TableCell>
              <TableCell sx={{ fontWeight: 500 }}>{timeLabel}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {rows &&
              rows.map((m, i) => (
                <TableRow key={i}>
                  <TableCell sx={{ pl: 0 }}>{m.tag}</TableCell>
                  <TableCell>
                    {m.lastFetchTime !== undefined
                      ? formatDate(new Date(m.lastFetchTime), dateFormats)
                      : 'Error'}
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </>
    );

    return (
      <Box sx={{ p: 1 }}>
        {allSuccess &&
          allSuccess?.length > 0 &&
          renderTable('Measurements Fetched Successfully', allSuccess)}

        {partiallyFailed && successAndFailedMeasurements && (
          <>
            {renderTable(
              'Successful Measurements',
              successAndFailedMeasurements.filter((m) => m.isSuccess),
            )}
            <Divider sx={{ my: 1 }} />
            {renderTable(
              'Failed Measurements',
              successAndFailedMeasurements.filter((m) => !m.isSuccess),
              theme.palette.error.main,
              'Status',
            )}
          </>
        )}

        {allFailed &&
          allFailed.length > 0 &&
          renderTable('All Measurements Failed', allFailed, theme.palette.error.main, 'Status')}
        {/* {allSuccess && allSuccess.length > 0 && (
          <>
            <Typography variant="subtitle2" gutterBottom>
              Measurements Fetched Successfully:
            </Typography>
            {allSuccess.map((m, i) => (
              <Typography key={i} variant="body2">
                {m.tag} –{' '}
                {m.lastFetchTime !== undefined
                  ? formatDate(new Date(m.lastFetchTime), dateFormats)
                  : 'There is no fetch'}
              </Typography>
            ))}
          </>
        )}
        {partiallyFailed && successAndFailedMeasurements && (
          <>
            <Typography variant="subtitle2" gutterBottom>
              Successful:
            </Typography>
            {successAndFailedMeasurements
              .filter((m) => m.isSuccess)
              .map((m, i) => (
                <Typography key={i} variant="body2">
                  {m.tag} –
                  {m.lastFetchTime !== undefined
                    ? formatDate(new Date(m.lastFetchTime), dateFormats)
                    : 'There is no fetch'}
                </Typography>
              ))}
            <Divider sx={{ my: 1 }} />
            <Typography variant="subtitle2" gutterBottom color="error">
              Failed:
            </Typography>
            {successAndFailedMeasurements
              .filter((m) => !m.isSuccess)
              .map((m, i) => (
                <Typography key={i} variant="body2" color="error">
                  {m.tag} – Last successful:{' '}
                  {formatDate(new Date(m.lastFetchTime ?? 0), dateFormats)}
                </Typography>
              ))}
          </>
        )}
        {allFailed && allFailed.length > 0 && (
          <>
            <Typography variant="subtitle2" gutterBottom>
              Failed Measurements
            </Typography>
            {allFailed.map((m, i) => (
              <Typography key={i} variant="body2">
                {m.tag} –{' '}
                {m.lastFetchTime !== undefined
                  ? formatDate(new Date(m.lastFetchTime), dateFormats)
                  : 'There is no fetch'}
              </Typography>
            ))}
          </>
        )} */}
      </Box>
    );
  };
  const WidgetContent = () => {
    return (
      <Box
        sx={{
          height: '100%',
          '.widget-drag-handle': {
            opacity: 0,
          },
          '&:hover .widget-drag-handle': {
            opacity: 1,
          },
          '.react-resizable-handle': {
            display: 'none',
          },
          '&:hover .react-resizable-handle': {
            display: 'block',
          },
          '.widget-settings': {
            visibility: 'hidden',
            position: 'absolute',
            right: 0,
            top: 0,
          },
          ':hover .widget-settings': {
            visibility: 'visible',
          },
        }}
      >
        {router.pathname !== '/dynamic-chart' && !router.query['measurement_trend'] && (
          <Box className="widget-settings">
            {!settings.isChildWidget && (options || hasPowerUserAccess) && (
              <IconButton
                id="widget-settings-icon"
                edge="start"
                color="inherit"
                aria-label="open drawer"
                onClick={handleMenu}
                sx={{
                  float: 'right',
                  zIndex: 10,
                  '@media (max-width: 600px)': {
                    display: 'none',
                  },
                }}
              >
                <Tooltip title="Options">
                  <MoreVertIcon />
                </Tooltip>
              </IconButton>
            )}

            {!settings.isChildWidget && (
              <>
                <IconButton
                  id="widget-settings-icon"
                  edge="start"
                  color="inherit"
                  aria-label="open drawer"
                  onClick={handleExpandWidget}
                  sx={{
                    float: 'right',
                    zIndex: 10,
                    mr: 0.5,
                  }}
                >
                  {widgetExpanded ? (
                    <Tooltip title="Collapse">
                      <CloseFullscreenOutlinedIcon />
                    </Tooltip>
                  ) : (
                    <Tooltip title="Expand">
                      <OpenInFullOutlinedIcon />
                    </Tooltip>
                  )}
                </IconButton>
              </>
            )}

            {!settings.isChildWidget && (
              <>
                <IconButton
                  id="title-widget-pin-icon"
                  edge="start"
                  color="inherit"
                  aria-label="lock drawer"
                  onClick={makePin}
                  sx={{
                    float: 'right',
                    zIndex: 10,
                    mr: 0.5,
                    '@media (max-width: 600px)': {
                      display: 'none',
                    },
                  }}
                >
                  {widgetLayout?.static ? (
                    <Tooltip title="Unpin">
                      <PushPinIcon />
                    </Tooltip>
                  ) : (
                    <Tooltip title="Pin">
                      <PushPinOutlinedIcon />
                    </Tooltip>
                  )}
                </IconButton>
              </>
            )}

            {dashboardLink &&
            dashboardLink.dashboard &&
            dashboardLink.dashboard !== null &&
            !(
              settings.mode === 'dashboard' &&
              settings.dashboardOrTemplate === 'template' &&
              (isMeasurementsFetching ||
                isTemplateError ||
                isLoadingDashboardTemplates ||
                dashboardTemplateExists <= 0 ||
                isLoadingDashboardTemplatesList)
            ) ? (
              <IconButton
                id={'title-widget-link-icon-' + id}
                edge="start"
                color="inherit"
                className="widget-drag-handle"
                sx={{
                  float: 'right',
                  opacity: 0,
                  transition: 'opacity 0.3s',
                  zIndex: 10,
                  mr: 0.5,
                }}
                disabled={
                  settings.mode === 'dashboard' &&
                  settings.dashboardOrTemplate === 'template' &&
                  (isMeasurementsFetching ||
                    isTemplateError ||
                    isLoadingDashboardTemplates ||
                    dashboardTemplateExists <= 0 ||
                    isLoadingDashboardTemplatesList)
                }
                {...(settings.mode === 'dashboard' ? { onClick: openDashboard } : {})}
              >
                <Tooltip
                  title={
                    <Typography variant="body2" color="inherit" fontSize={'0.7rem'}>
                      <>
                        {settings.dashboardOrTemplate === 'dashboard' ? (
                          <>Open Dashboard - {dashboardLink.dashboard.title}</>
                        ) : (
                          <>Open Dashboard Template- {dashboardLink.dashboard.title}</>
                        )}
                      </>
                    </Typography>
                  }
                >
                  <LaunchIcon />
                </Tooltip>
              </IconButton>
            ) : null}

            {hasPowerUserAccess &&
            !widgetLayout?.static &&
            !widgetExpanded &&
            !settings.isChildWidget ? (
              <IconButton
                id={'title-widget-drag-icon-' + id}
                edge="start"
                color="inherit"
                className="drag-handle widget-drag-handle"
                sx={{
                  float: 'right',
                  opacity: 0,
                  transition: 'opacity 0.3s',
                  zIndex: 10,
                  mr: 0.5,
                  '@media (max-width: 600px)': {
                    display: 'none',
                  },
                }}
              >
                <Tooltip title="Drag">
                  <DragIndicator />
                </Tooltip>
              </IconButton>
            ) : null}

            <Menu
              id={'widget-settings-menu-' + id}
              aria-labelledby={'title-widget-drag-icon' + id}
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleClose}
            >
              {menuOptions}
              {options !== undefined
                ? options.map((option, i) => (
                    <MenuItem
                      key={i}
                      onClick={() => {
                        handleClose();
                        option.method();
                      }}
                    >
                      {option.content}
                    </MenuItem>
                  ))
                : null}
              {hasPowerUserAccess && [
                <MenuItem onClick={cloneWidget} key={'clone-widget'}>
                  <ContentCopyIcon style={{ marginRight: '5px' }} />
                  Clone Widget
                </MenuItem>,

                <MenuItem
                  key={'widget-settings'}
                  onClick={() => {
                    handleClose();
                    if (customSettingsDialog !== undefined) {
                      setCustomSettingsDialogOpen?.(true);
                    } else {
                      handleDialogOpen();
                    }
                  }}
                >
                  <SettingsIcon style={{ marginRight: '5px' }} />
                  Widget Settings
                </MenuItem>,

                <MenuItem onClick={() => onDelete(id)} key={'delete-widget'}>
                  <DeleteIcon sx={{ mr: 1 }} />
                  Delete Widget
                </MenuItem>,
              ]}
            </Menu>
          </Box>
        )}
        {hasOverriddenSettings ? (
          <Tooltip
            title={
              <>
                <Typography variant="subtitle2" color="inherit">
                  This widget has overridden settings
                </Typography>
                {overriddenSettingsArray
                  .filter((setting) => setting.overridenSetting)
                  .map((setting) => setting.settingType)
                  .join(',')}
              </>
            }
          >
            <Badge
              variant="dot"
              sx={{
                color: 'white',
                position: 'absolute',
                top: 15,
                left: 20,
                height: '6px',
                width: '6px',
                minHeight: '6px',
                minWidth: '6px',
                '& .MuiBadge-badge': {
                  height: '100%',
                  width: '100%',
                  borderRadius: '50%',
                  background: '#5358ef',
                  fontSize: '0.6rem',
                  backgroundColor: 'gray',
                  minWidth: 'unset',
                },
              }}
            />
          </Tooltip>
        ) : null}
        {successAndFailedMeasurements && successAndFailedMeasurements.length > 0 && (
          <Tooltip
            title={<ToolTipContent />}
            componentsProps={{
              tooltip: {
                sx: {
                  bgcolor: 'background.paper',
                  boxShadow: 3,
                  borderRadius: 3,
                  p: 1,
                  maxWidth: 'none',
                },
              },
            }}
          >
            <Badge
              variant="dot"
              sx={{
                position: 'absolute',
                top: 15,
                left: 10,
                height: '6px',
                width: '6px',
                minHeight: '6px',
                minWidth: '6px',
                '& .MuiBadge-badge': {
                  height: '100%',
                  width: '100%',
                  borderRadius: '50%',
                  fontSize: '0.6rem',
                  backgroundColor: color,
                  minWidth: 'unset',
                  ...(settings.isRealTime
                    ? {
                        position: 'relative',
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          height: '100%',
                          borderRadius: '50%',
                          backgroundColor: color, // use your dot color
                          opacity: 0.4,
                          animation: 'ripple 1.5s ease-out infinite',
                          transform: 'scale(1)',
                          zIndex: 1,
                        },
                        '@keyframes ripple': {
                          '0%': {
                            transform: 'scale(1)',
                            opacity: 0.8,
                          },
                          '100%': {
                            transform: 'scale(2.5)',
                            opacity: 0,
                          },
                        },
                      }
                    : undefined),
                },
              }}
            />
          </Tooltip>
        )}
        {/* {settings.isRealTime && settings.refreshInterval && (
          <Tooltip
            title={
              <>
                <Typography variant="subtitle2" color="inherit">
                  Realtime mode and refresh interval
                </Typography>
                {overriddenSettingsArray
                  .filter((setting) => setting.overridenSetting)
                  .map((setting) => setting.settingType)
                  .join(',')}
              </>
            }
          >
            <Badge
              variant="dot"
              sx={{
                position: 'absolute',
                top: 20,
                left: 30,
                height: '10px',
                width: '10px',
                '& .MuiBadge-badge': {
                  height: '100%',
                  width: '100%',
                  borderRadius: '50%',
                  fontSize: '0.6rem',
                  backgroundColor: (theme) => theme.palette.success.main,
                },
              }}
            />
          </Tooltip>
        )}
        */}
        <Box
          sx={{
            width: '100%',
            height: '100%',
          }}
        >
          {widgetContent}
        </Box>
        {settingsDialog && (
          <CustomDialog
            open={open}
            maxWidth="lg"
            onClose={handleCloseDialog}
            title={
              widgetName
                ? `${widgetName.charAt(0).toUpperCase() + widgetName.slice(1)} Settings`
                : 'None'
            }
            content={
              settingsDialog({
                settings: currentSettings,
                handleSettingsChange: setCurrentSettings,
              }) as ReactNode
            }
            dialogActions={
              <>
                <Button
                  autoFocus
                  sx={{ mr: 'auto' }}
                  size="medium"
                  variant="outlined"
                  startIcon={<CancelIcon />}
                  onClick={handleCloseDialog}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  size="medium"
                  startIcon={<SaveAsIcon />}
                  disabled={!currentSettings.isValid}
                  onClick={() => {
                    handleCloseDialog();
                    onUpdateClick({ ...currentSettings, isDirty: true });
                  }}
                >
                  Update
                </Button>
              </>
            }
          />
        )}
        {customSettingsDialog !== undefined ? <>{customSettingsDialog}</> : null}
        <DeleteWidgetDialog
          widgetId={id}
          open={deleteDialog}
          widgetName={widgetType}
          onCancel={() => setDeleteDialog(false)}
        />
      </Box>
    );
  };
  return (
    <>
      {widgetExpanded ? (
        <CustomDialog
          content={<>{WidgetContent()}</>}
          dialogActions={null}
          onClose={handleExpandWidget}
          open={widgetExpanded}
          title={null}
          fullScreen={true}
          showCloseOnTop={false}
        />
      ) : (
        WidgetContent()
      )}
      <CustomDialog
        title="You have unsaved changes."
        content={<Typography color={'error'}>Do you want to still proceed?</Typography>}
        dialogActions={
          <>
            <Button
              onClick={() => {
                setConfirm(false);
              }}
              variant="outlined"
              startIcon={<CancelIcon />}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setConfirm(false);
                if (dashboardLink.mode === 'dashboard') {
                  setDashboard();
                } else if (dashboardLink.mode === 'template') {
                  if (dashboardLink.dashboardOrTemplate === 'dashboard') {
                    setDashboard();
                  }
                  if (dashboardLink.dashboardOrTemplate === 'template') {
                    dispatch(
                      dashboardSlice.actions.setTemplateId(dashboardLink.dashboard?.id ?? 0),
                    );
                    dispatch(
                      dashboardSlice.actions.setTemplateName(dashboardLink.dashboard?.title ?? ''),
                    );
                  }
                }
              }}
              variant="contained"
              color="error"
            >
              Proceed
            </Button>
          </>
        }
        onClose={() => setConfirm(false)}
        open={confirm}
      />
    </>
  );
};

export default CommonWidgetContainer;
