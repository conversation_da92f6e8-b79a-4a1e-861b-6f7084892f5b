import {
  Breakpoint,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Tooltip,
  Typography,
} from '@mui/material';
import React, { ReactNode } from 'react';
import DialogTransition from './DialogTransition';
import CloseIcon from '@mui/icons-material/Close';

interface DialogProps {
  open: boolean;
  title: string | ReactNode;
  content: ReactNode;
  onClose: () => void;
  dialogActions: ReactNode;
  fullScreen?: boolean;
  maxWidth?: Breakpoint;
  showCloseOnTop?: boolean;
}

const CustomDialog: React.FC<DialogProps> = ({
  open,
  title,
  content,
  onClose,
  dialogActions,
  fullScreen,
  maxWidth,
  showCloseOnTop = true,
}) => {
  return (
    <Dialog
      sx={{
        '& .MuiPaper-root': {
          borderRadius: 2,
        },
      }}
      maxWidth={maxWidth}
      open={open}
      onClose={onClose}
      fullWidth
      TransitionComponent={DialogTransition}
      fullScreen={fullScreen}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Typography variant="inherit">{title}</Typography>
        {showCloseOnTop ? (
          <Tooltip title="Close">
            <IconButton
              aria-label="close"
              onClick={onClose}
              sx={{
                borderRadius: '50%', // Ensure the close button itself has a rounded appearance
                '&:hover': {
                  backgroundColor: (theme) => theme.palette.grey[200], // Optional hover effect
                },
              }}
            >
              <CloseIcon />
            </IconButton>
          </Tooltip>
        ) : null}
      </DialogTitle>
      <DialogContent>{content}</DialogContent>
      {dialogActions && (
        <DialogActions sx={{ p: 2 }} className="actions">
          {dialogActions}
        </DialogActions>
      )}
    </Dialog>
  );
};

export default CustomDialog;
