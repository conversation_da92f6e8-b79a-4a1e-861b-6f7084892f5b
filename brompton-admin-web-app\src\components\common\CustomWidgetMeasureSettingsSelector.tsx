import { Box, Card, FormGroup, TextField, Typography } from '@mui/material';
import React, { Fragment, useEffect, useState } from 'react';
import { ImageTextDetails, ImageWidgetFontsProps, ImageWidgetSettings } from '~/types/widgets';
import MeasurementDashboard from '../ImageWidget/MeasurementDashboard';

type CustomWidgetMeasureSettingsSelectorProps<T extends ImageWidgetSettings> = {
  selectedDbMeasureIdToName: {
    [key: string]: string;
  };
  selectedMeasureNames: string[];
} & ImageWidgetFontsProps<T>;
type MeasureUnitsLabelProps<T extends ImageWidgetSettings> = {
  name: string;
  measureId: string;
  settings: T;
  setSettings: (value: (prevState: T) => T) => void;
  labelAndUnits: {
    label: string;
    unit: string;
    dashboard: {
      id: number;
      title: string;
    } | null;
    dashboardOrTemplate: 'dashboard' | 'template';
    assetOrAssetType: number | null;
    openDashboardInNewTab: boolean;
  };
};
const MeasureUnitsLabel = ({
  name,
  labelAndUnits,
  measureId,
  settings,
  setSettings,
}: MeasureUnitsLabelProps<ImageWidgetSettings>) => {
  const [label, setLabel] = useState<string>(labelAndUnits.label);
  const [unit, setUnit] = useState<string>(labelAndUnits.unit);

  useEffect(() => {
    setSettings((prevState) => {
      const { measureIdToImageTextDetails, ...rest } = prevState;
      const newMeasureIdToImageTextDetails = { ...measureIdToImageTextDetails };
      newMeasureIdToImageTextDetails[measureId] = {
        ...newMeasureIdToImageTextDetails[measureId],
        label,
        unit,
      };
      return {
        ...rest,
        measureIdToImageTextDetails: newMeasureIdToImageTextDetails,
      };
    });
  }, [label, measureId, setSettings, unit]);
  const setOnChangeLabel = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLabel(e.target.value);
  };
  return (
    <>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
        }}
      >
        <FormGroup
          sx={{
            width: '100%',
          }}
        >
          <TextField
            name="value"
            onChange={setOnChangeLabel}
            value={label}
            label={'Measurement Label for ' + name}
            variant="outlined"
            margin="normal"
            fullWidth
          />
        </FormGroup>
      </Box>
      <MeasurementDashboard
        labelAndUnits={{ ...labelAndUnits }}
        measureId={measureId}
        setSettings={setSettings}
        settings={settings}
      />
    </>
  );
};
type MeasureSettingProps<T extends ImageWidgetSettings> = {
  dbMeasureId: string;
  name: string;
  measureIdToImageTextDetails: Record<string, ImageTextDetails>;
  setSettings: (value: (prevState: T) => T) => void;
  settings: T;
};
const MeasureSetting = ({
  dbMeasureId,
  name,
  measureIdToImageTextDetails,
  setSettings,
  settings,
}: MeasureSettingProps<ImageWidgetSettings>) => {
  return (
    <Box mt={2}>
      {Object.entries(measureIdToImageTextDetails).map(([measureId, lableAndUnit], index) => (
        <Fragment key={index}>
          {dbMeasureId === measureId ? (
            <MeasureUnitsLabel
              name={name}
              labelAndUnits={lableAndUnit}
              measureId={measureId}
              setSettings={setSettings}
              settings={settings}
            />
          ) : null}
        </Fragment>
      ))}
    </Box>
  );
};
const CustomWidgetMeasureSettingsSelector = ({
  selectedDbMeasureIdToName,
  selectedMeasureNames,
  settings,
  setSettings,
}: CustomWidgetMeasureSettingsSelectorProps<ImageWidgetSettings>) => {
  return (
    <>
      {selectedMeasureNames.length > 0 ? (
        <Box mt={3} mb={3} component={Card} sx={{ p: 2 }}>
          {selectedMeasureNames.length > 0 && (
            <Typography variant="h5" mt={2}>
              Measurement Labels
            </Typography>
          )}
          {Object.entries(selectedDbMeasureIdToName).map(([dbMeasureId, name], index) => (
            <Fragment key={index}>
              {selectedMeasureNames.includes(dbMeasureId) ? (
                <MeasureSetting
                  key={dbMeasureId}
                  name={name}
                  dbMeasureId={dbMeasureId}
                  measureIdToImageTextDetails={settings.measureIdToImageTextDetails}
                  setSettings={setSettings}
                  settings={settings}
                  //   dbMeasureIdToSetting={dbMeasureIdToSetting}
                  //   handleDbMeasureIdToSettingUpdate={handleDbMeasureIdToSettingUpdate}
                />
              ) : null}
            </Fragment>
          ))}
        </Box>
      ) : null}
    </>
  );
};

export default CustomWidgetMeasureSettingsSelector;
