import { Alert, Snackbar, Typography } from '@mui/material';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import { ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { assetsApi } from '~/redux/api/assetsApi';
import { dashboardTemplateApi } from '~/redux/api/dashboardTemplate';
import { measuresApi } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getDashboardCrumb } from '~/redux/selectors/dashboardSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { RootState } from '~/redux/store';
import { DashboardCrumbsState, DashboardState, TimeRangeOptions } from '~/types/dashboard';
import { assetsPathMapper, getPreviousDate } from '~/utils/utils';

const DashboardsCrumb = () => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const crumbs = useSelector(getDashboardCrumb);
  const activeCustomer = useSelector(getActiveCustomer);
  const router = useRouter();
  const [openSnackBar, setOpenSnackBar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });
  const removeCrumb = async (index: number, crumb: DashboardCrumbsState) => {
    if (crumb.dashboardId !== -2) {
      dispatch(dashboardSlice.actions.removeDashboardCrumb(index));
      dispatch(dashboardSlice.actions.setCurrentDashboardId(crumb.dashboardId));
      dispatch(dashboardSlice.actions.setCurrentDashboardTitle(''));
      dispatch(
        dashboardSlice.actions.setWidget({
          widgets: [],
          deleteWidgets: [],
          lastWidgetId: 0,
          widgetLayout: [],
        }),
      );
      dispatch(dashboardSlice.actions.setWidgetsLayout([]));
      router.push(`/customer/${activeCustomer?.id}/dashboard/${crumb.dashboardId}`);
    } else {
      const { assetId, assetType, templateId } = crumb;
      if (assetId === undefined || assetType === undefined || templateId === undefined) {
        return;
      }
      setOpenSnackBar({
        open: true,
        message: 'Fetching dashboard template data...',
        severity: 'info',
      });
      const { data: assetMeasurements } = await dispatch(
        measuresApi.endpoints.getAllMeasurements.initiate({
          customerId: activeCustomer?.id ?? 0,
          assetId: assetId ?? 0,
        }),
      );
      const { data: assetsData } = await dispatch(
        assetsApi.endpoints.getAllAsset.initiate({
          customerId: activeCustomer?.id ?? 0,
          parentIds: [],
        }),
      );
      const assetTypesWithPathForAsset = assetsPathMapper(assetsData ?? []);

      const {
        isError,
        error,
        data: templateData,
      } = await dispatch(
        dashboardTemplateApi.endpoints.getDashboardTemplateDetails.initiate(templateId ?? 0),
      );
      if (isError) {
        setOpenSnackBar({
          open: true,
          message: `Error fetching dashboard template data: ${error}`,
          severity: 'error',
        });
        return;
      }
      if (templateData && templateData?.data && assetMeasurements) {
        const templateDetailsData = JSON.parse(templateData.data) as {
          widget: DashboardState['widget'];
          topPanel: DashboardState['template']['topPanel'];
          chart: DashboardState['template']['chart'];
        };
        const metricToMeasurementMap: Record<string, string[]> = {};
        assetMeasurements?.forEach((measurement) => {
          if (measurement.metric_id !== null) {
            const metricIdStr = measurement.metric_id.toString();
            const measurementIdStr = measurement.id.toString();

            if (!metricToMeasurementMap[metricIdStr]) {
              metricToMeasurementMap[metricIdStr] = [];
            }

            metricToMeasurementMap[metricIdStr].push(measurementIdStr);
          }
        });
        templateDetailsData.widget.widgets.map((widget) => {
          if (widget.type === 'chart') {
            widget.settings.settings.dashboardOrTemplate =
              widget.settings.settings.dashboardOrTemplate ?? 'template';
            widget.settings.settings.assetOrAssetType = assetId ?? null;
            widget.settings.settings.mode = 'dashboard';
            if (
              'assetMeasure' in widget.settings.settings &&
              widget.settings.settings.assetMeasure
            ) {
              if (Array.isArray(widget.settings.settings.assetMeasure)) {
                const measureIds: string[] = [];
                if ('selectedTitles' in widget.settings.settings) {
                  widget.settings.settings.selectedTitles.forEach((title) => {
                    if (metricToMeasurementMap[title]) {
                      measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                    }
                  });
                  widget.settings.settings.selectedTitles = [];
                }
                widget.settings.settings.assetMeasure.push({
                  assetId: assetId?.toString() ?? '',
                  measureId: measureIds,
                });
              } else {
                const measureIds: string[] = [];
                if (
                  'selectedDbMeasureId' in widget.settings.settings &&
                  metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId]
                ) {
                  measureIds.push(
                    ...metricToMeasurementMap[widget.settings.settings.selectedDbMeasureId],
                  );
                  widget.settings.settings.selectedDbMeasureId = '';
                }
                widget.settings.settings.assetMeasure.assetId = assetId?.toString() ?? '';
                widget.settings.settings.assetMeasure.measureId = measureIds;
              }
            }
            return widget;
          }

          widget.settings.dashboardOrTemplate = widget.settings.dashboardOrTemplate ?? 'template';
          widget.settings.assetOrAssetType = assetId ?? null;
          widget.settings.mode = 'dashboard';
          if (widget.type === 'dashboard-widget') {
            const assetPath = assetTypesWithPathForAsset.find(
              (assetTypeData) => assetTypeData.value === assetType,
            );
            widget.settings.assetOption = {
              id: assetId ?? 0,
              label: assetPath?.label ?? '',
            };
          }
          if (widget.type === 'map') {
            widget.settings.markers = widget.settings.markers.map((marker) => {
              if (marker.selectedTitles.length > 0) {
                const measureIds: string[] = [];
                marker.selectedTitles.forEach((title) => {
                  if (metricToMeasurementMap[title]) {
                    measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                  }
                });
                measureIds.forEach((measureId) => {
                  marker.assetMeasures.push({
                    assetId: assetId?.toString() ?? '',
                    measureId: measureId,
                  });
                });
                let labelUnits = {};
                measureIds.forEach((measureId) => {
                  const measure = assetMeasurements?.find(
                    (measure) => measure.id === Number(measureId),
                  );

                  labelUnits = {
                    ...labelUnits,
                    [measureId]: {
                      label: measure?.tag ?? '',
                      unit: '',
                      value: '',
                    },
                  };
                });
                marker.labelAndUnits = labelUnits;
              }
              marker.selectedTitles = [];
              return marker;
            });
          } else {
            if ('assetMeasure' in widget.settings && widget.settings.assetMeasure) {
              if (Array.isArray(widget.settings.assetMeasure)) {
                const measureIds: string[] = [];
                if ('selectedTitles' in widget.settings) {
                  widget.settings.selectedTitles.forEach((title) => {
                    if (metricToMeasurementMap[title]) {
                      measureIds.push(...metricToMeasurementMap[title]); // <-- Fix here
                    }
                  });
                  widget.settings.selectedTitles = [];
                }
                if (widget.type === 'image') {
                  widget.settings.dashboardOrTemplate =
                    widget.settings.dashboardOrTemplate ?? 'template';
                  widget.settings.assetOrAssetType = assetId ?? null;
                  widget.settings.imgDashboard = {
                    ...widget.settings.imgDashboard,
                    assetOrAssetType: assetId ?? null,
                    // dashboard:
                    openDashboardInNewTab: false,
                    dashboardOrTemplate: widget.settings.dashboardOrTemplate ?? 'dashboard',
                  };
                  widget.settings.mode = 'dashboard';
                }
                widget.settings.assetMeasure.push({
                  assetId: assetId?.toString() ?? '',
                  measureId: measureIds,
                });
              } else {
                const measureIds: string[] = [];
                if (
                  'selectedDbMeasureId' in widget.settings &&
                  metricToMeasurementMap[widget.settings.selectedDbMeasureId]
                ) {
                  measureIds.push(...metricToMeasurementMap[widget.settings.selectedDbMeasureId]);
                  widget.settings.selectedDbMeasureId = '';
                }
                if ('dbMeasureIdToName' in widget.settings) {
                  const selectedDbMeasureIdToName: {
                    [key: string]: string;
                  } = {};
                  measureIds.forEach((measureId) => {
                    const measureName = assetMeasurements?.find(
                      (measure) => measure.id === Number(measureId),
                    )?.tag;
                    selectedDbMeasureIdToName[measureId] = measureName ?? '';
                  });
                  widget.settings.dbMeasureIdToName = selectedDbMeasureIdToName;
                }
                widget.settings.assetMeasure.assetId = assetId?.toString() ?? '';
                widget.settings.assetMeasure.measureId = measureIds;
              }
            }
          }
          if (widget.type === 'Diagram') {
            widget.settings.mode = 'dashboard';
            Object.keys(widget.settings.elementIdVariabels).forEach((elementId) => {
              const variables = widget.settings.elementIdVariabels[elementId];

              variables.forEach((variable) => {
                const measurementKey = variable.measurementId;
                variable.aggBy = variable.aggBy ?? 1;
                // Only replace if there's a mapping available
                if (metricToMeasurementMap[measurementKey]) {
                  const mappedIds = metricToMeasurementMap[measurementKey];

                  // Only assign the first mapped ID (if single select expected), or modify to handle arrays
                  variable.assetId = assetId?.toString() ?? '';
                  variable.measurementId = mappedIds[0];

                  // Optional: clear label or other fields if needed
                  // variable.label = '';
                }
              });
            });
            widget.settings.elementVariable = widget.settings.elementVariable.map((variable) => {
              const measurementKey = variable.measurementId;
              // Only replace if there's a mapping available
              if (metricToMeasurementMap[measurementKey]) {
                const mappedIds = metricToMeasurementMap[measurementKey];

                // Only assign the first mapped ID (if single select expected), or modify to handle arrays
                variable.assetId = assetId?.toString() ?? '';
                variable.measurementId = mappedIds[0];

                // Optional: clear label or other fields if needed
                // variable.label = '';
              }
              variable.aggBy = variable.aggBy ?? 1;
              return variable;
            });
          }
          return widget;
        });
        router.push(`/customer/${activeCustomer?.id}/dashboard/-2`);
        dispatch(dashboardSlice.actions.setCurrentDashboardId(-2));
        dispatch(
          dashboardSlice.actions.setWidget({
            widgets: templateDetailsData.widget.widgets,
            deleteWidgets: [],
            widgetLayout: templateDetailsData.widget.widgetLayout,
            lastWidgetId: templateDetailsData.widget.lastWidgetId,
          }),
        );
        dispatch(dashboardSlice.actions.setWidgetsLayout(templateDetailsData.widget.widgetLayout));
        dispatch(dashboardSlice.actions.setSamplePeriod(templateDetailsData.topPanel.samplePeriod));
        dispatch(
          dashboardSlice.actions.setRefreshTimeInterval(
            templateDetailsData.topPanel.refreshInterval,
          ),
        );
        dispatch(
          dashboardSlice.actions.setTimeRangeType(templateDetailsData.topPanel.timeRangeType),
        );
        const minutes: number =
          TimeRangeOptions[templateDetailsData.topPanel.timeRangeType ?? 6].serverValue;
        const start = getPreviousDate(minutes);
        if (templateDetailsData.topPanel.timeRangeType !== 0) {
          dispatch(dashboardSlice.actions.setChartStartDate(new Date(start)));
          dispatch(dashboardSlice.actions.setChartEndDate(new Date()));
        } else {
          dispatch(
            dashboardSlice.actions.setChartStartDate(new Date(templateDetailsData.chart.startDate)),
          );
          dispatch(
            dashboardSlice.actions.setChartEndDate(new Date(templateDetailsData.chart.endDate)),
          );
        }
        dispatch(dashboardSlice.actions.removeDashboardCrumb(index));
        dispatch(dashboardSlice.actions.setCurrentDashboardId(-2));
      }
    }
  };
  useEffect(() => {
    const handleBackNavigation = () => {
      dispatch(dashboardSlice.actions.resetDashboardCrumb());
    };
    window.addEventListener('popstate', handleBackNavigation);
    return () => {
      window.removeEventListener('popstate', handleBackNavigation);
    };
  }, [crumbs, removeCrumb, router]);
  return (
    <>
      <Breadcrumbs aria-label="breadcrumb" separator=">">
        {crumbs.map((crumb, index) => (
          <Typography
            variant="body1"
            sx={
              index === crumbs.length - 1
                ? { cursor: 'pointer', fontWeight: 'bold' }
                : { cursor: 'pointer' }
            }
            key={index}
            onClick={async () => {
              await removeCrumb(index, crumb);
            }}
          >
            {crumb.dashboardTitle}
          </Typography>
        ))}
      </Breadcrumbs>
      <Snackbar
        open={openSnackBar.open}
        onClose={() => {
          setOpenSnackBar({ open: false, message: '', severity: 'info' });
        }}
        autoHideDuration={3000}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity={openSnackBar.severity}>{openSnackBar.message}</Alert>
      </Snackbar>
    </>
  );
};

export default DashboardsCrumb;
