import AddIcon from '@mui/icons-material/Add';
import { Box, Button } from '@mui/material';
import { GridToolbarQuickFilter } from '@mui/x-data-grid';
import { Dispatch, SetStateAction } from 'react';

function QuickSearchToolbar({
  setShowFilter,
  filteredData,
}: {
  setShowFilter: Dispatch<SetStateAction<boolean>>;
  filteredData?: React.ReactNode;
}) {
  return (
    <Box>
      <Box
        sx={{
          p: 0.5,
          pb: 0,
          display: 'flex',
          gap: 2,
          mb: 2,
        }}
      >
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          sx={{
            height: 50,
          }}
          onClick={() => {
            setShowFilter(true);
          }}
        >
          Filter
        </Button>
        <GridToolbarQuickFilter
          variant="outlined"
          InputProps={{
            sx: {
              width: 320,
              borderRadius: '10px',
              border: '1px solid #E0E0E0',
              '& .MuiInputBase-input': {
                px: 1,
                py: 1.5,
              },
            },
          }}
        />
      </Box>
      {filteredData ? filteredData : null}
    </Box>
  );
}

export default QuickSearchToolbar;
