import CancelIcon from '@mui/icons-material/Cancel';
import LaunchIcon from '@mui/icons-material/Launch';
import {
  Alert,
  Autocomplete,
  Box,
  Button,
  Card,
  Checkbox,
  CircularProgress,
  FormControl,
  FormControlLabel,
  IconButton,
  Radio,
  RadioGroup,
  SelectChangeEvent,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Role, useRolePermission } from '~/hooks/useRolePermission';
import { useGetAllAssetQuery, useGetAllBackOfficeAssetTypesQuery } from '~/redux/api/assetsApi';
import { useGetDashboardByCustomerIdQuery } from '~/redux/api/dashboardApi';
import { useGetDashboardTemplatesQuery } from '~/redux/api/dashboardTemplate';
import { getEndDate, getStartDate } from '~/redux/selectors/chartSelectors';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import {
  getCurrentAssetType,
  getCurrentDashboardId,
  isDashboardDirty,
  selectDashboardState,
} from '~/redux/selectors/dashboardSelectors';
import { getDeletedWidgets, getWidgets } from '~/redux/selectors/widgetSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { AssetTypeOption } from '~/types/asset';
import { AggByOptions, SamplePeriodOptions } from '~/types/dashboard';
import {
  NonRealTimeWidgets,
  OverrideAssetTzWidgets,
  Widgets,
  setOverrideAssetTzWidgets,
  setSettings,
} from '~/types/widgets';
import { assetTypePathMapperFilterTemplates } from '~/utils/mappers/asset-type-mapper';
import { assetsPathMapper } from '~/utils/utils';
import { AggregateBy } from './AggregateBy';
import CommonRealTimeSettings from './CommonRealTimeSettings';
import CustomDialog from './CustomDialog';
import OverRideGlobalSettings from './OverRideGlobalSettings';
import { SamplePeriod } from './SamplePeriod';
import WidgetAssetTz from './WidgetAssetTz';

type DataWidgetContainerProps<T extends Widgets> = {
  id: string;
  settings: T;
  setSettings: (value: ((prevState: T) => T) | T) => void;
  children?: React.ReactNode;
};
const DataWidgetContainer = ({
  id,
  children,
  settings,
  setSettings,
}: DataWidgetContainerProps<Widgets>) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const globalendDate = useSelector(getEndDate);
  const globalStartDate = useSelector(getStartDate);
  const dashboardId = useSelector(getCurrentDashboardId);
  const [showNoneWarning, setShowNoneWarning] = useState<boolean>(false);
  const [showWarning, setShowWarning] = useState<boolean>(false);
  const [showTimeRangeError, setShowTimeRangeError] = useState<boolean>(false);
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const [confirm, setConfirm] = useState<boolean>(false);
  const dashboardState = useSelector(selectDashboardState);
  const activeCustomer = useSelector(getActiveCustomer);
  const { hasDashboardPermission } = useRolePermission();
  const widgets = useSelector(getWidgets);
  const deleteWidgets = useSelector(getDeletedWidgets);
  const isDashboardStateDirty = useSelector(isDashboardDirty);
  const assetTypeTemplate = useSelector(getCurrentAssetType);
  const [assetToAssetType, seAssetToAssetType] = useState<number | null>(null);
  const { aggBy, samplePeriod, globalSamplePeriod } = settings;
  const { data: dashboardList, isLoading: isLoadingDashboards } = useGetDashboardByCustomerIdQuery(
    {
      customerId: activeCustomer?.id ?? 0,
      search: null,
    },
    {
      skip: !activeCustomer?.id,
    },
  );
  const { data: assetData, isFetching: isAssetReloading } = useGetAllAssetQuery(
    { customerId: activeCustomer?.id ?? 0, parentIds: [] },
    {
      skip: !activeCustomer || settings.mode === 'template',
      refetchOnMountOrArgChange: true,
    },
  );
  const assetsWithPath = useMemo(() => assetsPathMapper(assetData ?? []), [assetData]);

  const {
    data: assetTypeListData,
    isLoading: isAssetTypeLoading,
    isSuccess: isSuccessfullBackOffieAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery(undefined, {
    skip: settings.mode === 'dashboard' || !assetTypeTemplate || assetTypeTemplate <= 0,
  });
  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapperFilterTemplates(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes]);

  useEffect(() => {
    if (settings.mode === 'template' && settings.assetOrAssetType === null) {
      setSettings({
        ...settings,
        assetOrAssetType: assetTypeTemplate,
      });
    }
  }, [settings.assetOrAssetType, settings.mode, assetTypeTemplate]);
  useEffect(() => {
    if (
      assetData &&
      settings.mode === 'dashboard' &&
      settings.dashboardOrTemplate === 'template' &&
      settings.assetOrAssetType !== null
    ) {
      const assetToType = assetData?.find(
        (asset) => asset.id === settings.assetOrAssetType,
      )?.assetTypeId;
      seAssetToAssetType(assetToType ?? null);
    }
  }, [settings.assetOrAssetType, settings.mode, assetData, settings.dashboardOrTemplate]);
  const { data: dashboardTemplates, isLoading: isLoadingDashboardTemplates } =
    useGetDashboardTemplatesQuery(
      {
        assetTypeId:
          settings.mode === 'dashboard' ? assetToAssetType ?? 0 : settings.assetOrAssetType ?? 0,
      },
      {
        skip:
          settings.mode === 'dashboard'
            ? assetToAssetType === null || assetToAssetType === 0
            : !assetTypeTemplate || assetTypeTemplate <= 0 || settings.assetOrAssetType === null,
      },
    );
  useEffect(() => {
    if (dashboardTemplates && settings.dashboardOrTemplate === 'template') {
      const template =
        dashboardTemplates?.items?.find((dashboard) => dashboard.id === settings.dashboard?.id) ??
        null;
      setSettings({
        ...settings,
        dashboard:
          settings.dashboardOrTemplate === 'template' ? template ?? null : settings.dashboard,
      });
    }
  }, [dashboardTemplates, settings.dashboardOrTemplate]);
  const handleAggByChange = (event: SelectChangeEvent<number>) => {
    const index = event.target.value as number;
    setSettings({
      ...settings,
      aggBy: AggByOptions[index].value,
    });
  };
  useEffect(() => {
    let isInvalid = false;
    let showWarningMessage = false;
    let showNoneError = false;
    let isInvalidTimeRange = false;
    // Case 1: Sample period + aggBy warning
    if (settings.aggBy === 5 && settings.samplePeriod === 14 && globalSamplePeriod) {
      isInvalid = true;
      showWarningMessage = true;
    }

    // Case 2: Time difference validation for aggBy === 0
    if (settings.aggBy === 0) {
      const start = settings.overrideGlobalSettings ? settings.startDate : globalStartDate;
      const end = settings.overrideGlobalSettings ? settings.endDate : globalendDate;

      const timeDifferenceHours = (end - start) / (1000 * 60 * 60);
      if (timeDifferenceHours > 6) {
        isInvalid = true;
        showNoneError = true;
      }
    }

    // Case 3: Manual range – start date must be before end date
    if (
      settings.timeRange === 0 &&
      settings.startDate &&
      settings.endDate &&
      settings.startDate >= settings.endDate
    ) {
      isInvalid = true;
      isInvalidTimeRange = true;
    }

    // Update warnings
    setShowWarning(showWarningMessage);
    setShowNoneWarning(showNoneError);
    setShowTimeRangeError(isInvalidTimeRange);
    // Update settings validity once
    setSettings((prev) => ({
      ...prev,
      isValid: !isInvalid,
    }));
  }, [
    settings.aggBy,
    settings.samplePeriod,
    globalSamplePeriod,
    settings.overrideGlobalSettings,
    settings.startDate,
    settings.endDate,
    globalStartDate,
    globalendDate,
    settings.timeRange,
  ]);
  const handleSamplePeriodChange = (event: SelectChangeEvent<number>) => {
    const index = event.target.value as number;
    if (index === 14 && settings.aggBy === 5) {
      setShowWarning(true);
      setSettings({
        ...settings,
        isValid: false,
        samplePeriod: SamplePeriodOptions[index].value,
      });
      return;
    }
    setShowWarning(false);
    setSettings({
      ...settings,
      samplePeriod: SamplePeriodOptions[index].value,
      isValid: true,
    });
  };
  const setOverRideSettings = (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
    const { name } = event.target;
    let sameplePeriod = samplePeriod;
    if (name === 'globalSamplePeriod') {
      sameplePeriod = checked ? samplePeriod : dashboardState.topPanel.samplePeriod;
    }
    let timeRange = settings.timeRange;
    if (name === 'overrideGlobalSettings') {
      timeRange = checked ? settings.timeRange : dashboardState.topPanel.timeRangeType;
    }
    setSettings({
      ...settings,
      samplePeriod: sameplePeriod,
      timeRange: timeRange,
      [event.target.name]: checked,
    });
  };
  const handleChangeDashboard = (
    e: React.SyntheticEvent,
    value: { id: number; title: string } | null,
  ) => {
    setSettings((prevState) => ({
      ...prevState,
      dashboard: value,
      isDirty: true,
    }));
  };
  const handleDashboardChange = () => {
    if (dashboardId === -2) {
      return false;
    }
    if (!hasDashboardPermission('dashboard.update', Role.POWER_USER)) {
      return false;
    }
    if (
      widgets.filter((widget) => widget.type !== 'chart' && widget.settings.isDirty).length > 0 ||
      deleteWidgets.length > 0 ||
      widgets.filter((widget) => widget.type === 'chart' && widget.settings?.settings.isDirty)
        .length > 0 ||
      isDashboardStateDirty
    ) {
      setConfirm(true);
      return true;
    }
    return false;
  };
  const openDashboard = () => {
    if (handleDashboardChange()) return;
    const dashboard = settings.dashboard;
    const mode = settings.mode;
    const dashboardOrTemplate = settings.dashboardOrTemplate;
    if (mode === 'dashboard' && dashboard !== null) {
      // if (settings.openDashboardInNewTab) {
      //   window.open(
      //     `${router.basePath}/customer/${activeCustomer?.id}/dashboard/${settings.dashboard?.id}`,
      //     '_blank',
      //   );
      //   return;
      // }
      dispatch(
        dashboardSlice.actions.setDashboardCrumb({
          dashboardId: dashboard.id,
          title: dashboard.title,
        }),
      );
      dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
      dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
      router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
    }
    if (mode === 'template' && dashboard !== null) {
      if (dashboardOrTemplate === 'dashboard') {
        dispatch(
          dashboardSlice.actions.setDashboardCrumb({
            dashboardId: dashboard.id,
            title: dashboard.title,
            templateId: dashboard.id,
          }),
        );
        dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
        dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
        router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
      }
      if (dashboardOrTemplate === 'template') {
        dispatch(dashboardSlice.actions.setTemplateId(dashboard.id));
        dispatch(dashboardSlice.actions.setTemplateName(dashboard.title));
        dispatch(
          dashboardSlice.actions.setWidget({
            widgets: [],
            deleteWidgets: [],
            widgetLayout: [],
            lastWidgetId: 0,
          }),
        );
      }
    }
  };
  const handleDashboardOrTemplate = (event: React.ChangeEvent<HTMLInputElement>, value: string) => {
    setSettings((prevState) => ({
      ...prevState,
      dashboardOrTemplate: value as 'dashboard' | 'template',
      dashboard: null,
      isDirty: true,
    }));
  };
  return (
    <>
      <Box mt={2}>
        {!settings.isRealTime && id !== 'sankey' ? (
          <Box mb={2}>
            <Card sx={{ p: 2 }}>
              <AggregateBy id={id} value={aggBy} handleChange={handleAggByChange} />
              {showWarning ? (
                <Alert variant="outlined" severity="error" sx={{ mt: 2 }}>
                  Reset the aggregate option in widget setting where <strong>STD.P</strong> is
                  selected.
                </Alert>
              ) : null}
              {showNoneWarning ? (
                <Alert variant="outlined" severity="error" sx={{ mt: 2 }}>
                  Time range with more then <strong>6 hours</strong> time period is not allowed
                </Alert>
              ) : null}
            </Card>
          </Box>
        ) : null}
        <Box mb={2}>
          <Card>{children}</Card>
        </Box>

        {id !== 'sankey' && (
          <Box mb={2}>
            <Card>
              <Box sx={{ p: 2, pb: 0 }}>
                <FormControl component="fieldset">
                  <RadioGroup
                    row
                    value={settings.dashboardOrTemplate}
                    onChange={handleDashboardOrTemplate}
                    name="dashboardOrTemplate"
                  >
                    <FormControlLabel value="template" control={<Radio />} label="Template" />
                    <FormControlLabel value="dashboard" control={<Radio />} label="Dashboard" />
                  </RadioGroup>
                </FormControl>
              </Box>
              {settings.mode === 'template' && settings.dashboardOrTemplate === 'template' ? (
                <Box sx={{ display: 'flex', mt: 2 }}>
                  <Autocomplete
                    fullWidth
                    disablePortal
                    loading={isAssetReloading}
                    id="combo-box-demo"
                    options={assetTypesWithPath.map((item) => {
                      return {
                        id: item.value.toString(),
                        label: item.label,
                      };
                    })}
                    value={
                      assetTypesWithPath
                        .map((item) => {
                          return {
                            id: item.value.toString(),
                            label: item.label,
                          };
                        })
                        .find((item) => item.id === settings!.assetOrAssetType?.toString()) ?? null
                    }
                    sx={{ p: 2, pb: 0, pt: 0 }}
                    onChange={(event, value) => {
                      setSettings({
                        ...settings,
                        assetOrAssetType: Number(value?.id) ?? null,
                      });
                    }}
                    renderInput={(params) => <TextField {...params} label="Asset Type" />}
                  />
                </Box>
              ) : null}
              {settings.mode === 'dashboard' && settings.dashboardOrTemplate === 'template' ? (
                <Box sx={{ display: 'flex', mt: 2, pl: 2, pr: 2 }}>
                  <Autocomplete
                    fullWidth
                    id={`asset-autocomplete`}
                    loading={isAssetReloading}
                    options={
                      !isAssetReloading
                        ? assetsWithPath.map((asset) => ({
                            label: asset.label,
                            id: asset.id,
                          }))
                        : []
                    }
                    getOptionLabel={(option) => option?.label ?? ''}
                    onChange={(event, value) => {
                      setSettings({
                        ...settings,
                        assetOrAssetType: value?.id ?? null,
                      });
                    }}
                    value={assetsWithPath?.find((asset) => asset.id === settings!.assetOrAssetType)}
                    renderInput={(params) => (
                      <TextField {...params} label="Select Asset" variant="outlined" />
                    )}
                  />
                </Box>
              ) : null}
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Autocomplete
                  loading={isLoadingDashboards || isLoadingDashboardTemplates}
                  id="dashboards-combo-box"
                  options={
                    settings.dashboardOrTemplate === 'dashboard'
                      ? dashboardList?.items?.map((dashboard) => ({
                          id: dashboard.id,
                          title: dashboard.title,
                        })) ?? []
                      : dashboardTemplates?.items?.map((dashboardTemp) => ({
                          id: dashboardTemp.id,
                          title: dashboardTemp.title,
                        })) ?? []
                  }
                  getOptionLabel={(option) => option.title}
                  onChange={handleChangeDashboard}
                  sx={{ width: '100%', p: 2 }}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  // value={
                  //   settings.dashboardOrTemplate === 'template'
                  //     ? dashboardTemplates?.items?.find(
                  //         (dashboard) => dashboard.id === settings.dashboard?.id,
                  //       ) ?? null
                  //     : settings.dashboard ?? null
                  // }
                  value={settings.dashboard ?? null}
                  loadingText={
                    settings.dashboardOrTemplate === 'dashboard'
                      ? 'Loading Dashboards...'
                      : 'Loading Dashboard Templates...'
                  }
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label={
                        settings.dashboardOrTemplate === 'template'
                          ? 'Link Dashboard Template'
                          : 'Link Dashboard'
                      }
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {isLoadingDashboards || isLoadingDashboardTemplates ? (
                              <CircularProgress color="inherit" size={20} />
                            ) : null}
                            {params.InputProps.endAdornment}
                          </>
                        ),
                      }}
                    />
                  )}
                />
                {settings.dashboard !== null && settings.dashboard?.id && (
                  <IconButton
                    disableRipple
                    disableTouchRipple
                    id={'title-widget-link-icon-' + id}
                    edge="start"
                    color="inherit"
                    sx={{
                      zIndex: 10,
                      mr: 0.5,
                    }}
                    {...(settings.mode === 'dashboard' ? { onClick: openDashboard } : {})}
                  >
                    <Tooltip
                      title={
                        <Typography variant="body2" color="inherit" fontSize={'0.7rem'}>
                          {settings.dashboardOrTemplate === 'dashboard' ? (
                            <>Open Dashboard - {settings.dashboard.title}</>
                          ) : (
                            <>Open Dashboard Template- {settings.dashboard.title}</>
                          )}

                          {settings.mode}
                        </Typography>
                      }
                    >
                      <LaunchIcon />
                    </Tooltip>
                  </IconButton>
                )}
              </Box>
              {settings.dashboard !== null && settings.dashboard?.id && (
                <FormControl fullWidth>
                  <FormControlLabel
                    control={
                      <Checkbox
                        sx={{ mt: 1, ml: 2, mb: 1 }}
                        color="primary"
                        checked={settings.openDashboardInNewTab}
                        onChange={(
                          event: React.ChangeEvent<HTMLInputElement>,
                          checked: boolean,
                        ) => {
                          if (checked) {
                            setSettings({
                              ...settings,
                              openDashboardInNewTab: checked,
                            });
                          } else {
                            setSettings({
                              ...settings,
                              openDashboardInNewTab: false,
                            });
                          }
                        }}
                      />
                    }
                    label="Open in New Tab"
                  />
                </FormControl>
              )}
            </Card>
          </Box>
        )}

        <Box mt={2} mb={2}>
          <CommonRealTimeSettings
            settings={settings as NonRealTimeWidgets}
            setSettings={
              setSettings as (
                value: NonRealTimeWidgets | ((prevState: NonRealTimeWidgets) => NonRealTimeWidgets),
              ) => void
            }
          />
        </Box>
        {!settings.isRealTime && (
          <>
            <Box mb={2}>
              <WidgetAssetTz
                settings={settings as OverrideAssetTzWidgets}
                setSettings={setSettings as setOverrideAssetTzWidgets}
              />
            </Box>
            <Box>
              <SamplePeriod
                id={id}
                value={samplePeriod}
                handleChange={handleSamplePeriodChange}
                globalSamplePeriod={globalSamplePeriod}
                setOverRideSettings={setOverRideSettings}
              />
              {showWarning ? (
                <Alert variant="outlined" severity="error" sx={{ mt: 2 }}>
                  Reset the aggregate option in widget setting where <strong>STD.P</strong> is
                  selected.
                </Alert>
              ) : null}
            </Box>

            <Box mt={2}>
              <OverRideGlobalSettings
                settings={settings}
                setSettings={setSettings as setSettings}
                isInvalidTimeRange={showTimeRangeError}
              />
            </Box>
          </>
        )}
      </Box>
      <CustomDialog
        title="You have unsaved changes."
        content={<Typography color={'error'}>Do you want to still proceed?</Typography>}
        dialogActions={
          <>
            <Button
              onClick={() => {
                setConfirm(false);
              }}
              variant="outlined"
              startIcon={<CancelIcon />}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setConfirm(false);
                dispatch(
                  dashboardSlice.actions.setDashboardCrumb({
                    dashboardId: settings?.dashboard?.id ?? 0,
                    title: settings?.dashboard?.title ?? '',
                  }),
                );
                if (settings.openDashboardInNewTab) {
                  window.open(
                    `${router.basePath}/customer/${activeCustomer?.id}/dashboard/${settings.dashboard?.id}`,
                    '_blank',
                  );
                  return;
                }
                router.push(
                  `/customer/${activeCustomer?.id}/dashboard/${settings.dashboard?.id ?? 0}`,
                );
                dispatch(dashboardSlice.actions.setCurrentDashboardId(settings.dashboard?.id ?? 0));
                dispatch(
                  dashboardSlice.actions.setCurrentDashboardTitle(settings.dashboard?.title ?? ''),
                );
              }}
              variant="contained"
              color="error"
            >
              Proceed
            </Button>
          </>
        }
        onClose={() => setConfirm(false)}
        open={confirm}
      />
    </>
  );
};

export default DataWidgetContainer;
