import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Divider, SxProps, Theme } from '@mui/material';

type CommonLayoutCardProps = {
  title: string & React.ReactNode;
  children: React.ReactNode;
  sx?: SxProps<Theme> | undefined;
  boxSx?: SxProps<Theme>;
};
const CommonLayoutCard = ({ title, children, sx, boxSx }: CommonLayoutCardProps) => {
  return (
    <Card sx={{ p: 2, ...sx }}>
      <CardHeader title={title} sx={{ pl: 0, pt: 0 }} />
      <Divider />
      <Box sx={{ width: '100%', mt: 2, ...boxSx }}>{children}</Box>
    </Card>
  );
};

export default CommonLayoutCard;
