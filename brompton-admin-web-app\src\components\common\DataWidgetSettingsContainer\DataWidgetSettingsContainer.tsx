import { <PERSON><PERSON>, Box, Divider, Tab, Tabs } from '@mui/material';
import React, { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { getEndDate, getStartDate } from '~/redux/selectors/chartSelectors';
import { Widgets } from '~/types/widgets';
import { DataWidgetSettingsContainerProps } from './DataWidgetSettingsContainerSettings';
import WidgetDataSettingsTabContainer from './WidgetDataSettingsTabContainer';
import WidgetLookFeelContainer from './WidgetLookFeelContainer';
import WidgetTimeContext from './WidgetTimeContext';

const DataWidgetSettingsContainer = <T extends Widgets>({
  children,
  settings,
  setSettings,
  dataTabChildren,
  feelTabChidren,
  timeContextChildren,
  exculdedSettings,
  hideSettings,
}: DataWidgetSettingsContainerProps<T>) => {
  const [tabIndex, setTabIndex] = useState(0);
  const [showNoneWarning, setShowNoneWarning] = useState<boolean>(false);
  const [showWarning, setShowWarning] = useState<boolean>(false);
  const [showTimeRangeError, setShowTimeRangeError] = useState<boolean>(false);
  const globalendDate = useSelector(getEndDate);
  const globalStartDate = useSelector(getStartDate);
  const [isThereAssetMeasure, setIsThereAssetMeasure] = useState<boolean>(false);
  const handleChange = useCallback((_event: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
  }, []);
  const excludedHash = JSON.stringify(exculdedSettings || {});

  useEffect(() => {
    let isInvalid = false;
    let showWarningMessage = false;
    let showNoneError = false;
    let isInvalidTimeRange = false;
    const shouldRunCondition = !(
      exculdedSettings?.['aggBy'] &&
      exculdedSettings?.['samplePeriod'] &&
      exculdedSettings?.['globalSamplePeriod']
    );
    if (
      shouldRunCondition &&
      settings.aggBy === 5 &&
      settings.samplePeriod === 14 &&
      settings.globalSamplePeriod
    ) {
      isInvalid = true;
      showWarningMessage = true;
    }
    const shouldRunNoneCondition = !exculdedSettings?.['aggBy'];
    if (shouldRunNoneCondition && settings.aggBy === 0) {
      const start = settings.overrideGlobalSettings ? settings.startDate : globalStartDate;
      const end = settings.overrideGlobalSettings ? settings.endDate : globalendDate;

      const timeDifferenceHours = (end - start) / (1000 * 60 * 60);
      if (timeDifferenceHours > 6) {
        isInvalid = true;
        showNoneError = true;
      }
    }
    const shouldRunTimeRangeCondition = !exculdedSettings?.['timeRange'];
    if (
      shouldRunTimeRangeCondition &&
      settings.timeRange === 0 &&
      settings.startDate &&
      settings.endDate &&
      settings.startDate >= settings.endDate
    ) {
      isInvalid = true;
      isInvalidTimeRange = true;
    }
    let isValidAssetMeausure = false;
    if (settings.mode === 'dashboard') {
      if ('subplots' in settings && settings.subplots) {
        let subplotsRule = false;
        for (const subplot of settings.subplots) {
          for (const assetMeasure of subplot.assetMeasures) {
            if (assetMeasure.assetId && assetMeasure.measureId.length > 0) {
              subplotsRule = true;
              break;
            }
          }
          if (subplotsRule) {
            break;
          }
        }
        setIsThereAssetMeasure(subplotsRule);
        isValidAssetMeausure = subplotsRule;
        if (!isValidAssetMeausure) {
          isInvalid = true;
        }
      } else {
        if ('image' in settings && settings.image && settings.image !== '') {
          setIsThereAssetMeasure(true);
          isValidAssetMeausure = true;
        }
        if ('assetMeasure' in settings && settings.assetMeasure) {
          if (Array.isArray(settings.assetMeasure)) {
            setIsThereAssetMeasure(settings.assetMeasure.length > 0);
            isValidAssetMeausure = settings.assetMeasure.some(
              (assetMeas) =>
                assetMeas.assetId.trim() !== '' &&
                assetMeas.measureId.some((measure) => measure.trim() !== ''),
            );
            if (
              'image' in settings &&
              'allowUpload' in settings &&
              settings.image &&
              (settings.image !== '' || settings.image !== null)
            ) {
              setIsThereAssetMeasure(true);
              isValidAssetMeausure = true;
            }
            if ('measurementTypes' in settings && 'assetTypes' in settings) {
              if (
                'measurementTypes' in settings &&
                Array.isArray(settings.measurementTypes) &&
                settings.measurementTypes.length > 0
              ) {
                setIsThereAssetMeasure(true);
                isValidAssetMeausure = true;
              }
              if (
                'assetTypes' in settings &&
                Array.isArray(settings.assetTypes) &&
                settings.assetTypes.length > 0
              ) {
                console.log(settings.assetTypes);
                setIsThereAssetMeasure(true);
                isValidAssetMeausure = true;
              }
              if ('assetTypes' in settings && 'measurementTypes' in settings) {
                setIsThereAssetMeasure(settings.assetMeasure.length > 0);
                isValidAssetMeausure = settings.assetMeasure.some(
                  (assetMeas) => assetMeas.assetId.trim() !== '',
                );
              }
            }
            if (!isValidAssetMeausure) {
              isInvalid = true;
            }
          }
          if (settings.assetMeasure && !Array.isArray(settings.assetMeasure)) {
            setIsThereAssetMeasure(
              settings.assetMeasure.assetId.trim() !== '' &&
                settings.assetMeasure.measureId.some((measure) => measure.trim() !== ''),
            );
            isValidAssetMeausure =
              settings.assetMeasure.assetId.trim() !== '' &&
              settings.assetMeasure.measureId.some((measure) => measure.trim() !== '');
            if (!isValidAssetMeausure) {
              isInvalid = true;
            }
          }
        }
        if ('markers' in settings && settings.markers) {
          const markerRule = settings.markers.some((marker) => marker.assetMeasures.length > 0);
          setIsThereAssetMeasure(markerRule);
          isValidAssetMeausure = markerRule;
          if (!isValidAssetMeausure) {
            isInvalid = true;
          }
        }
      }
    }
    if (settings.mode === 'template') {
      if ('image' in settings && settings.image && settings.image !== '') {
        setIsThereAssetMeasure(true);
        isValidAssetMeausure = true;
      } else {
        if ('selectedTitles' in settings && settings.selectedTitles) {
          setIsThereAssetMeasure(settings.selectedTitles.length > 0);
          isValidAssetMeausure = settings.selectedTitles.length > 0;
          if (!isValidAssetMeausure) {
            isInvalid = true;
          }
        }
        if ('selectedDbMeasureId' in settings && settings.selectedDbMeasureId) {
          const selectedDbMeasureId = settings.selectedDbMeasureId.trim() !== '';
          setIsThereAssetMeasure(selectedDbMeasureId);
          isValidAssetMeausure = selectedDbMeasureId;
          if (isValidAssetMeausure) {
            isInvalid = false;
          }
        }
        if ('markers' in settings && settings.markers) {
          const markerRule = settings.markers.some((marker) => marker.selectedTitles.length > 0);
          setIsThereAssetMeasure(markerRule);
          isValidAssetMeausure = markerRule;
          if (!isValidAssetMeausure) {
            isInvalid = true;
          }
        }

        if ('subplots' in settings && Array.isArray(settings.subplots)) {
          const hasSelectedDbMeasure = settings.subplots.some((subplot) =>
            subplot.assetMeasures.some(
              (am) => am.selectedDbMeasureId && am.selectedDbMeasureId.trim() !== '',
            ),
          );
          setIsThereAssetMeasure(hasSelectedDbMeasure);
          if (hasSelectedDbMeasure) {
            isInvalid = false;
          }
        }
      }
    }
    setShowWarning(showWarningMessage);
    setShowNoneWarning(showNoneError);
    setShowTimeRangeError(isInvalidTimeRange);
    setSettings((prev) => ({
      ...prev,
      isValid: !isInvalid,
    }));
  }, [
    settings.aggBy,
    settings.samplePeriod,
    settings.globalSamplePeriod,
    settings.overrideGlobalSettings,
    settings.startDate,
    settings.endDate,
    globalStartDate,
    globalendDate,
    settings.timeRange,
    settings.mode,
    (settings as any).assetMeasure,
    (settings as any).markers,
    (settings as any).selectedTitles,
    (settings as any).selectedDbMeasureId,
    (settings as any).dbMeasureIdToName,
    (settings as any).image,
    (settings as any).allowUpload,
    (settings as any).subplots,
    (settings as any).assetTypes,
    (settings as any).measurementTypes,
    excludedHash,
  ]);
  const renderActiveTab = () => {
    switch (tabIndex) {
      case 0:
        return (
          <Box>
            <WidgetDataSettingsTabContainer
              settings={settings}
              setSettings={setSettings}
              hideSettings={hideSettings}
            >
              {dataTabChildren}
            </WidgetDataSettingsTabContainer>
            {children}
          </Box>
        );
      case 1:
        return (
          <WidgetLookFeelContainer
            settings={settings}
            setSettings={setSettings}
            hideSettings={hideSettings}
          >
            {feelTabChidren}
          </WidgetLookFeelContainer>
        );
      case 2:
        return (
          <WidgetTimeContext
            settings={settings}
            setSettings={setSettings}
            showTimeRangeError={showTimeRangeError}
            exculdedSettings={exculdedSettings}
            hideSettings={hideSettings}
          >
            {timeContextChildren}
          </WidgetTimeContext>
        );
      default:
        return null;
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Tabs
        value={tabIndex}
        onChange={handleChange}
        aria-label="Widget settings tabs"
        variant="fullWidth"
      >
        <Tab label="Data" />
        <Tab label="Look & feel" />
        {!hideSettings?.['timeContext'] ? <Tab label="Time Context" /> : null}
      </Tabs>
      <Divider sx={{ mb: 2 }} />
      {showWarning && (
        <Alert variant="outlined" severity="error" sx={{ mb: 1 }}>
          Please adjust the aggregate option. <strong>STD.P</strong> is not compatible with the
          selected sample period.
        </Alert>
      )}
      {showNoneWarning && (
        <Alert variant="outlined" severity="error" sx={{ mb: 1 }}>
          Time ranges longer than <strong>6 hours</strong> are not supported with the current
          settings.
        </Alert>
      )}
      {showTimeRangeError && (
        <Alert variant="outlined" severity="error">
          The selected start date must be earlier than the end date. Please adjust the date range.
        </Alert>
      )}
      {renderActiveTab()}
    </Box>
  );
};

export default DataWidgetSettingsContainer;
