import React, { useCallback } from 'react';
import { Widgets } from '~/types/widgets';
import TitleSettings from '../TitleSettings';
import CommonLayoutCard from './CommonLayoutCard';
import { WidgetLookFeelContainerProps } from './DataWidgetSettingsContainerSettings';

const WidgetLookFeelContainer = <T extends Widgets>({
  settings,
  setSettings,
  children,
  hideSettings,
}: WidgetLookFeelContainerProps<T>) => {
  const title = settings.title ?? { value: '', isVisible: false, color: '#000000' };
  const fontSize = settings.title?.fontSize ?? 12;
  const fontWeight = settings.title?.fontWeight ?? 'normal';

  const handleTitleChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value, checked, type } = event.target;
      setSettings((prev) => ({
        ...prev,
        title: {
          ...prev.title,
          [name]: type === 'checkbox' ? checked : value,
        },
      }));
    },
    [setSettings],
  );

  const handleFontSize = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = parseInt(event.target.value, 10);
      setSettings((prev) => ({
        ...prev,
        title: {
          ...prev.title,
          fontSize: value,
        },
      }));
    },
    [setSettings],
  );

  const handleColorChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const { value } = event.target;
      setSettings((prev) => ({
        ...prev,
        title: {
          ...prev.title,
          color: value,
        },
      }));
    },
    [setSettings],
  );

  const handleFontWeight = useCallback(
    (event: any) => {
      const value = event.target.value;
      setSettings((prev) => ({
        ...prev,
        title: {
          ...prev.title,
          fontWeight: value,
        },
      }));
    },
    [setSettings],
  );

  return (
    <>
      {!hideSettings?.['title'] && (
        <CommonLayoutCard title="Title">
          <TitleSettings
            title={title}
            fontSize={fontSize}
            fontWeight={fontWeight}
            handleTitleChange={handleTitleChange}
            handleFontSize={handleFontSize}
            handleFontWeight={handleFontWeight}
            handleColorChange={handleColorChange}
          />
        </CommonLayoutCard>
      )}

      {/* <Box component={Card} sx={{ width: '100%', mt: 2 }}> */}
      {children ? (
        <CommonLayoutCard title="Widget settings" sx={{ mt: 2 }}>
          {children}
        </CommonLayoutCard>
      ) : null}
      {/* </Box> */}
    </>
  );
};

export default WidgetLookFeelContainer;
