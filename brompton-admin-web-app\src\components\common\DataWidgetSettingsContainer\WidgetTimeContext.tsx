import { Box } from '@mui/material';
import { ChangeEvent, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useGetAllAssetQuery, useGetAllBackOfficeAssetTypesQuery } from '~/redux/api/assetsApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getCurrentAssetType } from '~/redux/selectors/dashboardSelectors';
import { AssetTypeOption } from '~/types/asset';
import { SamplePeriodOptions } from '~/types/dashboard';
import {
  NonRealTimeWidgets,
  OverrideAssetTzWidgets,
  setOverrideAssetTzWidgets,
  Widgets,
} from '~/types/widgets';
import { assetTypePathMapperFilterTemplates } from '~/utils/mappers/asset-type-mapper';
import CommonRealTimeSettings from '../CommonRealTimeSettings';
import OverRideGlobalSettings, { setSettings } from '../OverRideGlobalSettings';
import { SamplePeriod } from '../SamplePeriod';
import WidgetAssetTz from '../WidgetAssetTz';
import CommonLayoutCard from './CommonLayoutCard';
import { WidgetTimeContextProps } from './DataWidgetSettingsContainerSettings';

const WidgetTimeContext = <T extends Widgets>({
  showTimeRangeError,
  children,
  settings,
  setSettings,
  exculdedSettings,
  hideSettings,
}: WidgetTimeContextProps<T>) => {
  // handle local states
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);

  // handling redux state selectors
  const assetTypeTemplate = useSelector(getCurrentAssetType);
  const activeCustomer = useSelector(getActiveCustomer);
  const [assetToAssetType, seAssetToAssetType] = useState<number | null>(null);

  const { data: assetData, isFetching: isAssetReloading } = useGetAllAssetQuery(
    { customerId: activeCustomer?.id ?? 0, parentIds: [] },
    {
      skip: !activeCustomer || settings.mode === 'template',
      refetchOnMountOrArgChange: true,
    },
  );

  const {
    data: assetTypeListData,
    isLoading: isAssetTypeLoading,
    isSuccess: isSuccessfullBackOffieAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery(undefined, {
    skip: settings.mode === 'dashboard' || !assetTypeTemplate || assetTypeTemplate <= 0,
  });
  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapperFilterTemplates(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes]);

  useEffect(() => {
    if (
      assetData &&
      settings.mode === 'dashboard' &&
      settings.dashboardOrTemplate === 'template' &&
      settings.assetOrAssetType !== null
    ) {
      const assetToType = assetData?.find(
        (asset) => asset.id === settings.assetOrAssetType,
      )?.assetTypeId;
      seAssetToAssetType(assetToType ?? null);
    }
  }, [settings.assetOrAssetType, settings.mode, assetData, settings.dashboardOrTemplate]);

  return (
    <>
      {!hideSettings?.['realtime'] && (
        <CommonLayoutCard
          title={'Realtime'}
          sx={{
            mb: 2,
          }}
          boxSx={{
            p: 0,
            mt: 0,
            '& > *': {
              p: 0,
              boxShadow: 'unset !important',
            },
          }}
        >
          <CommonRealTimeSettings
            settings={settings as NonRealTimeWidgets}
            setSettings={
              setSettings as (
                value: NonRealTimeWidgets | ((prevState: NonRealTimeWidgets) => NonRealTimeWidgets),
              ) => void
            }
          />
        </CommonLayoutCard>
      )}
      {!settings.isRealTime && (
        <CommonLayoutCard
          title={'Override Settings'}
          boxSx={{
            '& > *': {
              marginBottom: 2,
              padding: 1,
            },
          }}
        >
          {!exculdedSettings?.['overrideAssetTz'] ? (
            <WidgetAssetTz
              settings={settings as OverrideAssetTzWidgets}
              setSettings={setSettings as setOverrideAssetTzWidgets}
            />
          ) : null}
          {!exculdedSettings?.['realtime'] ? (
            <>
              {!hideSettings?.['globalSamplePeriod'] && (
                <SamplePeriod
                  id={'sample-period'}
                  value={settings.samplePeriod}
                  handleChange={(e) => {
                    const index = e.target.value as number;
                    if (index === 14 && settings.aggBy === 5) {
                      setSettings({
                        ...settings,
                        isValid: false,
                        samplePeriod: SamplePeriodOptions[index].value,
                      });
                      return;
                    }
                    setSettings({
                      ...settings,
                      samplePeriod: SamplePeriodOptions[index].value,
                      isValid: true,
                    });
                  }}
                  globalSamplePeriod={settings.globalSamplePeriod}
                  setOverRideSettings={(event: ChangeEvent<HTMLInputElement>, checked: boolean) => {
                    setSettings({
                      ...settings,
                      globalSamplePeriod: checked,
                    });
                  }}
                />
              )}
              {!hideSettings?.['overrideGlobalSettings'] && (
                <OverRideGlobalSettings
                  settings={settings}
                  setSettings={setSettings as setSettings}
                  isInvalidTimeRange={showTimeRangeError}
                />
              )}
            </>
          ) : null}
        </CommonLayoutCard>
      )}
    </>
  );
};
export default WidgetTimeContext;
