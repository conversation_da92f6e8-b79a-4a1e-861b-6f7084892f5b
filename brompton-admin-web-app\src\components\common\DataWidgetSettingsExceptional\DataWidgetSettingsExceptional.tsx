import { Box, SelectChangeEvent, Tab, Tabs } from '@mui/material';
import { useCallback, useState } from 'react';
import { DashboardWidget, TitleWidget } from '~/types/widgets';
import CommonLayoutCard from '../DataWidgetSettingsContainer/CommonLayoutCard';
import TitleSettings from '../TitleSettings';
import { DEFAULT_TITLE_WIDGET } from '~/redux/slices/dashboardSlice';
type DataWidgetSettingExceptionalSettingsProps<T extends DashboardWidget | TitleWidget> = {
  children?: React.ReactNode;
  settings: Omit<
    T,
    | 'aggBy'
    | 'samplePeriod'
    | 'overrideGlobalSettings'
    | 'isRelativeToGlboalEndTime'
    | 'timeRange'
    | 'startDate'
    | 'endDate'
    | 'globalSamplePeriod'
    | 'showForecast'
    | 'period'
  >;
  setSettings: (
    value:
      | ((
          prevState: Omit<
            T,
            | 'aggBy'
            | 'samplePeriod'
            | 'overrideGlobalSettings'
            | 'isRelativeToGlboalEndTime'
            | 'timeRange'
            | 'startDate'
            | 'endDate'
            | 'globalSamplePeriod'
            | 'showForecast'
            | 'period'
          >,
        ) => Omit<
          T,
          | 'aggBy'
          | 'samplePeriod'
          | 'overrideGlobalSettings'
          | 'isRelativeToGlboalEndTime'
          | 'timeRange'
          | 'startDate'
          | 'endDate'
          | 'globalSamplePeriod'
          | 'showForecast'
          | 'period'
        >)
      | Omit<
          T,
          | 'aggBy'
          | 'samplePeriod'
          | 'overrideGlobalSettings'
          | 'isRelativeToGlboalEndTime'
          | 'timeRange'
          | 'startDate'
          | 'endDate'
          | 'globalSamplePeriod'
          | 'showForecast'
          | 'period'
        >,
  ) => void;
  dataTabChildren?: React.ReactNode;
  feelTabChidren?: React.ReactNode;
};
const DataWidgetSettingExceptionalSettings = <T extends DashboardWidget>({
  settings,
  setSettings,
  children,
  dataTabChildren,
  feelTabChidren,
}: DataWidgetSettingExceptionalSettingsProps<T>) => {
  const [tabIndex, setTabIndex] = useState(0);
  const handleChange = useCallback((_event: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
  }, []);
  const handleFontSize = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const fontSize = Number(event.target.value);
    setSettings((prevSettings) => ({
      ...prevSettings,
      title: {
        ...prevSettings.title,
        fontSize: fontSize,
      },
    }));
  };
  const handleFontWeight = (event: SelectChangeEvent<string>, child: React.ReactNode) => {
    setSettings((prevSettings) => ({
      ...prevSettings,
      title: {
        ...prevSettings.title,
        fontWeight: event.target.value,
      },
    }));
  };
  const handleColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings((prevSettings) => ({
      ...prevSettings,
      title: {
        ...prevSettings.title,
        color: event.target.value,
      },
    }));
  };
  const handleTitleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.name === 'value') {
      setSettings((prevSettings) => ({
        ...prevSettings,
        title: {
          ...prevSettings.title,
          value: event.target.value,
        },
      }));
    } else if (event.target.name === 'isVisible') {
      setSettings((prevSettings) => ({
        ...prevSettings,
        title: {
          ...prevSettings.title,
          isVisible: event.target.checked,
          value: event.target.checked ? prevSettings.title.value : 'Title',
        },
      }));
    }
  };
  const renderActiveTab = () => {
    switch (tabIndex) {
      case 0:
        return <Box>{dataTabChildren}</Box>;
      case 1:
        return (
          <CommonLayoutCard title="Title">
            <TitleSettings
              title={settings?.title ?? DEFAULT_TITLE_WIDGET.title.value ?? ''}
              fontSize={
                (settings?.title && settings.title.fontSize) ??
                DEFAULT_TITLE_WIDGET.title.fontSize ??
                12
              }
              fontWeight={settings?.title?.fontWeight ?? DEFAULT_TITLE_WIDGET.title.fontWeight}
              handleTitleChange={handleTitleChange}
              handleFontSize={handleFontSize}
              handleFontWeight={handleFontWeight}
              handleColorChange={handleColorChange}
            />
            {feelTabChidren}
          </CommonLayoutCard>
        );

      default:
        return null;
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Tabs
        value={tabIndex}
        onChange={handleChange}
        aria-label="Widget settings tabs"
        variant="fullWidth"
      >
        <Tab label="Data" />
        <Tab label="Look & feel" />
      </Tabs>
      {renderActiveTab()}
    </Box>
  );
};
export default DataWidgetSettingExceptionalSettings;
