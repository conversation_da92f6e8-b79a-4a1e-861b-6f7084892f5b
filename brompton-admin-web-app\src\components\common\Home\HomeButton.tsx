import { HomeOutlined } from '@mui/icons-material';
import { Box, Button, SxProps } from '@mui/material';
import Link from 'next/link';
import { useDispatch, useSelector } from 'react-redux';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getCurrentDashboardId } from '~/redux/selectors/dashboardSelectors';
import { dashboardListSlice } from '~/redux/slices/dashboardListSlice';
type HomeButtonProps = {
  sx?: SxProps<any>;
};
const HomeButton = ({ sx }: HomeButtonProps) => {
  const currentDashboard = useSelector(getCurrentDashboardId);
  const activeCustomer = useSelector(getActiveCustomer);
  const dispatch = useDispatch();
  return (
    <Box sx={{ width: '100%', display: 'flex', justifyContent: 'end', ...sx }}>
      <Link
        href={
          activeCustomer?.id !== undefined
            ? `/customer/${activeCustomer?.id}/dashboard/${currentDashboard}`
            : '/customer'
        }
        onClick={() => {
          dispatch(dashboardListSlice.actions.setSearchValue(null));
        }}
      >
        <Button variant="outlined">
          <HomeOutlined />
        </Button>
      </Link>
    </Box>
  );
};

export default HomeButton;
