import CalculateIcon from '@mui/icons-material/Calculate';
import CancelIcon from '@mui/icons-material/Cancel';
import EmergencyShareIcon from '@mui/icons-material/EmergencyShare';
import HomeIcon from '@mui/icons-material/Home';
import LayersOutlinedIcon from '@mui/icons-material/LayersOutlined';
import LockIcon from '@mui/icons-material/Lock';
import LogoutIcon from '@mui/icons-material/Logout';
import ManageAccountsOutlinedIcon from '@mui/icons-material/ManageAccountsOutlined';
import MenuIcon from '@mui/icons-material/Menu';
import PeopleIcon from '@mui/icons-material/People';
import SettingsAccessibilityIcon from '@mui/icons-material/SettingsAccessibility';
import SpaceDashboardOutlinedIcon from '@mui/icons-material/SpaceDashboardOutlined';
import SupportOutlinedIcon from '@mui/icons-material/SupportOutlined';
import TextureIcon from '@mui/icons-material/Texture';
import DataThresholdingIcon from '@mui/icons-material/DataThresholding';

import {
  AppBar,
  Avatar,
  Box,
  Button,
  Divider,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  Popover,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { authApi, useLogoutUserMutation } from '~/redux/api/authApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';

import LockOpenIcon from '@mui/icons-material/LockOpen';
import { openobserveRum } from '@openobserve/browser-rum';
import { useHasPowerUserAccess } from '~/hooks/useHasPowerUserAccess';
import { Role, useRolePermission } from '~/hooks/useRolePermission';
import { useGetCustomersQuery } from '~/redux/api/customersApi';
import {
  getCurrentDashboardId,
  getIsFullScreen,
  getIsLeftPanelOpen,
  getIsUserLoggedIn,
  getMainPanel,
  getTopPanelVisibility,
  getZoomEnabled,
  isDashboardDirty,
} from '~/redux/selectors/dashboardSelectors';
import { getDeletedWidgets, getWidgets } from '~/redux/selectors/widgetSelectors';
import { dashboardListSlice } from '~/redux/slices/dashboardListSlice';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { leftMenuWidth } from '~/types/dashboard';
import { hexToRgbA } from '~/utils/utils';
import CustomDialog from '../CustomDialog';

const LeftMenuDrawer = () => {
  const { hasPermission, hasDashboardPermission } = useRolePermission();
  const hasPowerUserAccess = useHasPowerUserAccess();
  const currentDashboard = useSelector(getCurrentDashboardId);
  const activeCustomer = useSelector(getActiveCustomer);
  const isUserLoggedIn = useSelector(getIsUserLoggedIn);
  const isTopPanel = useSelector(getTopPanelVisibility);
  const fullScreen = useSelector(getIsFullScreen);
  const enabledZoom = useSelector(getZoomEnabled);
  const LeftPanelOpen = useSelector(getIsLeftPanelOpen);
  const theme = useTheme();
  const [logoutUser] = useLogoutUserMutation();
  const { logout } = dashboardSlice.actions;
  const router = useRouter();
  const dispatch = useDispatch();
  const { globalAdmin, admin } = useHasAdminAccess();
  const isDashboardStateDirty = useSelector(isDashboardDirty);
  const widgets = useSelector(getWidgets);
  const deleteWidgets = useSelector(getDeletedWidgets);
  const mainPanel = useSelector(getMainPanel);
  const currentUrl = router.asPath;
  const [confirm, setConfirm] = useState(false);
  const [navigation, setNavigation] = useState<string | null>(null);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedNavItem, setSelectedNavItem] = useState<number | null>(null);
  const [customerLogo, setCustomerLogo] = useState<string>('');

  // Add these media query hooks
  const isPortrait = useMediaQuery('(max-width: 600px) and (orientation: portrait)');
  const isLandscape = useMediaQuery('(max-height: 500px) and (orientation: landscape)');
  const isMobile = isPortrait || isLandscape;

  const { data, isFetching: isLoading } = useGetCustomersQuery({ is_logo: true });

  useEffect(() => {
    if (data) {
      const customerData = data.map((customer) => ({
        id: customer.id,
        logo: customer.logo,
      }));

      // Check if the active customer ID matches any of the IDs in the array
      if (activeCustomer) {
        const matchingCustomer = customerData.find((item) => {
          if (item.id === activeCustomer.id) {
            return item.logo;
          }
        });

        if (matchingCustomer) {
          setCustomerLogo(String(matchingCustomer.logo));
        } else {
          setCustomerLogo('/logo_image.png');
        }
      }
    }
  }, [data, activeCustomer]);

  const logOutUser = async () => {
    await logoutUser();
    dispatch(logout());
    dispatch(authApi.util.resetApiState());
    const cookies = document.cookie.split(';');

    for (const cookie of cookies) {
      const eqPos = cookie.indexOf('=');
      const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();

      // Clear cookie for current path and root path
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    }
    console.log('Cookies after logout:', document.cookie);
  };
  const navigate = (link: string) => {
    if (link === '/logout') {
      logOutUser();
    }
    router.push(link);
    setAnchorEl(null);
  };
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const [settingAnchorEl, setSettingAnchorEl] = useState<null | HTMLElement>(null);
  const handleSettingMenu = (event: React.MouseEvent<HTMLElement>) => {
    setSettingAnchorEl(event.currentTarget);
  };

  const setDashboardFullScreen = async () => {
    try {
      if (document.documentElement) await document.documentElement.requestFullscreen();
    } catch (error) {}
  };

  const removeDashboardFullScreen = async () => {
    try {
      if (document && document.exitFullscreen) await document.exitFullscreen();
    } catch (error) {}
  };

  useEffect(() => {
    const handleEsc = async (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        dispatch(dashboardSlice.actions.setFullScreen(false));
        dispatch(dashboardSlice.actions.setTopPanelVisibility(true));
        removeDashboardFullScreen();
      }
    };
    window.addEventListener('keydown', handleEsc);
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [dispatch]);

  const setTopPanelVisibility = async () => {
    removeDashboardFullScreen();
    dispatch(dashboardSlice.actions.setIsDirty(true));
    dispatch(dashboardSlice.actions.setTopPanelVisibility(!isTopPanel));
    dispatch(dashboardSlice.actions.setFullScreen(false));
  };

  const onLayoutChangeToKiosk = async () => {
    dispatch(dashboardSlice.actions.setFullScreen(!fullScreen));
    if (fullScreen) {
      removeDashboardFullScreen();
      dispatch(dashboardSlice.actions.setTopPanelVisibility(true));
    } else {
      setDashboardFullScreen();
      dispatch(dashboardSlice.actions.setTopPanelVisibility(false));
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, index: number) => {
    setMenuAnchor(event.currentTarget);
    setSelectedNavItem(index);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedNavItem(null);
  };

  const handleManageAsset = () => {
    removeDashboardFullScreen();
    dispatch(dashboardSlice.actions.setIsLeftPanelOpen(true));
    router.push({
      pathname: `/customer/${activeCustomer?.id}/dashboard/${currentDashboard}`,
      query: { manageAsset: true },
    });
  };

  const menuItemsConfig: Record<string, { label: string; action: () => void; visible: boolean }[]> =
    {
      Dashboards: [
        // {
        //   label: 'Add New',
        //   action: () => handleAddNewDashboard(),
        //   visible: (globalAdmin || admin || hasPowerUserAccess) && true,
        // },
        // { label: 'Manage', action: () => router.push('/dashboard-list'), visible: true },
        {
          label: 'Manage',
          action: () => {
            if (handleDashboardChange()) {
              setNavigation('/dashboard-list');
            } else {
              router.push('/dashboard-list');
            }
          },
          visible: true,
        },
        {
          label: 'Templates',
          action: () => {
            dispatch(dashboardSlice.actions.setTemplateId(0));
            dispatch(dashboardSlice.actions.setTemplateName(''));
            dispatch(
              dashboardSlice.actions.setWidget({
                widgets: [],
                deleteWidgets: [],
                widgetLayout: [],
                lastWidgetId: 0,
              }),
            );
            dispatch(dashboardSlice.actions.setAssetType(0));
            dispatch(dashboardSlice.actions.setAssetTemplate(0));
            dispatch(dashboardSlice.actions.setWidgetsLayout([]));
            navToDashboardTemplate();
            router.push('/dashboard-template/list');
          },
          visible: (globalAdmin || admin || hasPowerUserAccess) && true,
        },
        // {
        //   label: 'New Dashboard Template',
        //   action: () => {
        //     dispatch(dashboardSlice.actions.setTemplateId(0));
        //     dispatch(dashboardSlice.actions.setTemplateName(''));
        //     dispatch(
        //       dashboardSlice.actions.setWidget({
        //         widgets: [],
        //         deleteWidgets: [],
        //         widgetLayout: [],
        //         lastWidgetId: 0,
        //       }),
        //     );
        //     dispatch(dashboardSlice.actions.setAssetType(0));
        //     dispatch(dashboardSlice.actions.setAssetTemplate(0));
        //     dispatch(dashboardSlice.actions.setWidgetsLayout([]));
        //     navToDashboardTemplate();
        //     router.push('/dashboard-template');
        //   },
        //   visible: (globalAdmin || admin || hasPowerUserAccess) && true,
        // },
        { label: 'Dynamic Chart', action: () => router.push('/dynamic-chart'), visible: true },
      ],
      Assets: [
        {
          label: 'Manage Assets',
          action: () => handleManageAsset(),
          visible: true,
        },
        {
          label: 'Manage Types',
          action: () => router.push('/asset-types'),
          visible:
            hasPermission('asset.types.view') && (globalAdmin || admin || hasPowerUserAccess),
        },
        {
          label: 'Manage Templates',
          action: () => router.push('/asset-templates'),
          visible: (globalAdmin || admin || hasPowerUserAccess) && true,
        },
        {
          label: 'Measurement Browser',
          action: () => router.push('/measurement-browser'),
          // visible: (globalAdmin || admin || hasPowerUserAccess) && true,
          visible: true,
        },
        {
          label: 'Measurement Types',
          action: () => router.push('/measurement-types'),
          visible: globalAdmin && true,
        },
      ],
      Alerts: [
        { label: 'History', action: () => router.push('/alerts'), visible: true },
        {
          label: 'Manage',
          action: () =>
            router.push({
              pathname: '/alerts',
              query: { addAlert: true },
            }),
          visible: (globalAdmin || admin || hasPowerUserAccess) && true,
        },
        { label: 'Analytics', action: () => router.push('/alert-analytics'), visible: true },
        { label: 'Excursions', action: () => router.push('/excursions'), visible: true },
      ],
      CalcEngine: [
        // {
        //   label: 'New Expression template',
        //   action: () => router.push('/expression-template'),
        //   visible:
        //     // hasPermission('calculation-engine.create') &&
        //     (globalAdmin || admin || hasPowerUserAccess) && true,
        // },
        {
          label: 'Expression templates',
          action: () => router.push('/calc-engine'),
          visible:
            // hasPermission('calculation-engine.view') &&
            (globalAdmin || admin || hasPowerUserAccess) && true,
        },
        {
          label: 'Unit of Measure',
          action: () => router.push('/unit-of-measure'),
          visible: (globalAdmin || admin || hasPowerUserAccess) && true,
        },
        {
          label: 'Unit of Groups',
          action: () => router.push('/units-of-group-units'),
          visible:
            // hasPermission('unit.measure.view') &&
            (globalAdmin || admin || hasPowerUserAccess) && true,
        },
      ],
    };

  const secondNavs = [
    {
      name: 'Help',
      icon: <SupportOutlinedIcon />,
      href: 'https://support.bromptonenergy.com/portal/en/signin',
      active: router.asPath === '#',
      external: true,
    },
    // {
    //   name: 'View',
    //   icon: <SettingsOutlinedIcon />,
    //   href: '',
    //   active: settingAnchorEl,
    //   onclick: handleSettingMenu,
    //   component: (
    //     <Popover
    //       anchorEl={settingAnchorEl}
    //       open={Boolean(settingAnchorEl)}
    //       onClose={() => setSettingAnchorEl(null)}
    //       anchorPosition={{
    //         top: 10,
    //         left: 0,
    //       }}
    //       anchorOrigin={{
    //         horizontal: 'center',
    //         vertical: 'top',
    //       }}
    //       sx={{
    //         '& .MuiPopover-paper': {
    //           width: 240,
    //           borderRadius: 2,
    //           left: leftMenuWidth + 'px!important',
    //         },
    //       }}
    //     >
    //       <ListItem
    //         disablePadding
    //         sx={{
    //           cursor: 'pointer',
    //           m: 0,
    //           p: 2,
    //           pt: 0,
    //           pb: 0,
    //           height: 40,
    //           gap: 2,
    //         }}
    //       >
    //         <ListItemIcon sx={{ minWidth: 5 }}>
    //           <CalendarMonthSharpIcon />
    //         </ListItemIcon>
    //         <ListItemText primary={'Filter Bar'} />
    //         <IOSSwitch sx={{ m: 1 }} onChange={setTopPanelVisibility} checked={isTopPanel} />
    //       </ListItem>
    //       <Divider />
    //       <ListItem
    //         disablePadding
    //         sx={{
    //           cursor: 'pointer',
    //           m: 0,
    //           p: 2,
    //           pt: 0,
    //           pb: 0,
    //           height: 40,
    //           gap: 2,
    //         }}
    //       >
    //         <ListItemIcon sx={{ minWidth: 5 }}>
    //           <AccountTreeOutlinedIcon />
    //         </ListItemIcon>
    //         <ListItemText primary={'Tree'} />
    //         <IOSSwitch sx={{ m: 1 }} onChange={onLayoutChangeToTree} checked={LeftPanelOpen} />
    //       </ListItem>
    //       <Divider />
    //       <ListItem
    //         disablePadding
    //         sx={{
    //           cursor: 'pointer',
    //           m: 0,
    //           p: 2,
    //           pt: 0,
    //           pb: 0,
    //           height: 40,
    //           gap: 2,
    //         }}
    //       >
    //         <ListItemIcon sx={{ minWidth: 5 }}>
    //           <FullscreenIcon />
    //         </ListItemIcon>
    //         <ListItemText primary={'Fullscreen'} />
    //         <IOSSwitch sx={{ m: 1 }} onChange={onLayoutChangeToKiosk} checked={fullScreen} />
    //       </ListItem>
    //     </Popover>
    //   ),
    //   external: false,
    // },
    // {
    //   name: 'Notifications',
    //   icon: <NotificationsNoneOutlinedIcon />,
    //   href: '/notifications',
    //   active: router.asPath === '/notifications',
    //   external: false,
    // },
  ];

  const links = [
    // ...(hasPermission('calculation-engine.view') && (globalAdmin || admin || hasPowerUserAccess)
    //   ? [
    //       {
    //         title: 'Calc Engine',
    //         link: '/calc-engine',
    //         icon: <EngineeringIcon />,
    //       },
    //     ]
    //   : []),

    // {
    //   title: 'Test dashboard',
    //   link: '/test-dashboard',
    //   icon: <BiotechIcon />,
    // },
    ...(hasPermission('dashboard.create') && (globalAdmin || admin || hasPowerUserAccess)
      ? [
          {
            title: 'Dashboad Template',
            link: '/dashboard-template',
            icon: <SpaceDashboardOutlinedIcon />,
          },
        ]
      : []),
    ...(hasPermission('diagram.view') && (globalAdmin || admin || hasPowerUserAccess)
      ? [
          {
            title: 'Diagram Builder',
            link: '/diagram',
            icon: <TextureIcon />,
          },
        ]
      : []),
    {
      title: 'Data Ingestion',
      link: '/data-ingestion',
      icon: <DataThresholdingIcon />,
    },
    {
      title: 'Customer Management',
      link: '/active-customer',
      icon: <PeopleIcon />,
    },
    // ...(globalAdmin || admin || hasPowerUserAccess
    //   ? [
    //       {
    //         title: 'Unit of Measure',
    //         link: '/unit-of-measure',
    //         icon: <StraightenIcon />,
    //       },
    //     ]
    //   : []),
    {
      title: 'User Preferences',
      link: '/user-preferences',
      icon: <SettingsAccessibilityIcon />,
    },
    {
      title: 'Release Details',
      link: '/release',
      icon: <LayersOutlinedIcon />,
    },
    // {
    //   title: 'Reset Password',
    //   link: '/reset-password',
    //   icon: <LockIcon />,
    // },
  ];
  const navItems = [
    {
      name: 'Dashboards',
      icon: <HomeIcon />,
      href:
        activeCustomer !== null && activeCustomer !== undefined
          ? `/customer/${activeCustomer?.id}/dashboard/${currentDashboard}`
          : `/customer/`,
    },
    // ...(hasPermission('customer.view') && (hasPowerUserAccess || globalAdmin || admin)
    //   ? [
    //       {
    //         name: 'Customers',
    //         icon: <PeopleIcon />,
    //         href: '/customers',
    //       },
    //     ]
    //   : []),

    {
      name: 'Assets',
      icon: <LayersOutlinedIcon />,
      href: '/asset-templates',
    },
    // {
    //   name: 'Reports',
    //   icon: <ArticleOutlinedIcon />,
    //   href: '/reports',
    // },
    ...(hasPermission('user.view') && (hasPowerUserAccess || globalAdmin || admin)
      ? [
          {
            name: 'Users',
            icon: <ManageAccountsOutlinedIcon />,
            href: '/customer-users',
          },
        ]
      : []),
    ...(hasPermission('alert.view')
      ? [
          {
            name: 'Alerts',
            icon: <EmergencyShareIcon />,
            href: '/alerts',
          },
        ]
      : []),
    ...(hasPermission('calculation-engine.view') && (globalAdmin || admin || hasPowerUserAccess)
      ? [
          {
            name: 'CalcEngine',
            href: '/calc-engine',
            icon: <CalculateIcon />,
          },
        ]
      : []),
  ];

  const handleDashboardChange = () => {
    if (currentDashboard === -2) {
      return false;
    }
    if (
      mainPanel === 'chart' &&
      activeCustomer &&
      (widgets.filter((widget) => widget.type !== 'chart' && widget.settings.isDirty).length > 0 ||
        deleteWidgets.length > 0 ||
        widgets.filter((widget) => widget.type === 'chart' && widget?.settings?.settings?.isDirty)
          .length > 0 ||
        isDashboardStateDirty) &&
      currentUrl.match(/\/dashboard\/\d+/) &&
      currentDashboard > -1 &&
      hasDashboardPermission('dashboard.update', Role.POWER_USER)
    ) {
      setConfirm(true);
      return true;
    }
    return false;
  };
  const [sideMenu, setSideMenu] = useState<boolean>(false);
  const mobile = useMediaQuery('@media (max-width: 600px)');
  const [mobileAnchorEl, setMobileAnchorEl] = useState<null | HTMLElement>(null);
  useEffect(() => {
    if (enabledZoom && !mobile && !isLandscape) {
      dispatch(dashboardSlice.actions.setWidgetsZoom(false));
    }
  }, [enabledZoom, mobile, isLandscape, dispatch]);
  const openMobileMenu = Boolean(mobileAnchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setMobileAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setMobileAnchorEl(null);
  };

  const navToDashboardTemplate = () => {
    dispatch(dashboardSlice.actions.setTemplateId(0));
    dispatch(dashboardSlice.actions.setTemplateName(''));
    dispatch(
      dashboardSlice.actions.setWidget({
        widgets: [],
        deleteWidgets: [],
        widgetLayout: [],
        lastWidgetId: 0,
      }),
    );
    dispatch(dashboardSlice.actions.setWidgetsLayout([]));
  };
  return (
    <>
      <AppBar
        position="relative"
        sx={{
          backgroundColor: (theme) => theme.palette.background.paper,
          display: 'none',
          boxShadow: 'rgba(9, 30, 66, 0.25) 0px 1px 1px, rgba(9, 30, 66, 0.13) 0px 0px 1px 1px',
          // Show AppBar on all small devices
          '@media (max-width: 600px), (max-height: 500px)': {
            display: 'block',
          },
          // Landscape specific styles
          ...(isLandscape && {
            width: '60px',
            height: '100vh',
            left: 0,
            top: 0,
            position: 'fixed',
            flexDirection: 'column',
            justifyContent: 'space-between',
          }),
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            px: 1,
            ...(isLandscape && {
              flexDirection: 'column',
              height: '100%',
              py: 1,
            }),
          }}
        >
          <Link href={`/customer/${activeCustomer?.id}/dashboard/${currentDashboard}`} passHref>
            <Box
              component="img"
              src={customerLogo ? customerLogo : '/logo_image.png'}
              alt="Customer Logo"
              sx={{
                width: 50,
                height: 50,
                objectFit: 'contain',
                py: 0.5,
                ...(isLandscape && {
                  mb: 2,
                }),
              }}
            />
          </Link>

          {/* Mobile controls - group them in landscape mode */}
          <Box
            sx={{
              display: 'flex',
              ...(isLandscape && {
                flexDirection: 'column',
                mt: 'auto',
              }),
            }}
          >
            <IconButton
              onClick={() => dispatch(dashboardSlice.actions.setWidgetsZoom(!enabledZoom))}
            >
              {enabledZoom ? <LockIcon /> : <LockOpenIcon />}
            </IconButton>

            <Tooltip title="Account settings">
              <IconButton
                onClick={handleClick}
                size="small"
                sx={{ ml: isLandscape ? 0 : 2, mt: isLandscape ? 2 : 0 }}
                aria-controls={openMobileMenu ? 'account-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={openMobileMenu ? 'true' : undefined}
              >
                <Avatar
                  sx={{
                    fontSize: 'medium',
                    bgcolor: (theme) => theme.palette.grey[200],
                    color: 'gray',
                    borderColor: (theme) =>
                      Boolean(mobileAnchorEl)
                        ? theme.palette.success.main
                        : theme.palette.grey[400],
                    borderWidth: 1,
                    borderStyle: 'solid',
                    textTransform: 'capitalize',
                    width: 30,
                    height: 30,
                  }}
                >
                  {isUserLoggedIn?.first_name?.[0]}
                </Avatar>
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </AppBar>
      <Drawer
        sx={{
          width: leftMenuWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: leftMenuWidth,
            boxSizing: 'border-box',
            backgroundColor: (theme) => theme.palette.background.paper,
            borderRight: 'none',
            boxShadow: 'rgba(9, 30, 66, 0.25) 0px 1px 1px, rgba(9, 30, 66, 0.13) 0px 0px 1px 1px',
          },
          // Hide Drawer on all small devices (portrait and landscape)
          '@media (max-width: 600px), (max-height: 500px)': {
            display: 'none',
          },
        }}
        open={sideMenu}
        onClose={() => setSideMenu(false)}
        variant={mobile ? 'temporary' : 'permanent'}
        anchor="left"
      >
        <Box
          p={3}
          pb={2}
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <Link
            href={
              activeCustomer !== null && activeCustomer !== undefined
                ? currentDashboard && currentDashboard > 0
                  ? `/customer/${activeCustomer?.id}/dashboard/${currentDashboard}`
                  : `/customer/${activeCustomer?.id}/dashboard/0`
                : `/customer/`
            }
            onClick={() => {
              dispatch(dashboardSlice.actions.selectMainPanel('chart'));
            }}
            passHref
          >
            <Tooltip arrow title={activeCustomer?.name ?? ''}>
              <Image
                // src="/logo_image.png"
                src={customerLogo ? customerLogo : '/logo_image.png'}
                alt="Customer Avatar"
                priority={true}
                width={50}
                height={48}
              />
            </Tooltip>
          </Link>
          {/* <IconButton
            sx={{
              display: 'none',
              '@media (max-width: 600px)': {
                display: 'block',
              },
            }}
            onClick={() => setSideMenu(false)}
          >
            <CloseIcon />
          </IconButton> */}
        </Box>
        <List>
          {navItems.map((item, index) => {
            const hasMenuItems =
              menuItemsConfig[item.name]?.some((menuItem) => menuItem.visible) || false;

            return (
              <ListItem
                key={index}
                disablePadding
                onClick={() => {
                  dispatch(dashboardListSlice.actions.setSearchValue(null));
                }}
              >
                <ListItemButton
                  LinkComponent={Link}
                  {...(!hasMenuItems && { href: item.href })}
                  onClick={(event) => {
                    mobile && setSideMenu(false);
                    handleMenuOpen(event, index); // Open Menu
                  }}
                  sx={{
                    margin: 1,
                    mt: 0.5,
                    mb: 0.5,
                    display: 'flex',
                    flexDirection: 'column',
                    backgroundColor:
                      item.href === router.asPath
                        ? `rgba(${hexToRgbA(theme.palette.primary.main)}, 0.2)`
                        : '',
                    borderRadius: 2,
                    color: (theme) => theme.palette.secondary.dark,
                  }}
                >
                  <ListItemIcon
                    sx={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      color: item.href === router.asPath ? 'primary.main' : 'secondary.dark',
                    }}
                  >
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText
                    primary={item.name}
                    sx={{
                      color: item.href === router.asPath ? 'primary.main' : 'secondary.dark',
                      '& span': {
                        fontSize: '0.75rem',
                        fontWeight: item.href === router.asPath ? '600' : '500',
                      },
                    }}
                  />
                </ListItemButton>

                {selectedNavItem === index && menuItemsConfig[item.name] && (
                  <Menu
                    anchorEl={menuAnchor}
                    open={Boolean(menuAnchor)}
                    onClose={handleMenuClose}
                    anchorOrigin={{
                      vertical: 'top',
                      horizontal: 'right',
                    }}
                  >
                    {menuItemsConfig[item.name]
                      .filter((menuItem) => menuItem.visible)
                      .map((menuItem, i) => (
                        <MenuItem
                          key={i}
                          onClick={() => {
                            menuItem.action();
                            handleMenuClose();
                          }}
                        >
                          {menuItem.label}
                        </MenuItem>
                      ))}
                  </Menu>
                )}
              </ListItem>
            );
          })}
        </List>
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            width: '100%',
            backgroundColor: theme.palette.background.paper,
          }}
        >
          <List>
            {secondNavs.map((item, index) => (
              <ListItem key={index} disablePadding sx={{ height: (theme) => theme.spacing(8) }}>
                <ListItemButton
                  LinkComponent={Link}
                  href={item?.href ?? ''}
                  target={item.external ? '_blank' : '_self'}
                  sx={{
                    margin: 1,
                    mt: 0.5,
                    mb: 0.5,
                    display: 'flex',
                    flexDirection: 'column',
                    backgroundColor: item.active
                      ? `rgba(${hexToRgbA(theme.palette.primary.main)}, 0.2)`
                      : '',
                    color: (theme) => theme.palette.secondary.dark,
                    borderRadius: 2,
                  }}
                  // onClick={item.onclick}
                >
                  <ListItemIcon
                    sx={{
                      justifyContent: 'center',
                      alignItems: 'center',
                      color: item.active ? 'primary.main' : 'secondary.dark',
                    }}
                  >
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText
                    primary={item.name}
                    sx={{
                      color: item.active ? 'primary.main' : 'secondary.dark',
                      '& span': { fontSize: '0.75rem', fontWeight: item.active ? '600' : '500' },
                    }}
                  />
                </ListItemButton>
                {/* {item.component ?? null} */}
              </ListItem>
            ))}
          </List>
          <Divider />
          <IconButton
            edge="end"
            color="primary"
            disableRipple
            sx={{ p: 1.5, width: 'calc(100% - 10px)' }}
            onClick={handleMenu}
          >
            <Avatar
              sx={{
                fontSize: 'medium',
                bgcolor: (theme) => theme.palette.grey[200],
                color: 'gray',
                borderColor: (theme) =>
                  Boolean(anchorEl) ? theme.palette.success.main : theme.palette.grey[400],
                borderWidth: 1,
                borderStyle: 'solid',
                textTransform: 'capitalize',
              }}
            >
              {isUserLoggedIn?.first_name[0]}
            </Avatar>
          </IconButton>
          <Popover
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={() => setAnchorEl(null)}
            anchorPosition={{
              top: 0,
              left: 0,
            }}
            anchorOrigin={{
              horizontal: 'left',
              vertical: 'bottom',
            }}
            sx={{
              '& .MuiPopover-paper': {
                width: 240,
                borderRadius: 2,
                ...(isMobile
                  ? {
                      left: isLandscape ? '65px !important' : 'auto !important',
                    }
                  : {
                      left: leftMenuWidth + 'px !important',
                    }),
              },
            }}
          >
            <ListItem
              disablePadding
              sx={{
                cursor: 'pointer',
                m: 0,
                p: 1.5,
                pt: 2,
                pb: 2,
                height: (theme) => theme.spacing(8),
              }}
            >
              <Avatar
                sx={{
                  fontSize: 'medium',
                  bgcolor: theme.palette.grey[300],
                  borderColor: (theme) => theme.palette.grey[400],
                  borderWidth: 1,
                  borderStyle: 'solid',
                  color: (theme) => theme.palette.secondary.dark,
                  textTransform: 'capitalize',
                }}
              >
                {isUserLoggedIn?.first_name?.[0]}
              </Avatar>
              <ListItemText sx={{ ml: 2 }}>
                <Typography
                  variant="h6"
                  component="p"
                  noWrap
                  sx={{
                    color: (theme) => theme.palette.text.primary,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    ':hover': {
                      overflow: 'visible',
                      wordWrap: 'break-word',
                    },
                  }}
                >
                  {isUserLoggedIn?.first_name}
                </Typography>
                <Typography
                  variant="body2"
                  component="p"
                  sx={{
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    ':hover': {
                      overflow: 'visible',
                      wordWrap: 'break-word',
                    },
                  }}
                >
                  {isUserLoggedIn?.email}
                </Typography>
              </ListItemText>
            </ListItem>
            <Divider />
            {links.map((link, index) => {
              return (
                <ListItem
                  key={index}
                  disablePadding
                  onClick={() => {
                    if (link.link === '/diagram') {
                      sessionStorage.removeItem('diagramId');
                      sessionStorage.removeItem('diagramName');
                    }
                    if (handleDashboardChange()) {
                      setNavigation(link.link);
                      // navToDashboardTemplate(link.link);
                    } else {
                      navigate(link.link);
                      navToDashboardTemplate();
                    }
                    if (link.link === '/dashboard-template') {
                      // dispatch(dashboardSlice.actions.setTemplateId(0));
                      // dispatch(dashboardSlice.actions.setTemplateName(''));
                      // dispatch(
                      //   dashboardSlice.actions.setWidget({
                      //     widgets: [],
                      //     deleteWidgets: [],
                      //     widgetLayout: [],
                      //     lastWidgetId: 0,
                      //   }),
                      // );
                      // dispatch(dashboardSlice.actions.setWidgetsLayout([]));
                      // navToDashboardTemplate();
                    }
                    mobile && setSideMenu(false);
                  }}
                  sx={{
                    cursor: 'pointer',
                    m: 0,
                    p: 1.25,
                    pt: 2,
                    pb: 2,
                    height: 40,
                    gap: 2,
                    textTransform: 'capitalize',
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 5 }}>{link.icon}</ListItemIcon>
                  <ListItemText primary={link.title} />
                </ListItem>
              );
            })}
            <Divider />
            <ListItem
              disablePadding
              onClick={() => {
                router.push(`/logout`);
                logOutUser();
              }}
              sx={{
                cursor: 'pointer',
                m: 0,
                p: 2,
                pt: 0,
                pb: 0,
                height: 40,
                gap: 2,
              }}
            >
              <ListItemIcon sx={{ minWidth: 5 }}>
                <LogoutIcon />
              </ListItemIcon>
              <ListItemText primary={'Logout'} />
            </ListItem>
          </Popover>
        </Box>
      </Drawer>
      <CustomDialog
        title="You have unsaved changes."
        content={<Typography color={'error'}>Do you want to still proceed?</Typography>}
        dialogActions={
          <>
            <Button
              onClick={() => {
                setConfirm(false);
                // setNavigation(null);
              }}
              variant="outlined"
              startIcon={<CancelIcon />}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setConfirm(false);
                navigate(navigation as string);
                if (navigation === '/login') {
                  router.push('/login');
                  logoutUser();
                  dispatch(logout());
                  dispatch(authApi.util.resetApiState());
                }
                if (navigation === '/dashboard-template') {
                  navToDashboardTemplate();
                  router.push(navigation);
                }
                setNavigation(null);
              }}
              variant="contained"
              color="error"
            >
              Proceed
            </Button>
          </>
        }
        onClose={() => setConfirm(false)}
        open={confirm}
      />
      <Menu
        anchorEl={mobileAnchorEl}
        id="account-menu"
        open={openMobileMenu}
        onClose={handleClose}
        onClick={handleClose}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              mt: 1.5,
              ml: isLandscape ? 1 : 0,
              '& .MuiAvatar-root': {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1,
              },
              '&::before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                zIndex: 1,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                // Conditional positioning based on isLandscape
                ...(isLandscape
                  ? {
                      bottom: 12,
                      left: -5,
                      transform: 'rotate(45deg)', // Points toward left
                    }
                  : {
                      top: 0,
                      right: 14,
                      transform: 'translateY(-50%) rotate(45deg)', // Points upward
                    }),
              },
            },
          },
        }}
        anchorOrigin={{
          horizontal: isLandscape ? 'right' : 'right',
          vertical: isLandscape ? 'top' : 'bottom',
        }}
        transformOrigin={{
          horizontal: isLandscape ? 'left' : 'right',
          vertical: isLandscape ? 'bottom' : 'top',
        }}
      >
        <MenuItem
          onClick={() => {
            navigate('/active-customer');
            handleClose();
          }}
        >
          <PeopleIcon sx={{ mr: 1 }} /> Customer Management
        </MenuItem>

        <Divider />

        <MenuItem
          onClick={() => {
            router.push(`/logout`);
            logOutUser();
            localStorage.removeItem('sessionPopupDismissed');
            openobserveRum.clearUser();
            openobserveRum.stopSessionReplayRecording();
            handleClose();
          }}
        >
          <LogoutIcon sx={{ mr: 1 }} /> Logout
        </MenuItem>
      </Menu>
    </>
  );
};
export default LeftMenuDrawer;
