import { Box, MenuItem, Select } from '@mui/material';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';
import { formatMetricLabel } from '~/utils/utils';

import { SelectChangeEvent } from '@mui/material/Select';
import { ChartMeasureSetting, YAxisSide } from '~/types/widgets';
type MeasureSettingSelectorProps = {
  selectedDbMeasureIdToName: {
    [key: string]: string;
  };
  dbMeasureIdToSetting: Record<string, ChartMeasureSetting>;
  handleDbMeasureIdToSettingUpdate: (
    updatedDbMeasureIdToSetting: Record<string, ChartMeasureSetting>,
  ) => void;
  selectedMeasureNames: string[];
  dbMeasureIdToAnnotation: Record<string, boolean>; // Add annotation state
  handleDbMeasureIdToAnnotationUpdate: (
    updatedDbMeasureIdToAnnotation: Record<string, boolean>,
  ) => void; // Add update handler for annotations
};

const axisSide = ['left', 'right'];

type MeasureSettingProps = {
  selectedDbMeasureIdToName: {
    [key: string]: string;
  };
  dbMeasureIdToSetting: Record<string, ChartMeasureSetting>;
  dbMeasureId: string;
  handleDbMeasureIdToSettingUpdate: (
    updatedDbMeasureIdToSetting: Record<string, ChartMeasureSetting>,
  ) => void;
  dbMeasureIdToAnnotation: Record<string, boolean>; // Add annotation state
  handleDbMeasureIdToAnnotationUpdate: (
    updatedDbMeasureIdToAnnotation: Record<string, boolean>,
  ) => void;
};

import { Checkbox, FormControlLabel } from '@mui/material';

function MeasureSetting({
  dbMeasureIdToSetting,
  dbMeasureId,
  handleDbMeasureIdToSettingUpdate,
  selectedDbMeasureIdToName,
  dbMeasureIdToAnnotation,
  handleDbMeasureIdToAnnotationUpdate,
}: MeasureSettingProps) {
  const onYAxisChange = (e: SelectChangeEvent<YAxisSide>) => {
    handleDbMeasureIdToSettingUpdate({
      ...dbMeasureIdToSetting,
      [dbMeasureId]: {
        ...dbMeasureIdToSetting[dbMeasureId],
        yAxisSide: e.target.value as YAxisSide,
      },
    });
  };

  const onAnnotationChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newAnnotationValue = event.target.checked;
    handleDbMeasureIdToAnnotationUpdate({
      ...dbMeasureIdToAnnotation,
      [dbMeasureId]: newAnnotationValue,
    });
  };

  return (
    <FormControl id={'select-' + dbMeasureId}>
      <Box display="flex" alignItems="center" width="100%" gap={1}>
        <FormLabel id={'label-' + dbMeasureId}>
          {selectedDbMeasureIdToName[dbMeasureId]
            ? formatMetricLabel(selectedDbMeasureIdToName[dbMeasureId])
            : 'Unknown Measure for' + dbMeasureId}
        </FormLabel>

        {/* Y-axis selection */}
        <Select
          labelId={'label-' + dbMeasureId}
          id={dbMeasureId}
          value={dbMeasureIdToSetting[dbMeasureId]?.yAxisSide || 'left'}
          onChange={onYAxisChange}
        >
          {axisSide.map((side) => (
            <MenuItem key={dbMeasureId + side} value={side}>
              {side}
            </MenuItem>
          ))}
        </Select>

        {/* Add annotation checkbox */}
        <FormControlLabel
          control={
            <Checkbox
              checked={dbMeasureIdToAnnotation[dbMeasureId] || false} // Set the checkbox state
              onChange={onAnnotationChange}
              color="primary"
            />
          }
          label="Add Annotation"
        />
      </Box>
    </FormControl>
  );
}

export function MeasureSettingSelector({
  dbMeasureIdToSetting,
  selectedDbMeasureIdToName,
  handleDbMeasureIdToSettingUpdate,
  selectedMeasureNames,
  dbMeasureIdToAnnotation,
  handleDbMeasureIdToAnnotationUpdate,
}: MeasureSettingSelectorProps) {
  return (
    <>
      {Object.entries(selectedDbMeasureIdToName ?? {}).map(([dbMeasureId, name], i) => (
        <div key={i}>
          {selectedMeasureNames.includes(dbMeasureId) ? (
            <MeasureSetting
              key={dbMeasureId}
              dbMeasureIdToSetting={dbMeasureIdToSetting}
              selectedDbMeasureIdToName={selectedDbMeasureIdToName}
              dbMeasureId={dbMeasureId}
              handleDbMeasureIdToSettingUpdate={handleDbMeasureIdToSettingUpdate}
              dbMeasureIdToAnnotation={dbMeasureIdToAnnotation} // Pass annotation state
              handleDbMeasureIdToAnnotationUpdate={handleDbMeasureIdToAnnotationUpdate} // Pass annotation update handler
            />
          ) : null}
        </div>
      ))}
    </>
  );
}
