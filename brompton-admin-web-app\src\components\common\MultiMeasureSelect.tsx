import { Chip, MenuItem, OutlinedInput, Select, Stack } from '@mui/material';
import { getDbMeasureIdToName } from '~/redux/selectors/treeSelectors';
import { useSelector } from 'react-redux';
import { MultiMeasureWidgets, setMultiMeasureWidgetSettings, WidgetMode } from '~/types/widgets';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';

type MultiMeasureSelectionProps = {
  mode: WidgetMode;
  handleChangeMeasure?: (e: string[]) => void;
  settings: MultiMeasureWidgets;
  setSettings: setMultiMeasureWidgetSettings;
};
const MultiMeasureSelect = ({
  mode,
  handleChangeMeasure,
  settings,
  setSettings,
}: MultiMeasureSelectionProps) => {
  const metricsIdToName = useSelector(getMetricsIdToName);
  const dbMeasureIdToName = useSelector(getDbMeasureIdToName);
  const handleMultiMeasureChange = (e: string[]) => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    setSettings({
      ...settings,
      selectedTitles: e,
    });
    if (handleChangeMeasure) {
      handleChangeMeasure(e);
    }
  };
  return (
    <>
      {mode === 'dashboard' ? (
        <Select
          multiple
          value={settings.selectedTitles}
          fullWidth
          onChange={(e) => {
            handleMultiMeasureChange(e.target.value as string[]);
          }}
          input={
            <OutlinedInput
              label="Select Measure"
              sx={{
                '& legend': {
                  maxWidth: '100%',
                  height: 'fit-content',
                  '& span': {
                    opacity: 1,
                  },
                },
              }}
            />
          }
          renderValue={(selected) => (
            <Stack gap={1} direction="row" flexWrap="wrap">
              {selected.map((value: any) => {
                return <Chip key={value} label={dbMeasureIdToName[value]} />;
              })}
            </Stack>
          )}
        >
          {Object.keys(dbMeasureIdToName).map((measure) => {
            return (
              <MenuItem key={measure} value={measure}>
                {dbMeasureIdToName[measure]}
              </MenuItem>
            );
          })}
        </Select>
      ) : (
        <Select
          multiple
          value={settings.selectedTitles}
          fullWidth
          onChange={(e) => {
            handleMultiMeasureChange(e.target.value as string[]);
          }}
          input={
            <OutlinedInput
              label="Select Measure"
              sx={{
                '& legend': {
                  maxWidth: '100%',
                  height: 'fit-content',
                  '& span': {
                    opacity: 1,
                  },
                },
              }}
            />
          }
          renderValue={(selected) => (
            <Stack gap={1} direction="row" flexWrap="wrap">
              {selected.map((value: any) => {
                return <Chip key={value} label={metricsIdToName[value]} />;
              })}
            </Stack>
          )}
        >
          {Object.keys(metricsIdToName).map((measure) => {
            return (
              <MenuItem key={measure} value={measure}>
                {metricsIdToName[measure]}
              </MenuItem>
            );
          })}
        </Select>
      )}
    </>
  );
};
export default MultiMeasureSelect;
