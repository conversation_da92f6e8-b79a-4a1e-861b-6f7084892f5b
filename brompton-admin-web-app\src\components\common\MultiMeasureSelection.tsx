import { Chip, MenuItem, OutlinedInput, Select, Stack } from '@mui/material';
import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';
import { getDbMeasureIdToName } from '~/redux/selectors/treeSelectors';
import { WidgetMode } from '~/types/widgets';

type MultiMeasureSelectionProps = {
  mode: WidgetMode;
  selectedMeasureNames: string[];
  handleChangeMeasure: (e: string[]) => void;
};
const MultiMeasureSelection = ({
  mode,
  selectedMeasureNames,
  handleChangeMeasure,
}: MultiMeasureSelectionProps) => {
  const dbMeasureIdToName = useSelector(getDbMeasureIdToName);
  const metricsIdToName = useSelector(getMetricsIdToName);
  return (
    <>
      {mode === 'dashboard' ? (
        <Select
          multiple
          value={selectedMeasureNames}
          fullWidth
          onChange={(e) => {
            handleChangeMeasure(e.target.value as string[]);
          }}
          input={
            <OutlinedInput
              label="Select Measure"
              sx={{
                '& legend': {
                  maxWidth: '100%',
                  height: 'fit-content',
                  '& span': {
                    opacity: 1,
                  },
                },
              }}
            />
          }
          renderValue={(selected) => (
            <Stack gap={1} direction="row" flexWrap="wrap">
              {selected.map((value: any) => {
                return <Chip key={value} label={dbMeasureIdToName[value]} />;
              })}
            </Stack>
          )}
        >
          {Object.keys(dbMeasureIdToName).map((measure) => {
            return (
              <MenuItem key={measure} value={measure}>
                {dbMeasureIdToName[measure]}
              </MenuItem>
            );
          })}
        </Select>
      ) : (
        <Select
          multiple
          value={selectedMeasureNames}
          fullWidth
          onChange={(e) => {
            handleChangeMeasure(e.target.value as string[]);
          }}
          input={
            <OutlinedInput
              label="Select Measure"
              sx={{
                '& legend': {
                  maxWidth: '100%',
                  height: 'fit-content',
                  '& span': {
                    opacity: 1,
                  },
                },
              }}
            />
          }
          renderValue={(selected) => (
            <Stack gap={1} direction="row" flexWrap="wrap">
              {selected.map((value: any) => {
                return <Chip key={value} label={metricsIdToName[value]} />;
              })}
            </Stack>
          )}
        >
          {Object.keys(metricsIdToName).map((measure) => {
            return (
              <MenuItem key={measure} value={measure}>
                {metricsIdToName[measure]}
              </MenuItem>
            );
          })}
        </Select>
      )}
    </>
  );
};
export default MultiMeasureSelection;
