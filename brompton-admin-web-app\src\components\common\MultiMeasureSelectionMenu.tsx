import AddIcon from '@mui/icons-material/Add';
import { Box, Button, IconButton, Tooltip, Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import { AssetMeasureOptions, MultiMeasureWidgets, WidgetMode } from '~/types/widgets';
import AssetMeasure from './AssetMeasure'; // Ensure correct import path
import { formatMetricLabel } from '~/utils/utils';

type MultiMeasureSelectionMenuProps<T extends MultiMeasureWidgets> = {
  mode: WidgetMode;
  settings: T;
  setSettings: (value: ((prevState: T) => T) | T) => void;
  required?: boolean;
};

const MultiMeasureSelectionMenu = ({
  mode,
  settings,
  setSettings,
  required,
}: MultiMeasureSelectionMenuProps<MultiMeasureWidgets>) => {
  const [assetMeasures, setAssetMeasures] = useState<AssetMeasureOptions[]>(
    settings.assetMeasure ?? [],
  );

  useEffect(() => {
    if (settings.assetMeasure.length > 0) {
      setAssetMeasures(settings.assetMeasure);
      if (!settings.title.isVisible) {
        const assetMeasureTitles = settings.assetMeasure
          .flatMap((assetMeas) => assetMeas.measureId)
          .filter((measure) => measure.trim() !== '');
        const titles = Object.keys(settings.dbMeasureIdToName)
          .map((title) => {
            if (!settings.dbMeasureIdToName[title] || !assetMeasureTitles.includes(title))
              return '';
            return formatMetricLabel(settings.dbMeasureIdToName[title]);
          })
          .filter((title) => title !== '')
          .join(' Vs.');
        setSettings((prevSettings) => ({
          ...prevSettings,
          title: {
            ...prevSettings.title,
            value: prevSettings.title.isVisible ? prevSettings.title.value : titles,
          },
        }));
      }
    } else {
      setAssetMeasures([
        {
          assetId: '',
          measureId: [],
        },
      ]);
    }
  }, [settings.assetMeasure, settings.dbMeasureIdToName, settings.title.isVisible]);

  // Handler to delete an asset measurement
  const handleDeleteAssetMeasure = (index: number) => {
    const updatedAssetMeasures = assetMeasures.filter((_, i) => i !== index);
    setAssetMeasures(updatedAssetMeasures);
    setSettings((prevSettings) => ({
      ...prevSettings,
      assetMeasure: updatedAssetMeasures,
    }));
  };

  // Handler to update an asset measurement
  const handleUpdateAssetMeasure = (index: number, updatedMeasure: AssetMeasureOptions) => {
    const updatedAssetMeasures = assetMeasures.map((measure, i) =>
      i === index ? updatedMeasure : measure,
    );
    setAssetMeasures(updatedAssetMeasures);
    setSettings((prevSettings) => ({
      ...prevSettings,
      assetMeasure: updatedAssetMeasures,
    }));
  };

  // Handler to add a new asset measurement
  const handleAddAssetMeasure = () => {
    const newAssetMeasure: AssetMeasureOptions = {
      assetId: '',
      measureId: [],
    };
    const updatedAssetMeasures = [...assetMeasures, newAssetMeasure];
    setAssetMeasures(updatedAssetMeasures);
    setSettings((prevSettings) => ({
      ...prevSettings,
      assetMeasure: updatedAssetMeasures,
    }));
  };

  return (
    <>
      {mode === 'dashboard' ? (
        <Box>
          <Box sx={{ display: 'flex', justifyContent: 'end', mb: 2 }}>
            <Button
              startIcon={<AddIcon />}
              color="primary"
              variant="contained"
              onClick={handleAddAssetMeasure}
            >
              Add
            </Button>
          </Box>

          {/* Render AssetMeasure Components */}
          {assetMeasures.map((assetMesures, index) => (
            <AssetMeasure
              key={`${assetMesures.assetId}-${index}`} // Use combination to ensure uniqueness
              assetMesures={assetMesures}
              index={index}
              onDelete={handleDeleteAssetMeasure}
              onUpdate={handleUpdateAssetMeasure} // Pass the update handler
              setSettings={setSettings}
              required={required}
            />
          ))}
        </Box>
      ) : null}
    </>
  );
};

export default MultiMeasureSelectionMenu;
