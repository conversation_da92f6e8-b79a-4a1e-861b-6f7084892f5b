import { Card } from '@material-ui/core';
import {
  Box,
  Button,
  FormControl,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from '@mui/material';
import { Dispatch, SetStateAction } from 'react';
import { useSelector } from 'react-redux';
import { Connection, Label } from '~/components/ChartSettings/SankeyChartSettings';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';
import { SankeyChartWidget } from '~/types/widgets';
type SankeySettingsConnectorProps = {
  mode: 'dashboard' | 'template';
  lables: Label[];
  index: number;
  connection: Connection[];
  singleConnection: Connection;
  setConnections: Dispatch<SetStateAction<Connection[]>>;
  handleSettings: (
    value: ((prevState: SankeyChartWidget) => SankeyChartWidget) | SankeyChartWidget,
  ) => void;
};
const SelectSx = {
  width: '100%',
  p: 0.3,
  '& fieldset': {
    '& legend': {
      maxWidth: '100%',
      height: 'auto',
      '& span': {
        opacity: 1,
      },
    },
  },
};
const SankeySettingsConnector = ({
  mode,
  lables,
  index,
  connection,
  singleConnection,
  setConnections,
  handleSettings,
}: SankeySettingsConnectorProps) => {
  const metricsIdToName = useSelector(getMetricsIdToName);
  const handleSoruceChange = (event: SelectChangeEvent<string>) => {
    const updatedSource = event.target.value;
    const updatedConnections = [...connection];
    updatedConnections[index] = {
      ...singleConnection,
      source: updatedSource,
    };
    setConnections(updatedConnections);
    handleSettings((prevState) => ({
      ...prevState,
      connections: updatedConnections,
    }));
  };

  const handleDestinationChange = (event: SelectChangeEvent<string>) => {
    const updatedDest = event.target.value;
    const updatedConnections = [...connection];
    updatedConnections[index] = {
      ...singleConnection,
      destination: updatedDest,
    };
    setConnections(updatedConnections);
    handleSettings((prevState) => ({
      ...prevState,
      connections: updatedConnections,
    }));
  };

  return (
    <Box component={Card} sx={{ p: 2, mb: 2 }}>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 1,
        }}
      >
        <Typography>Connection #{index + 1}</Typography>
        <Box>
          {index === connection.length - 1 ? (
            <Button
              color="primary"
              variant="contained"
              sx={{ mt: 1, mb: 1, mr: 1 }}
              onClick={() => {
                setConnections([...connection, { source: '', destination: '' }]);
              }}
            >
              Add Connection
            </Button>
          ) : null}

          <Button
            color="error"
            variant="contained"
            onClick={() => {
              const updatedConnections = [...connection];
              updatedConnections.splice(index, 1); // ✅ Only modify connections
              setConnections(updatedConnections);
              handleSettings((prevState) => ({
                ...prevState,
                connections: updatedConnections,
              }));
            }}
          >
            Delete
          </Button>
        </Box>
      </Box>

      {mode === 'dashboard' && (
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl fullWidth>
            <Select
              labelId="source-label"
              id="source-select"
              label="Source"
              sx={SelectSx}
              value={singleConnection.source}
              onChange={handleSoruceChange}
            >
              {lables.map(
                (label, idx) =>
                  label.sourceLabel !== '' && (
                    <MenuItem value={idx} key={idx}>
                      {label.sourceLabel}
                    </MenuItem>
                  ),
              )}
            </Select>
          </FormControl>

          <FormControl fullWidth>
            <Select
              labelId="destination-label"
              label="Destination"
              id="destination-select"
              sx={SelectSx}
              value={singleConnection.destination}
              onChange={handleDestinationChange}
            >
              {lables.map(
                (label, idx) =>
                  label.sourceLabel !== '' &&
                  label.sourceFrom === 'Calculated' && (
                    <MenuItem value={idx} key={idx}>
                      {label.sourceLabel}
                    </MenuItem>
                  ),
              )}
            </Select>
          </FormControl>
        </Box>
      )}
      {mode === 'template' && (
        <Box sx={{ display: 'flex' }}>
          <FormControl sx={{ width: '50%' }}>
            <Select
              labelId="source-label"
              id="source-select"
              label="Source"
              sx={SelectSx}
              value={connection[index].source}
              onChange={handleSoruceChange}
            >
              {lables.map(
                (label, idx) =>
                  label.sourceLabel !== '' && (
                    <MenuItem value={idx} key={idx}>
                      {label.sourceLabel}
                    </MenuItem>
                  ),
              )}
            </Select>
          </FormControl>

          <FormControl sx={{ width: '50%', pl: 1 }}>
            <Select
              labelId="destination-label"
              label="Destination"
              id="destination-select"
              sx={SelectSx}
              value={connection[index].destination}
              onChange={handleDestinationChange}
            >
              {lables.map(
                (label, idx) =>
                  label.sourceFrom === 'Calculated' &&
                  label.sourceLabel !== '' && (
                    <MenuItem value={idx} key={idx}>
                      {label.sourceLabel}
                    </MenuItem>
                  ),
              )}
            </Select>
          </FormControl>
        </Box>
      )}
    </Box>
  );
};

export default SankeySettingsConnector;
