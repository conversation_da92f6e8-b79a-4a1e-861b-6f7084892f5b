import { useEffect, useState } from 'react';
import {
  Box,
  Button,
  Checkbox,
  CircularProgress,
  FormControl,
  FormControlLabel,
  FormGroup,
  Typography,
} from '@mui/material';
import CustomDialog from '../CustomDialog';
import CancelIcon from '@mui/icons-material/Cancel';
import LinkIcon from '@mui/icons-material/Link';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import { useSelector } from 'react-redux';
import { getEndDate, getStartDate } from '~/redux/selectors/chartSelectors';
import { getGlobalSamplePeriod, getGlobalTimeRangeType } from '~/redux/selectors/topPanleSelectors';

type ShareProps = {
  open: boolean;
  onClose: () => void;
};

const Share = ({ open, onClose }: ShareProps) => {
  const [retainTime, setRetainTime] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showLink, setShowLink] = useState(false);
  const samplePeriod = useSelector(getGlobalSamplePeriod);
  const [retainSamplePeriod, setSamplePeriod] = useState(false);
  const startDate = useSelector(getStartDate);
  const endDate = useSelector(getEndDate);
  const globalTimeRange = useSelector(getGlobalTimeRangeType);
  let link = window.location.origin + window.location.pathname;

  if (retainTime && !retainSamplePeriod) {
    link += `?gtr=${globalTimeRange}&s=${startDate}&e=${endDate}`;
  } else if (!retainTime && retainSamplePeriod) {
    link += `?sample_period=${samplePeriod}`;
  } else if (retainSamplePeriod && retainSamplePeriod) {
    link += `?gtr=${globalTimeRange}&s=${startDate}&e=${endDate}&sample_period=${samplePeriod}`;
  }
  useEffect(() => {
    if (!open) {
      setRetainTime(false);
      setIsLoading(false);
      setShowLink(false);
      setSamplePeriod(false);
    }
  }, [open]);

  const handleCopyLink = () => {
    navigator.clipboard.writeText(link);
    onClose();
  };

  const handleRetainTimeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRetainTime(event.target.checked);
  };
  const handleRetainSamplePeriodChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSamplePeriod(event.target.checked);
  };
  return (
    <CustomDialog
      title="Share Dashboard"
      open={open}
      content={
        <>
          <FormControl component="fieldset" fullWidth>
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={retainSamplePeriod}
                    onChange={handleRetainSamplePeriodChange}
                    name="start"
                  />
                }
                label="Retain Sample Period"
              />
            </FormGroup>
          </FormControl>
          <FormControl component="fieldset">
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox checked={retainTime} onChange={handleRetainTimeChange} name="start" />
                }
                label="Retain Start & End Time"
              />
            </FormGroup>
          </FormControl>
          <Typography variant="body1" sx={{ mt: 2 }}>
            Your generated link: <span style={{ fontWeight: 'bold' }}>{link}</span>
          </Typography>
        </>
      }
      dialogActions={
        <>
          <Button onClick={onClose} variant="outlined" startIcon={<CancelIcon />}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={<FileCopyIcon />}
            onClick={handleCopyLink}
          >
            Copy Link
          </Button>
        </>
      }
      onClose={onClose}
    />
  );
};

export default Share;
