import { FormControl, MenuItem, Select, SelectChangeEvent } from '@mui/material';
import { useSelector } from 'react-redux';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';
import { SingleMeasureWidgets } from '~/types/widgets';
import SingleAssetMeasure from './SingleAssetMeasure';
import { useEffect } from 'react';
type SingleMeasureSelectProps<T extends SingleMeasureWidgets> = {
  id: string;
  settings: T;
  setSettings: (value: ((prevState: T) => T) | T) => void;
};
const SingleMeasureSelect = ({
  id,
  settings,
  setSettings,
}: SingleMeasureSelectProps<SingleMeasureWidgets>) => {
  const { selectedDbMeasureId } = settings;
  const metricsIdToName = useSelector(getMetricsIdToName);
  const handleMeasureChange = (event: SelectChangeEvent<string>) => {
    const selectedDbMeasureId = event.target.value as string;
    setSettings({
      ...settings,
      selectedDbMeasureId,
    });
  };
  useEffect(() => {
    if (settings.mode === 'template') {
      setSettings({
        ...settings,
        title: {
          ...settings.title,
          value: metricsIdToName[selectedDbMeasureId] ?? '',
        },
      });
    }
  }, [settings.mode, selectedDbMeasureId]);
  return (
    <>
      {settings.mode === 'dashboard' ? (
        <SingleAssetMeasure settings={settings} setSettings={setSettings} id={id} />
      ) : (
        <FormControl sx={{ pt: 2, pb: 2 }} fullWidth>
          <Select
            labelId={id}
            id={id}
            sx={{
              width: '100%',
              p: 0.3,
              '& fieldset': {
                '& legend': {
                  maxWidth: '100%',
                  height: 'auto',
                  '& span': {
                    opacity: 1,
                  },
                },
              },
            }}
            value={selectedDbMeasureId}
            onChange={handleMeasureChange}
            label="Metric"
          >
            {Object.keys(metricsIdToName).map((dbMeasureId) => {
              return (
                <MenuItem key={dbMeasureId} value={dbMeasureId}>
                  {metricsIdToName[dbMeasureId]}
                </MenuItem>
              );
            })}
          </Select>
        </FormControl>
      )}
    </>
  );
};

export default SingleMeasureSelect;
