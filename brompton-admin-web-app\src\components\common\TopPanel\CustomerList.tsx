import { Customer } from '~/types/customers';

type CustomerListProps = {
  customerList: Customer[] | undefined;
  isCustomerListLoading: boolean;
  handleChangeCustomer: (e: React.SyntheticEvent, value: Customer | null) => void;
  activeCustomer: Customer | null;
};
const CustomerList = ({
  customerList,
  isCustomerListLoading,
  handleChangeCustomer,
  activeCustomer,
}: CustomerListProps) => {
  return (
    <>
      {/* <Autocomplete<Customer>
        id="top-panel-customer-list"
        options={customerList ?? []}
        loading={isCustomerListLoading}
        getOptionLabel={(option) => option.name}
        onChange={handleChangeCustomer}
        size="small"
        sx={{
          width: 300,
        }}
        isOptionEqualToValue={(option, value) => option.id === value.id}
        value={activeCustomer ?? null}
        renderInput={(params) => <TextField {...params} label="Select Customer" />}
      /> */}
    </>
  );
};
export default CustomerList;
