import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Button, TextField, Typography } from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import { Customer, EditCustomer, editCustomerSchema } from '~/types/customers';
import React, { ChangeEvent, useEffect, useState } from 'react'; // Import React and ChangeEvent

type EditCustomerFormProps = {
  loading: boolean;
  customer: EditCustomer | undefined;
  onValidSubmit: (data: EditCustomer) => void;
  onCancel: () => void;
};

export default function EditCustomerForm({
  loading,
  customer,
  onValidSubmit,
  onCancel,
}: EditCustomerFormProps): JSX.Element {
  const [renderLogo, setRenderLogo] = useState<string>('');
  const { control, handleSubmit, setValue, reset, watch } = useForm<EditCustomer>({
    defaultValues: {
      nameId: '',
      name: '',
      address: '',
      logo: undefined, // Update the initial value of the logo field to an empty object
    },
    resolver: yupResolver(editCustomerSchema),
  });
  const logo = watch('logo');

  useEffect(() => {
    if (customer) {
      setValue('nameId', customer.nameId);
      setValue('name', customer.name);
      setValue('address', customer.address);
      setValue('logo', customer.logo);
    }
  }, [customer]);

  return (
    <form onSubmit={handleSubmit(onValidSubmit)} noValidate>
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Controller
          name="nameId"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              onBlur={onBlur}
              value={value}
              label="Name id"
              variant="outlined"
              margin="normal"
              disabled={true}
              fullWidth
              required
            />
          )}
        />
        <Controller
          name="name"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              onBlur={onBlur}
              value={value}
              label="Name"
              variant="outlined"
              margin="normal"
              disabled={loading}
              fullWidth
              required
            />
          )}
        />
        <Controller
          name="address"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={onChange}
              onBlur={onBlur}
              value={value}
              label="Address"
              variant="outlined"
              margin="normal"
              disabled={loading}
              fullWidth
              required
            />
          )}
        />
        <Controller
          name="logo"
          control={control}
          render={({ field: { onChange, onBlur }, fieldState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              type="file"
              onChange={async (e: ChangeEvent<HTMLInputElement>) => {
                // Update the onChange function to properly handle the file change event
                const reader = new FileReader();
                const file = await new Promise((resolve, reject) => {
                  reader.onloadend = () => {
                    resolve(reader.result as string);
                  };
                  reader.onerror = reject;
                  if (e.target.files?.[0] instanceof Blob) {
                    reader.readAsDataURL(e.target.files?.[0]);
                  }
                });
                onChange(file);
              }}
              onBlur={onBlur}
              variant="outlined"
              margin="normal"
              disabled={loading}
              fullWidth
            />
          )}
        />
        <Box sx={{ width: '100%', alignContent: 'start' }}>
          {logo && <img src={String(logo)} height={50} width={50} />}
        </Box>
        <Box sx={{ display: 'flex', mt: 3, width: '100%', gap: 2 }}>
          <Button type="submit" variant="contained" color="primary" fullWidth>
            Update
          </Button>
          <Button
            fullWidth
            type="button"
            variant="outlined"
            color="primary"
            onClick={() => {
              onCancel();
              reset();
            }}
          >
            Cancel
          </Button>
        </Box>
      </Box>
    </form>
  );
}
