import CloseIcon from '@mui/icons-material/Close';
import { Box, Dialog, DialogContent, DialogTitle, IconButton, Typography } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import AlertWidgetSettingsDialogs from '~/components/AlertSetting/AlertWidgetSettingsDialogs';
import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import Loader from '~/components/common/Loader';
import CustomNoRowsOverlay from '~/components/common/NoRowsOverlay';
import TestDashboards from '~/components/TestDahboard/TestDashboards';
import useGetAlertWidgetData from '~/hooks/useGetAlertWidgetData';
import { measuresApi } from '~/redux/api/measuresApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';
import { RootState } from '~/redux/store';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { AlertWidget, AssetMeasureOptions } from '~/types/widgets';
import { formatMetricLabel, fortmatUTCDate, hexToRgbA } from '~/utils/utils';

function hasNoMeasuresSelected(
  assetMeasure: AssetMeasureOptions[],
  mode: 'dashboard' | 'template',
  selectedTitles: string[],
): boolean {
  if (mode === 'template') {
    return selectedTitles.length === 0;
  }

  // If there's no assetMeasure array or it is empty, consider "no measure"
  if (!assetMeasure?.length) return true;

  // If at least one asset exists, return false (since assets are present)
  const hasAtLeastOneAsset = assetMeasure.some((am) => am.assetId.trim() !== '');
  if (hasAtLeastOneAsset) return false;

  // If no assets exist, return true (indicating "no measure" because no asset is available)
  return true;
}
type AlertWidgetProps = {
  id: string;
  settings: AlertWidget;
};

const AlertWidgetContainer = ({ id, settings }: AlertWidgetProps) => {
  const router = useRouter();
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const customerId = useSelector(getCustomerId);

  const [openTrendModal, setOpenTrendModal] = useState<boolean>(false);
  const url = new URL(window.location.href);
  const { data } = useGetAlertWidgetData({ id, state: settings });
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();

  const dateFormats = useSelector(getDateTimeFormat);
  const updateUrlParams = async (
    assetId: string,
    measurementId: string,
    startDate: number,
    endDate: number,
    customerId: number,
    limit: string,
    condition: string,
  ) => {
    const { data: measurements, isError } = await dispatch(
      measuresApi.endpoints.getAllMeasurements.initiate({
        assetId: Number(assetId),
        customerId,
      }),
    );
    if (isError) {
      showErrorAlert('Measurement not found');
    }
    const measureID = measurements?.find(
      (measure) => measure.measurementId?.toString() === measurementId,
    );
    if (!measureID) {
      showErrorAlert('Measurement not found');
      return;
    }
    setOpenTrendModal(true);
    url.searchParams.set('asset_id', assetId);
    url.searchParams.set('measurement_id', measureID?.id.toString() ?? '');
    url.searchParams.set('start_date', startDate.toString());
    url.searchParams.set('end_date', endDate.toString());
    url.searchParams.set('measurement_trend', 'true');
    url.searchParams.set('limit', limit.toString());
    url.searchParams.set('condition', condition);
    router.push(url, undefined, { shallow: true });
  };
  const deleteUrlParams = () => {
    const { pathname, searchParams } = url;

    const paramsToRemove = [
      'asset_id',
      'measurement_id',
      'start_date',
      'end_date',
      'measurement_trend',
      'limit',
      'condition',
    ];

    // Remove only the specified parameters
    paramsToRemove.forEach((param) => searchParams.delete(param));

    // Construct updated query parameters
    const updatedQuery = Object.fromEntries(searchParams.entries());

    // Update URL with the remaining query parameters
    router.push({ pathname, query: updatedQuery }, undefined, { shallow: true });
  };
  // const noMeasureSelected = hasNoMeasuresSelected(settings.assetMeasure, settings.mode, []);
  return (
    <>
      <AlertSnackbar {...snackbarState} />

      <CommonWidgetContainer
        id={id}
        settings={settings}
        settingsDialog={AlertWidgetSettingsDialogs}
        widgetContent={
          <Box sx={{ width: '100%', height: '100%' }}>
            {/* {noMeasureSelected && (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                height: '100%',
                alignItems: 'center',
              }}
            >
              <Typography>Please select a asset/measure from widget settings.</Typography>
            </Box>
          )} */}
            {data.isLoading && <Loader style={{ height: 'inherit' }} />}
            <Box
              sx={{
                width: '100%',
                overflowX: 'auto',
                display: 'flex',
                justifyContent: 'flex-start',
                alignItems: 'flex-start',
              }}
            >
              <DataGrid
                getRowClassName={(param) => {
                  return param.row.state === 'NORMAL' ? 'normal' : 'exceeded';
                }}
                rows={data.data.map((alert) => ({
                  id: alert.id,
                  asset: alert.asset.tag,
                  measurementTag: formatMetricLabel(alert.measurement.tag ?? ''),
                  limit: alert.limit,
                  input_value: alert.input_value,
                  aggregator: alert.aggregate.label,
                  deadband: alert.deadband,
                  description: alert.alert_id.description,
                  comparator: alert.comparator.condition,
                  timestamp: alert.timestamp,
                  enabled: alert.alert_id.enabled,
                  state:
                    Number(alert.state) === 0
                      ? 'NORMAL'
                      : Number(alert.state) === 1
                      ? 'EXCEEDED'
                      : alert.state,
                }))}
                columns={[
                  {
                    field: 'asset',
                    headerName: 'Asset Tag',
                    minWidth: 180,
                    flex: 1,
                  },
                  {
                    field: 'measurementTag',
                    headerName: 'Measurement Tag',
                    minWidth: 180,
                    flex: 1,
                  },
                  { field: 'description', headerName: 'Description', minWidth: 150, flex: 1 },
                  { field: 'state', headerName: 'State', minWidth: 100, flex: 0.7 },
                  {
                    field: 'timestamp',
                    headerName: 'Timestamp (UTC)',
                    minWidth: 180,
                    flex: 1.2,
                    renderCell: (params) => {
                      return (
                        <Box
                          sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                          }}
                        >
                          {fortmatUTCDate(new Date(params.row.timestamp), dateFormats)}
                        </Box>
                      );
                    },
                  },
                  {
                    field: 'output_value',
                    headerName: 'Reference',
                    minWidth: 120,
                    flex: 0.8,
                    renderCell: (params) => {
                      return (
                        <Box
                          onClick={() => {
                            const alertDetails = data.data.find((alert) => alert.id === params.id);

                            if (alertDetails) {
                              const timeData = new Date(params.row.timestamp).getTime();
                              const endDate = timeData + 1000 * 60 * 60 * 1;
                              const startDate = timeData - 1000 * 60 * 60 * 4;
                              updateUrlParams(
                                alertDetails?.asset!.id?.toString(),
                                alertDetails?.measurement.id?.toString(),
                                new Date(startDate).getTime(),
                                new Date(endDate).getTime(),
                                customerId,
                                alertDetails.limit,
                                alertDetails.comparator.condition ?? 'N/A',
                              );
                            }
                          }}
                          sx={{
                            cursor: 'pointer',
                            color: 'blue',
                            textDecoration: 'underline',
                          }}
                        >
                          Trend
                        </Box>
                      );
                    },
                  },
                ]}
                loading={data.isLoading}
                autoHeight
                slots={{
                  noRowsOverlay: CustomNoRowsOverlay,
                  toolbar: () => (
                    <>
                      {settings.title?.isVisible && (
                        <Typography
                          variant="h4"
                          component="div"
                          sx={{ flexGrow: 1, mb: 3, mt: 3, textAlign: 'center' }}
                          style={{
                            fontSize: settings.title.fontSize + 'px',
                            fontWeight: settings.title.fontWeight,
                            color: settings.title.color,
                          }}
                        >
                          {settings.title.value}
                        </Typography>
                      )}
                    </>
                  ),
                }}
                initialState={{
                  pagination: {
                    paginationModel: { page: 0, pageSize: 10 },
                  },
                }}
                sx={{
                  minWidth: 700,
                  width: '100%',
                  background: '#fff',
                  borderRadius: 2,
                  boxShadow: 0,
                  '.MuiDataGrid-root': {
                    border: 'none',
                  },
                  '.MuiDataGrid-columnHeaders': {
                    background: '#F9FAFB',
                  },
                  '.MuiDataGrid-columnHeader, .MuiDataGrid-cell': {
                    whiteSpace: 'nowrap',
                  },
                  '.MuiDataGrid-row.exceeded': {
                    background: (theme) => `rgba(${hexToRgbA(theme.palette.warning.main)}, 0.2)`,
                  },
                  '& .MuiDataGrid-virtualScroller': {
                    overflowX: 'auto',
                  },
                  '@media (max-width: 900px)': {
                    minWidth: 600,
                  },
                  '@media (max-width: 600px)': {
                    minWidth: 500,
                    fontSize: '12px',
                  },
                }}
                rowSelection={false}
                pageSizeOptions={[5, 10]}
                disableColumnMenu
                disableRowSelectionOnClick
                disableColumnSelector
                disableDensitySelector
                hideFooterSelectedRowCount
                hideFooterPagination={false}
                hideFooter={false}
              />
            </Box>
          </Box>
        }
        widgetName="alert-widget"
        widgetType="alert-widget"
      />
      {openTrendModal && (
        <Dialog
          fullWidth
          sx={{
            width: '100%',
            maxWidth: '100%',
            minWidth: '600px', // Set a minimum width
            minHeight: '400px', // Set a minimum height
            '& .MuiDialog-paper': {
              // Target the inner Dialog content
              minWidth: '90%',
              minHeight: '85%',
            },
            '@media (max-width: 600px)': {
              minWidth: '100%', // Full width on small screens
            },
          }}
          open={openTrendModal}
          onClose={() => {
            deleteUrlParams();
            setOpenTrendModal(false);
          }}
        >
          <DialogTitle id="customized-dialog-title">Measurement Trend</DialogTitle>
          <IconButton
            aria-label="close"
            onClick={() => {
              deleteUrlParams();
              setOpenTrendModal(false);
            }}
            sx={(theme) => ({
              position: 'absolute',
              right: 8,
              top: 8,
              color: theme.palette.grey[500],
            })}
          >
            <CloseIcon />
          </IconButton>
          <DialogContent dividers sx={{ '@media (max-width: 600px)': { padding: 1 } }}>
            <TestDashboards />
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

export default AlertWidgetContainer;
