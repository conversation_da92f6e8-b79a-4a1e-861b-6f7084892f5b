import { Box, Button, Stack, Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useGetAllBackOfficeAssetTypesQuery } from '~/redux/api/assetsApi';
import { getIsUserLoggedIn } from '~/redux/selectors/dashboardSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { AssetDoDetails, AssetTypeOption } from '~/types/asset';
import { assetTypePathMapper } from '~/utils/mappers/asset-type-mapper';
import AssetDetailsMeasurements from './AssetMeasurements';

export default function AssetDetail({
  asset,
  assetTypeOptions,
  onDelete,
}: {
  asset: AssetDoDetails;
  assetTypeOptions: AssetTypeOption[];
  onDelete: (assetId: number) => unknown;
}): JSX.Element {
  const loggedInuser = useSelector(getIsUserLoggedIn);
  const assetTypeOption = assetTypeOptions.find(
    (assetTypeOption) => assetTypeOption.value === asset.type_id,
  );
  const dispatch = useDispatch();
  const {
    data: assetTypeListData,
    status,
    isSuccess: isSuccessfullBackOffieAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery();
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);

  // on asset type list loaded
  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(assetTypePathMapper(assetTypeListData));
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes, status]);
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
      <Stack direction={'row'}>
        <Box display={'flex'} justifyContent={'space-between'} width={'100%'}>
          <Typography variant="h4">Asset Detail</Typography>
          {!loggedInuser?.scoped_roles?.find((role) => role.role === 'USER')?.role ? (
            <Box>
              <Button
                sx={{ ml: 'auto' }}
                onClick={() => {
                  dispatch(dashboardSlice.actions.selectMainPanel('chart'));
                }}
              >
                Back
              </Button>
              <Button
                sx={{ ml: 'auto' }}
                onClick={() => dispatch(dashboardSlice.actions.selectMainPanel('editAsset'))}
              >
                Edit
              </Button>
              <Button sx={{ ml: 'auto' }} onClick={() => onDelete(asset.id)}>
                Delete
              </Button>
            </Box>
          ) : null}
        </Box>
      </Stack>

      <Typography variant="h5" mt={2}>
        {asset.id} - &quot;{asset.tag}&quot;
      </Typography>

      <Typography variant="h6" mt={2}>
        Type
      </Typography>

      <Typography variant="body2" paragraph>
        {assetTypeOption?.label ?? 'N/A'}
      </Typography>

      <Typography variant="h6">Description</Typography>

      <Typography variant="body2" paragraph>
        {asset.description ?? 'No description available.'}
      </Typography>

      <Typography variant="h6">Asset Template</Typography>
      {asset.assetTemplate ? (
        <>
          <Typography variant="body2">
            <strong>Manufacturer :</strong> {asset.assetTemplate.manufacturer || 'N/A'}
          </Typography>
          <Typography variant="body2" paragraph>
            <strong>Model Number :</strong> {asset.assetTemplate.modelNumber || 'N/A'}
          </Typography>
        </>
      ) : (
        <Typography variant="body2" paragraph>
          -
        </Typography>
      )}

      <Typography variant="h6">Location</Typography>
      <Typography variant="body2" paragraph>
        Time zone: {asset.time_zone ?? 'Inherited from parent'}
      </Typography>
      {asset.latitude && asset.longitude ? (
        <Typography variant="body2" paragraph>
          Latitude: {asset.latitude}
          <br />
          Longitude: {asset.longitude}
        </Typography>
      ) : (
        <Typography variant="body2">No location available.</Typography>
      )}
      <AssetDetailsMeasurements asset={asset} />
    </Box>
  );
}
