import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { useRouter } from 'next/router';
import { useState } from 'react';
import useFetchAssetMeausrementsHistory, {
  ExtendedAssetMeasurement,
} from '~/hooks/useFetchAssetMeausrementsHistory';
import { AssetDo } from '~/types/asset';
import { formatChartDateToAssetTz, formatNumber, roundNumber } from '~/utils/utils';
import TestDashboards from '../TestDahboard/TestDashboards';
import { getDateTimeFormat, getThousandSeparator } from '~/redux/selectors/userPreferences';
import { useSelector } from 'react-redux';
import dayjs from 'dayjs';

type AssetDetailsMeasurementsProps = {
  asset: AssetDo;
};

const AssetDetailsMeasurements = ({ asset }: AssetDetailsMeasurementsProps) => {
  const router = useRouter();
  const thousandSeparator = useSelector(getThousandSeparator);
  const dateTimeFormat = useSelector(getDateTimeFormat);

  // Extract separate formats
  const dateFormat = dateTimeFormat.split(' ')[0]; // e.g., 'DD-MM-YYYY'
  const timeFormat = dateTimeFormat.split(' ').slice(1).join(' '); // e.g., 'hh:mm:ss a'

  const url = new URL(window.location.href);
  const [openTrendModal, setOpenTrendModal] = useState<boolean>(false);
  const { isLoading, assetMeasurementsHistoryData, error, errorMessage } =
    useFetchAssetMeausrementsHistory({
      assetId: asset.id,
    });

  const updateUrlParams = (
    assetId: string,
    measurementId: string,
    startDate: number,
    endDate: number,
  ) => {
    setOpenTrendModal(true);
    url.searchParams.set('asset_id', assetId);
    url.searchParams.set('measurement_id', measurementId);
    url.searchParams.set('start_date', startDate.toString());
    url.searchParams.set('end_date', endDate.toString());
    url.searchParams.set('measurement_trend', 'true');
    router.push(url, undefined, { shallow: true });
  };

  const deleteUrlParams = () => {
    const { pathname, searchParams } = url;

    const paramsToRemove = [
      'asset_id',
      'measurement_id',
      'start_date',
      'end_date',
      'measurement_trend',
    ];

    // Remove only the specified parameters
    paramsToRemove.forEach((param) => searchParams.delete(param));

    // Construct updated query parameters
    const updatedQuery = Object.fromEntries(searchParams.entries());

    // Update URL with the remaining query parameters
    router.push({ pathname, query: updatedQuery }, undefined, { shallow: true });
  };

  const renderMeasurementRow = (measurement: ExtendedAssetMeasurement, index: number) => {
    if (measurement.error) {
      return (
        <TableRow key={index}>
          <TableCell>{measurement.tag}</TableCell>
          <TableCell colSpan={2}>{measurement.error}</TableCell>
        </TableRow>
      );
    } else {
      let dateTime = 'N/A';
      let value: string | number = 'N/A';
      if (measurement['ts,val'] && measurement['ts,val'].length > 0) {
        const lastPair = measurement['ts,val'][measurement['ts,val'].length - 1];
        const date = new Date(lastPair[0]);
        // Format date and time using user preferences
        const formattedDate = dayjs ? dayjs(date).utc().format(dateFormat) : date.toLocaleDateString();
        const formattedTime = dayjs ? dayjs(date).utc().format(timeFormat) : date.toLocaleTimeString();
        dateTime = `${formattedDate} ${formattedTime}`;
        value = lastPair[1];
      }
      const baseUrl = `${window.location.protocol}//${window.location.host}`;
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - 5 * 60 * 60 * 1000); // 5 hours in milliseconds
      const link = `${baseUrl}/dynamic-chart?asset_id=${asset.id}&measurement_id=${
        measurement.id
      }&start_date=${startDate.getTime()}&end_date=${endDate.getTime()}`;

      return (
        <TableRow key={index}>
          <TableCell>{measurement.tag}</TableCell>
          <TableCell>{dateTime}</TableCell>
          <TableCell>
            {value !== 'N/A'
              ? thousandSeparator
                ? formatNumber(value as number)
                : roundNumber(value as number)
              : value}{' '}
            ({measurement?.tag_meta?.uom ?? 'N/A'})
          </TableCell>
          <TableCell>
            <Typography
              component={'a'}
              onClick={() => {
                updateUrlParams(
                  asset.id.toString(),
                  measurement.id.toString(),
                  startDate.getTime(),
                  endDate.getTime(),
                );
              }}
              sx={{ color: 'blue', textDecoration: 'underline', cursor: 'pointer' }}
            >
              Trend
            </Typography>
          </TableCell>
        </TableRow>
      );
    }
  };

  if (isLoading) {
    return (
      <Typography variant="h5" gutterBottom color={'warning.main'} mt={4}>
        Loading measurements data...
      </Typography>
    );
  }
  if (error || errorMessage) {
    return (
      <Typography variant="h5" gutterBottom color={'error'} mt={4}>
        {errorMessage}
      </Typography>
    );
  }
  return (
    <>
      <Box mt={4} p={2}>
        <Typography variant="h5" gutterBottom>
          Measurements
        </Typography>
        {assetMeasurementsHistoryData?.length === 0 ? (
          <Typography>No measurement found for this asset</Typography>
        ) : (
          <TableContainer component={Paper}>
            <Table aria-label="Measurements Data Table">
              <TableHead>
                <TableRow>
                  <TableCell>Measurement Tag</TableCell>
                  <TableCell>Last Recorded timestamp (UTC)</TableCell>
                  <TableCell>Value</TableCell>
                  <TableCell>Reference</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>{assetMeasurementsHistoryData?.map(renderMeasurementRow)}</TableBody>
            </Table>
          </TableContainer>
        )}
      </Box>
      {openTrendModal && (
        <Dialog
          fullWidth
          sx={{
            width: '100%',
            maxWidth: '100%',
            minWidth: '600px', // Set a minimum width
            minHeight: '400px', // Set a minimum height
            '& .MuiDialog-paper': {
              // Target the inner Dialog content
              minWidth: '90%',
              minHeight: '85%',
            },
          }}
          open={openTrendModal}
          onClose={() => {
            deleteUrlParams();
            setOpenTrendModal(false);
          }}
        >
          <DialogTitle id="customized-dialog-title">Measurement Trend</DialogTitle>
          <IconButton
            aria-label="close"
            onClick={() => {
              deleteUrlParams();
              setOpenTrendModal(false);
            }}
            sx={(theme) => ({
              position: 'absolute',
              right: 8,
              top: 8,
              color: theme.palette.grey[500],
            })}
          >
            <CloseIcon />
          </IconButton>
          <DialogContent dividers>
            <TestDashboards />
            {/* <DynamicCharts /> */}
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

export default AssetDetailsMeasurements;
