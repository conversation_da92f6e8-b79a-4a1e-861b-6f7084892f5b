import {
  Alert,
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  Grid,
  MenuItem,
  Select,
  SelectChangeEvent,
} from '@mui/material';
import { Dispatch, ReactNode, SetStateAction, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ExpressionTemplateDetails from '~/components/CalcEngine/ExpressionTemplateDetails';
import Loader from '~/components/common/Loader';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { NewAssetMeasurement } from '~/measurements/domain/types';
import { assetsApi } from '~/redux/api/assetsApi';
import {
  useCreateCalculationEngineInstanceMutation,
  useGetCalculationEngineTemplatesQuery,
  useGetPollPeriodsQuery,
} from '~/redux/api/calculationEngine';
import {
  measuresApi,
  useCreateMeasurementMutation,
  useGetAllMeasurementsByCustomerQuery,
} from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getNewMeasureId } from '~/redux/selectors/dashboardSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { AlertMessage } from '~/shared/forms/types';
import { Asset } from '~/types/asset';
import { calc_engine_template } from '~/types/calc_engine';
import SelectVariables from './SelectVariables';
type variables = {
  variable_name: string;
  variable_value_Type: 'measurement' | 'custom';
  variable_value: string;
  comments: string;
};
type CalcEngineProps = {
  parentAsset: Asset;
  measure: NewAssetMeasurement;
  assetPath: string;
  setCalcEngine: Dispatch<SetStateAction<boolean>>;
};
const CalcEngine = ({ measure, parentAsset, assetPath, setCalcEngine }: CalcEngineProps) => {
  const activeCustomer = useSelector(getActiveCustomer);
  const newMeasureId = useSelector(getNewMeasureId);
  const [isPersistance, setIsPersistance] = useState<boolean>(false);
  const [iswriteback, setWriteBack] = useState<boolean>(false);
  const [outputMeasure, setOutputMeasure] = useState<number>(0);
  const { data: pollPeriods } = useGetPollPeriodsQuery();
  const [expressionTemplate, setExpressionTemplate] = useState<calc_engine_template | null>(null);
  const { data: expressionTemplates, isLoading: fetchingExpressionTemplates } =
    useGetCalculationEngineTemplatesQuery();
  const [variables, setVariables] = useState<variables[]>([]);
  const [pollPeriod, setPollPeriod] = useState<number | null>(null);
  const dispatch = useDispatch();
  const [createInstance, { isError, isLoading, isSuccess, data: createInstanceData, error }] =
    useCreateCalculationEngineInstanceMutation();

  const [
    createMeasurement,
    {
      data: assetMeasurement,
      isSuccess: measureSuccess,
      error: measureError,
      isError: measureIsError,
      isLoading: measureLoading,
    },
  ] = useCreateMeasurementMutation();

  useEffect(() => {
    if (measureSuccess && assetMeasurement) {
      setAlertMessage({
        message: `Asset measurement "${assetMeasurement.id}" with measurement "${assetMeasurement.measurementId}" created successfully!`,
        severity: 'success',
      });

      dispatch(assetsApi.util.invalidateTags([{ type: 'Asset', id: parentAsset.id }]));
      dispatch(assetsApi.util.invalidateTags([{ type: 'Asset' }]));
      dispatch(measuresApi.util.invalidateTags(['Measure']));
      createInstance({
        customerId: activeCustomer?.id ?? 0,
        templateId: expressionTemplate?.id ?? 0,
        outputMesurementId: Number(assetMeasurement.measurementId),
        ispersisted: isPersistance,
        pollPeriod: isPersistance ? pollPeriod ?? 0 : undefined,
        iswriteback: iswriteback,
        inputs: variables.map((variable) => {
          return {
            inputLabel: variable.variable_name,
            constantType: !Number.isNaN(variable.variable_value) ? 'number' : 'string',
            constantValue:
              variable.variable_value_Type === 'custom' ? variable.variable_value : undefined,
            measurementId:
              variable.variable_value_Type === 'measurement'
                ? Number(variable.variable_value)
                : undefined,
            comments: variable.comments,
            comment: variable.comments,
          };
        }),
      });
    }

    if (measureIsError && measureError) {
      const err = measureError as CustomError;
      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [
    assetMeasurement,
    measureError,
    measureIsError,
    measureSuccess,
    measureLoading,
    parentAsset.id,
  ]);
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const [instanceMessage, setInstanceMessage] = useState<AlertMessage | undefined>(undefined);
  useEffect(() => {
    if (newMeasureId) {
      setOutputMeasure(newMeasureId);
    }
  }, [newMeasureId]);
  const { data: measurementsList, isFetching } = useGetAllMeasurementsByCustomerQuery(
    { customerId: activeCustomer?.id ?? 0 },
    {
      skip: !activeCustomer || activeCustomer.id === 0,
      refetchOnMountOrArgChange: true,
    },
  );
  const handleChange = (event: SelectChangeEvent<number | null>, child: ReactNode) => {
    setExpressionTemplate(
      (expressionTemplates?.items?.find(
        (template) => template.id === event.target.value,
      ) as calc_engine_template) || null,
    );
  };
  useEffect(() => {
    if (expressionTemplate) {
      const variables = expressionTemplate.expression.match(/\$\w+/g);
      const uniqueVariables = new Set(variables);
      setVariables(
        Array.from(uniqueVariables)?.map((variable) => {
          return {
            variable_name: variable,
            variable_value_Type: 'measurement',
            variable_value: '',
            comments: '',
          };
        }) ?? [],
      );
    }
  }, [expressionTemplate]);
  useEffect(() => {
    if (isSuccess && createInstanceData) {
      setInstanceMessage({
        message: `Expression Template Instance created successfully!`,
        severity: 'success',
      });
      dispatch(dashboardSlice.actions.setNewMeasureId(0));
    }

    if (isError && error) {
      const err = error as CustomError;
      setInstanceMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [createInstanceData, error, isError, isSuccess]);

  const saveInstance = () => {
    createMeasurement({
      customerId: activeCustomer?.id ?? 0,
      assetId: parentAsset.id,
      assetMeasurement: {
        ...measure,
        tag: (assetPath === '' ? parentAsset.tag : assetPath) + '\\' + measure.tag,
      },
    });
  };
  const handlePollPeriodChange = (event: SelectChangeEvent<string>) => {
    setPollPeriod(Number(event.target.value));
  };
  return (
    <Box pl={5}>
      <Box display={'flex'} justifyContent={'end'}></Box>
      {fetchingExpressionTemplates ? (
        <Loader
          style={{
            height: '100vh',
          }}
        />
      ) : (
        <>
          <Box mt={3}>
            {measure?.tag && <h3>Selected Measure: {measure.tag}</h3>}
            {measure?.description && <p>{measure.description}</p>}
          </Box>
          <Box mb={3}>
            <Select
              value={expressionTemplate?.id ?? ''}
              sx={{
                width: 300,
                '& fieldset': {
                  '& legend': {
                    maxWidth: '100%',
                    height: 'auto',
                    '& span': {
                      opacity: 1,
                    },
                  },
                },
              }}
              onChange={handleChange}
              label="Expression Template"
            >
              {expressionTemplates?.items?.map((template) => (
                <MenuItem key={template.id} value={template.id}>
                  {template.name}
                </MenuItem>
              ))}
            </Select>
          </Box>
          <ExpressionTemplateDetails selectedTemplate={expressionTemplate} />
          <Grid container spacing={2}>
            {variables.map((variable, index) => {
              return (
                <>
                  {measurementsList ? (
                    <SelectVariables
                      variables={variable}
                      measurementsList={measurementsList ?? []}
                      isLoading={isFetching}
                      setVariables={setVariables}
                      currentVariableIndex={index}
                      key={index}
                    />
                  ) : null}
                </>
              );
            })}
          </Grid>
          <Box m={3} ml={0}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={isPersistance}
                  onChange={(event) => setIsPersistance(event.target.checked)}
                />
              }
              label="Persistance"
            />
          </Box>
          {isPersistance ? (
            <FormControl fullWidth sx={{ mb: 2 }}>
              <Select
                sx={{
                  width: 300,
                  p: 0.3,
                  '& fieldset': {
                    '& legend': {
                      maxWidth: '100%',
                      height: 'auto',
                      '& span': {
                        opacity: 1,
                      },
                    },
                  },
                }}
                value={pollPeriod?.toString() ?? ''}
                onChange={handlePollPeriodChange}
                label="Poll Period"
              >
                {pollPeriods?.items.map((pollPeriod) => {
                  return (
                    <MenuItem key={pollPeriod.id} value={pollPeriod.id}>
                      {pollPeriod.value}
                    </MenuItem>
                  );
                })}
              </Select>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={iswriteback}
                    onChange={(event) => setWriteBack(event.target.checked)}
                  />
                }
                label="writeback"
              />
            </FormControl>
          ) : null}{' '}
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button color="primary" variant="outlined" onClick={() => setCalcEngine(false)}>
              Previous
            </Button>
            <Button
              onClick={saveInstance}
              color="primary"
              variant="contained"
              disabled={
                isSuccess ||
                expressionTemplate === null ||
                isLoading ||
                measureLoading ||
                variables.some((variable) => !variable.variable_value) ||
                variables.find((variable) => variable.variable_value_Type === 'measurement') ===
                  undefined
              }
            >
              Submit
            </Button>
          </Box>
          {alertMessage && (
            <Alert severity={alertMessage.severity} sx={{ mt: 3 }}>
              {alertMessage.message}
            </Alert>
          )}
          {instanceMessage && (
            <Alert severity={instanceMessage.severity} sx={{ mt: 3 }}>
              {instanceMessage.message}
            </Alert>
          )}
        </>
      )}
    </Box>
  );
};

export default CalcEngine;
