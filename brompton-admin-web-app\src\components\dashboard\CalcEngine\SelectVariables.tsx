import {
  Autocomplete,
  Box,
  FormControl,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  TextField,
} from '@mui/material';
import { ChangeEvent, Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { measurementsDTO } from '~/types/measures';
import { mapListToOptions } from '~/utils/utils';
type variables = {
  variable_name: string;
  variable_value_Type: 'measurement' | 'custom';
  variable_value: string;
  comments: string;
  id?: string;
};
type SelectVariablesProps = {
  variables: variables;
  measurementsList: measurementsDTO;
  setVariables: Dispatch<SetStateAction<variables[]>>;
  currentVariableIndex: number;
  isLoading: boolean;
};
const SelectVariables = ({
  variables,
  measurementsList,
  currentVariableIndex,
  setVariables,
  isLoading,
}: SelectVariablesProps) => {
  const measureValueTypeOptions = useMemo(
    () =>
      mapListToOptions(
        measurementsList?.items?.map((measurement) => ({
          id: measurement.id,
          name: measurement.tag,
        })) ?? [],
      ),
    [measurementsList.items],
  );
  const [variable, setVariable] = useState<variables>(variables);
  useEffect(() => {
    setVariable(variables);
  }, [variables]);
  const handleVariableTypeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setVariable({
      ...variable,
      variable_value: '',
      variable_value_Type: event.target.value as 'measurement' | 'custom',
    });
    setVariables((prev) => {
      const newVariables = [...prev];
      newVariables[currentVariableIndex] = {
        ...variable,
        variable_value: '',
        variable_value_Type: event.target.value as 'measurement' | 'custom',
      };
      return newVariables;
    });
  };
  const handleVariableChange = (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setVariable({
      ...variable,
      variable_value: event.target.value,
    });
    setVariables((prev) => {
      const newVariables = [...prev];
      newVariables[currentVariableIndex] = {
        ...variable,
        variable_value: event.target.value,
      };
      return newVariables;
    });
  };
  const handleVariableMeasureChange = (
    event: React.SyntheticEvent,
    value: {
      id: string;
      label: string;
    } | null,
  ) => {
    if (value) {
      setVariable({
        ...variable,
        variable_value: value.id.toString(),
      });
      setVariables((prev) => {
        const newVariables = [...prev];
        newVariables[currentVariableIndex] = {
          ...variable,
          variable_value: value.id.toString(),
        };
        return newVariables;
      });
    }
  };
  const handleCommentsChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    setVariable({
      ...variable,
      comments: event.target.value,
    });
    setVariables((prev) => {
      const newVariables = [...prev];
      newVariables[currentVariableIndex] = {
        ...variable,
        comments: event.target.value,
      };
      return newVariables;
    });
  };
  return (
    <Grid item xs={3}>
      <FormControl component="fieldset" fullWidth>
        <RadioGroup
          value={variable.variable_value_Type}
          onChange={(event) => handleVariableTypeChange(event)}
          row
        >
          <FormControlLabel value="measurement" control={<Radio />} label="Measurement" />
          <FormControlLabel value="custom" control={<Radio />} label="Constant" />
        </RadioGroup>
      </FormControl>
      {variable.variable_value_Type === 'measurement' ? (
        <FormControl fullWidth sx={{ mb: 2 }}>
          <Autocomplete
            id="measurement-list"
            options={measureValueTypeOptions ?? []}
            loading={isLoading}
            getOptionLabel={(option) => option.label}
            onChange={handleVariableMeasureChange}
            fullWidth
            isOptionEqualToValue={(option, value) => option.id === value.id}
            PaperComponent={({ children }) => (
              <Box
                sx={{
                  position: 'absolute',
                  zIndex: 10000,
                  backgroundColor: (theme) => theme.palette.background.paper,
                  overflow: 'auto',
                  border: '1px solid #d3d4d5',
                }}
              >
                {children}
              </Box>
            )}
            value={
              measureValueTypeOptions.find(
                (option) =>
                  option.id === variable.variable_value ||
                  option.id === variable.variable_value?.toString(),
              ) ?? null
            }
            renderInput={(params) => <TextField {...params} label="Select Measure" />}
          />
        </FormControl>
      ) : null}
      {variable.variable_value_Type === 'custom' ? (
        <TextField
          sx={{ mb: 2, mt: 1 }}
          fullWidth
          InputLabelProps={{ shrink: true }}
          value={variable.variable_value}
          onChange={(event) => handleVariableChange(event)}
          label={`Variable ${variable.variable_name} Input value`}
          variant="outlined"
        />
      ) : null}
      <TextField
        fullWidth
        margin="normal"
        InputLabelProps={{ shrink: true }}
        value={variable.comments}
        onChange={(event) => handleCommentsChange(event)}
        label={`Variable ${variable.variable_name} add comments`}
        variant="outlined"
      />
    </Grid>
  );
};
export default SelectVariables;
