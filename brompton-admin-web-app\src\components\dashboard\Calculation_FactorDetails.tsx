import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { Box, Divider, Grid, Tooltip, Typography } from '@mui/material';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Datasource } from '~/measurements/domain/types';
import {
  useGetCalculationByMeasureIdQuery,
  useGetPollPeriodsQuery,
} from '~/redux/api/calculationEngine';
import { useGetFactorByMeasureIdQuery, useGetFactorsQuery } from '~/redux/api/factorApi';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';
import { AssetMeasurementDetails } from '~/types/measures';
import { formatMetricTag, getWeekdays } from '~/utils/utils';

type Calculation_FactorDetailsProps = {
  measure: AssetMeasurementDetails;
  dataSource: Datasource[];
};
const formatDate = (date: string, format: string) => {
  try {
    return dayjs(date).format(format);
  } catch (error) {
    console.error('Invalid date format:', error);
    return '';
  }
};
const Calculation_FactorDetails = ({ measure, dataSource }: Calculation_FactorDetailsProps) => {
  const [measurement, setMeasurement] = useState<AssetMeasurementDetails>(measure);
  const dateTimeFormat = useSelector(getDateTimeFormat);
  useEffect(() => {
    setMeasurement(measure);
  }, [measure]);
  const CalculationDataSource = dataSource.find(
    (data) => data.id === measure?.datasourceId && data.name === 'Calculation',
  );
  const TimeVaryingFactorDataSource = dataSource.find(
    (data) => data.id === measurement?.datasourceId && data.name === 'TimeVaryingFactor',
  );
  const { data } = useGetCalculationByMeasureIdQuery(measurement?.measurementId ?? 0, {
    skip: !CalculationDataSource,
    refetchOnMountOrArgChange: true,
  });
  const { data: pollPeriods } = useGetPollPeriodsQuery(undefined, {
    skip: !CalculationDataSource,
    refetchOnMountOrArgChange: true,
  });
  const { data: factorTypes } = useGetFactorsQuery(undefined, {
    skip: !TimeVaryingFactorDataSource,
    refetchOnMountOrArgChange: true,
  });
  const { data: timeVaringFactor } = useGetFactorByMeasureIdQuery(measurement?.measurementId ?? 0, {
    skip: !TimeVaryingFactorDataSource,
    refetchOnMountOrArgChange: true,
  });

  return (
    <>
      {data && CalculationDataSource && (
        <>
          <Divider />
          <Typography variant="h6">Calculation Name</Typography>
          <Typography variant="body2" paragraph>
            {data?.calcTemplate?.name ?? 'N/A'}
          </Typography>
          <Typography variant="h6">Expression</Typography>
          <Typography variant="body2" paragraph>
            {data?.calcTemplate?.expression ?? 'N/A'}
          </Typography>
          <Typography variant="h6">Description</Typography>
          <Typography variant="body2" paragraph>
            {data?.calcTemplate?.description ?? 'N/A'}
          </Typography>
          <Box display={'flex'} gap={2} alignItems={'center'}>
            <Typography variant="h6">Data Type</Typography>
            <Tooltip title="Data type of the calculation template" placement="right">
              <InfoOutlinedIcon fontSize="small" color="primary" />
            </Tooltip>
          </Box>
          <Typography variant="body2" paragraph>
            {data.calcTemplate.dataType.name ?? 'N/A'}
          </Typography>
          <Typography variant="h6">Persisted</Typography>
          <Typography variant="body2" paragraph>
            {data?.calcInstance?.ispersisted ? 'Yes' : 'No'}
          </Typography>
          {data?.calcInstance?.ispersisted && pollPeriods ? (
            <>
              <Box display={'flex'} gap={2} alignItems={'center'}>
                <Typography variant="h6">Poll period</Typography>
                <Tooltip
                  title={
                    'If Persisted is Yes then poll period will have value. The period of time between each calculation execution. '
                  }
                  placement="right"
                >
                  <InfoOutlinedIcon fontSize="small" color="primary" />
                </Tooltip>
              </Box>
              <Typography variant="body2" paragraph>
                {pollPeriods?.items.find((poll) => poll.id === data.calcInstance.pollPeriod)
                  ?.value ?? 'N/A'}
              </Typography>
            </>
          ) : null}
          <Typography variant="h6">Inputs</Typography>
          <Grid container>
            {data?.calcInputs?.map((input, i) => (
              <Grid item md={3} key={i}>
                <Typography variant="body2" paragraph>
                  Variable- <b>{input.inputLabel}</b>
                </Typography>
                {input.measurement ? (
                  <Typography variant="body2" paragraph>
                    Measure -<b>{formatMetricTag(input.measurement)}</b>
                  </Typography>
                ) : null}
                {input.constantValue ? (
                  <Typography variant="body2" paragraph>
                    Measure - {input.constantValue}
                  </Typography>
                ) : null}
                {input.constantNumber ? (
                  <Typography variant="body2">Number - {input.constantNumber}</Typography>
                ) : null}
              </Grid>
            ))}
          </Grid>
        </>
      )}
      {TimeVaryingFactorDataSource && timeVaringFactor && (
        <>
          <Divider />
          <Typography variant="h6">Factor Type</Typography>
          <Typography variant="body2" paragraph>
            {factorTypes?.items.find((factor) => factor.id === timeVaringFactor.factorType)?.name ??
              'N/A'}
          </Typography>
          <Typography variant="h6">Seasonal</Typography>
          <Typography variant="body2" paragraph>
            {timeVaringFactor.seasonal ? 'Yes' : 'No'}
          </Typography>
          <Typography variant="h6">Effective Date</Typography>
          <Typography variant="body2" paragraph>
            {timeVaringFactor.factorTimeOfDayValue?.map((factor) => {
              return (
                <Box key={factor.effectiveDate} display={'flex'} gap={5}>
                  <Typography variant="body2" paragraph fontWeight={'bold'}>
                    Date -{' '}
                    {dateTimeFormat.includes(' ')
                      ? formatDate(factor.effectiveDate, dateTimeFormat.split(' ')[0])
                      : formatDate(factor.effectiveDate, dateTimeFormat)}
                  </Typography>
                  {factor.factorTimeOfDayValue.map((factor) => {
                    return (
                      <Box key={factor.timeOfDay}>
                        <Typography variant="body2" paragraph>
                          <b> Weekday </b>- {getWeekdays(factor.weekday)}
                        </Typography>
                        <Typography variant="body2" paragraph>
                          <b> Time</b> - {factor.timeOfDay}
                        </Typography>
                        <Typography variant="body2" paragraph>
                          <b>Value</b> - {factor.value}
                        </Typography>
                      </Box>
                    );
                  })}
                </Box>
              );
            })}
          </Typography>
        </>
      )}
    </>
  );
};

export default Calculation_FactorDetails;
