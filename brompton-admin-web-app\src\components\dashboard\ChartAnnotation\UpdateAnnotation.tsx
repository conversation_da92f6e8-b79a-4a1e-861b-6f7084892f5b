import CancelIcon from '@mui/icons-material/Cancel';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import dayjs from 'dayjs';
import { Annotations } from 'plotly.js';
import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import CustomDialog from '~/components/common/CustomDialog';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { CustomAnnotation } from '~/measurements/domain/types';
import { useUpdateAnnotationMutation } from '~/redux/api/annotation';
import { getCurrentDashboardId } from '~/redux/selectors/dashboardSelectors';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';
import { formatDate } from '~/utils/utils';
type updateChartAnnotationProps = {
  id: string;
  updateAnnotation: {
    id: number | null;
    showAnnotation: boolean;
    measurementId: string | null;
    time: number | null;
    value: number | null;
    settings: Annotations | null;
  };
  setUpdateAnnotation: (
    value: React.SetStateAction<{
      id: number | null;
      showAnnotation: boolean;
      measurementId: string | null;
      time: number | null;
      value: number | null;
      settings: Annotations | null;
    }>,
  ) => void;
  showSuccessAlert: (message: string) => void;
  showErrorAlert: (message: string) => void;
  setMeasureIdAnnotations: React.Dispatch<
    React.SetStateAction<Partial<Annotations | CustomAnnotation>[]>
  >;
};
const UpdateAnnotation = ({
  id,
  setMeasureIdAnnotations,
  setUpdateAnnotation,
  showErrorAlert,
  showSuccessAlert,
  updateAnnotation,
}: updateChartAnnotationProps) => {
  const dateTimeFormat = useSelector(getDateTimeFormat);
  const dashboard = useSelector(getCurrentDashboardId);

  const [updateAnnotationQuery, { isError, isSuccess, error }] = useUpdateAnnotationMutation();

  useEffect(() => {
    if (isError) {
      const err = error as CustomError;
      showErrorAlert(err?.data?.message ?? 'Error updating annotation');
    }
    if (isSuccess) {
      showSuccessAlert('Annotation updated successfully');
      if (
        updateAnnotation?.settings &&
        updateAnnotation?.time !== null &&
        updateAnnotation?.value !== null
      ) {
        setMeasureIdAnnotations((prevState) => {
          return prevState.map((annotation) => {
            // Check if the annotation id matches updateAnnotation.id
            if ((annotation as CustomAnnotation).id === updateAnnotation.id) {
              // Return the updated annotation with the new settings
              return {
                // ...annotation,
                ...updateAnnotation.settings, // Apply new settings
                x: formatDate(new Date(Number(updateAnnotation.time)), 'YYYY-MM-DD HH:mm:ss'),
                y: Number(updateAnnotation.value),
                visible: true, // Default to visible
                captureevents: true,
                showarrow: false,
                // width: 100, // Default width
                // height: 30, // Default height
                opacity: 1, // Default opacity
                // text: updateAnnotation.settings?.text ?? '', // Ensure text is always a string, default to empty string
                // textangle: updateAnnotation.settings?.textangle ?? '0', // Default to '0' if undefined
                font: updateAnnotation.settings?.font ?? {}, // Default to empty object if font is undefined
              };
            }
            // If id doesn't match, return the annotation unchanged
            return annotation;
          });
        });

        // Reset updateAnnotation state
        setUpdateAnnotation({
          id: null,
          measurementId: null,
          showAnnotation: false,
          time: null,
          value: null,
          settings: null,
        });
      }
    }
  }, [isError, isSuccess, error]);
  return (
    <>
      <CustomDialog
        open={
          updateAnnotation.showAnnotation &&
          updateAnnotation.id !== null &&
          updateAnnotation.measurementId !== null
        }
        title={<Typography variant="h6">Update Annotation</Typography>}
        maxWidth="md"
        onClose={() => {
          setUpdateAnnotation({
            id: updateAnnotation.id,
            measurementId: null,
            showAnnotation: false,
            time: null,
            value: null,
            settings: null,
          });
        }}
        dialogActions={
          <Box sx={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="outlined"
              startIcon={<CancelIcon />}
              onClick={() => {
                setUpdateAnnotation({
                  ...updateAnnotation,
                  measurementId: null,
                  showAnnotation: false,
                  time: null,
                  value: null,
                });
              }}
              color="primary"
            >
              Cancel
            </Button>
            <Button
              startIcon={<SaveAsIcon />}
              onClick={() => {
                let settings: Annotations | null = null;
                if (updateAnnotation.settings !== null) {
                  const defaultSettings = {
                    arrowhead: 7,
                    bgcolor: '#e01010',
                    hovertext: '',
                    visible: true,
                    hoverlabel: {
                      bgcolor: '#ffffff',
                      font: {
                        color: '#e01010',
                        size: 13,
                      },
                    },
                  };

                  settings = {
                    ...defaultSettings,
                    ...updateAnnotation.settings,
                    hoverlabel: {
                      ...defaultSettings.hoverlabel,
                      ...updateAnnotation.settings.hoverlabel,
                      font: {
                        ...defaultSettings.hoverlabel.font,
                        ...(updateAnnotation.settings.hoverlabel?.font || {}),
                      },
                    },
                  };
                }
                setUpdateAnnotation({
                  ...updateAnnotation,
                  settings,
                  showAnnotation: true,
                });
                updateAnnotationQuery({
                  id: updateAnnotation.id ?? 0,
                  dashboard: dashboard,
                  widget_id: Number(id),
                  description: updateAnnotation.settings?.text ?? '',
                  measurement_id: Number(updateAnnotation.measurementId), // Ensure it's a number
                  time_of_annotation: updateAnnotation.time ? updateAnnotation.time : 0, // Handle undefined time
                  value: updateAnnotation.value ? updateAnnotation.value : 0, // Handle undefined value
                  settings: updateAnnotation.settings ? JSON.stringify(settings) : '{}', // Ensure settings is a valid JSON string
                });
              }}
              color="primary"
              variant="contained"
            >
              Update
            </Button>
          </Box>
        }
        content={
          <>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                label="Value"
                disabled
                sx={{ mt: 2 }}
                variant="outlined"
                value={updateAnnotation.value || ''}
                fullWidth
              />
              <TextField
                label={'Timestamp'}
                disabled
                type={'datetime-local' + updateAnnotation.time}
                variant="outlined"
                value={
                  updateAnnotation.time
                    ? dayjs(new Date(updateAnnotation.time)).format(dateTimeFormat)
                    : ''
                }
                fullWidth
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextField
                label={'Label'}
                variant="outlined"
                value={updateAnnotation.settings?.hovertext}
                fullWidth
                onChange={(event) => {
                  setUpdateAnnotation({
                    ...updateAnnotation,
                    settings: {
                      ...(updateAnnotation.settings || {}),
                      hovertext: event.target.value,
                      visible: updateAnnotation.settings?.visible ?? true, // Set default visible if null
                    } as Annotations,
                  });
                }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextField
                label={'Background'}
                type="color"
                variant="outlined"
                value={updateAnnotation.settings?.bgcolor}
                fullWidth
                onChange={(event) => {
                  setUpdateAnnotation({
                    ...updateAnnotation,
                    settings: {
                      ...(updateAnnotation.settings || {}),
                      bgcolor: event.target.value,
                      visible: updateAnnotation.settings?.visible ?? true, // Set default visible if null
                    } as Annotations,
                  });
                }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <FormControl fullWidth>
                  <InputLabel id="shape-select-label">Shape</InputLabel>
                  <Select
                    labelId="shape-select-label"
                    value={updateAnnotation.settings?.arrowhead || 0}
                    onChange={(event) => {
                      setUpdateAnnotation({
                        ...updateAnnotation,
                        settings: {
                          ...(updateAnnotation.settings || {}),
                          arrowhead: Number(event.target.value), // Set the shape in settings
                        } as Annotations,
                      });
                    }}
                    label="Shape"
                  >
                    <MenuItem value={7}>Rectangle</MenuItem>
                    <MenuItem value={6}>Circle</MenuItem>
                    <MenuItem value={4}>Arrow</MenuItem>
                  </Select>
                </FormControl>
              </Box>
              <TextField
                label={'Font Size'}
                variant="outlined"
                type="number" // Add this to allow color selection
                value={updateAnnotation.settings?.hoverlabel?.font?.size || 13} // Default to black if not set
                fullWidth
                onChange={(event) => {
                  setUpdateAnnotation({
                    ...updateAnnotation,
                    settings: {
                      ...(updateAnnotation.settings || {}),
                      hoverlabel: {
                        ...(updateAnnotation.settings?.hoverlabel || {}),
                        font: {
                          ...(updateAnnotation.settings?.hoverlabel?.font || {}),
                          size: Number(event.target.value), // Update font color here
                        },
                      },
                      visible: updateAnnotation.settings?.visible ?? true, // Set default visible if null
                    } as Annotations,
                  });
                }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextField
                label={'Font Color'}
                variant="outlined"
                type="color" // Add this to allow color selection
                value={updateAnnotation.settings?.hoverlabel?.font?.color || '#000000'} // Default to black if not set
                fullWidth
                onChange={(event) => {
                  setUpdateAnnotation({
                    ...updateAnnotation,
                    settings: {
                      ...(updateAnnotation.settings || {}),
                      hoverlabel: {
                        ...(updateAnnotation.settings?.hoverlabel || {}),
                        font: {
                          ...(updateAnnotation.settings?.hoverlabel?.font || {}),
                          color: event.target.value, // Update font color here
                        },
                        visible: updateAnnotation.settings?.visible ?? true, // Set default visible if null
                      },
                    } as Annotations,
                  });
                }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
              <TextField
                label={'Description Background'}
                variant="outlined"
                type="color" // Use 'color' input type to allow color selection
                value={updateAnnotation.settings?.hoverlabel?.bgcolor || '#ffffff'} // Default to white if not set
                fullWidth
                onChange={(event) => {
                  const backgroundColor = event.target.value; // Get the selected color

                  // Update the settings with the new background color
                  setUpdateAnnotation({
                    ...updateAnnotation,
                    settings: {
                      ...(updateAnnotation.settings || {}),
                      hoverlabel: {
                        ...(updateAnnotation.settings?.hoverlabel || {}),
                        bgcolor: backgroundColor, // Set the new background color
                      },
                      visible: updateAnnotation.settings?.visible ?? true, // Set default visible if null
                    } as Annotations, // Ensure the type is correct here
                  });
                }}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Box>
          </>
        }
      />
    </>
  );
};

export default UpdateAnnotation;
