import { yupResolver } from '@hookform/resolvers/yup';
import CancelIcon from '@mui/icons-material/Cancel';
import SaveIcon from '@mui/icons-material/Save';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import {
  Alert,
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  Radio,
  RadioGroup,
  TextField,
} from '@mui/material';
import { ChangeEvent, Dispatch, FC, SetStateAction, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import {
  useGetAllAssetTemplatedByAssetTypeQuery,
  useGetAllBackOfficeAssetTypesQuery,
} from '~/redux/api/assetsApi';
import { useGetDashboardByCustomerIdQuery } from '~/redux/api/dashboardApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getCurrentDashboardId } from '~/redux/selectors/dashboardSelectors';
import { AssetTypeOption } from '~/types/asset';
import { DashboarForm, dashboardTitleSchema } from '~/types/dashboardList';
import { assetTypePathMapperFilterTemplates } from '~/utils/mappers/asset-type-mapper';
import DialogTransition from '../common/DialogTransition';
import { getWidgets } from '~/redux/selectors/widgetSelectors';

type DashboardTitleDialogProps = {
  open: boolean;
  onClose: () => void;
  flow: 'save' | 'update' | 'template';
  onSave: (data: DashboarForm, flow: 'save' | 'update' | 'template') => void;
  initialTitle: string;
  assetType: number;
  setAssetType: Dispatch<SetStateAction<number>>;
  assetTemplate: number;
  setAssetTemplate: Dispatch<SetStateAction<number>>;
  saveAsGlobal: boolean;
  setSaveAsGlobal: Dispatch<SetStateAction<boolean>>;
};

const DashboardTitleDialog: FC<DashboardTitleDialogProps> = ({
  open,
  onClose,
  onSave,
  flow,
  initialTitle,
  assetType,
  setAssetType,
  assetTemplate,
  setAssetTemplate,
  saveAsGlobal,
  setSaveAsGlobal,
}) => {
  const widgets = useSelector(getWidgets);
  const [duplicateTitle, setDuplicateTitle] = useState<boolean>(true);
  const [unlinkWarning, setUnlinkWarning] = useState<boolean>(false);
  const [assetTypeError, setAssetTypeError] = useState(false);
  const [assetTemplateError, setAssetTemplateError] = useState(false);
  const { globalAdmin } = useHasAdminAccess();
  const {
    data: assetTypeListData,
    isLoading: isAssetTypeLoading,
    isSuccess: isSuccessfullBackOffieAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapperFilterTemplates(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes]);
  const { data: assetTemplates, isLoading } = useGetAllAssetTemplatedByAssetTypeQuery(
    {
      assetTypeId: assetType !== undefined && assetType !== null ? assetType?.toString() : '',
    },
    {
      refetchOnMountOrArgChange: true,
      skip: assetType === null || assetType === undefined,
    },
  );
  const currentDashboardId = useSelector(getCurrentDashboardId);
  const { control, handleSubmit, reset } = useForm<DashboarForm>({
    defaultValues: {
      title: initialTitle,
    },
    resolver: yupResolver(dashboardTitleSchema),
  });
  const activeCustomer = useSelector(getActiveCustomer);
  const { data: dashboardList } = useGetDashboardByCustomerIdQuery(
    {
      customerId: activeCustomer?.id ?? 0,
      search: null,
    },
    {
      skip: !activeCustomer?.id,
      refetchOnMountOrArgChange: true,
    },
  );

  const [action, setAction] = useState<'save' | 'update' | 'template'>(flow);

  useEffect(() => {
    if (action === 'save' || action === 'template') {
      reset({ title: '' });
    } else {
      reset({ title: currentDashboardId > 0 ? initialTitle : '' });
    }
  }, [flow, action, reset, initialTitle, currentDashboardId]);

  useEffect(() => {
    if (
      action === 'update' && // Ensure it's only for update flow/action
      dashboardList?.items
    ) {
      const findDashboard = dashboardList.items.find(
        (dashboard) => dashboard.id === currentDashboardId,
      );

      if (
        findDashboard &&
        findDashboard.asset !== null &&
        findDashboard.dashboardTemplate !== null
      ) {
        setUnlinkWarning(true);
      } else {
        setUnlinkWarning(false);
      }
    } else {
      setUnlinkWarning(false);
    }
  }, [dashboardList, action, currentDashboardId]);

  const checkDuplicateTitle = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (action !== 'update') {
      if (
        dashboardList?.items?.find(
          (dashboard) => dashboard.title.toUpperCase() === e.target.value.toUpperCase(),
        )
      ) {
        setDuplicateTitle(false);
      } else {
        setDuplicateTitle(true);
      }
    }
  };

  const handleChangeAssetType = (
    event: React.SyntheticEvent,
    value: {
      id: string;
      label: string;
    } | null,
  ) => {
    setAssetType(Number(value?.id ?? 0));
  };

  const handleChangeAssetTemplate = (
    event: React.SyntheticEvent,
    value: {
      id: string;
      label: string;
    } | null,
  ) => {
    setAssetTemplate(Number(value?.id ?? 0));
  };

  const handleFormSubmit = async (data: DashboarForm) => {
    if (action === 'template') {
      let hasError = false;

      if (!assetType || assetType === 0) {
        setAssetTypeError(true);
        hasError = true;
      } else {
        setAssetTypeError(false);
      }

      if (!assetTemplate || assetTemplate === 0) {
        setAssetTemplateError(true);
        hasError = true;
      } else {
        setAssetTemplateError(false);
      }

      if (hasError) {
        return; // Stop form submission
      }
    }

    onClose();
    onSave(data, action);
  };

  const isDashboardTileWidget = widgets.some((widget) => widget.type === 'dashboard-widget');

  return (
    <Dialog
      maxWidth="md"
      open={open}
      onClose={() => {
        onClose();
        setAction(flow);
        reset({ title: flow === 'update' ? initialTitle : '' });
        setDuplicateTitle(true);
      }}
      TransitionComponent={DialogTransition}
      sx={{
        '& .MuiDialog-container': {
          '& .MuiPaper-root': {
            width: '100%',
            // maxWidth: '400px',
            p: 1,
          },
        },
      }}
    >
      <form onSubmit={handleSubmit(handleFormSubmit)} noValidate>
        <DialogTitle>{currentDashboardId <= 0 ? 'Save Dashboard' : 'Update Dashboard'}</DialogTitle>
        <DialogContent>
          {currentDashboardId > 0 ? (
            <RadioGroup
              aria-label="action"
              name="action"
              value={action}
              onChange={(e) => {
                setAction(e.target.value as 'save' | 'update' | 'template');
                setDuplicateTitle(true);
                setAssetTypeError(false);
                setAssetTemplateError(false);
                setAssetTemplate(0);
                setAssetType(0);
              }}
            >
              <FormControlLabel value="save" control={<Radio />} label="Save As" />
              <FormControlLabel value="update" control={<Radio />} label="Update" />
              <FormControlLabel value="template" control={<Radio />} label="Save as Template" />
            </RadioGroup>
          ) : (
            <RadioGroup
              aria-label="action"
              name="action"
              value={action}
              onChange={(e) => {
                setAction(e.target.value as 'save' | 'update' | 'template');
                setDuplicateTitle(true);
              }}
            >
              <FormControlLabel value="save" control={<Radio />} label="Save" />
              <FormControlLabel value="template" control={<Radio />} label="Save as Template" />
            </RadioGroup>
          )}
          <Controller
            name="title"
            control={control}
            render={({ field: { onChange, onBlur, value }, fieldState }) => (
              <TextField
                error={!!fieldState.error || !duplicateTitle}
                helperText={
                  fieldState.error?.message ||
                  (!duplicateTitle ? 'Title already exists. Please use a different title.' : '')
                }
                onChange={(e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
                  onChange(e);
                  checkDuplicateTitle(e);
                }}
                onBlur={onBlur}
                value={value}
                label={action === 'template' ? 'Template Title' : 'Dashboard Title'}
                variant="outlined"
                margin="normal"
                fullWidth
                required
              />
            )}
          />
          {action === 'template' && !isDashboardTileWidget ? (
            <>
              <Box sx={{ width: '100%', mt: 1 }}>
                <Autocomplete
                  disablePortal
                  fullWidth
                  id="asset-type-select"
                  options={assetTypesWithPath.map((item) => ({
                    id: item.value.toString(),
                    label: item.label,
                  }))}
                  value={
                    assetTypesWithPath
                      .map((item) => ({
                        id: item.value.toString(),
                        label: item.label,
                      }))
                      .find((item) => item.id === assetType?.toString()) ?? null
                  }
                  loading={isAssetTypeLoading}
                  onChange={(event, value) => {
                    handleChangeAssetType(event, value);
                    setAssetTypeError(false); // Clear error when selecting
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Asset Type"
                      required
                      error={assetTypeError}
                      helperText={assetTypeError ? 'Asset Type is required' : ''}
                    />
                  )}
                />
              </Box>

              <Box sx={{ width: '100%', mt: 2 }}>
                <Autocomplete
                  disablePortal
                  id="asset-template-select"
                  disabled={isLoading}
                  options={
                    assetTemplates?.items?.map((item) => ({
                      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                      //@ts-ignore
                      id: item.id.toString(),
                      label: `${item.model_number} - ${item.manufacturer}`,
                    })) ?? []
                  }
                  value={
                    assetTemplates?.items
                      ?.map((item) => ({
                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                        //@ts-ignore
                        id: item.id.toString(),
                        label: `${item.model_number} - ${item.manufacturer}`,
                      }))
                      .find((item) => item.id === assetTemplate?.toString()) ?? null
                  }
                  loading={isLoading}
                  onChange={(event, value) => {
                    handleChangeAssetTemplate(event, value);
                    setAssetTemplateError(false); // Clear error when selecting
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Asset Template"
                      required
                      error={assetTemplateError}
                      helperText={assetTemplateError ? 'Asset Template is required' : ''}
                    />
                  )}
                />
              </Box>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={saveAsGlobal}
                    disabled={!globalAdmin}
                    onChange={(e) => setSaveAsGlobal(e.target.checked)}
                  />
                }
                label="Save as Global Dashboard Template"
                sx={{ mt: 1 }}
              />
            </>
          ) : null}
          {unlinkWarning && (
            <Alert severity="warning" sx={{ mt: 3 }}>
              <strong>Important Notice:</strong> Any modifications made to this dashboard will
              automatically detach it from the associated dashboard template. Please review your
              changes carefully.
            </Alert>
          )}
          {action === 'template' && isDashboardTileWidget && (
            <Alert severity="warning" sx={{ mt: 3 }}>
              <strong>Warning: </strong>You cannot save this dashboard as template as it&apos;s
              containing the <b>Dashboard Widget</b>. Remove the Dashboard Widget to save this
              dashboard as Template.
            </Alert>
          )}
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'space-between' }}>
          <Button
            onClick={() => {
              onClose();
              setAction(flow);
              reset({ title: flow === 'update' ? initialTitle : '' });
              setDuplicateTitle(true);
              setAssetTemplate(0);
            }}
            variant="outlined"
            startIcon={<CancelIcon />}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={!duplicateTitle || (isDashboardTileWidget && action === 'template')}
            onClick={() => {
              if (action === 'template') {
                let hasError = false;

                if (!assetType || assetType === 0) {
                  setAssetTypeError(true);
                  hasError = true;
                } else {
                  setAssetTypeError(false);
                }

                if (!assetTemplate || assetTemplate === 0) {
                  setAssetTemplateError(true);
                  hasError = true;
                } else {
                  setAssetTemplateError(false);
                }

                if (hasError) {
                  return; // Stop form submission
                }
              }
            }}
            startIcon={
              action === 'save' ? (
                <SaveIcon />
              ) : action === 'template' ? (
                <SaveAsIcon />
              ) : (
                <SaveIcon />
              )
            }
          >
            {action === 'save' ? 'Save' : action === 'template' ? 'Save as Template' : 'Update'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default DashboardTitleDialog;
