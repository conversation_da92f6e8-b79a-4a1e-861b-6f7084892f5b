import { Box, CircularProgress, Typography } from '@mui/material';
import { ThunkDispatch } from '@reduxjs/toolkit';
import { useEffect, useState } from 'react';
import { Layout } from 'react-grid-layout';
import { useDispatch, useSelector } from 'react-redux';
import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import { WidgetContainer } from '~/layout/containers/WidgetContainer';
import {
  dashboardTemplateApi,
  useGetDashboardTemplateDetailsQuery,
} from '~/redux/api/dashboardTemplate';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { getDashboardTemplateId } from '~/redux/selectors/dashboardSelectors';
import { getCurrentSelectedAssetId } from '~/redux/selectors/treeSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { RootState } from '~/redux/store';
import { DashboardState } from '~/types/dashboard';
import { DashboardTemplate } from '~/types/dashboardTemplate';
import { elementVariable } from '~/types/diagram';
import { DashboardWidget, ImageTextDetails, Widget } from '~/types/widgets';
import { formatMetricLabel } from '~/utils/utils';
import DashboardWidgetSettingsDialog from './DashboardWidgetSettingsDialog';

type DashboardWidgetProps = {
  id: string;
  settings: DashboardWidget;
};

const DashboardWidgetContainer = ({ id, settings }: DashboardWidgetProps) => {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const dashboardTemplateId = useSelector(getDashboardTemplateId);
  const assetId = useSelector(getCurrentSelectedAssetId);
  const customerId = useSelector(getCustomerId);
  const [setsyncedWidgets, setSetsyncedWidgets] = useState<{
    widgets: Widget[];
    widgetLayout: Layout[];
  }>({
    widgets: [],
    widgetLayout: [],
  });
  const [assetMeasurementsListMap, setAssetMeasurementsListMap] = useState<
    Map<
      number,
      {
        id: number;
        tag: string;
      }[]
    >
  >(new Map());
  const [loading, setLoading] = useState(false);

  const {
    isLoading: isLoadingDashboardTemplates,
    data: templateData,
    isSuccess: isTemplateSuccess,
    isError: isTemplateError,
  } = useGetDashboardTemplateDetailsQuery(
    settings.dashboardTemplateOption ? Number(settings.dashboardTemplateOption.id) : 0,
    {
      skip: !settings.dashboardTemplateOption || Number(settings.dashboardTemplateOption.id) === 0,
      refetchOnMountOrArgChange: true,
    },
  );

  const { data: assetMeasurements, isFetching: isMeasurementsFetching } =
    useGetAllMeasurementsQuery(
      {
        assetId: Number(settings.assetOption?.id),
        customerId: customerId,
      },
      {
        skip: !settings.assetOption?.id || !customerId || String(settings.assetOption?.id) === '',
        refetchOnMountOrArgChange: true,
      },
    );

  useEffect(() => {
    if (isMeasurementsFetching) {
      setAssetMeasurementsListMap(new Map());
    } else if (assetMeasurements) {
      const assetMeasurementsMap = new Map<number, { id: number; tag: string }[]>();
      assetMeasurements.forEach((item) => {
        if (item.metric_id) {
          const existingMeasures = assetMeasurementsMap.get(item.metric_id) ?? [];
          assetMeasurementsMap.set(item.metric_id, [
            ...existingMeasures,
            { id: item.id, tag: item.tag },
          ]);
        }
      });
      setAssetMeasurementsListMap(assetMeasurementsMap);
    }
  }, [assetMeasurements, isMeasurementsFetching]);
  const transformTemplate = async (
    templateData: DashboardTemplate,
    assetMeasurementsListMap: Map<number, { id: number; tag: string }[]>,
    settings: DashboardWidget,
  ) => {
    if (templateData && templateData.data && assetMeasurementsListMap) {
      const templateDetailsData = JSON.parse(templateData.data) as {
        widget: DashboardState['widget'];
        topPanel: DashboardState['template']['topPanel'];
        chart: DashboardState['template']['chart'];
      };
      const metricsToShow: number[] = [];
      const metricMeasurements: Record<string, { metricName: string; measurement: string }> = {};
      const widgets = templateDetailsData.widget.widgets.map(async (widget) => {
        switch (widget.type) {
          case 'dashboard-widget': {
            const { data: templateDataInner, isError } = await dispatch(
              dashboardTemplateApi.endpoints.getDashboardTemplateDetails.initiate(
                widget.settings.dashboardTemplateOption.id,
              ),
            );
            if (!templateDataInner || isError || !templateDataInner.data) {
              return widget;
            }
            const { metricMeasurements: metricMeasurementsInner, widgets } =
              await transformTemplate(templateDataInner, assetMeasurementsListMap, settings);
            const templateDetailsInnerData = JSON.parse(templateDataInner.data) as {
              widget: DashboardState['widget'];
              topPanel: DashboardState['template']['topPanel'];
              chart: DashboardState['template']['chart'];
            };
            widget.settings.mode = 'dashboard';
            widget.settings.assetOrAssetType = settings.assetOption.id;
            widget.settings.assetOption = settings.assetOption;
            widget.settings.isChildWidget = true;
            widget.settings.dashboardTemplateData = {
              widgets,
              deleteWidgets: [],
              widgetLayout: templateDetailsInnerData.widget.widgetLayout,
              lastWidgetId: templateDetailsInnerData.widget.lastWidgetId,
            };
            const metricToMeasures: Record<
              string,
              {
                metricName: string;
                measurement: string;
              }
            > = metricMeasurementsInner;
            Object.keys(metricToMeasures).forEach((key) => {
              metricMeasurements[key] = metricToMeasures[key];
            });
            return widget;
          }
          case 'title': {
            widget.settings.isChildWidget = true;
            widget.settings.mode = 'dashboard';
            if (
              widget.settings.valueMode === 'measurement' &&
              widget.settings.selectedDbMeasureId
            ) {
              const measures = assetMeasurementsListMap.get(
                Number(widget.settings.selectedDbMeasureId),
              );
              if (measures && measures.length > 0) {
                widget.settings.measurementId = measures[0].id;
                widget.settings.assetId = Number(settings.assetOption.id);
                widget.settings.selectedDbMeasureId = measures[0].id.toString();
              } else {
                widget.settings.measurementId = undefined;
                widget.settings.assetId = Number(settings.assetOption.id);
                widget.settings.selectedDbMeasureId = '';
              }
            } else if (widget.settings.valueMode === 'fixed') {
              widget.settings.measurementId = undefined;
              widget.settings.assetId = Number(settings.assetOption.id);
              widget.settings.selectedDbMeasureId = '';
            }
            return widget;
          }
          case 'Weather': {
            if (settings.mode === 'template') {
              widget.settings.isChildWidget = true;
              return widget;
            }
            widget.settings.isChildWidget = true;
            widget.settings.assetOrAssetType = settings.assetOption.id;
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'stats': {
            if (settings.mode === 'template') {
              widget.settings.isChildWidget = true;
              return widget;
            }
            if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
              metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
            }
            const measures =
              assetMeasurementsListMap?.get(Number(widget.settings.selectedDbMeasureId)) !==
                undefined &&
              assetMeasurementsListMap?.get(Number(widget.settings.selectedDbMeasureId));
            if (measures) {
              widget.settings.selectedDbMeasureId = measures[0].id.toString();
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.id.toString()),
              };
            } else {
              widget.settings.selectedDbMeasureId = '';
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: [],
              };
            }
            widget.settings.mode = 'dashboard';
            widget.settings.isChildWidget = true;
            widget.settings.assetOrAssetType = settings.assetOption.id;
            metricMeasurements[widget.settings.selectedDbMeasureId] = {
              metricName: widget.settings.selectedDbMeasureId,
              measurement: measures ? measures[0].tag : 'N/A',
            };
            return widget;
          }
          case 'map': {
            if (settings.mode === 'template') {
              widget.settings.isChildWidget = true;
              return widget;
            }
            widget.settings.isChildWidget = true;
            widget.settings.assetOrAssetType = settings.assetOption.id;
            widget.settings.mode = 'dashboard';
            widget.settings.markers = widget.settings.markers.map((marker) => {
              if (marker.selectedTitles) {
                marker.selectedTitles.forEach((title) => {
                  if (!metricsToShow.includes(Number(title))) {
                    metricsToShow.push(Number(title));
                  }
                });
                const measures = marker.selectedTitles.flatMap((title) => {
                  const measureList = assetMeasurementsListMap.get(Number(title));
                  return measureList
                    ? measureList.map((measure) => ({
                        assetId: settings.assetOption.id.toString(),
                        measureId: measure.id.toString(),
                      }))
                    : [];
                });
                marker.assetMeasures = measures.length > 0 ? measures : [];
                marker.labelAndUnits = marker.selectedTitles.reduce((acc, title) => {
                  const measureList = assetMeasurementsListMap.get(Number(title));
                  if (measureList) {
                    measureList.forEach((measure) => {
                      acc[measure.id.toString()] = {
                        label: measure.tag,
                        unit: marker.labelAndUnits[measure.id.toString()]?.unit || '',
                        value: '',
                      };
                    });
                  }
                  return acc;
                }, {} as Record<string, { label: string; unit: string; value: string }>);
                marker.selectedTitles = measures.map((measure) => measure.measureId);
              } else {
                marker.assetMeasures = [];
                marker.labelAndUnits = {};
              }
              return marker;
            });
            return widget;
          }
          case 'table': {
            if (settings.mode === 'template') {
              widget.settings.isChildWidget = true;
              return widget;
            }
            widget.settings.selectedTitles.map((title) => {
              if (!metricsToShow.includes(Number(title))) {
                metricsToShow.push(Number(title));
              }
            });
            const measures = widget.settings.selectedTitles
              .map((title) => {
                const measurements: number[] = [];
                if (assetMeasurementsListMap.get(Number(title)) !== undefined) {
                  const measureIds =
                    assetMeasurementsListMap.get(Number(title))?.map((title) => title.id) ?? [];
                  measurements.push(...measureIds);
                }
                return measurements;
              })
              .flat();
            widget.settings.isChildWidget = true;
            widget.settings.assetOrAssetType = settings.assetOption.id;
            widget.settings.mode = 'dashboard';
            widget.settings.selectedTitles = measures.map((measure) => measure.toString());
            widget.settings.assetMeasure = [
              {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.toString()),
              },
            ];
            return widget;
          }
          case 'alert-widget': {
            if (settings.mode === 'template') {
              widget.settings.isChildWidget = true;
              return widget;
            }
            widget.settings.selectedTitles.map((title) => {
              if (!metricsToShow.includes(Number(title))) {
                metricsToShow.push(Number(title));
              }
            });
            const measures = widget.settings.selectedTitles
              .map((title) => {
                const measurements: number[] = [];
                if (assetMeasurementsListMap.get(Number(title)) !== undefined) {
                  const measureIds =
                    assetMeasurementsListMap.get(Number(title))?.map((title) => title.id) ?? [];
                  measurements.push(...measureIds);
                }
                return measurements;
              })
              .flat();
            widget.settings.selectedTitles = measures.map((measure) => measure.toString());
            widget.settings.assetMeasure = [
              {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.toString()),
              },
            ];
            widget.settings.isChildWidget = true;
            widget.settings.assetOrAssetType = settings.assetOption.id;
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'chart': {
            if (settings.mode === 'template') {
              widget.settings.settings.isChildWidget = true;
              return widget;
            }
            const chartType = widget.settings.chartType;
            widget.settings.settings.isChildWidget = true;
            widget.settings.settings.assetOrAssetType = settings.assetOption.id;
            widget.settings.settings.mode = 'dashboard';
            if (chartType === 'scatter' || chartType === 'bar') {
              widget.settings.settings.selectedTitles.map((title) => {
                if (!metricsToShow.includes(Number(title))) {
                  metricsToShow.push(Number(title));
                }
              });
              const measures = widget.settings.settings.selectedTitles
                .map((title) => {
                  const measurements: number[] = [];
                  if (assetMeasurementsListMap.get(Number(title)) !== undefined) {
                    const measureIds =
                      assetMeasurementsListMap.get(Number(title))?.map((title) => title.id) ?? [];
                    measurements.push(...measureIds);
                  }
                  return measurements;
                })
                .flat();
              widget.settings.settings.selectedTitles = measures.map((measure) =>
                measure.toString(),
              );
              const titles: number[] = [];
              if (widget.settings.settings.overrideGlobalBarColor) {
                widget.settings.settings.barColors = widget.settings.settings.barColors.map(
                  (bar) => {
                    const { color, measureId } = bar;
                    const measurement = assetMeasurementsListMap.get(Number(measureId));
                    const measureToMap: { id: number | null } = { id: null }; // Changed to `const`
                    if (measurement && measurement.length > 0) {
                      for (const measure of measurement) {
                        if (measures.includes(measure.id) && !titles.includes(measure.id)) {
                          titles.push(measure.id);
                          measureToMap.id = measure.id;
                        }
                      }
                    }
                    return {
                      color,
                      measureId: measureToMap.id?.toString() ?? '', // Use the valid id or fallback to an empty string
                    };
                  },
                );
              }
              widget.settings.settings.dbMeasureIdToName = Array.from(
                assetMeasurementsListMap.entries(),
              ).reduce((acc: Record<string, string>, [_, measurements]) => {
                measurements.forEach((measurement) => {
                  acc[measurement.id.toString()] = measurement.tag;
                });
                return acc;
              }, {});
              if (
                chartType === 'scatter' &&
                widget.settings.settings.showStacked &&
                widget.settings.settings.selectedSparkMeasure?.measureId &&
                widget.settings.settings.selectedSparkMeasure?.measureId !== ''
              ) {
                if (
                  widget.settings.settings.showStacked &&
                  widget.settings.settings.selectedSparkMeasure?.measureId &&
                  widget.settings.settings.selectedSparkMeasure?.measureId !== ''
                ) {
                  if (
                    !metricsToShow.includes(
                      Number(widget.settings.settings.selectedSparkMeasure?.measureId),
                    )
                  ) {
                    metricsToShow.push(
                      Number(widget.settings.settings.selectedSparkMeasure?.measureId),
                    );
                  }
                  const measures = assetMeasurementsListMap.get(
                    Number(widget.settings.settings.selectedSparkMeasure?.measureId),
                  );
                  widget.settings.settings.selectedSparkMeasure.assetId = assetId.toString();
                  widget.settings.settings.selectedSparkMeasure.measureId =
                    measures?.[0].id.toString() ?? '';
                } else {
                  widget.settings.settings.showSparkLine = false;
                  widget.settings.settings.selectedSparkMeasure.measureId = '';
                  widget.settings.settings.selectedSparkMeasure.assetId = '';
                }
              }
              widget.settings.settings.assetMeasure = [
                {
                  assetId: settings.assetOption.id.toString(),
                  measureId: measures.map((measure) => measure.toString()),
                },
              ];
              return widget;
            }
            if (chartType === 'bullet' || chartType === 'indicator' || chartType === 'heatmap') {
              if (!metricsToShow.includes(Number(widget.settings.settings.selectedDbMeasureId))) {
                metricsToShow.push(Number(widget.settings.settings.selectedDbMeasureId));
              }
              const measures =
                assetMeasurementsListMap.get(
                  Number(widget.settings.settings.selectedDbMeasureId),
                ) !== undefined &&
                assetMeasurementsListMap.get(Number(widget.settings.settings.selectedDbMeasureId));
              if (measures) {
                widget.settings.settings.selectedDbMeasureId = measures[0].id.toString();
                widget.settings.settings.assetMeasure = {
                  assetId: settings.assetOption.id.toString(),
                  measureId: measures.map((measure) => measure.id.toString()),
                };
              } else {
                widget.settings.settings.selectedDbMeasureId = '';
                widget.settings.settings.assetMeasure = {
                  assetId: settings.assetOption.id.toString(),
                  measureId: [],
                };
              }
            }
            if (chartType === 'sankey') {
              const metrics = widget.settings.settings.Label.filter(
                (metric) => metric.sourceFrom === 'Default',
              ).map((metric) => metric.sourceName);
              metrics.map((title) => {
                if (!metricsToShow.includes(Number(title))) {
                  metricsToShow.push(Number(title));
                }
              });
              widget.settings.settings.Label = widget.settings.settings.Label.map((label) => {
                if (label.sourceFrom === 'Default') {
                  let measureToMap: {
                    id: number | null;
                    tag: string | null;
                  } = {
                    id: null,
                    tag: null,
                  };
                  const measure =
                    assetMeasurementsListMap.get(Number(label.sourceAssetMeasure?.measureId)) ??
                    assetMeasurementsListMap.get(Number(label.sourceName));

                  if (measure && measure.length > 0) {
                    const selectedMeasure = measure.at(0);
                    measureToMap = {
                      id: selectedMeasure?.id ?? 0,
                      tag: selectedMeasure?.tag.toString() ?? '',
                    };
                  }
                  return {
                    ...label,
                    sourceLabel: formatMetricLabel(measureToMap?.tag ?? ''),
                    sourceName: measureToMap?.id?.toString() ?? '',
                    sourceAssetMeasure: {
                      ...label.sourceAssetMeasure,
                      assetId: settings.assetOption.id.toString(),
                      measureId: measureToMap?.id?.toString() ?? '',
                    },
                  };
                }
                return label;
              });
            }
            return widget;
          }
          case 'kpi-bar-chart': {
            if (settings.mode === 'template') {
              widget.settings.isChildWidget = true;
              return widget;
            }
            if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
              metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
            }
            const measures =
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId)) !==
                undefined &&
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId));
            if (measures) {
              widget.settings.selectedDbMeasureId = measures[0].id.toString();
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.id.toString()),
              };
            } else {
              widget.settings.selectedDbMeasureId = '';
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: [],
              };
            }
            const dbMeasureIdToName: Record<string, string> = {};
            assetMeasurementsListMap.forEach((measures) => {
              measures.forEach((measure) => {
                if (widget.settings.selectedDbMeasureId === measure.id.toString()) {
                  dbMeasureIdToName[measure.id] = measure.tag;
                }
              });
            });
            widget.settings.dbMeasureIdToName = dbMeasureIdToName;
            widget.settings.isChildWidget = true;
            widget.settings.assetOrAssetType = settings.assetOption.id;
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'kpi-percentage': {
            if (settings.mode === 'template') {
              widget.settings.isChildWidget = true;
              return widget;
            }
            if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
              metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
            }
            const measures =
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId)) !==
                undefined &&
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId));
            if (measures) {
              widget.settings.selectedDbMeasureId = measures[0].id.toString();
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.id.toString()),
              };
            } else {
              widget.settings.selectedDbMeasureId = '';
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: [],
              };
            }
            widget.settings.isChildWidget = true;
            widget.settings.assetOrAssetType = settings.assetOption.id;
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'kpi-table': {
            if (settings.mode === 'template') {
              widget.settings.isChildWidget = true;
              return widget;
            }
            widget.settings.selectedTitles.map((title) => {
              if (!metricsToShow.includes(Number(title))) {
                metricsToShow.push(Number(title));
              }
            });
            const measures = widget.settings.selectedTitles
              .map((title) => {
                const measurements: number[] = [];
                if (assetMeasurementsListMap.get(Number(title)) !== undefined) {
                  const measureIds =
                    assetMeasurementsListMap.get(Number(title))?.map((title) => title.id) ?? [];
                  measurements.push(...measureIds);
                }
                return measurements;
              })
              .flat();
            widget.settings.isChildWidget = true;
            widget.settings.assetOrAssetType = settings.assetOption.id;
            widget.settings.mode = 'dashboard';
            widget.settings.selectedTitles = measures.map((measure) => measure.toString());
            widget.settings.assetMeasure = [
              {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.toString()),
              },
            ];
            return widget;
          }
          case 'kpi-color-box': {
            if (settings.mode === 'template') {
              widget.settings.isChildWidget = true;
              return widget;
            }
            if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
              metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
            }
            const measures =
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId)) !==
                undefined &&
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId));
            if (measures) {
              widget.settings.selectedDbMeasureId = measures[0].id.toString();
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.id.toString()),
              };
            } else {
              widget.settings.selectedDbMeasureId = '';
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: [],
              };
            }
            widget.settings.isChildWidget = true;
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'image-stats':
          case 'kpi-value-indicator':
          case 'kpi-sparkline': {
            if (settings.mode === 'template') {
              widget.settings.isChildWidget = true;
              return widget;
            }
            if (!metricsToShow.includes(Number(widget.settings.selectedDbMeasureId))) {
              metricsToShow.push(Number(widget.settings.selectedDbMeasureId));
            }
            const measures =
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId)) !==
                undefined &&
              assetMeasurementsListMap.get(Number(widget.settings.selectedDbMeasureId));
            if (measures) {
              widget.settings.selectedDbMeasureId = measures[0].id.toString();
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.id.toString()),
              };
            } else {
              widget.settings.selectedDbMeasureId = '';
              widget.settings.assetMeasure = {
                assetId: settings.assetOption.id.toString(),
                measureId: [],
              };
            }
            widget.settings.isChildWidget = true;
            widget.settings.assetOrAssetType = settings.assetOption.id;
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'Diagram': {
            if (settings.mode === 'template') {
              widget.settings.isChildWidget = true;
              return widget;
            }
            Object.keys(widget.settings.elementIdVariabels).map((ids) => {
              const variables = widget.settings.elementIdVariabels[ids];
              return variables.map((vars) => {
                const title = vars.measurementId;
                if (!metricsToShow.includes(Number(title))) {
                  metricsToShow.push(Number(title));
                }
                return {
                  measurementId: vars.measurementId, // Returning the measurementId for each variable
                };
              });
            });

            widget.settings.elementIdVariabels = Object.keys(
              widget.settings.elementIdVariabels,
            ).reduce(
              (acc, ids) => {
                const variables = widget.settings.elementIdVariabels[ids];

                // Assign measurementId to each variable and push to the accumulator
                const updatedVariables = variables.map((vars) => {
                  const measures = assetMeasurementsListMap.get(Number(vars.measurementId));
                  const measurementId = measures ? measures[0].id.toString() : ''; // Take the first measure's id
                  return {
                    ...vars, // Spread the original variable properties
                    assetId: settings.assetOption.id.toString(),
                    measurementId: measurementId, // Add the measurementId to each variable
                  };
                });

                // Accumulate the updated variables under the corresponding ids
                acc[ids] = updatedVariables;
                return acc;
              },
              {} as Record<string, elementVariable[]>, // Initialize the accumulator with the correct type
            );
            widget.settings.elementVariable = widget.settings.elementVariable.map((variable) => {
              const measures = assetMeasurementsListMap.get(Number(variable.measurementId));
              const measurementId = measures ? measures[0].id.toString() : '';
              return {
                ...variable,
                assetId: settings.assetOption.id.toString(),
                measurementId: measurementId,
              };
            });
            widget.settings.isChildWidget = true;
            widget.settings.assetOrAssetType = settings.assetOption.id;
            widget.settings.mode = 'dashboard';
            return widget;
          }
          case 'image': {
            if (settings.mode === 'template') {
              widget.settings.isChildWidget = true;
              return widget;
            }
            widget.settings.selectedTitles.map((title) => {
              if (!metricsToShow.includes(Number(title))) {
                metricsToShow.push(Number(title));
              }
            });

            const measures = widget.settings.selectedTitles
              .map((title) => {
                const measurements: number[] = [];
                if (assetMeasurementsListMap.get(Number(title)) !== undefined) {
                  const measureIds =
                    assetMeasurementsListMap.get(Number(title))?.map((title) => title.id) ?? [];
                  measurements.push(...measureIds);
                }
                return measurements;
              })
              .flat();
            widget.settings.isChildWidget = true;
            widget.settings.assetOrAssetType = settings.assetOption.id;
            widget.settings.mode = 'dashboard';
            widget.settings.selectedTitles = measures.map((measure) => measure.toString());
            widget.settings.assetMeasure = [
              {
                assetId: settings.assetOption.id.toString(),
                measureId: measures.map((measure) => measure.toString()),
              },
            ];

            const measureIdImageText: Record<string, ImageTextDetails> = {};
            Array.from(assetMeasurementsListMap.keys()).forEach((metric) => {
              const imageToText = widget.settings.measureIdToImageTextDetails[metric.toString()];
              if (imageToText) {
                const measurement = assetMeasurementsListMap.get(metric)?.map((measures) => {
                  return measures;
                });
                measurement?.forEach((measure) => {
                  measureIdImageText[measure.id.toString()] = {
                    ...imageToText,
                    id: measure.id.toString(),
                    label: measure.tag,
                  };
                });
              }
            });
            const dbMeasureIdToName: Record<string, string> = {};
            assetMeasurementsListMap.forEach((measures) => {
              measures.forEach((measure) => {
                dbMeasureIdToName[measure.id] = measure.tag;
              });
            });
            widget.settings.dbMeasureIdToName = dbMeasureIdToName;
            widget.settings.measureIdToImageTextDetails = measureIdImageText;
            return widget;
          }

          case 'multi-plot': {
            const allSelectedTitles: string[] = [];

            widget.settings.subplots = widget.settings.subplots.map((subplot) => {
              const updatedAssetMeasures = subplot.assetMeasures.map((assetMeasure) => {
                if (!assetMeasure.selectedDbMeasureId) {
                  return assetMeasure;
                }

                const selectedDbId = Number(assetMeasure.selectedDbMeasureId);

                if (!metricsToShow.includes(selectedDbId)) {
                  metricsToShow.push(selectedDbId);
                }

                const measures = assetMeasurementsListMap.get(selectedDbId);

                if (measures && measures.length > 0) {
                  allSelectedTitles.push(...measures.map((m) => m.id.toString()));

                  return {
                    ...assetMeasure,
                    assetId: settings.assetOption.id.toString(),
                    measureId: measures.map((m) => m.id.toString()),
                  };
                } else {
                  return {
                    ...assetMeasure,
                    assetId: settings.assetOption.id.toString(),
                    measureId: [],
                  };
                }
              });

              return {
                ...subplot,
                assetMeasures: updatedAssetMeasures,
              };
            });

            widget.settings.dbMeasureIdToName = Array.from(
              assetMeasurementsListMap.entries(),
            ).reduce((acc: Record<string, string>, [_, measures]) => {
              measures.forEach((measure) => {
                acc[measure.id.toString()] = measure.tag;
              });
              return acc;
            }, {});

            widget.settings.selectedTitles = allSelectedTitles;

            widget.settings.mode = 'dashboard';
            widget.settings.isChildWidget = true;
            console.log('widget', widget);
            return widget;
          }
        }
        return widget;
      });
      return {
        widgets: (await Promise.all(widgets)) as Widget[],
        metricMeasurements,
      };
    }
    return { widgets: [], metricMeasurements: {} };
  };
  useEffect(() => {
    const loadTemplateAndTransform = async () => {
      if (templateData && templateData.data) {
        const templateDetailsData = JSON.parse(templateData.data) as {
          widget: DashboardState['widget'];
          topPanel: DashboardState['template']['topPanel'];
          chart: DashboardState['template']['chart'];
        };
        if (settings.mode === 'dashboard' && assetMeasurementsListMap.size > 0) {
          const { metricMeasurements, widgets } = await transformTemplate(
            templateData,
            assetMeasurementsListMap,
            settings,
          );
          dispatch(dashboardSlice.actions.setMetricMeasurements(metricMeasurements));
          setSetsyncedWidgets({
            widgets,
            widgetLayout: templateDetailsData.widget.widgetLayout,
          });
        } else {
          const { metricMeasurements, widgets } = await transformTemplate(
            templateData,
            assetMeasurementsListMap,
            settings,
          );
          dispatch(dashboardSlice.actions.setMetricMeasurements(metricMeasurements));
          setSetsyncedWidgets({
            widgets,
            widgetLayout: templateDetailsData.widget.widgetLayout,
          });
        }
      }
    };

    loadTemplateAndTransform();
  }, [templateData, assetMeasurementsListMap, dispatch, settings.mode]);
  return (
    <CommonWidgetContainer
      id={id}
      settings={settings}
      widgetName="dashboard-widget"
      widgetType="dashboard-widget"
      widgetContent={
        <>
          {settings.title && settings.title.isVisible && (
            <Typography
              variant="h4"
              component="div"
              sx={{ flexGrow: 1, mb: 3, mt: 3 }}
              style={{
                textAlign: 'center',
                fontSize: settings.title.fontSize + 'px',
                fontWeight: settings.title.fontWeight,
                color: settings.title.color,
              }}
            >
              {settings.title.value}
            </Typography>
          )}
          {loading ? (
            <Box
              sx={{
                position: 'absolute',
                height: '100%',
                width: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <CircularProgress />
            </Box>
          ) : settings.selectedTitles.length > 0 ? (
            <Box
              sx={{
                position: 'absolute',
                height: '100%',
                width: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Typography>Measurement(s) selected successfully.</Typography>
            </Box>
          ) : !settings.dashboardTemplateData ? (
            <Box
              sx={{
                position: 'absolute',
                height: '100%',
                width: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Typography>No measurement(s) mapping found</Typography>
            </Box>
          ) : (
            <Box>
              {settings.dashboardTemplateData && setsyncedWidgets && (
                <WidgetContainer
                  widgets={setsyncedWidgets.widgets}
                  widgetLayout={setsyncedWidgets.widgetLayout}
                  dashboardId={dashboardTemplateId}
                  isDashboardDetailsSuccess={true}
                  isError={false}
                  isLoading={setsyncedWidgets.widgets.length === 0}
                />
              )}
            </Box>
          )}
        </>
      }
      settingsDialog={DashboardWidgetSettingsDialog}
      setLoading={setLoading}
    />
  );
};

export default DashboardWidgetContainer;
