import AddIcon from '@mui/icons-material/Add';
import AddLocationIcon from '@mui/icons-material/AddLocation';
import AltRouteIcon from '@mui/icons-material/AltRoute';
import AnalyticsOutlinedIcon from '@mui/icons-material/AnalyticsOutlined';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import AssessmentIcon from '@mui/icons-material/Assessment';
import BentoIcon from '@mui/icons-material/Bento';
import CandlestickChartIcon from '@mui/icons-material/CandlestickChart';
import CloseIcon from '@mui/icons-material/Close';
import CrisisAlertIcon from '@mui/icons-material/CrisisAlert';
import DashboardIcon from '@mui/icons-material/Dashboard';
import FiveMpIcon from '@mui/icons-material/FiveMp';
import FlashOnOutlinedIcon from '@mui/icons-material/FlashOnOutlined';
import HvacIcon from '@mui/icons-material/Hvac';
import InsightsIcon from '@mui/icons-material/Insights';
import LabelIcon from '@mui/icons-material/Label';
import MultilineChartIcon from '@mui/icons-material/MultilineChart';
import PanoramaIcon from '@mui/icons-material/Panorama';
import PercentIcon from '@mui/icons-material/Percent';
import SpeedIcon from '@mui/icons-material/Speed';
import TableRowsIcon from '@mui/icons-material/TableRows';
import TableViewIcon from '@mui/icons-material/TableView';
import {
  Box,
  IconButton,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  useMediaQuery,
} from '@mui/material';
import { Fragment, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHasPowerUserAccess } from '~/hooks/useHasPowerUserAccess';
import { Role, useRolePermission } from '~/hooks/useRolePermission';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getMainPanel } from '~/redux/selectors/dashboardSelectors';
import { getSelectedNodeIds } from '~/redux/selectors/treeSelectors';

const widgets = [
  { icon: <PanoramaIcon />, name: 'Image', widget: 'image', checkVisibility: true },
  // {
  //   icon: <TimelineIcon />,
  //   name: 'Realtime',
  //   widget: 'real-time',
  //   checkVisibility: true,
  // },
  { icon: <LabelIcon />, name: 'Value', widget: 'title', checkVisibility: false },
  { icon: <AssessmentIcon />, name: 'Stats', widget: 'stats', checkVisibility: false },
  { icon: <TableRowsIcon />, name: 'Table', widget: 'table', checkVisibility: false },
  { icon: <AddLocationIcon />, name: 'Map', widget: 'map', checkVisibility: false },
  { icon: <AltRouteIcon />, name: 'Diagram', widget: 'Diagram', checkVisibility: false },
  {
    icon: <CrisisAlertIcon />,
    name: 'Alerts',
    widget: 'alert-widget',
    checkVisibility: false,
  },
  {
    icon: <DashboardIcon />,
    name: 'Dashboard',
    widget: 'dashboard-widget',
    checkVisibility: false,
  },
  {
    icon: <MultilineChartIcon />,
    name: 'Multi Plot',
    widget: 'multi-plot',
    checkVisibility: false,
  },
  {
    icon: <HvacIcon />,
    name: 'Heatmap',
    widget: 'chart-heatmap',
    checkVisibility: false,
  },
  {
    icon: <SpeedIcon />,
    name: 'Gauge',
    widget: 'chart-Gauge',
    checkVisibility: false,
  },
  {
    icon: <InsightsIcon />,
    name: 'Chart',
    widget: 'chart',
    checkVisibility: false,
    charts: [
      {
        name: 'Chart',
        type: 'Scatter',
        title: 'Trend',
        checkVisibility: false,
      },
      {
        name: 'Chart',
        type: 'Bar',
        title: 'Bar',
        checkVisibility: false,
      },
      {
        name: 'Chart',
        type: 'Bullet',
        title: 'Bullet',
        checkVisibility: false,
      },
      {
        name: 'Chart',
        type: 'Sankey',
        title: 'Sankey',
        checkVisibility: false,
      },
    ],
  },
  {
    name: 'KPI',
    icon: <FlashOnOutlinedIcon />,
    widget: 'kpi',
    checkVisibility: false,
    kpis: [
      {
        icon: <AnalyticsOutlinedIcon />,
        name: 'KPI Bar chart',
        widget: 'kpi-bar-chart',
        checkVisibility: false,
      },
      // {
      //   icon: <DonutLargeIcon />,
      //   name: 'Circle widget',
      //   widget: 'kpi-value-indicator',
      //   checkVisibility: false,
      // },
      {
        icon: <BentoIcon />,
        name: 'KPI Trend',
        widget: 'kpi-color-box',
        checkVisibility: false,
      },
      {
        icon: <CandlestickChartIcon />,
        name: 'KPI Sparkline',
        widget: 'kpi-sparkline',
        checkVisibility: false,
      },
      {
        icon: <PercentIcon />,
        name: 'KPI Percentage',
        widget: 'kpi-percentage',
        checkVisibility: false,
      },
      {
        icon: <TableViewIcon />,
        name: 'KPI Table',
        widget: 'kpi-table',
        checkVisibility: false,
      },
      {
        icon: <FiveMpIcon />,
        name: 'KPI Current',
        widget: 'image-stats',
        checkVisibility: false,
      },
    ],
  },
  // {
  //   icon: <CloudIcon />,
  //   name: 'Weather',
  //   widget: 'Weather',
  //   checkVisibility: false,
  // },
];
const DashboardWidgetsIcons = () => {
  const [open, setOpen] = useState(false);
  const hasPowerUserAccess = useHasPowerUserAccess();
  const mainPanel = useSelector(getMainPanel);
  const activeCustomer = useSelector(getActiveCustomer);
  const selectedNodeIds = useSelector(getSelectedNodeIds);
  const { hasDashboardPermission } = useRolePermission();

  // Media queries to detect device size
  const isSmallScreen = useMediaQuery('(max-width: 768px)');
  const isShortScreen = useMediaQuery('(max-height: 500px)');

  // Detect touch-capable devices (mobile/tablets)
  const isTouchDevice =
    typeof window !== 'undefined' && ('ontouchstart' in window || navigator.maxTouchPoints > 0);

  // Final mobile check (portrait or landscape + touch-capable)
  const isMobileDevice = (isSmallScreen || isShortScreen) && isTouchDevice;

  const [showChildren, setShowChildren] = useState<{
    widget: string;
    children: boolean;
  }>({
    widget: '',
    children: false,
  });

  return (
    <>
      {hasDashboardPermission('widget.create', Role.POWER_USER) ? (
        <>
          {mainPanel === 'chart' &&
          (activeCustomer !== null || selectedNodeIds.length > 1) &&
          hasPowerUserAccess ? (
            <>
              <IconButton
                size="large"
                id={'widgets-icon'}
                sx={{
                  p: 1.5,
                  position: 'absolute',
                  right: 20,
                  bottom: 20,
                  zIndex: 100,
                  background: '#000000',
                  '&:hover': {
                    background: '#000000',
                  },
                  boxShadow: 3,
                  '& .MuiSvgIcon-root': {
                    color: '#ffffff',
                    transform: !open ? 'rotate(90deg)' : 'rotate(-90deg)',
                  },
                  display: isMobileDevice ? 'none' : 'flex',
                }}
                onClick={() => setOpen(!open)}
              >
                {open ? <CloseIcon /> : <AddIcon />}
              </IconButton>

              <Box
                sx={{
                  position: 'absolute',
                  right: 30,
                  bottom: 80,
                  zIndex: 100,
                  display: open ? 'flex' : 'none',
                  flexDirection: 'column',
                  gap: 0.5,
                  boxShadow: 3,
                  width: 240,
                  borderRadius: 2,
                  background: (theme) => theme.palette.background.paper,
                  // transition: 'all 0.3s ease-in-out', // Added transition property
                  pointerEvents: open ? 'all' : 'none',
                  transition: 'top 0s linear 0.2s',
                }}
              >
                {widgets.map((widget, index) => (
                  <Fragment key={index}>
                    <ListItemButton
                      draggable={widget.widget !== 'chart' && widget.widget !== 'kpi'}
                      className="droppable-element"
                      id={widget.widget}
                      sx={{
                        pt: 0.5,
                        pb: 0.5,
                      }}
                      onDragStart={(e) => e.dataTransfer.setData('text/plain', widget.widget)}
                      onMouseEnter={() =>
                        setShowChildren({
                          widget: widget.widget,
                          children: true,
                        })
                      }
                      onMouseLeave={() =>
                        setShowChildren({
                          widget: widget.widget,
                          children: false,
                        })
                      }
                    >
                      <ListItemIcon>{widget.icon}</ListItemIcon>
                      <ListItemText primary={widget.name} />
                      {widget.charts !== undefined && widget.charts ? (
                        <ListItemIcon sx={{ justifyContent: 'end' }}>
                          <IconButton size="small" disableRipple>
                            <ArrowForwardIosIcon fontSize="inherit" />
                          </IconButton>
                        </ListItemIcon>
                      ) : null}
                      {widget.kpis !== undefined && widget.kpis ? (
                        <ListItemIcon sx={{ justifyContent: 'end' }}>
                          <IconButton size="small" disableRipple>
                            <ArrowForwardIosIcon fontSize="inherit" />
                          </IconButton>
                        </ListItemIcon>
                      ) : null}
                    </ListItemButton>
                    <>
                      {showChildren.widget === 'chart' &&
                        showChildren.children &&
                        widget.charts && (
                          <Box
                            onMouseEnter={() =>
                              setShowChildren({
                                widget: widget.widget,
                                children: true,
                              })
                            }
                            onMouseLeave={() =>
                              setShowChildren({
                                widget: widget.widget,
                                children: false,
                              })
                            }
                            sx={{
                              position: 'absolute',
                              right: 240,
                              bottom: 0,
                              zIndex: 100,
                              display: 'flex',
                              flexDirection: 'column',
                              gap: 0.5,
                              boxShadow: 3,
                              width: 240,
                              borderRadius: 2,
                              background: (theme) => theme.palette.background.paper,
                              transition: 'all 0.3s ease-in-out', // Added transition property
                            }}
                          >
                            {widget.charts.map((child, ind) => (
                              <ListItemButton
                                key={ind}
                                draggable={true}
                                sx={{
                                  pt: 0.5,
                                  pb: 0.5,
                                }}
                                className="droppable-element"
                                onDragStart={(e) =>
                                  e.dataTransfer.setData(
                                    'text/plain',
                                    widget.widget + '-' + child.type,
                                  )
                                }
                              >
                                <ListItemText primary={child.title} />
                              </ListItemButton>
                            ))}
                          </Box>
                        )}
                      {showChildren.widget === 'kpi' && showChildren.children && widget.kpis && (
                        <Box
                          onMouseEnter={() =>
                            setShowChildren({
                              widget: widget.widget,
                              children: true,
                            })
                          }
                          onMouseLeave={() =>
                            setShowChildren({
                              widget: widget.widget,
                              children: false,
                            })
                          }
                          sx={{
                            position: 'absolute',
                            right: 240,
                            bottom: 0,
                            zIndex: 100,
                            display: 'flex',
                            flexDirection: 'column',
                            gap: 0.5,
                            boxShadow: 3,
                            width: 240,
                            borderRadius: 2,
                            background: (theme) => theme.palette.background.paper,
                            transition: 'all 0.3s ease-in-out', // Added transition property
                          }}
                        >
                          {widget.kpis.map((child, inds) => (
                            <ListItemButton
                              key={inds}
                              draggable={true}
                              sx={{
                                pt: 0.5,
                                pb: 0.5,
                              }}
                              className="droppable-element"
                              onDragStart={(e) =>
                                e.dataTransfer.setData('text/plain', child.widget)
                              }
                            >
                              <ListItemText primary={child.name} />
                            </ListItemButton>
                          ))}
                        </Box>
                      )}
                    </>
                  </Fragment>
                ))}
              </Box>
            </>
          ) : null}
        </>
      ) : null}
    </>
  );
};

export default DashboardWidgetsIcons;
