import { yupResolver } from '@hookform/resolvers/yup';
import {
  <PERSON>ert,
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  InputAdornment,
  Tooltip,
  Typography,
} from '@mui/material';
import { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import {
  AssetMeasurementSchemaEdit,
  Datasource,
  DataType,
  EditAssetMeasurementForm,
  MeasurementLocation,
  MeasurementType,
  UnitOfMeasure,
  ValueType,
} from '~/measurements/domain/types';
import { getMainPanel } from '~/redux/selectors/dashboardSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { ControlledAutocomplete } from '~/shared/forms/components/ControlledAutocomplete';
import { ControlledTextField } from '~/shared/forms/components/ControlledTextField';
import { AlertMessage } from '~/shared/forms/types';
import { Asset } from '~/types/asset';
import MeasureFactor from './MeasureFactor/MeasureFactor';
import EditCalcEngine from './CalcEngine/EditCalcEngine';
import MeasureTimeVaringFactor from './MeasureFactor/MeasureTimeVaringFactor';
import { InfoOutlined } from '@mui/icons-material';

type EditMeasurementFormProps = {
  loading: boolean;
  onValidSubmit: (data: EditAssetMeasurementForm) => Promise<unknown>;
  defaultValues: EditAssetMeasurementForm | undefined;
  measurementTypeList: MeasurementType[];
  dataTypeList: DataType[];
  valueTypeList: ValueType[];
  unitOfMeasureList: UnitOfMeasure[];
  locationList: MeasurementLocation[];
  datasourceList: Datasource[];
  alertMessage: AlertMessage | undefined;
  onMeasurementTypeIdChange: (measurementTypeId: number) => unknown;
  selectedMeasurementTypeId: number;
  calcEngine: boolean;
  setCalcEngine: Dispatch<SetStateAction<boolean>>;
  factor: boolean;
  setFactor: Dispatch<SetStateAction<boolean>>;
  parentAsset: Asset;
  measurementId: string[];
  assetPath: string;
  writeback: boolean;
};
const mapListToOptions = (
  typeList: { id: number; name: string }[],
): { id: string; label: string }[] => {
  return typeList.map((type) => ({ id: type.id.toString(), label: type.name }));
};
const EditMeasurementForm = ({
  loading,
  defaultValues: defaultValue,
  onValidSubmit,
  alertMessage,
  dataTypeList,
  datasourceList,
  locationList,
  measurementTypeList,
  onMeasurementTypeIdChange,
  unitOfMeasureList,
  valueTypeList,
  calcEngine,
  setCalcEngine,
  factor,
  setFactor,
  parentAsset,
  measurementId,
  assetPath,
  writeback,
}: EditMeasurementFormProps) => {
  const dispatch = useDispatch();
  const { control, watch, handleSubmit, reset } = useForm<EditAssetMeasurementForm>({
    defaultValues: defaultValue,
    resolver: yupResolver(AssetMeasurementSchemaEdit),
  });
  useEffect(() => {
    reset(defaultValue);
    reset({ ...defaultValue, tag: defaultValue?.tag.split('\\').pop() });
  }, [defaultValue]);
  const selectedMeasurementTypeId = watch('type_id');
  const datasource_id = watch('datasource_id');
  const [data, setData] = useState<EditAssetMeasurementForm | undefined>(undefined);
  useEffect(() => {
    onMeasurementTypeIdChange(selectedMeasurementTypeId);
  }, [onMeasurementTypeIdChange, selectedMeasurementTypeId]);
  const mainPanel = useSelector(getMainPanel);
  const measurementTypeOptions = useMemo(
    () => mapListToOptions(measurementTypeList),
    [measurementTypeList],
  );
  const dataTypeOptions = useMemo(() => mapListToOptions(dataTypeList), [dataTypeList]);
  const valueTypeOptions = useMemo(() => mapListToOptions(valueTypeList), [valueTypeList]);
  const unitOfMeasureOptions = useMemo(
    () => mapListToOptions(unitOfMeasureList),
    [unitOfMeasureList],
  );
  const locationOptions = useMemo(() => mapListToOptions(locationList), [locationList]);
  const datasourceOptions = useMemo(() => mapListToOptions(datasourceList), [datasourceList]);
  return (
    <>
      {!factor && !calcEngine ? (
        <form
          onSubmit={handleSubmit(async (data) => {
            try {
              if (
                datasource_id &&
                datasourceList?.find((item) => item.name === 'Calculation')?.id.toString() ===
                  datasource_id.toString() &&
                mainPanel === 'editMeasure'
              ) {
                setCalcEngine(true);
                setData(data);
                return;
              }
              if (
                datasource_id &&
                datasourceList?.find((item) => item.name === 'TimeVaryingFactor')?.id.toString() ===
                  datasource_id.toString() &&
                mainPanel === 'editMeasure'
              ) {
                setFactor(true);
                setData(data);
                return;
              }
              await onValidSubmit(data as EditAssetMeasurementForm);
              reset();
            } catch (err) {}
          })}
          noValidate
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <ControlledTextField
              control={control}
              fieldName="tag"
              label="Tag"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start" disablePointerEvents disableTypography>{`${
                    assetPath === '' ? parentAsset.tag : assetPath
                  }:`}</InputAdornment>
                ),
              }}
              loading={loading}
              required
            />
            <ControlledTextField
              control={control}
              fieldName="description"
              label="Description"
              loading={loading}
              multiline
            />
            <ControlledAutocomplete
              control={control}
              fieldName="type_id"
              label="Select measurement type"
              loading={loading}
              options={measurementTypeOptions}
              required
            />

            <ControlledAutocomplete
              control={control}
              fieldName="data_type_id"
              label="Select data type"
              loading={loading}
              options={dataTypeOptions}
              required
              disabled={true}
            />
            <ControlledAutocomplete
              control={control}
              fieldName="value_type_id"
              label="Select value type"
              loading={loading}
              options={valueTypeOptions}
              required
            />

            <ControlledAutocomplete
              control={control}
              fieldName="unit_of_measure_id"
              label={
                selectedMeasurementTypeId ? 'Select unit of measure' : 'Choose metric type first'
              }
              loading={loading || !selectedMeasurementTypeId}
              options={unitOfMeasureOptions}
            />
            <ControlledAutocomplete
              control={control}
              fieldName="datasource_id"
              label="Select datasource"
              loading={true}
              options={datasourceOptions}
            />
            <ControlledAutocomplete
              control={control}
              fieldName="location_id"
              label="Select location"
              loading={loading}
              options={locationOptions}
            />
            <ControlledTextField
              control={control}
              fieldName="meter_factor"
              label="Meter factor"
              loading={loading}
              type="number"
            />

            <Controller
              control={control}
              name="writeback"
              defaultValue={defaultValue?.writeback ?? false}
              render={({ field: { onChange, value, ...rest } }) => {
                const shouldShowWriteback = datasource_id === null;
                return (
                  <>
                    {shouldShowWriteback && (
                      <Box sx={{ width: '100%' }}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              {...rest}
                              checked={Boolean(value)}
                              disabled
                              onChange={(e) => {
                                const isChecked = e.target.checked;
                                onChange(isChecked);
                              }}
                            />
                          }
                          label={
                            <Box display="flex" alignItems="center">
                              Writeback
                              <Tooltip title="This action cannot be undone." placement="top">
                                <InfoOutlined sx={{ ml: 0.5, color: 'action.active' }} />
                              </Tooltip>
                            </Box>
                          }
                        />
                      </Box>
                    )}
                  </>
                );
              }}
            />

            <Button
              type="submit"
              variant="contained"
              size="large"
              sx={{ mt: 2, width: 200 }}
              disabled={loading}
            >
              {datasourceList?.find((item) => item.name === 'Calculation')?.id.toString() ===
                datasource_id?.toString() ||
              datasourceList?.find((item) => item.name === 'TimeVaryingFactor')?.id.toString() ===
                datasource_id?.toString()
                ? 'Next'
                : 'Submit'}
            </Button>
            {alertMessage && (
              <Alert severity={alertMessage.severity} sx={{ mt: 3, mb: 3 }}>
                <Typography
                  sx={{ cursor: 'pointer' }}
                  onClick={() => {
                    dispatch(dashboardSlice.actions.selectMainPanel('MeasureDetails'));
                  }}
                >
                  {alertMessage.message}
                </Typography>
              </Alert>
            )}
          </Box>
        </form>
      ) : (
        <>
          {data && factor ? (
            <>
              <Box>
                {/* <MeasureFactor
                  measure={data ?? {}}
                  parentAsset={parentAsset}
                  isEdit={true}
                  assetPath={assetPath === '' ? parentAsset.tag : assetPath}
                /> */}
                <MeasureTimeVaringFactor
                  measure={data ?? {}}
                  parentAsset={parentAsset}
                  isEditFlow={true}
                  assetPath={assetPath === '' ? parentAsset.tag : assetPath}
                  setFactor={setFactor}
                />
              </Box>
            </>
          ) : null}
          {data && calcEngine ? (
            <>
              <Box>
                <EditCalcEngine
                  setCalcEngine={setCalcEngine}
                  measure={data ?? {}}
                  parentAsset={parentAsset}
                  measurementId={measurementId}
                  assetPath={assetPath === '' ? parentAsset.tag : assetPath}
                  isWriteback={writeback}
                />
              </Box>
            </>
          ) : null}
        </>
      )}
    </>
  );
};
export default EditMeasurementForm;
