import { Box, Button, TextField, Typography } from '@mui/material';
import { MutationTrigger } from '@reduxjs/toolkit/dist/query/react/buildHooks';
import { FetchArgs } from '@reduxjs/toolkit/query';
import { BaseQueryFn, FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { MutationDefinition } from '@reduxjs/toolkit/query';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import CustomDialog from '~/components/common/CustomDialog';
import Loader from '~/components/common/Loader';
import { useGetDashboardByCustomerIdQuery } from '~/redux/api/dashboardApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getIsUserLoggedIn, getUserToken } from '~/redux/selectors/dashboardSelectors';
import { getUserPreferences } from '~/redux/selectors/userPreferences';
import { CreateDashboardParam, DashboardParams, DashboardState } from '~/types/dashboard';

interface ImportDashboardProps {
  open: boolean;
  setShowImport: Dispatch<SetStateAction<boolean>>;
  createDashboard: MutationTrigger<
    MutationDefinition<
      CreateDashboardParam,
      BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError>,
      'me' | 'auth' | 'User' | 'Dashboard' | 'Customer',
      DashboardParams,
      'authApi'
    >
  >;
}

const ImportDashboard = ({ open, setShowImport, createDashboard }: ImportDashboardProps) => {
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [fileContent, setFileContent] = useState<any | null>(null);
  const [dashboardTitle, setDashboardTitle] = useState<string>('');
  const [dashboardState, setDashboardState] = useState<DashboardState | null>(null);
  const [newDashboardTitle, setNewDashboardTitle] = useState<string>('');
  const [isDuplicateTitle, setIsDuplicateTitle] = useState<boolean>(false);
  const [isInvalidData, setIsInvalidData] = useState<boolean>(false);

  const userToken = useSelector(getUserToken);
  const userDetails = useSelector(getIsUserLoggedIn);
  const userPreferences = useSelector(getUserPreferences);
  const activeCustomer = useSelector(getActiveCustomer);

  const { data: dashboardList, isLoading: isLoadingDashboards } = useGetDashboardByCustomerIdQuery(
    {
      customerId: activeCustomer?.id ?? 0,
      search: null,
    },
    {
      skip: !activeCustomer?.id,
      refetchOnMountOrArgChange: true,
    },
  );

  useEffect(() => {
    if (!open) {
      setFile(null);
      setError(null);
      setFileContent(null);
      setDashboardTitle('');
      setNewDashboardTitle('');
      setIsDuplicateTitle(false);
      setIsInvalidData(false);
    }
  }, [open]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0] || null;
    if (selectedFile) {
      if (selectedFile.type === 'application/json') {
        setFile(selectedFile);
        setError(null);

        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const content = JSON.parse(e.target?.result as string);
            const importedTitle = content?.entityInfo?.entityData?.title;
            const importedData = content?.entityInfo?.entityData?.data;

            if (!importedTitle || !importedData) {
              setError('Invalid JSON structure: Missing required data.');
              setIsInvalidData(true);
              return;
            }

            setFileContent(content?.entityInfo?.entityData);
            setDashboardTitle(importedTitle);
            setNewDashboardTitle(importedTitle); // Default to imported title
            setDashboardState(importedData);
            setIsInvalidData(false);

            // Check for duplicate title
            if (dashboardList?.items.some((dashboard) => dashboard.title === importedTitle)) {
              setIsDuplicateTitle(true);
            } else {
              setIsDuplicateTitle(false);
            }
          } catch (err) {
            setError('Invalid JSON format.');
            setFile(null);
            setFileContent(null);
            setIsInvalidData(true);
          }
        };
        reader.readAsText(selectedFile);
      } else {
        setError('Only JSON files are allowed.');
        setFile(null);
        setFileContent(null);
      }
    }
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const updatedTitle = e.target.value;
    setNewDashboardTitle(updatedTitle);

    // Update duplicate check when user modifies the title
    setIsDuplicateTitle(
      dashboardList?.items.some((dashboard) => dashboard.title === updatedTitle.trim()) ?? false,
    );
  };

  const handleUpload = () => {
    if (file && !isInvalidData && dashboardState) {
      const finalTitle = newDashboardTitle || 'Untitled Dashboard';
      const finalState: DashboardState = {
        ...dashboardState,
        dashboardTitle: finalTitle,
        isDirty: false,
        userToken,
        userDetails,
        userPreferences,
      };

      createDashboard({
        customerId: activeCustomer?.id ?? 0,
        data: JSON.stringify(finalState, null, 2),
        description: '',
        title: finalTitle,
      });
    }
  };

  return (
    <CustomDialog
      open={open}
      title={<>Import Dashboard</>}
      onClose={() => {
        setShowImport(false);
        setFile(null);
        setError(null);
        setFileContent(null);
        setDashboardTitle('');
        setNewDashboardTitle('');
        setIsDuplicateTitle(false);
        setIsInvalidData(false);
      }}
      maxWidth="md"
      showCloseOnTop
      content={
        <>
          {isLoadingDashboards ? (
            <Loader />
          ) : (
            <>
              <input type="file" accept=".json" onChange={handleFileChange} />
              {error && <Typography color="error">{error}</Typography>}
              {file && <Typography>Selected File: {file.name}</Typography>}
              {fileContent && (
                <Box>
                  <Typography variant="h6">Imported Dashboard Title: {dashboardTitle}</Typography>
                  <TextField
                    sx={{ mt: 2 }}
                    fullWidth
                    error={isDuplicateTitle}
                    helperText={
                      isDuplicateTitle
                        ? 'Dashboard title already exists. Please enter a new title.'
                        : 'You can override the dashboard title if needed.'
                    }
                    label="Dashboard Title"
                    value={newDashboardTitle}
                    onChange={handleTitleChange}
                  />
                </Box>
              )}
            </>
          )}
        </>
      }
      dialogActions={
        <>
          <Button onClick={() => setShowImport(false)}>Cancel</Button>
          <Button
            onClick={handleUpload}
            disabled={!file || isInvalidData || !newDashboardTitle.trim() || isDuplicateTitle}
            variant="contained"
            color="primary"
          >
            Upload
          </Button>
        </>
      }
    />
  );
};

export default ImportDashboard;
