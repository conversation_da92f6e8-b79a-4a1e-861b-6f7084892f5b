import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { Box, IconButton, Tab, Tabs, Tooltip, Typography, useMediaQuery } from '@mui/material';
import dynamic from 'next/dynamic';
import { useDispatch, useSelector } from 'react-redux';
import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import Error from '~/components/common/Error/Error';
import Loader from '~/components/common/Loader';
import NoMeasureSelected from '~/components/common/NoMeasureSelected';
import { useFetchKPIBar } from '~/hooks/useFetchKPIBar';
import { getMetricsIdToName, getZoomEnabled } from '~/redux/selectors/dashboardSelectors';
import { getDateTimeFormat, getThousandSeparator } from '~/redux/selectors/userPreferences';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { KPIBarChart, KPIBarChartSamplePeriod, Widget } from '~/types/widgets';
import {
  formatDate,
  formatMetricLabel,
  formatNumber,
  hasNoMeasureSelected,
  hexToRgbA,
  roundNumber,
} from '~/utils/utils';
import KPIBarChartSettings from './KPIBarChartSettings';
const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });
type KPIBarChartContainerProps = {
  id: string;
  settings: KPIBarChart;
};

const KPIBarChartContainer = ({ id, settings }: KPIBarChartContainerProps) => {
  const metricsIdToName = useSelector(getMetricsIdToName);
  const enabledZoom = useSelector(getZoomEnabled);
  const thousandSeparator = useSelector(getThousandSeparator);

  const defaultTitle =
    settings.mode === 'dashboard'
      ? formatMetricLabel(
          Object.keys(settings?.dbMeasureIdToName ?? {}).length > 0 &&
            settings.assetMeasure.measureId.length > 0
            ? settings.dbMeasureIdToName[settings.assetMeasure.measureId[0]] ?? 'Unknown Measure'
            : 'Unknown Measure',
        )
      : settings.mode === 'template' && settings.selectedDbMeasureId !== ''
      ? metricsIdToName[settings.selectedDbMeasureId] ?? 'Unknown Measure'
      : settings.title.value;
  const dateFormats = useSelector(getDateTimeFormat);
  const dispatch = useDispatch();
  const mobile = useMediaQuery('@media (max-width:600px)');
  const { isLoading, chartData, layout, avg, percentageChange, unitOfMeasure, dates, isError } =
    useFetchKPIBar({
      settings,
    });

  const setSamplePeriod = (selectedSamplePeriod: KPIBarChartSamplePeriod) => {
    dispatch(
      dashboardSlice.actions.setCurrentWidgetSettings({
        id: id,
        type: 'kpi-bar-chart',
        settings: { ...settings, selectedSamplePeriod },
      } as Widget),
    );
  };
  return (
    <CommonWidgetContainer
      id={id}
      settings={settings}
      widgetContent={
        <Box height={'100%'}>
          {hasNoMeasureSelected(settings) ? (
            <NoMeasureSelected />
          ) : (
            <>
              {isError ? (
                <Error />
              ) : (
                <>
                  <Box>
                    {settings.title.isVisible && (
                      <Box
                        sx={{
                          width: '100%',
                          display: 'flex',
                          justifyContent: 'center',
                          marginY: '10px',
                        }}
                      >
                        <Typography
                          variant="h5"
                          sx={{
                            fontSize: settings.title.isVisible
                              ? settings.title.fontSize + 'px'
                              : undefined,
                            fontWeight: settings.title.isVisible
                              ? settings.title.fontWeight
                              : undefined,
                            color: settings.title.isVisible ? settings.title.color : undefined,
                          }}
                          component="div"
                        >
                          {settings.title.isVisible ? settings.title.value : defaultTitle}
                        </Typography>
                      </Box>
                    )}
                    <Box>
                      <Tabs
                        value={settings.selectedSamplePeriod}
                        aria-label="KPI Bar Chart Tabs"
                        variant="scrollable"
                        scrollButtons="auto"
                        allowScrollButtonsMobile
                        onChange={(event, newValue) => {
                          setSamplePeriod(newValue);
                        }}
                        sx={{
                          mt: !settings.title.isVisible ? 4 : undefined,
                          minHeight: 40,

                          '& .MuiTabs-indicator': {
                            backgroundColor: (theme) => theme.palette.primary.main,
                          },
                          '& .Mui-selected': {
                            backgroundColor: (theme) =>
                              `rgba(${hexToRgbA(theme.palette.primary.main)}, 0.08)`,
                            color: (theme) => theme.palette.primary.main,
                            borderRadius: '1px',
                          },
                          '& .MuiTab-root': {
                            flex: 1,
                            borderWidth: 1,
                            borderStyle: 'solid',
                            borderColor: (theme) => theme.palette.divider,
                            padding: 0,
                            paddingLeft: 1,
                            paddingRight: 1,
                            height: 40,
                            minHeight: 40,
                            gap: 1,
                            textTransform: 'none',
                          },
                          '& .MuiButtonBase-root': {
                            padding: '8px 20px',
                          },
                          '& .MuiTabs-scrollButtons.Mui-disabled': {
                            display: 'none',
                          },
                        }}
                      >
                        <Tab label="D" value={'D'} />
                        <Tab label="W" value={'W'} />
                        <Tab label="M" value={'M'} />
                        <Tab label="6M" value={'6M'} />
                        <Tab label="Y" value={'Y'} />
                      </Tabs>
                      {!isLoading && (
                        <Box display="flex" justifyContent={'space-around'} pt={2}>
                          <Box pl={3} pr={3}>
                            <Typography>
                              {settings.selectedSamplePeriod === 'D' && 'Daily Avg.'}
                            </Typography>
                            <Typography>
                              {settings.selectedSamplePeriod === 'W' && 'Weekly Avg.'}
                            </Typography>
                            <Typography>
                              {settings.selectedSamplePeriod === 'M' && 'Monthly Avg.'}
                            </Typography>
                            <Typography>
                              {settings.selectedSamplePeriod === '6M' && 'Quarterly Avg.'}
                            </Typography>
                            <Typography>
                              {settings.selectedSamplePeriod === 'Y' && 'Yearly Avg.'}
                            </Typography>
                            <Typography component="b" sx={{ fontWeight: 'bold' }}>
                              {thousandSeparator ? formatNumber(avg) : roundNumber(Number(avg))}{' '}
                              {avg !== '-' ? unitOfMeasure : '-'}
                            </Typography>
                            {dates.length > 1 && (
                              <Typography fontSize={'0.8rem'}>
                                {formatDate(new Date(dates[0].start), dateFormats)} -
                                {formatDate(new Date(dates[0].end), dateFormats)}
                              </Typography>
                            )}
                          </Box>
                          <Box>
                            <Box pl={3} pr={3}>
                              <Box display={'flex'} alignItems={'center'}>
                                <Typography>Change</Typography>
                                {percentageChange === 'N/A' ? (
                                  <Tooltip title="Data is not available">
                                    <IconButton>
                                      <InfoOutlinedIcon />
                                    </IconButton>
                                  </Tooltip>
                                ) : null}
                              </Box>
                              <Typography fontWeight={'bold'}>{percentageChange} %</Typography>

                              {dates.length > 1 && (
                                <Typography fontSize={'0.8rem'}>
                                  vs. {formatDate(new Date(dates[1].start), dateFormats)} -{' '}
                                  {formatDate(new Date(dates[1].end), dateFormats)}
                                </Typography>
                              )}
                            </Box>
                          </Box>
                        </Box>
                      )}
                    </Box>
                  </Box>
                  <Box flexGrow={1} minHeight={200}>
                    {isLoading ? (
                      <Box
                        display="flex"
                        justifyContent="center"
                        alignItems="center"
                        height="100%"
                        width="100%"
                      >
                        <Loader style={{ height: '40vh', width: '100%' }} />
                      </Box>
                    ) : (
                      <Plot
                        data={chartData}
                        useResizeHandler={true}
                        style={{ width: '100%', height: '100%' }}
                        layout={{
                          ...layout,
                          xaxis: {
                            ...layout.xaxis,
                            title: 'Time',
                            position: 0,
                            fixedrange: enabledZoom ? true : undefined,
                          },
                          yaxis: {
                            ...layout.yaxis,
                            position: 0,
                            fixedrange: enabledZoom ? true : undefined,
                          },
                          legend: {
                            x: 0, // Position legend at the left
                            y: -0.1, // Position legend slightly below the x-axis
                            xanchor: 'left', // Anchor the legend to the right side of the x position
                            yanchor: 'top', // Anchor the legend to the top side of the y position
                          },
                          autosize: true,
                          margin: {
                            t: 0,
                            b: 200,
                            l: mobile ? 0 : undefined,
                            r: mobile ? 0 : undefined,
                            pad: mobile ? 0 : undefined,
                          },
                          // ...data.layout,
                          // width: dimensions.width,
                          // height: dimensions.height,
                        }}
                        config={{
                          responsive: true,
                          displaylogo: false,
                          displayModeBar: false, // This will hide the entire mode bar
                          modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
                          watermark: false,
                        }}
                      />
                    )}
                  </Box>
                </>
              )}
            </>
          )}
        </Box>
      }
      widgetName="KPI Bar chart"
      widgetType="kpi-bar-chart"
      settingsDialog={KPIBarChartSettings}
    />
  );
};
export default KPIBarChartContainer;
