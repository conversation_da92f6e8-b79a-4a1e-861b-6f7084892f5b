import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import RemoveIcon from '@mui/icons-material/Remove';
import { Box, Grid, Typography } from '@mui/material';
import dynamic from 'next/dynamic';
import { useSelector } from 'react-redux';
import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import Loader from '~/components/common/Loader';
import { NoDataFound } from '~/components/common/NoDataFound';
import NoMeasureSelected from '~/components/common/NoMeasureSelected';
import { useFetchKPIColorBoxdata } from '~/hooks/useFetchKPIColorBoxdata';
import { getZoomEnabled } from '~/redux/selectors/dashboardSelectors';
import { getThousandSeparator } from '~/redux/selectors/userPreferences';
import { KPIColorBox } from '~/types/widgets';
import { formatMetricLabel, formatNumber, hasNoMeasureSelected, roundNumber } from '~/utils/utils';
import KPIColorBoxSettingsDialog from './KPIColorBoxSettingsDialog';
const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });
type KPIColorBoxProps = {
  id: string;
  settings: KPIColorBox;
};

const KPIColorBoxContainer = ({ id, settings }: KPIColorBoxProps) => {
  const defaultTitle = formatMetricLabel(settings.title.value);
  const { data, isLoading, removedResult, successAndFailedMeasurements } =
    useFetchKPIColorBoxdata(settings);
  const noMeasuresSelected = hasNoMeasureSelected(settings);
  const enabledZoom = useSelector(getZoomEnabled);
  const thousandSeparator = useSelector(getThousandSeparator);

  return (
    <CommonWidgetContainer
      id={id}
      settings={settings}
      successAndFailedMeasurements={successAndFailedMeasurements}
      removedResults={removedResult === undefined ? undefined : [removedResult!]}
      widgetName="KPI Trend"
      widgetType="kpi-color-box"
      widgetContent={
        <>
          {noMeasuresSelected ? (
            <NoMeasureSelected />
          ) : (
            <>
              {isLoading && <Loader />}
              {!isLoading && !data && (
                <Box
                  sx={{
                    position: 'absolute',
                    height: '100%',
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <NoDataFound />
                </Box>
              )}
              {!isLoading && data && (
                <Box
                  sx={{
                    height: '100%',
                    width: '100%',
                    p: 2,
                    backgroundColor: data.trendUp
                      ? settings.positive.backgroundColor
                      : settings.negative.backgroundColor,
                  }}
                >
                  {settings.title.isVisible && (
                    <Typography
                      variant="h5"
                      sx={{
                        fontSize: settings.title.isVisible
                          ? settings.title.fontSize + 'px'
                          : undefined,
                        fontWeight: settings.title.isVisible
                          ? settings.title.fontWeight
                          : undefined,
                        color: settings.title.isVisible ? settings.title.color : undefined,
                      }}
                      textAlign="center"
                      component="div"
                    >
                      {settings.title.isVisible ? settings.title.value : defaultTitle}
                    </Typography>
                  )}
                  <Typography
                    variant="h4"
                    color={
                      data.trendUp ? settings.positive.font.color : settings.negative.font?.color
                    }
                    textAlign="center"
                    fontWeight={'bold'}
                  >
                    {settings.prefix?.isVisible ? settings.prefix?.value : null}{' '}
                    {thousandSeparator
                      ? formatNumber(data.lastValue)
                      : roundNumber(Number(data.lastValue))}{' '}
                    {data.unit} {settings.suffix?.isVisible ? settings.suffix?.value : null}{' '}
                  </Typography>
                  <Box>
                    {(settings.menuOption === 'Chart' || settings.menuOption === 'Both') && (
                      <Box sx={{ minHeight: 200 }}>
                        <Plot
                          data={data.data}
                          useResizeHandler={true}
                          style={{ width: '100%', height: '100%', maxHeight: 200 }}
                          layout={{
                            ...data.layout,
                            xaxis: {
                              ...data.layout.xaxis,
                              position: 0,
                              fixedrange: enabledZoom ? true : undefined,
                            },
                            yaxis: {
                              ...data.layout.yaxis,
                              position: 0,
                              fixedrange: enabledZoom ? true : undefined,
                            },
                            legend: {
                              x: 0, // Position legend at the left
                              y: -0.1, // Position legend slightly below the x-axis
                              xanchor: 'left', // Anchor the legend to the right side of the x position
                              yanchor: 'top', // Anchor the legend to the top side of the y position
                            },
                            autosize: true,
                            // width: dimensions.width,
                            // height: dimensions.height,
                          }}
                          config={{
                            responsive: true,
                            displaylogo: false,
                            displayModeBar: false, // This will hide the entire mode bar
                            modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
                            watermark: false,
                          }}
                        />
                      </Box>
                    )}
                    {(settings.menuOption === 'Stats' || settings.menuOption === 'Both') && (
                      <Grid container mt={1} zIndex={10}>
                        <Grid
                          item
                          xs={4}
                          sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            flexDirection: 'column',
                            textAlign: 'center',
                            height: '100%',
                            zIndex: 10,
                          }}
                        >
                          <Typography
                            variant="h6"
                            width={'100%'}
                            color={
                              data.trendUp
                                ? settings.positive.font.color
                                : settings.negative.font?.color
                            }
                          >
                            Previous
                          </Typography>
                          <Typography
                            variant="h6"
                            width={'100%'}
                            color={
                              data.trendUp
                                ? settings.positive.font.color
                                : settings.negative.font?.color
                            }
                          >
                            {thousandSeparator
                              ? formatNumber(data.prevValue)
                              : roundNumber(Number(data.prevValue))}{' '}
                            {data.unit}
                          </Typography>
                        </Grid>
                        <Grid
                          item
                          xs={4}
                          sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            flexDirection: 'column',
                            textAlign: 'center',
                            height: '100%',
                            zIndex: 10,
                          }}
                        >
                          <Typography
                            variant="h6"
                            width={'100%'}
                            color={
                              data.trendUp
                                ? settings.positive.font.color
                                : settings.negative.font?.color
                            }
                          >
                            Change
                          </Typography>
                          <Typography
                            variant="h6"
                            width={'100%'}
                            color={
                              data.trendUp
                                ? settings.positive.font.color
                                : settings.negative.font?.color
                            }
                          >
                            {thousandSeparator
                              ? formatNumber(data.delta)
                              : roundNumber(Number(data.delta))}{' '}
                            {data.unit}
                          </Typography>
                        </Grid>
                        <Grid
                          item
                          xs={4}
                          sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            flexDirection: 'column',
                            textAlign: 'center',
                            height: '100%',
                            zIndex: 10,
                          }}
                        >
                          <Typography
                            variant="h6"
                            width={'100%'}
                            color={
                              data.trendUp
                                ? settings.positive.font.color
                                : settings.negative.font?.color
                            }
                          >
                            Trend
                          </Typography>
                          <Typography
                            component={'div'}
                            width={'100%'}
                            color={
                              data.trendUp
                                ? settings.positive.font.color
                                : settings.negative.font?.color
                            }
                          >
                            {data.isTrendSame ? (
                              <RemoveIcon
                                sx={{
                                  fontSize: '2.5rem',
                                }}
                              />
                            ) : (
                              <>
                                {data.trendUp ? (
                                  <ArrowUpwardIcon sx={{ fontSize: '2.5rem' }} />
                                ) : (
                                  <ArrowDownwardIcon sx={{ fontSize: '2.5rem' }} />
                                )}
                              </>
                            )}
                          </Typography>
                        </Grid>
                      </Grid>
                    )}
                  </Box>
                </Box>
              )}
            </>
          )}
        </>
      }
      settingsDialog={KPIColorBoxSettingsDialog}
    />
  );
};

export default KPIColorBoxContainer;
