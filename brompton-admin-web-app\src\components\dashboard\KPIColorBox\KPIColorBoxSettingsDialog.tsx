import {
  Box,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
} from '@mui/material';
import ForecastSettings from '~/components/ForecastSettings/ForecastSettings';
import DataWidgetSettingsContainer from '~/components/common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import PrefixSuffixContaner from '~/components/common/PrefixSuffixContainer';
import SingleMeasureSelect from '~/components/common/SingleMeasureSelect';
import {
  KPIColorBox,
  setForecastWidgetSettings,
  setKPIWidgetSettings,
  setSingleMeasureWidgetSettings,
} from '~/types/widgets';

type KPIColorBoxSettingsDialogProps = {
  settings: KPIColorBox;
  handleSettingsChange: (value: ((prevState: KPIColorBox) => KPIColorBox) | KPIColorBox) => void;
};
const menuOptions = ['Stats', 'Chart', 'Both'];
const KPIColorBoxSettingsDialog = ({
  settings,
  handleSettingsChange,
}: KPIColorBoxSettingsDialogProps) => {
  const handlePositiveBackgroundColor = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      positive: { ...settings.positive, backgroundColor: event.target.value },
    });
  };
  const handlePositiveFontColor = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      positive: { ...settings.positive, font: { color: event.target.value } },
    });
  };
  const handleNegativeBackgroundColor = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      negative: { ...settings.negative, backgroundColor: event.target.value },
    });
  };
  const handleNegativeFontColor = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      negative: { ...settings.negative, font: { color: event.target.value } },
    });
  };
  const handleSparkLineColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      sparkLineColor: event.target.value,
    });
  };
  const handleMenuOptionChange = (event: SelectChangeEvent<string>) => {
    handleSettingsChange({
      ...settings,
      menuOption: event.target.value as 'Stats' | 'Chart' | 'Both',
    });
  };
  return (
    <DataWidgetSettingsContainer
      settings={settings}
      setSettings={handleSettingsChange}
      dataTabChildren={
        <Box>
          <SingleMeasureSelect
            id={'KPI-value-indicator'}
            settings={settings}
            setSettings={handleSettingsChange as setSingleMeasureWidgetSettings}
          />
        </Box>
      }
      feelTabChidren={
        <>
          {settings.mode === 'template' && settings.selectedDbMeasureId !== '' && (
            <ForecastSettings
              settings={settings}
              setSettings={handleSettingsChange as setForecastWidgetSettings}
            />
          )}
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="menu-option-label">Menu Option</InputLabel>
                <Select
                  labelId="menu-option-label"
                  id="menu-option"
                  label="Menu Option"
                  value={settings.menuOption ?? 'Stats'}
                  onChange={handleMenuOptionChange}
                  displayEmpty
                >
                  {menuOptions.map((option) => (
                    <MenuItem key={option} value={option}>
                      {option}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* SparkLine Color Picker */}
            <Grid item xs={12} sm={6}>
              <TextField
                label="SparkLine Color"
                name="color"
                type="color"
                sx={{ margin: 'unset' }}
                onChange={handleSparkLineColorChange}
                value={settings?.sparkLineColor ?? '#5959d1'}
                variant="outlined"
                margin="normal"
                fullWidth
              />
            </Grid>
          </Grid>
          <Grid container sx={{ mt: 1 }} spacing={2}>
            {/* Positive Background Color */}
            <Grid item xs={12} sm={6}>
              <TextField
                label="Positive Background Color"
                name="color"
                type="color"
                onChange={handlePositiveBackgroundColor}
                value={settings.positive.backgroundColor ?? '#00FF00'}
                variant="outlined"
                margin="normal"
                fullWidth
              />
            </Grid>

            {/* Positive Font Color */}
            <Grid item xs={12} sm={6}>
              <TextField
                label="Positive Font Color"
                name="color"
                type="color"
                onChange={handlePositiveFontColor}
                value={settings.positive.font.color ?? '#000000'}
                variant="outlined"
                margin="normal"
                fullWidth
              />
            </Grid>

            {/* Negative Background Color */}
            <Grid item xs={12} sm={6}>
              <TextField
                label="Negative Background Color"
                name="color"
                type="color"
                onChange={handleNegativeBackgroundColor}
                value={settings.negative.backgroundColor ?? '#FF0000'}
                variant="outlined"
                margin="normal"
                fullWidth
              />
            </Grid>

            {/* Negative Font Color */}
            <Grid item xs={12} sm={6}>
              <TextField
                label="Negative Font Color"
                name="color"
                type="color"
                onChange={handleNegativeFontColor}
                value={settings.negative.font.color ?? '#000000'}
                variant="outlined"
                margin="normal"
                fullWidth
              />
            </Grid>
          </Grid>
          <PrefixSuffixContaner
            settings={settings}
            handleSettingsChange={handleSettingsChange as setKPIWidgetSettings}
          />
        </>
      }
    />
  );
};
export default KPIColorBoxSettingsDialog;
