import { Delete<PERSON>orever } from '@mui/icons-material';
import {
  Alert,
  Box,
  Button,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormLabel,
  IconButton,
  MenuItem,
  OutlinedInput,
  Radio,
  RadioGroup,
  Select,
  SelectChangeEvent,
  TextField,
  Tooltip,
} from '@mui/material';
import { ReactNode, useEffect, useState } from 'react';
import DataWidgetSettingsContainer from '~/components/common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import SingleMeasureSelect from '~/components/common/SingleMeasureSelect';
import {
  KpiCurrentWidget,
  PeriodOrNull,
  images,
  setSingleMeasureWidgetSettings,
} from '~/types/widgets';
import { fontWeights } from '~/utils/utils';

const samplePeriodOptions = ['Daily', 'Weekly', 'Monthly'];
const imagePositions = ['Image-Left', 'Image-Right', 'Image-Center'];
const imageSize = ['Small', 'Medium', 'Large'];
type ImageWidgetDialogProps = {
  settings: KpiCurrentWidget;
  handleSettingsChange: React.Dispatch<React.SetStateAction<KpiCurrentWidget>>;
};
const ImageStatsSettings = ({ settings, handleSettingsChange }: ImageWidgetDialogProps) => {
  const [samples, setSamples] = useState<PeriodOrNull[]>([...settings.samples]);
  const [warnByAgg, setwarnByAgg] = useState<boolean>(false);
  useEffect(() => {
    setSamples([...settings.samples]);
  }, [settings.samples]);

  useEffect(() => {
    if (settings.aggBy === 5 && settings.samples.includes('Monthly')) {
      setwarnByAgg(true);
      handleSettingsChange({ ...settings, isValid: true });
    } else {
      setwarnByAgg(false);
      handleSettingsChange({ ...settings, isValid: true });
    }
  }, [settings.samples, settings.aggBy]);
  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange((prevState) => ({
      ...prevState,
      image: event.target.value as KpiCurrentWidget['image'],
    }));
  };
  const handleFontSize = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange((prevState) => ({
      ...prevState,
      font: {
        ...prevState.font,
        size: Number(event.target.value),
      },
    }));
  };
  const handleColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange((prevState) => ({
      ...prevState,
      font: {
        ...prevState.font,
        color: event.target.value,
      },
    }));
  };
  const handleFontWeight = (event: SelectChangeEvent<string>) => {
    handleSettingsChange((prevState) => ({
      ...prevState,
      font: {
        ...prevState.font,
        weight: event.target.value,
      },
    }));
  };
  const handleImagePlacementChange = (event: SelectChangeEvent<string>, child: ReactNode) => {
    handleSettingsChange({
      ...settings,
      placement: event.target.value as KpiCurrentWidget['placement'],
    });
  };
  const handleImageSizeChange = (event: SelectChangeEvent<string>, child: ReactNode) => {
    handleSettingsChange({
      ...settings,
      imageSize: event.target.value as KpiCurrentWidget['imageSize'],
    });
  };
  return (
    <DataWidgetSettingsContainer
      settings={settings}
      setSettings={handleSettingsChange}
      exculdedSettings={{
        aggBy: true,
        samplePeriod: true,
        globalSamplePeriod: true,
        realtime: true,
      }}
      hideSettings={{
        realtime: true,
      }}
      dataTabChildren={
        <>
          <Box>
            <SingleMeasureSelect
              id={'image-stats-widget'}
              settings={settings}
              setSettings={handleSettingsChange as setSingleMeasureWidgetSettings}
            />
          </Box>
          {settings.samples.length <= 2 && (
            <Box mt={2} sx={{ display: 'flex', justifyContent: 'end' }}>
              <Button
                variant="contained"
                onClick={() => {
                  //setSamples([...samples, null]);
                  handleSettingsChange((prevState) => ({
                    ...prevState,
                    samples: [...samples, null],
                  }));
                }}
              >
                Add Sample Period
              </Button>
            </Box>
          )}
          {settings.samples.map((sample, i) => {
            return (
              <Box
                mt={2}
                key={i}
                sx={{
                  display: 'flex',
                }}
              >
                <FormControl fullWidth>
                  <Select
                    labelId="sample-period-select"
                    id="sample-period-select"
                    value={sample}
                    input={
                      <OutlinedInput
                        label="Sample Period"
                        sx={{
                          //p: 0.5,
                          '& legend': {
                            maxWidth: '100%',
                            height: 'fit-content',
                            '& span': {
                              opacity: 1,
                            },
                          },
                        }}
                      />
                    }
                    onChange={(e) => {
                      const samples = [...settings.samples];
                      samples[i] = e.target.value as PeriodOrNull;
                      handleSettingsChange({
                        ...settings,
                        samples: samples,
                      });
                    }}
                    label="Sample Period"
                    variant="outlined"
                  >
                    {samplePeriodOptions.map((option) => (
                      <MenuItem key={option} value={option}>
                        {option}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <Tooltip title="Remove Sample Period">
                  <IconButton
                    color="error"
                    onClick={() => {
                      const sample = settings.samples;
                      const newSample = sample.filter((_, ind) => i !== ind);
                      handleSettingsChange({
                        ...settings,
                        samples: newSample,
                      });
                    }}
                  >
                    <DeleteForever />
                  </IconButton>
                </Tooltip>
              </Box>
            );
          })}
          {warnByAgg ? (
            <Alert variant="outlined" severity="error" sx={{ mt: 2 }}>
              Reset the aggregate option in widget setting where <strong>STD.P</strong> is selected,
              <strong>STD.P</strong> is not supported with monthly sample period
            </Alert>
          ) : null}
        </>
      }
      feelTabChidren={
        <>
          <Box mt={2} mb={2}>
            <FormGroup>
              <FormLabel>Select Images</FormLabel>
              <RadioGroup
                sx={{ mt: 1 }}
                aria-labelledby="demo-controlled-radio-buttons-group"
                name="radio-buttons-group"
                value={settings.image}
                onChange={handleImageChange}
                row
              >
                {images.map((image, i) => (
                  <FormControlLabel
                    key={i}
                    value={image}
                    control={<Radio />}
                    label={
                      <img src={image} alt={image} style={{ width: '80px', height: '80px' }} />
                    }
                  />
                ))}
              </RadioGroup>
            </FormGroup>
          </Box>
          <Box sx={{ mt: 1.2 }}>
            <FormControl fullWidth>
              <Select
                labelId={'image-postion-select'}
                id={'image-postion-select'}
                sx={{
                  width: '100%',
                  '& fieldset': {
                    '& legend': {
                      maxWidth: '100%',
                      height: 'auto',
                      '& span': {
                        opacity: 1,
                      },
                    },
                  },
                }}
                value={settings.placement}
                onChange={handleImagePlacementChange}
                label="Selected Image position"
              >
                {imagePositions.map((position, i) => {
                  return (
                    <MenuItem key={position} value={position}>
                      {position}
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
          </Box>
          <Box sx={{ mt: 1.2 }}>
            <FormControl fullWidth>
              <Select
                labelId={'image-size-select'}
                id={'image-size-select'}
                sx={{
                  width: '100%',
                  '& fieldset': {
                    '& legend': {
                      maxWidth: '100%',
                      height: 'auto',
                      '& span': {
                        opacity: 1,
                      },
                    },
                  },
                }}
                value={settings.imageSize}
                onChange={handleImageSizeChange}
                label="Selected Image Size"
              >
                {imageSize.map((size) => {
                  return (
                    <MenuItem key={size} value={size}>
                      {size}
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
          </Box>
          <Box>
            <FormGroup>
              <TextField
                name="fontSize"
                type="number"
                onChange={handleFontSize}
                defaultValue={12}
                value={settings.font?.size}
                label="Font Size"
                variant="outlined"
                margin="normal"
                fullWidth
              />
            </FormGroup>
            <FormGroup>
              <TextField
                name="color"
                type="color"
                label="Font Color"
                onChange={handleColorChange}
                value={settings.font?.color}
                variant="outlined"
                margin="normal"
                fullWidth
              />
            </FormGroup>
            <FormControl fullWidth>
              <Select
                labelId="y-select-lable"
                id="tabel-series"
                fullWidth
                defaultValue="bolder"
                value={settings.font?.weight}
                onChange={handleFontWeight}
                label={'Font Weight'}
                input={
                  <OutlinedInput
                    label="Font Weight"
                    sx={{
                      p: 0.5,
                      '& legend': {
                        maxWidth: '100%',
                        height: 'fit-content',
                        '& span': {
                          opacity: 1,
                        },
                      },
                    }}
                  />
                }
              >
                {fontWeights.map((fonts: string) => {
                  return (
                    <MenuItem key={fonts} value={fonts}>
                      {fonts}
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
          </Box>
        </>
      }
    />
  );
};

export default ImageStatsSettings;
