import { Grid, TextField } from '@mui/material';
import DataWidgetSettingsContainer from '~/components/common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import SingleMeasureSelect from '~/components/common/SingleMeasureSelect';
import { KPIPercentage, setSingleMeasureWidgetSettings } from '~/types/widgets';

type KPIPercentageDialogProps = {
  settings: KPIPercentage;
  handleSettingsChange: (
    value: ((prevState: KPIPercentage) => KPIPercentage) | KPIPercentage,
  ) => void;
};
const KPIPercentageDialog = ({ settings, handleSettingsChange }: KPIPercentageDialogProps) => {
  const handlePositiveColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      positiveColor: event.target.value,
    });
  };
  const handleNegativeColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      negativeColor: event.target.value,
    });
  };
  return (
    <>
      <DataWidgetSettingsContainer
        settings={settings}
        setSettings={handleSettingsChange}
        dataTabChildren={
          <>
            <SingleMeasureSelect
              id={'KPI-Percentage'}
              settings={settings}
              setSettings={handleSettingsChange as setSingleMeasureWidgetSettings}
            />
          </>
        }
        feelTabChidren={
          <>
            <Grid container spacing={2}>
              {/* Positive Color */}
              <Grid item xs={12} sm={6}>
                <TextField
                  type="color"
                  name="positiveColor"
                  label="Positive Color"
                  onChange={handlePositiveColorChange}
                  value={settings.positiveColor}
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </Grid>

              {/* Negative Color */}
              <Grid item xs={12} sm={6}>
                <TextField
                  type="color"
                  name="negativeColor"
                  label="Negative Color"
                  onChange={handleNegativeColorChange}
                  value={settings.negativeColor}
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </Grid>
            </Grid>
          </>
        }
      />
    </>
  );
};
export default KPIPercentageDialog;
