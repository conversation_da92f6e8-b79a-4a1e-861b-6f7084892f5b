import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  MenuItem,
  OutlinedInput,
  Select,
  SelectChangeEvent,
  TextField,
} from '@mui/material';
import DataWidgetSettingsContainer from '~/components/common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import PrefixSuffixContaner from '~/components/common/PrefixSuffixContainer';
import SingleMeasureSelect from '~/components/common/SingleMeasureSelect';
import {
  KPISparkline,
  setKPIWidgetSettings,
  setSingleMeasureWidgetSettings,
} from '~/types/widgets';
import { fontWeights } from '~/utils/utils';

type KPISparkLineDialogProps = {
  settings: KPISparkline;
  handleSettingsChange: (value: ((prevState: KPISparkline) => KPISparkline) | KPISparkline) => void;
};
const KPISparkLineDialog = ({ settings, handleSettingsChange }: KPISparkLineDialogProps) => {
  const handleTooltipChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      tooltip: event.target.value,
    });
  };
  const handleValueChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      value: {
        ...settings.value,
        [event.target.name]: event.target.checked,
      },
    });
  };
  const handleValueFontSize = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      value: {
        ...settings.value,
        fontSize: Number(event.target.value),
      },
    });
  };
  const handleValueColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      value: {
        ...settings.value,
        color: event.target.value,
      },
    });
  };
  const handleSparkLineColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      sparkLineColor: event.target.value,
    });
  };
  const handleValueFontWeight = (event: SelectChangeEvent<string>, child: React.ReactNode) => {
    handleSettingsChange({
      ...settings,
      value: {
        ...settings.value,
        fontWeight: event.target.value,
      },
    });
  };
  return (
    <DataWidgetSettingsContainer
      settings={settings}
      setSettings={handleSettingsChange}
      dataTabChildren={
        <>
          <SingleMeasureSelect
            id={'KPI-SparkLine-setting'}
            settings={settings}
            setSettings={handleSettingsChange as setSingleMeasureWidgetSettings}
          />
        </>
      }
      feelTabChidren={
        <>
          <FormGroup>
            <TextField
              name="tooltip"
              onChange={handleTooltipChange}
              value={settings.tooltip}
              label="ToolTip"
              variant="outlined"
              margin="normal"
              fullWidth
            />
          </FormGroup>
          <PrefixSuffixContaner
            settings={settings}
            handleSettingsChange={handleSettingsChange as setKPIWidgetSettings}
          />
          <FormGroup>
            <TextField
              label="SparkLine Color"
              name="color"
              type="color"
              onChange={handleSparkLineColorChange}
              value={settings?.sparkLineColor ?? '#5959d1'}
              variant="outlined"
              margin="normal"
              fullWidth
            />
          </FormGroup>
          <Box width={'100%'}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={settings.value?.isOverRide}
                  onChange={handleValueChange}
                  name="isOverRide"
                />
              }
              label="Override Default Value style"
            />
          </Box>
          {settings.value?.isOverRide ? (
            <>
              <FormGroup>
                <TextField
                  name="fontSize"
                  type="number"
                  onChange={handleValueFontSize}
                  defaultValue={12}
                  value={settings.value.fontSize}
                  label="Font Size"
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormGroup>
              <FormGroup>
                <TextField
                  label="Font Color"
                  name="color"
                  type="color"
                  onChange={handleValueColorChange}
                  value={settings.value.color}
                  variant="outlined"
                  margin="normal"
                  fullWidth
                />
              </FormGroup>
              <FormControl sx={{ width: '100%' }}>
                <Box display="flex" alignItems="center" width="100%" gap={1}>
                  <Select
                    labelId="y-select-lable"
                    id="tabel-series"
                    defaultValue="bolder"
                    fullWidth
                    input={
                      <OutlinedInput
                        label="Font Weight"
                        sx={{
                          '& legend': {
                            maxWidth: '100%',
                            height: 'fit-content',
                            '& span': {
                              opacity: 1,
                            },
                          },
                        }}
                      />
                    }
                    value={settings.value.fontWeight}
                    onChange={handleValueFontWeight}
                  >
                    {fontWeights.map((fonts: string) => {
                      return (
                        <MenuItem key={fonts} value={fonts}>
                          {fonts}
                        </MenuItem>
                      );
                    })}
                  </Select>
                </Box>
              </FormControl>
            </>
          ) : null}
        </>
      }
    />
  );
};

export default KPISparkLineDialog;
