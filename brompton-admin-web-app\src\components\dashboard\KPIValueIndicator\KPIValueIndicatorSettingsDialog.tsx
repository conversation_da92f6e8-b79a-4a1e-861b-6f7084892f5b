import { Box, Card, FormGroup, SelectChangeEvent, TextField } from '@mui/material';
import DataWidgetContainer from '~/components/common/DataWidgetContainer';
import PrefixSuffixContaner from '~/components/common/PrefixSuffixContainer';
import SingleMeasureSelect from '~/components/common/SingleMeasureSelect';
import TitleSettings from '~/components/common/TitleSettings';
import {
  KPIValueIndicator,
  setKPIWidgetSettings,
  setSettings,
  setSingleMeasureWidgetSettings,
} from '~/types/widgets';

type KPIValueIndicatorSettingsDialogProps = {
  settings: KPIValueIndicator;
  handleSettingsChange: (
    value: ((prevState: KPIValueIndicator) => KPIValueIndicator) | KPIValueIndicator,
  ) => void;
};
const KPIValueIndicatorSettingsDialog = ({
  settings,
  handleSettingsChange,
}: KPIValueIndicatorSettingsDialogProps) => {
  const handleFontSize = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const fontSize = Number(event.target.value);
    handleSettingsChange({
      ...settings,
      title: { ...settings.title, fontSize },
    });
  };
  const handleFontWeight = (event: SelectChangeEvent<string>) => {
    handleSettingsChange({
      ...settings,
      title: { ...settings.title, fontWeight: event.target.value },
    });
  };
  const handleColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      title: { ...settings.title, color: event.target.value },
    });
  };
  const handleTitleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.name === 'value') {
      handleSettingsChange({
        ...settings,
        title: { ...settings.title, [event.target.name]: event.target.value },
      });
    } else if (event.target.name === 'isVisible') {
      handleSettingsChange({
        ...settings,
        title: { ...settings.title, [event.target.name]: event.target.checked },
      });
    }
  };
  const handleLineColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleSettingsChange({
      ...settings,
      lineColor: event.target.value,
    });
  };

  return (
    <>
      <Card>
        <TitleSettings
          title={settings.title}
          fontSize={settings.title.fontSize ?? 12}
          fontWeight={settings.title.fontWeight}
          handleFontSize={handleFontSize}
          handleFontWeight={handleFontWeight}
          handleTitleChange={handleTitleChange}
          handleColorChange={handleColorChange}
        />
      </Card>
      <DataWidgetContainer
        id={'KPI-value-indicator'}
        settings={settings}
        setSettings={handleSettingsChange as setSettings}
      >
        <Box sx={{ p: 2 }}>
          <SingleMeasureSelect
            id={'KPI-value-indicator'}
            settings={settings}
            setSettings={handleSettingsChange as setSingleMeasureWidgetSettings}
          />
        </Box>
        <FormGroup sx={{ p: 2, pt: 0, pb: 0 }}>
          <TextField
            label="Line Color"
            name="color"
            type="color"
            onChange={handleLineColorChange}
            value={settings.lineColor}
            variant="outlined"
            margin="normal"
            fullWidth
          />
        </FormGroup>
        <PrefixSuffixContaner
          settings={settings}
          handleSettingsChange={handleSettingsChange as setKPIWidgetSettings}
        />
      </DataWidgetContainer>
    </>
  );
};

export default KPIValueIndicatorSettingsDialog;
