import CancelIcon from '@mui/icons-material/Cancel';
import { Button, Typography } from '@mui/material';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import TreePanelContainer, { SelectEventProps } from '~/layout/containers/TreePanelContainer';
import { assetsApi, useDeleteAssetMutation } from '~/redux/api/assetsApi';
import { measuresApi, useDeleteMeasureMutation } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import {
  getCurrentDashboardId,
  getMainPanel,
  isDashboardDirty,
} from '~/redux/selectors/dashboardSelectors';
import { getDeletedWidgets, getWidgets } from '~/redux/selectors/widgetSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { ConfirmationDialog } from '~/shared/dialogs/components/ConfirmationDialog';
import { useDialog } from '~/shared/dialogs/dialog-hooks';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { Customer } from '~/types/customers';
import { DashboardCollection } from '~/types/dashboard';
import CustomDialog from '../common/CustomDialog';
type LeftPanelProps = {
  dashboardList: DashboardCollection;
  isLoadingDashboards: boolean;
  customerList: Customer[];
  isCustomerListLoading: boolean;
  isCustomerListSuccess: boolean;
};
type SelectedMeasurementWidget = {
  id: string;
  type: string;
  chartType: string | undefined;
  title: string;
};
export function LeftPanel({
  customerList,
  dashboardList,
  isCustomerListLoading,
  isCustomerListSuccess,
  isLoadingDashboards,
}: LeftPanelProps) {
  const dispatch = useDispatch();
  const customer = useSelector(getActiveCustomer);
  const customerId = customer?.id ?? 0;
  const router = useRouter();
  const currentDashboardId = useSelector(getCurrentDashboardId);
  const isDashboardStateDirty = useSelector(isDashboardDirty);
  const widgets = useSelector(getWidgets);
  const deleteWidgets = useSelector(getDeletedWidgets);
  const mainPanel = useSelector(getMainPanel);
  const ActiveCustomer = useSelector(getActiveCustomer);

  const {
    selectMainPanel,
    setSelectedNodeIds,
    setCurrentSelectedNodeId,
    selectCheckbox,
    unselectCheckbox,
    removeSelectedNodeId,
    setSelectedViewMeasureId,
    setIsDirty,
  } = dashboardSlice.actions;

  const [dialogState, showConfirmationDialog, closeConfirmationDialog] = useDialog();
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const [confirm, setConfirm] = useState(false);
  const [removeMeasureConfirm, setRemoveMeasureConfirm] = useState(false);
  const [allSelectedMeasure, setAllSelectedMeasure] = useState<{
    nodeId: string;
    event: SelectEventProps | null;
    widgets: SelectedMeasurementWidget[] | [];
  }>({
    nodeId: '',
    event: null,
    widgets: [],
  });
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [deleteAsset, { isError: isAssetDeleteError, isSuccess: isAssetDeleteSuccess }] =
    useDeleteAssetMutation();
  const [deleteMeasure, { isError: isMeasureDeleteError, isSuccess: isMeasureDeleteSuccess }] =
    useDeleteMeasureMutation();

  useEffect(() => {
    if (isAssetDeleteSuccess) {
      closeConfirmationDialog();
      showSuccessAlert('Asset deleted');
      dispatch(dashboardSlice.actions.setCurrentSelectedNodeId('-1'));
      dispatch(assetsApi.util.invalidateTags(['Asset']));
      dispatch(measuresApi.util.invalidateTags(['Measure']));
    }

    if (isAssetDeleteError) {
      closeConfirmationDialog();
      showErrorAlert('Error deleting asset');
    }

    if (isMeasureDeleteSuccess) {
      closeConfirmationDialog();
      showSuccessAlert('Measurement deleted');
      dispatch(dashboardSlice.actions.setCurrentSelectedNodeId('-1'));
      dispatch(assetsApi.util.invalidateTags(['Asset']));
      dispatch(measuresApi.util.invalidateTags(['Measure']));
    }

    if (isMeasureDeleteError) {
      closeConfirmationDialog();
      showErrorAlert('Error deleting measurement');
    }
  }, [isAssetDeleteError, isAssetDeleteSuccess, isMeasureDeleteError, isMeasureDeleteSuccess]);

  // if (!dashboardId) return <Loader />;
  if (!customerId || !customer) return null;

  const getWidgetsBasedOnMeasurement = (
    nodeId: string,
    { eventType, currentNode, selectedNodeIds, type }: SelectEventProps,
  ) => {
    const [_, assetId, metricId] = nodeId.split(':');

    const selectedWidgets: SelectedMeasurementWidget[] = [];

    widgets.forEach((widget) => {
      if (widget.type === 'chart') {
        const settings = widget.settings;

        switch (settings?.chartType) {
          case 'bar':
            if (settings.settings.selectedTitles.includes(metricId)) {
              selectedWidgets.push({
                id: widget.id,
                type: widget.type,
                chartType: widget.settings.chartType,
                title: settings.settings.title.value ?? widget.type,
              });
            }
            break;
          case 'scatter':
            if (
              settings.settings.selectedTitles.includes(metricId) ||
              settings.settings.selectedSparkTitle === metricId
            ) {
              selectedWidgets.push({
                id: widget.id,
                type: widget.type,
                chartType: widget.settings.chartType,
                title: settings.settings.title.value ?? widget.type,
              });
            }
            break;
          case 'heatmap':
            if (settings.settings.selectedDbMeasureId === metricId) {
              selectedWidgets.push({
                id: widget.id,
                type: widget.type,
                chartType: widget.settings.chartType,
                title: settings.settings.title.value ?? widget.type,
              });
            }
            break;
          case 'bullet':
            if (settings.settings.selectedDbMeasureId === metricId) {
              selectedWidgets.push({
                id: widget.id,
                type: widget.type,
                chartType: widget.settings.chartType,
                title: settings.settings.title.value ?? widget.type,
              });
            }
            break;
          case 'indicator':
            if (settings.settings.selectedDbMeasureId === metricId) {
              selectedWidgets.push({
                id: widget.id,
                type: widget.type,
                chartType: widget.settings.chartType,
                title: settings.settings.title.value ?? widget.type,
              });
            }
            break;
          case 'sankey':
            const selectedMeasure = settings.settings.Label.find(
              (label) => label.sourceFrom === 'Default' && label.sourceName === metricId,
            );
            if (selectedMeasure) {
              selectedWidgets.push({
                id: widget.id,
                type: widget.type,
                chartType: widget.settings.chartType,
                title: settings.settings.title.value ?? widget.type,
              });
            }
            break;
        }
      } else if (
        widget.type === 'stats' ||
        widget.type === 'kpi-value-indicator' ||
        widget.type === 'kpi-color-box' ||
        widget.type === 'kpi-percentage' ||
        widget.type === 'image-stats' ||
        widget.type === 'kpi-bar-chart' ||
        widget.type === 'kpi-sparkline'
      ) {
        if (widget.settings.selectedDbMeasureId === metricId) {
          selectedWidgets.push({
            id: widget.id,
            type: widget.type,
            chartType: widget.type,
            title: widget.settings.title.value ?? widget.type,
          });
        }
      } else if (widget.type === 'table') {
        if (
          widget.settings.selectedDbMeasureId === metricId ||
          widget.settings.selectedTitles.includes(metricId)
        ) {
          selectedWidgets.push({
            id: widget.id,
            type: widget.type,
            chartType: widget.type,
            title: widget.settings.title.value ?? widget.type,
          });
        }
      } else if (
        widget.type === 'static' ||
        widget.type === 'solar_panel' ||
        widget.type === 'voltage' ||
        widget.type === 'image' ||
        widget.type === 'vertical' ||
        widget.type === 'kpi-table'
      ) {
        if (widget.settings.selectedTitles.includes(metricId)) {
          selectedWidgets.push({
            id: widget.id,
            type: widget.type,
            chartType: widget.type,
            title: widget.settings.title.value ?? widget.type,
          });
        }
      } else if (widget.type === 'map') {
        const selectedMeasureFromMarkers = widget.settings.markers.find((marker) =>
          marker.selectedTitles.includes(metricId),
        );
        if (selectedMeasureFromMarkers) {
          selectedWidgets.push({
            id: widget.id,
            type: widget.type,
            chartType: widget.type,
            title: widget.settings.title.value ?? widget.type,
          });
        }
      }
    });

    if (selectedWidgets?.length) {
      setRemoveMeasureConfirm(true);
      setAllSelectedMeasure({
        nodeId,
        event: {
          eventType,
          currentNode,
          selectedNodeIds,
          type,
        },
        widgets: [...selectedWidgets],
      });
    } else {
      removeMeasurement(nodeId, {
        eventType,
        currentNode,
        selectedNodeIds,
        type,
      });
    }
  };

  const removeMeasurement = (nodeId: string, event: SelectEventProps) => {
    const [_, assetId, metricId] = nodeId.split(':');
    const { eventType, currentNode, selectedNodeIds } = event;

    dispatch(setCurrentSelectedNodeId(nodeId));
    dispatch(setSelectedNodeIds(selectedNodeIds));

    dispatch(selectMainPanel('chart'));
    if (eventType === 'select') {
      const metricName = currentNode.tag;
      dispatch(selectCheckbox({ assetId, metricId, metricName }));
      dispatch(setIsDirty(true));
    } else {
      dispatch(unselectCheckbox({ assetId, metricId }));
      dispatch(setIsDirty(true));
    }
  };

  return (
    <>
      <ConfirmationDialog {...dialogState} />
      <AlertSnackbar {...snackbarState} />
      {isCustomerListSuccess && !isLoadingDashboards && (
        <TreePanelContainer
          onNewTemplateFromAsset={(nodeId) => {
            dispatch(setCurrentSelectedNodeId(nodeId));
            dispatch(selectMainPanel('newAssetTemplate'));
          }}
          onMetricAlertCreate={(nodeId) => {
            dispatch(setSelectedViewMeasureId(nodeId));
            dispatch(selectMainPanel('create-alert'));
          }}
          onViewAlerts={(nodeId) => {
            dispatch(setSelectedViewMeasureId(nodeId));
            dispatch(selectMainPanel('view-alerts'));
          }}
          onDashboardTemplate={(nodeId) => {
            dispatch(setCurrentSelectedNodeId(nodeId));
            dispatch(selectMainPanel('dashboard-template'));
          }}
          onNewRootAsset={(nodeId) => {
            dispatch(setCurrentSelectedNodeId(nodeId));
            dispatch(selectMainPanel('newAsset'));
          }}
          onNewMeasurement={(nodeId) => {
            dispatch(setCurrentSelectedNodeId(nodeId));
            dispatch(selectMainPanel('newMeasure'));
          }}
          onNewChildAsset={(nodeId) => {
            dispatch(setCurrentSelectedNodeId(nodeId));
            dispatch(selectMainPanel('newAsset'));
          }}
          onAssetDetails={(nodeId) => {
            dispatch(setCurrentSelectedNodeId(nodeId));
            dispatch(selectMainPanel('assetDetails'));
          }}
          onAssetEdit={(nodeId) => {
            dispatch(setCurrentSelectedNodeId(nodeId));
            dispatch(selectMainPanel('editAsset'));
          }}
          onMeasurementEdit={(nodeId) => {
            dispatch(setCurrentSelectedNodeId(nodeId));
            dispatch(selectMainPanel('editMeasure'));
          }}
          onMeasurementView={(nodeId) => {
            dispatch(setCurrentSelectedNodeId(nodeId));
            dispatch(selectMainPanel('MeasureDetails'));
          }}
          onAssetSelection={({ currentNode, selectedNodeIds }: SelectEventProps) => {
            const nodeId = currentNode.id;
            const [assetId] = nodeId.split(':');

            dispatch(setCurrentSelectedNodeId(assetId));
            dispatch(setSelectedNodeIds(selectedNodeIds));
            dispatch(selectMainPanel('assetDetails'));
          }}
          onNewAssetTemplate={(nodeId) => {
            dispatch(setCurrentSelectedNodeId(nodeId));
            dispatch(selectMainPanel('addAssetTemplate'));
          }}
          onMetricSelection={({ eventType, currentNode, selectedNodeIds }) => {
            const nodeId = currentNode.id;
            getWidgetsBasedOnMeasurement(nodeId, {
              eventType,
              currentNode,
              selectedNodeIds,
              type: 'metric',
            });
          }}
          onDeleteAsset={(assetId) => {
            showConfirmationDialog(
              'Delete asset',
              'Are you sure you wish to delete asset and its children?',
              () => {
                deleteAsset({ customerId, assetId: String(assetId) });
              },
            );
          }}
          onViewMeasurement={(nodeId) => {
            dispatch(setSelectedViewMeasureId(nodeId));
            dispatch(selectMainPanel('MeasureDetails'));
          }}
          onEditMeasurement={(nodeId) => {
            dispatch(setCurrentSelectedNodeId(nodeId));
            dispatch(setSelectedViewMeasureId(nodeId));
            dispatch(selectMainPanel('editMeasure'));
          }}
          onDeleteMeasurement={(assetId, assetMeasurementId) => {
            showConfirmationDialog(
              'Delete measurement',
              'Are you sure you wish to delete this measurement?',
              () => {
                deleteMeasure({
                  customerId,
                  assetId: String(assetId),
                  measId: String(assetMeasurementId),
                });
                dispatch(
                  unselectCheckbox({
                    assetId: String(assetId),
                    metricId: String(assetMeasurementId),
                  }),
                );
                dispatch(removeSelectedNodeId(`m:${assetId}:${assetMeasurementId}`));
              },
            );
          }}
        />
      )}
      <CustomDialog
        title="You have unsaved changes."
        content={<Typography color={'error'}>Do you want to still proceed?</Typography>}
        dialogActions={
          <>
            <Button
              onClick={() => {
                setConfirm(false);
                setSelectedCustomer(null);
              }}
              variant="outlined"
              startIcon={<CancelIcon />}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setConfirm(false);
                if (selectedCustomer) {
                  dispatch(dashboardSlice.actions.selectMainPanel('chart'));
                  dispatch(dashboardSlice.actions.setCurrentDashboardTitle(''));
                  dispatch(dashboardSlice.actions.resetDashboardCrumb());
                  dispatch(dashboardSlice.actions.setCurrentDashboardId(-1));
                  dispatch(dashboardSlice.actions.setActiveCustomer(selectedCustomer));
                  router.push(`/customer/${selectedCustomer.id}`);
                }
              }}
              variant="contained"
              color="error"
            >
              Proceed
            </Button>
          </>
        }
        onClose={() => setConfirm(false)}
        open={confirm}
      />
      <CustomDialog
        title="You have unsaved changes."
        content={
          <Typography>
            {allSelectedMeasure.widgets && allSelectedMeasure.widgets.length > 0 && (
              <Typography>
                <b>The following measurement remove from below widgets:</b>

                {allSelectedMeasure.widgets.map((widget) => (
                  <Typography key={widget.id}>
                    <b>
                      {widget.type}
                      {widget.type === 'chart' ? ' - ' + widget.chartType : ''}
                    </b>{' '}
                    : {widget.title}
                  </Typography>
                ))}
              </Typography>
            )}
          </Typography>
        }
        dialogActions={
          <>
            <Button
              onClick={() => {
                setRemoveMeasureConfirm(false);
              }}
              variant="outlined"
              startIcon={<CancelIcon />}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setRemoveMeasureConfirm(false);
                if (allSelectedMeasure.event) {
                  removeMeasurement(allSelectedMeasure.nodeId, allSelectedMeasure.event);
                }
              }}
              variant="contained"
              color="error"
            >
              Proceed
            </Button>
          </>
        }
        onClose={() => setRemoveMeasureConfirm(false)}
        open={removeMeasureConfirm}
      />
    </>
  );
}
