import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import AssetDetail from '~/components/dashboard/AssetDetail';
import {
  assetsApi,
  useDeleteAssetMutation,
  useGetAllBackOfficeAssetTypesQuery,
  useGetAssetByIdQuery,
} from '~/redux/api/assetsApi';
import { measuresApi } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getCurrentSelectedAssetId } from '~/redux/selectors/treeSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { ConfirmationDialog } from '~/shared/dialogs/components/ConfirmationDialog';
import { useDialog } from '~/shared/dialogs/dialog-hooks';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { AssetDoDetails } from '~/types/asset';
import { assetTypePathMapper } from '~/utils/mappers/asset-type-mapper';

export function MainAssetDetails() {
  const dispatch = useDispatch();
  const activeCustomer = useSelector(getActiveCustomer);
  const assetId = useSelector(getCurrentSelectedAssetId);
  const { data: asset, isLoading: isAssetDataLoading } = useGetAssetByIdQuery(
    { customerId: activeCustomer?.id ?? 0, assetId: assetId },
    {
      skip: !activeCustomer?.id, // In RTK Query, the option is `skip` instead of `enabled`
    },
  );

  const { data: assetTypeListData, isLoading: isAssetLoading } =
    useGetAllBackOfficeAssetTypesQuery();
  const [dialogState, showConfirmationDialog, closeConfirmationDialog] = useDialog();
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();

  const [deleteAsset, { isError, isSuccess }] = useDeleteAssetMutation();

  useEffect(() => {
    if (isSuccess) {
      closeConfirmationDialog();
      showSuccessAlert('Asset deleted');
      dispatch(dashboardSlice.actions.setCurrentSelectedNodeId('-1'));
      dispatch(dashboardSlice.actions.selectMainPanel('none'));
      dispatch(assetsApi.util.invalidateTags(['Asset']));
      dispatch(measuresApi.util.invalidateTags(['Measure']));
    }

    if (isError) {
      closeConfirmationDialog();
      showErrorAlert('Error deleting asset');
    }
  }, [isError, isSuccess]);

  if (!activeCustomer) return <></>;

  if (isAssetDataLoading || isAssetLoading) return <>Loading</>;

  return (
    <>
      <ConfirmationDialog {...dialogState} />
      <AlertSnackbar {...snackbarState} />
      {asset && assetTypeListData && (
        <AssetDetail
          asset={asset as AssetDoDetails}
          assetTypeOptions={assetTypePathMapper(
            assetTypeListData?.map((item) => ({
              ...item,
              name: item.name,
              id: item.id,
            })) ?? [],
          )}
          onDelete={(assetId) => {
            showConfirmationDialog(
              'Delete asset',
              'Are you sure you wish to delete asset and its children?',
              () => {
                deleteAsset({ customerId: activeCustomer.id, assetId: String(assetId) });
              },
            );
          }}
        />
      )}
    </>
  );
}
