import { Box, Button } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import EditMeasurementContainer from '~/layout/containers/EditMeasurementContainer';
import NewMeasurementContainer from '~/layout/containers/NewMeasurementContainer';
import { useGetAssetByIdQuery } from '~/redux/api/assetsApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getIsUserLoggedIn, getMainPanel } from '~/redux/selectors/dashboardSelectors';
import { getCurrentSelectedAssetId } from '~/redux/selectors/treeSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { Asset } from '~/types/asset';

export function MainCreateNewMeasure() {
  const activeCustomer = useSelector(getActiveCustomer);
  const assetId = useSelector(getCurrentSelectedAssetId);
  const dispatch = useDispatch();
  const loggedInuser = useSelector(getIsUserLoggedIn);
  const { data: asset } = useGetAssetByIdQuery(
    { customerId: activeCustomer?.id ?? 0, assetId: assetId },
    {
      skip: !activeCustomer?.id, // In RTK Query, the option is `skip` instead of `enabled`
    },
  );
  const mainPanel = useSelector(getMainPanel);

  return (
    <>
      {!loggedInuser?.scoped_roles?.find((role) => role.role === 'USER')?.role ? (
        <Box mt={2} sx={{ width: '100%', display: 'flex', justifyContent: 'end' }}>
          <Button
            sx={{ ml: 'auto' }}
            onClick={() => {
              dispatch(dashboardSlice.actions.selectMainPanel('chart'));
            }}
          >
            Back
          </Button>
        </Box>
      ) : null}
      <Box component={'div'} sx={{ float: 'left', width: '100%', pr: 4 }}>
        {activeCustomer && asset && mainPanel === 'newMeasure' && (
          <NewMeasurementContainer customer={activeCustomer} parentAsset={asset as Asset} />
        )}
        {activeCustomer && asset && mainPanel === 'editMeasure' && (
          <EditMeasurementContainer customer={activeCustomer} parentAsset={asset as Asset} />
        )}
      </Box>
    </>
  );
}
