import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { useDialog } from '~/shared/dialogs/dialog-hooks';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import {
  assetsApi,
  useGetAllBackOfficeAssetTypesMetricsQuery,
  useGetAllBackOfficeAssetTypesQuery,
  useGetAssetByIdQuery,
} from '~/redux/api/assetsApi';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { ConfirmationDialog } from '~/shared/dialogs/components/ConfirmationDialog';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import {
  measuresApi,
  useDeleteMeasureMutation,
  useGetAllDataTypesQuery,
  useGetAllDatasourcesQuery,
  useGetAllLocationsQuery,
  useGetAllMeasureTypesQuery,
  useGetAllUnitsOfMeasureQuery,
  useGetAllValueTypesQuery,
  useGetMeasurementByIdQuery,
} from '~/redux/api/measuresApi';
import { getSelectedViewMeasureId } from '~/redux/selectors/treeSelectors';
import { MeasureDetails } from '~/components/dashboard/MeasureDetails';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { AssetDo } from '~/types/asset';

export function MainMeasureDetails() {
  const dispatch = useDispatch();
  const activeCustomer = useSelector(getActiveCustomer);
  const SelectedViewMeasureId = useSelector(getSelectedViewMeasureId);
  const measurementId = SelectedViewMeasureId.split(':');
  const { data: dataTypeList } = useGetAllDataTypesQuery();
  const {
    data: measurement,
    isLoading: isAssetDataLoading,
    isError: isMeasurementError,
    error: measurementError,
  } = useGetMeasurementByIdQuery(
    { customerId: activeCustomer?.id ?? 0, assetId: measurementId[1], measId: measurementId[2] },
    {
      skip: !activeCustomer?.id, // In RTK Query, the option is `skip` instead of `enabled`
    },
  );

  const { data: measurementTypeList } = useGetAllMeasureTypesQuery();
  const { data: assetDedtails } = useGetAssetByIdQuery(
    {
      customerId: activeCustomer?.id ?? 0,
      assetId: measurementId[1],
    },
    {
      // refetchOnMountOrArgChange: true,
    },
  );
  const { data: datasourceList } = useGetAllDatasourcesQuery({});
  const { data: valueTypeList } = useGetAllValueTypesQuery();
  const { data: unitsOfMeasure, isSuccess: success } = useGetAllUnitsOfMeasureQuery(
    {
      measurementTypeId: measurement?.typeId ?? 0,
    },
    {
      skip: !measurement?.typeId,
      // refetchOnMountOrArgChange: true,
    },
  );
  const { data: metrics } = useGetAllBackOfficeAssetTypesMetricsQuery(
    {
      assetId: (assetDedtails as AssetDo)?.type_id.toString(),
    },
    {
      skip: assetDedtails === undefined || (assetDedtails as AssetDo)?.type_id === undefined,
      refetchOnMountOrArgChange: true,
    },
  );

  const [dialogState, showConfirmationDialog, closeConfirmationDialog] = useDialog();
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();

  const [deleteMeasure, { isError, isSuccess }] = useDeleteMeasureMutation();

  const { data: locationList } = useGetAllLocationsQuery();
  useEffect(() => {
    if (isSuccess) {
      closeConfirmationDialog();
      showSuccessAlert('Measure deleted');
      dispatch(dashboardSlice.actions.setCurrentSelectedNodeId('-1'));
      dispatch(dashboardSlice.actions.selectMainPanel('none'));
      dispatch(assetsApi.util.invalidateTags(['Asset']));
      dispatch(measuresApi.util.invalidateTags(['Measure']));
    }

    if (isError) {
      closeConfirmationDialog();
      showErrorAlert('Error deleting asset');
    }
  }, [isError, isSuccess]);

  useEffect(() => {
    if (isMeasurementError && measurementError) {
      const err = measurementError as CustomError;
      showErrorAlert('Something went wrong');
    }
  }, [isMeasurementError, measurementError]);

  if (!activeCustomer) return <></>;

  if (isAssetDataLoading) return <>Loading</>;

  return (
    <>
      <ConfirmationDialog {...dialogState} />
      <AlertSnackbar {...snackbarState} />
      <MeasureDetails
        measure={measurement}
        asset={assetDedtails}
        metrics={metrics?.items ?? []}
        valueTypeList={valueTypeList ?? []}
        dataSource={datasourceList?.items ?? []}
        unitsOfMeasure={unitsOfMeasure ?? []}
        measureTypeOptions={measurementTypeList ?? []}
        locationList={locationList?.items ?? []}
        dataTypeList={dataTypeList ?? []}
        onEdit={() => {
          dispatch(dashboardSlice.actions.selectMainPanel('editMeasure'));
        }}
        onDelete={() => {
          showConfirmationDialog('Delete asset', 'Are you sure you wish to delete Measure?', () => {
            deleteMeasure({
              customerId: activeCustomer.id,
              assetId: measurementId[1],
              measId: measurementId[2],
            });
          });
        }}
      />
    </>
  );
}
