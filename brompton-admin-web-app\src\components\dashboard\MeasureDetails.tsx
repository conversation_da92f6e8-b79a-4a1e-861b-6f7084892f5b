import { Box, Button, Stack, Typography } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import {
  DataType,
  Datasource,
  MeasurementLocation,
  MeasurementType,
  UnitOfMeasure,
  ValueType,
} from '~/measurements/domain/types';
import { getIsUserLoggedIn } from '~/redux/selectors/dashboardSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { Asset, AssetDo, AssetTypeMetricsCollection } from '~/types/asset';
import { AssetMeasurementDetails } from '~/types/measures';
import Calculation_FactorDetails from './Calculation_FactorDetails';
export function MeasureDetails({
  measure,
  asset,
  metrics,
  dataSource,
  onDelete,
  onEdit,
  measureTypeOptions,
  unitsOfMeasure,
  locationList,
  valueTypeList,
  dataTypeList,
  hideOptions,
}: {
  measure: AssetMeasurementDetails | undefined;
  asset: AssetDo | Asset | undefined;
  dataSource: Datasource[];
  onDelete: (measureId: number) => unknown;
  onEdit: () => unknown;
  measureTypeOptions: MeasurementType[];
  unitsOfMeasure: UnitOfMeasure[];
  locationList: MeasurementLocation[];
  valueTypeList: ValueType[];
  metrics: AssetTypeMetricsCollection['items'];
  dataTypeList: DataType[];
  hideOptions?: boolean;
}) {
  const dispatch = useDispatch();
  const loggedInuser = useSelector(getIsUserLoggedIn);
  const measureTypeOption =
    measure &&
    measureTypeOptions.find((measureTypeOption) => measureTypeOption.id === measure.typeId);
  const location = measure && locationList.find((location) => location.id === measure.locationId);
  const dataSourceData = measure && dataSource.find((ds) => ds.id === measure.datasourceId);
  const valueType = measure && valueTypeList.find((vt) => vt.id === measure.valueTypeId);
  const units = measure && unitsOfMeasure.find((uom) => uom.id === measure.unitOfMeasureId);
  const dataType = measure && dataTypeList.find((dt) => dt.id === measure.dataTypeId);
  const metric = measure && metrics.find((metric) => metric.id === measure?.metricId);
  const writeback = measure?.writeback;
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', pl: 2 }}>
      <Stack
        direction={'row'}
        sx={{
          justifyContent: 'space-between',
        }}
      >
        {(hideOptions === undefined || hideOptions === false) && (
          <Typography variant="h4">Measure Detail</Typography>
        )}
        {(hideOptions === undefined || hideOptions === false) &&
        !loggedInuser?.scoped_roles?.find((role) => role.role === 'USER')?.role ? (
          <Box>
            <Button
              sx={{ mr: 3 }}
              onClick={() => {
                dispatch(dashboardSlice.actions.selectMainPanel('chart'));
              }}
            >
              Back
            </Button>
            {measure && (
              <>
                <Button
                  sx={{ mr: 5 }}
                  onClick={() => {
                    onEdit();
                    dispatch(
                      dashboardSlice.actions.setCurrentSelectedNodeId(
                        'm:' + asset?.id + ':' + measure?.id,
                      ),
                    );
                  }}
                >
                  Edit Measure
                </Button>
                <Button sx={{ ml: 'auto' }} onClick={() => onDelete(measure?.id)}>
                  Delete
                </Button>
              </>
            )}
          </Box>
        ) : null}
      </Stack>
      {measure && (
        <>
          <Typography variant="h5" mt={2}>
            {measure?.measurementId} - &quot;{measure?.tag}&quot;
          </Typography>
          <Typography variant="h6">Parent</Typography>

          <Typography variant="body2" paragraph>
            {asset?.tag ?? 'N/A'}
          </Typography>

          <Typography variant="h6">Description</Typography>
          <Typography variant="body2" paragraph>
            {measure?.description ?? 'No description available.'}
          </Typography>
          <Typography variant="h6">Data Type</Typography>

          <Typography variant="body2" paragraph>
            {dataType?.name ?? 'N/A'}
          </Typography>

          <Typography variant="h6">Value Type</Typography>
          <Typography variant="body2" paragraph>
            {valueType?.name ?? 'N/A'}
          </Typography>

          <Typography variant="h6">Measurement Type</Typography>

          <Typography variant="body2" paragraph>
            {measureTypeOption?.name ?? 'N/A'}
          </Typography>

          <Typography variant="h6">Unit of Measure</Typography>
          <Typography variant="body2" paragraph>
            {units?.name ?? 'N/A'}
          </Typography>

          <Typography variant="h6">Data Source</Typography>

          <Typography variant="body2" paragraph>
            {dataSourceData?.name ?? 'N/A'}
          </Typography>

          <Typography variant="h6">Location</Typography>
          <Typography variant="body2" paragraph>
            Time zone: {location?.name ?? 'UTC'}
          </Typography>

          <Typography variant="h6">Metric</Typography>
          <Typography variant="body2" paragraph>
            {metric ? metric.name : 'N/A'}
          </Typography>

          <Typography variant="h6">Meter Factor</Typography>
          <Typography variant="body2" paragraph>
            {measure.meterFactor ?? 'N/A'}
          </Typography>

          <Typography variant="h6">Writeback</Typography>
          <Typography variant="body2" paragraph>
            {writeback !== undefined ? String(writeback) : 'N/A'}
          </Typography>

          <Calculation_FactorDetails measure={measure} dataSource={dataSource} />
        </>
      )}
    </Box>
  );
}
