import {
  Alert,
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Loader from '~/components/common/Loader';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { EditAssetMeasurementForm, NewAssetMeasurement } from '~/measurements/domain/types';
import { assetsApi } from '~/redux/api/assetsApi';
import {
  useCreateTimeVaringFactorScheduleMutation,
  useEditTimeVaringFactorScheduleMutation,
  useGetFactorByMeasureIdQuery,
  useGetFactorsQuery,
} from '~/redux/api/factorApi';
import {
  measuresApi,
  useCreateMeasurementMutation,
  useEditMeasureMutation,
} from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { AlertMessage } from '~/shared/forms/types';
import { Asset } from '~/types/asset';
import MeasureTable from './MeasureTable';

type MeasureFactorProps = {
  parentAsset: Asset;
  measure: NewAssetMeasurement | EditAssetMeasurementForm;
  isEdit?: boolean;
  assetPath: string;
};
const MeasureFactor = ({ measure, parentAsset, isEdit, assetPath }: MeasureFactorProps) => {
  const [selectedAssetType, setSelectedAssetType] = useState<number | null>(null);
  const activeCustomer = useSelector(getActiveCustomer);
  const [isSeasonal, setIsSeasonal] = useState<boolean>(false);
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const [varingMessage, setVaringMessage] = useState<AlertMessage | undefined>(undefined);
  const [isEditFlow, setIsEditFlow] = useState<boolean>(isEdit ?? false);
  const { isFetching, data: factors } = useGetFactorsQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });
  const [effective_dates, setEffectiveDates] = useState<
    {
      effectiveDate: string;
      rows: object[];
    }[]
  >([]);
  const { data: timeVaringFactorToUpdate } = useGetFactorByMeasureIdQuery(
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    measure?.measurement_id ?? 0,
    {
      skip: !isEditFlow,
      refetchOnMountOrArgChange: true,
    },
  );
  useEffect(() => {
    if (timeVaringFactorToUpdate && isEditFlow) {
      setSelectedAssetType(timeVaringFactorToUpdate.factorType);
    }
  }, [timeVaringFactorToUpdate, isEditFlow]);
  useEffect(() => {
    if (timeVaringFactorToUpdate && isEditFlow) {
      setIsSeasonal(timeVaringFactorToUpdate.seasonal);
    }
  }, [timeVaringFactorToUpdate, isEditFlow]);
  useEffect(() => {
    if (timeVaringFactorToUpdate && isEdit) {
      const dates =
        timeVaringFactorToUpdate.factorTimeOfDayValue
          ?.filter((time) => time.factorTimeOfDayValue.length > 0)
          ?.map((time) => {
            return {
              effectiveDate: new Date(time.effectiveDate).toISOString().split('T')[0],
              factorTimeOfDayValue: time.factorTimeOfDayValue,
            };
          }) ?? [];
      const effectiveDates = dates?.map((curr: any) => {
        const effectiveDate = curr.effectiveDate;
        const rows =
          curr.factorTimeOfDayValue.map((time: any) => {
            return {
              effectiveDate,
              ...time,
            };
          }) ?? [];
        return {
          effectiveDate,
          rows,
        };
      });
      setEffectiveDates(effectiveDates);
      setIsEditFlow(true);
    }
  }, [timeVaringFactorToUpdate, isEdit]);

  const [createMeasurement, { data: assetMeasurement, isSuccess, error, isError, isLoading }] =
    useCreateMeasurementMutation();
  const [
    editMeasurement,
    {
      data: editAssetMeasurement,
      isSuccess: successFull,
      error: errorData,
      isError: haveError,
      isLoading: editLoading,
    },
  ] = useEditMeasureMutation();
  const [
    createTimeVaringFactor,
    { data, isSuccess: isVaringSuccess, error: varingError, isLoading: varingLoading },
  ] = useCreateTimeVaringFactorScheduleMutation();
  const dispatch = useDispatch();
  useEffect(() => {
    if (isVaringSuccess) {
      setVaringMessage({
        message: 'Factor schedule created successfully!',
        severity: 'success',
      });
    }
    if (varingError) {
      const err = varingError as CustomError;
      setVaringMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [data, isVaringSuccess, varingError, varingLoading]);
  useEffect(() => {
    if (isSuccess && assetMeasurement) {
      setAlertMessage({
        message: `Asset measurement "${assetMeasurement.tag}" is created successfully!`,
        severity: 'success',
      });

      dispatch(assetsApi.util.invalidateTags([{ type: 'Asset', id: parentAsset.id }]));
      dispatch(assetsApi.util.invalidateTags([{ type: 'Asset' }]));
      dispatch(measuresApi.util.invalidateTags(['Measure']));
      const factorSchedule = effective_dates.map((date) => {
        return {
          effectiveDate: date.effectiveDate,
        };
      });
      const timeVaryingFactor = {
        measurement: assetMeasurement.measurementId,
        seasonal: isSeasonal,
        factorType: selectedAssetType as number,
      };
      const factorTimeOfDayValue = effective_dates.flatMap((date) => {
        return date.rows.flatMap((row: { [key: string]: any }) => {
          return {
            effectiveDate: new Date(date.effectiveDate).toISOString().split('T')[0],
            timeOfDay: row['timeOfDay'],
            weekday: row['weekday'],
            value: row['value'],
          };
        });
      });
      createTimeVaringFactor({
        factorSchedule,
        factorTimeOfDayValue,
        timeVaryingFactor,
      });
    }
    if (isError && error) {
      const err = error as CustomError;
      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [assetMeasurement, error, isError, isSuccess, parentAsset.id]);
  const [
    editTimeVaringFactor,
    { data: editData, isSuccess: isEditSuccess, error: editError, isLoading: editFactorLoading },
  ] = useEditTimeVaringFactorScheduleMutation();
  useEffect(() => {
    if (isEditSuccess) {
      setVaringMessage({
        message: 'Factor schedule updated successfully!',
        severity: 'success',
      });
    }
    if (editError) {
      const err = editError as CustomError;
      setVaringMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [editData, isEditSuccess, editError]);

  useEffect(() => {
    if (successFull) {
      setAlertMessage({
        message: 'Measure updated successfully!',
        severity: 'success',
      });
      const factorSchedule = effective_dates.map((date) => {
        return {
          effectiveDate: date.effectiveDate,
        };
      });
      const timeVaryingFactor = {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        measurement: measure.measurement_id,
        seasonal: isSeasonal,
        factorType: selectedAssetType as number,
      };
      const factorTimeOfDayValue = effective_dates.flatMap((date) => {
        return date.rows.flatMap((row: { [key: string]: any }) => {
          return {
            effectiveDate: new Date(date.effectiveDate).toISOString().split('T')[0],
            timeOfDay: row['timeOfDay'],
            weekday: row['weekday'],
            value: row['value'],
          };
        });
      });
      editTimeVaringFactor({
        id: timeVaringFactorToUpdate?.id ?? 0,
        factorSchedule,
        factorTimeOfDayValue,
        timeVaryingFactor,
      });
    }
    if (editError) {
      const err = editError as CustomError;
      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [editAssetMeasurement, editError, haveError, parentAsset.id]);
  const onSave = () => {
    if (isEdit) {
      editMeasurement({
        assetId: parentAsset.id.toString(),
        customerId: activeCustomer?.id ?? 0,
        measId: (measure as EditAssetMeasurementForm)?.id.toString() ?? '',
        editAssetMeasurement: {
          ...measure,
          tag: (assetPath === '' ? parentAsset.tag : assetPath) + '\\' + measure.tag,
        } as EditAssetMeasurementForm,
      });
    } else {
      createMeasurement({
        assetId: parentAsset.id,
        assetMeasurement: {
          ...measure,
          tag: (assetPath === '' ? parentAsset.tag : assetPath) + '\\' + measure.tag,
        } as NewAssetMeasurement,
        customerId: activeCustomer?.id ?? 0,
      });
    }
  };

  return (
    <>
      <Box pl={3}>
        <Typography>Measure Name :{measure.tag}</Typography>
        <Typography>Parent :{parentAsset.tag}</Typography>
        {isFetching ? (
          <Loader />
        ) : (
          <>
            <Box display={'flex'} justifyContent={'space-between'}>
              <FormControl
                sx={{
                  mt: 2,
                  minWidth: 600,
                }}
              >
                <Select
                  label="Factor Type"
                  sx={{
                    width: '100%',
                    p: 0.3,
                    '& fieldset': {
                      '& legend': {
                        maxWidth: '100%',
                        height: 'auto',
                        '& span': {
                          opacity: 1,
                        },
                      },
                    },
                  }}
                  value={selectedAssetType?.toString()}
                  onChange={(e: SelectChangeEvent<string>) =>
                    setSelectedAssetType(parseInt(e.target.value))
                  }
                >
                  {factors?.items?.map((factor) => (
                    <MenuItem key={factor.id} value={factor.id}>
                      {factor.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isSeasonal}
                    onChange={(e) => setIsSeasonal(e.target.checked)}
                  />
                }
                label="Is Seasonal ?"
              />
            </Box>
            <Box mt={2} display={'flex'} justifyContent={'end'}>
              <Button
                variant="contained"
                color="primary"
                onClick={() => {
                  setEffectiveDates([
                    ...effective_dates,
                    { effectiveDate: new Date().toISOString().split('T')[0], rows: [] },
                  ]);
                }}
              >
                Add Effective Date
              </Button>
            </Box>
            {effective_dates?.map((date, index) => {
              return (
                <Box key={index} pt={3}>
                  <Box display={'flex'} alignItems={'center'} justifyContent={'space-between'}>
                    <Box display={'flex'} alignItems={'center'}>
                      <FormLabel>Effective Date :</FormLabel>
                      <DatePicker
                        disablePast
                        label="Date"
                        value={dayjs(date.effectiveDate)}
                        onChange={(newValue) => {
                          setEffectiveDates((prev) => {
                            return prev.map((d, index) =>
                              index === effective_dates.indexOf(date)
                                ? {
                                    effectiveDate:
                                      (newValue as unknown as Date)?.toISOString().split('T')[0] ??
                                      '',
                                    rows: d.rows,
                                  }
                                : d,
                            );
                          });
                        }}
                      />
                    </Box>
                    <Button
                      variant="contained"
                      color="error"
                      onClick={() => {
                        setEffectiveDates((prev) => {
                          return prev.filter((_, i) => i !== index);
                        });
                      }}
                    >
                      Delete
                    </Button>
                  </Box>
                  <MeasureTable
                    effective_dates={date.effectiveDate}
                    setEffectiveDates={setEffectiveDates}
                  />
                </Box>
              );
            })}
            <Box mt={5}>
              <Button
                variant="contained"
                onClick={onSave}
                disabled={
                  editLoading ||
                  varingLoading ||
                  editFactorLoading ||
                  isLoading ||
                  isSuccess ||
                  isVaringSuccess ||
                  !selectedAssetType ||
                  !effective_dates.length
                }
              >
                Save
              </Button>
            </Box>
          </>
        )}
        {alertMessage && (
          <Alert severity={alertMessage.severity} sx={{ mt: 3, mb: 3 }}>
            {alertMessage.message}
          </Alert>
        )}
        {varingMessage && (
          <Alert severity={varingMessage.severity} sx={{ mt: 3, mb: 3 }}>
            {varingMessage.message}
          </Alert>
        )}
      </Box>
    </>
  );
};
export default MeasureFactor;
