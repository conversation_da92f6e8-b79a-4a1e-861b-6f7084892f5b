import { Box, Divider, FormControl, FormControlLabel, Radio, RadioGroup } from '@mui/material';
import React from 'react';
import { MultiPlotWidget } from '~/types/widgets';
import AssetMeasureSelector from './AssetMeasureSelector';

type Props = {
  subplotIndex: number;
  measureIndex: number;
  measure: MultiPlotWidget['subplots'][number]['assetMeasures'][number];
  dbMeasureIdToName: Record<string, string>;
  handleSettingsChange: (
    value: MultiPlotWidget | ((prev: MultiPlotWidget) => MultiPlotWidget),
  ) => void;
  settings: MultiPlotWidget;
  assetMeasureLength: number;
};

const AssetMeasureConfig: React.FC<Props> = ({
  subplotIndex,
  measureIndex,
  measure,
  handleSettingsChange,
  dbMeasureIdToName,
  settings,
  assetMeasureLength,
}) => {
  const handleChartTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const chartType = e.target.value;
    handleSettingsChange((prev) => {
      const updated = { ...prev };
      const updatedSubplots = [...updated.subplots];
      updatedSubplots[subplotIndex] = {
        ...updatedSubplots[subplotIndex],
        assetMeasures: [
          ...updatedSubplots[subplotIndex].assetMeasures.slice(0, measureIndex),
          { ...updatedSubplots[subplotIndex].assetMeasures[measureIndex], chartType },
          ...updatedSubplots[subplotIndex].assetMeasures.slice(measureIndex + 1),
        ],
      };
      updated.subplots = updatedSubplots;
      return updated;
    });
  };

  const updateMeasureField = (updatedFields: Partial<typeof measure>) => {
    handleSettingsChange((prev) => {
      const updated = JSON.parse(JSON.stringify(prev)); // Deep copy to avoid immutability issues
      updated.subplots[subplotIndex].assetMeasures[measureIndex] = {
        ...updated.subplots[subplotIndex].assetMeasures[measureIndex],
        ...updatedFields,
      };
      return updated;
    });
  };

  return (
    <Box width="100%" mb={3}>
      {/* Chart Type */}
      <FormControl component="fieldset" sx={{ mb: 2 }}>
        <RadioGroup row value={measure.chartType} onChange={handleChartTypeChange}>
          <FormControlLabel value="bar" control={<Radio />} label="Bar" />
          <FormControlLabel value="trend" control={<Radio />} label="Trend" />
        </RadioGroup>
      </FormControl>

      {/* {measure.measureId.length > 0 && (
        <>
          <FormGroup row sx={{ mb: 2 }}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={measure.showMinLine ?? false}
                  onChange={(e) =>
                    handleSettingsChange((prev) => {
                      const updated = JSON.parse(JSON.stringify(prev));
                      updated.subplots[subplotIndex].assetMeasures[measureIndex].showMinLine =
                        e.target.checked;
                      return updated;
                    })
                  }
                />
              }
              label="Show Min"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={measure.showMaxLine ?? false}
                  onChange={(e) =>
                    handleSettingsChange((prev) => {
                      const updated = JSON.parse(JSON.stringify(prev));
                      updated.subplots[subplotIndex].assetMeasures[measureIndex].showMaxLine =
                        e.target.checked;
                      return updated;
                    })
                  }
                />
              }
              label="Show Max"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={measure.showAvgLine ?? false}
                  onChange={(e) =>
                    handleSettingsChange((prev) => {
                      const updated = JSON.parse(JSON.stringify(prev));
                      updated.subplots[subplotIndex].assetMeasures[measureIndex].showAvgLine =
                        e.target.checked;
                      return updated;
                    })
                  }
                />
              }
              label="Show Avg"
            />

            <FormControlLabel
              control={
                <Checkbox
                  checked={measure.showThresholdLine ?? false}
                  onChange={(e) => updateMeasureField({ showThresholdLine: e.target.checked })}
                />
              }
              label="Threshold Line"
            />

            <FormControlLabel
              control={
                <Checkbox
                  checked={measure.overrideChartColor ?? false}
                  onChange={(e) => updateMeasureField({ overrideChartColor: e.target.checked })}
                />
              }
              label="Override Chart Color"
            />

            {assetMeasureLength === 1 && (
              <>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={measure.ForecastSettings?.showForecast}
                      onChange={(e) => {
                        updateMeasureField({
                          ForecastSettings: {
                            ...measure.ForecastSettings,
                            showForecast: e.target.checked,
                          },
                        });
                        handleSettingsChange((prevState) => ({
                          ...prevState,
                          showForecast: e.target.checked,
                        }));
                      }}
                      name="showForecast"
                    />
                  }
                  label="Show Forecast"
                />

                {measure.ForecastSettings?.showForecast && (
                  <Grid container spacing={2} my={2}>
                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel id="Forecast-label">Forecast Period</InputLabel>
                        <Select
                          labelId="Forecast-label"
                          id="ForecastPeriod"
                          label="Forecast Period"
                          value={measure.ForecastSettings.period}
                          onChange={(e) => {
                            updateMeasureField({
                              ForecastSettings: {
                                ...measure.ForecastSettings,
                                period: e.target.value as '24hr' | 'eom',
                              },
                            });
                          }}
                        >
                          {forecastPeriods.map((period) => {
                            return (
                              <MenuItem key={period} value={period}>
                                {period}
                              </MenuItem>
                            );
                          })}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <TextField
                        name="forecastColor"
                        type="color"
                        label="Forecast Color"
                        // onChange={handleColorChange}
                        onChange={(e) =>
                          updateMeasureField({
                            ForecastSettings: {
                              ...measure.ForecastSettings,
                              forecastColor: e.target.value,
                            },
                          })
                        }
                        value={measure.ForecastSettings.forecastColor ?? '#000000'}
                        variant="outlined"
                        fullWidth
                      />
                    </Grid>
                  </Grid>
                )}
              </>
            )}
          </FormGroup>

          {measure.overrideChartColor && (
            <FormControl fullWidth sx={{ mb: 2 }}>
              <FormLabel sx={{ mb: 1.5 }}>
                {measure.chartType === 'trend' ? 'Trend' : 'Bar'} ({' '}
                {formatMetricLabel(dbMeasureIdToName?.[measure.measureId[0]] ?? 'Measurement')})
              </FormLabel>
              <TextField
                label={`Chart Color`}
                type="color"
                value={measure.chartColor ?? '#000000'}
                onChange={(e) => updateMeasureField({ chartColor: e.target.value ?? '#000000' })}
                fullWidth
              />
            </FormControl>
          )}

          <Grid container spacing={2} mb={4}>
            {measure.showThresholdLine && (
              <>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label="Threshold Name"
                    value={measure.thresholdName || ''}
                    onChange={(e) => updateMeasureField({ thresholdName: e.target.value })}
                    fullWidth
                  />
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label="Threshold Color"
                    type="color"
                    value={measure.thresholdColor || '#ff0000'}
                    onChange={(e) => updateMeasureField({ thresholdColor: e.target.value })}
                    fullWidth
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label="Threshold Value"
                    type="number"
                    value={measure.thresholdValue ?? ''}
                    onChange={(e) =>
                      updateMeasureField({ thresholdValue: parseFloat(e.target.value) })
                    }
                    fullWidth
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Line Style</InputLabel>
                    <Select
                      value={measure.thresholdStyle || 'solid'}
                      label="Line Style"
                      onChange={(e) =>
                        updateMeasureField({
                          thresholdStyle: e.target.value as 'solid' | 'dash' | 'dot',
                        })
                      }
                    >
                      <MenuItem value="solid">Solid</MenuItem>
                      <MenuItem value="dash">Dashed</MenuItem>
                      <MenuItem value="dot">Dotted</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </>
            )}
          </Grid>
        </>
      )} */}

      {/* Asset + Measurement + Delete */}
      <AssetMeasureSelector
        subplotIndex={subplotIndex}
        measureIndex={measureIndex}
        measure={measure}
        handleSettingsChange={handleSettingsChange}
        settings={settings}
      />

      {/* Divider between asset-measure rows */}
      <Divider sx={{ my: 3 }} />
    </Box>
  );
};

export default AssetMeasureConfig;
