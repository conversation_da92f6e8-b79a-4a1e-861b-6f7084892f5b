import AddIcon from '@mui/icons-material/Add';
import {
  Box,
  Button,
  Card,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  FormLabel,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
  Typography,
} from '@mui/material';
import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import DataWidgetSettingsContainer from '~/components/common/DataWidgetSettingsContainer/DataWidgetSettingsContainer';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';
import { forecastPeriods, MultiPlotWidget } from '~/types/widgets';
import { formatMetricLabel } from '~/utils/utils';
import SubplotSettingsCard from './SubplotSettingsCard';

type Props = {
  settings: MultiPlotWidget;
  handleSettingsChange: (
    value: MultiPlotWidget | ((prev: MultiPlotWidget) => MultiPlotWidget),
  ) => void;
};

const MultiPlotSubplotsContainer: React.FC<Props> = ({ settings, handleSettingsChange }) => {
  const showRangeSlider = settings.subplots.some((subplot) => subplot.showRangeSlider);
  const metricsIdToName = useSelector(getMetricsIdToName);
  useEffect(() => {
    if (settings.mode === 'dashboard' && !settings.title.isVisible) {
      const titles = settings.subplots
        .flatMap((subplot) => subplot.assetMeasures)
        .flatMap((am) => am.measureId)
        .filter((measure) => measure.trim() !== '');
      const titlesFormatted = titles
        .map((title) => {
          if (!settings.dbMeasureIdToName[title]) return '';
          return formatMetricLabel(settings.dbMeasureIdToName[title]);
        })
        .filter((title) => title !== '')
        .join(' Vs.');
      handleSettingsChange((prevState) => ({
        ...prevState,
        title: {
          ...prevState.title,
          value: prevState.title.isVisible ? prevState.title.value : titlesFormatted,
        },
      }));
    }
    // Remove the condition that's causing the infinite loop
    // Only update the title if it's not already set
    if (settings.mode === 'template' && !settings.title.isVisible) {
      const titles = settings.subplots.flatMap((sub) =>
        sub.assetMeasures.map((measure) => measure.selectedDbMeasureId),
      );

      const titlesFormatted = titles
        .map((title) => {
          if (!metricsIdToName[title]) return '';
          return formatMetricLabel(metricsIdToName[title]);
        })
        .filter((title) => title !== '')
        .join(' Vs.');
      handleSettingsChange((prevState) => ({
        ...prevState,
        title: {
          ...prevState.title,
          value: prevState.title.isVisible ? prevState.title.value : titlesFormatted,
        },
      }));
    }
  }, [settings.mode, settings.subplots, settings.dbMeasureIdToName, settings.title]);
  // Layout & Subplots
  const handleLayoutTypeChange = (e: SelectChangeEvent<string>) => {
    handleSettingsChange({
      ...settings,
      layoutType: e.target.value as 'grid' | 'horizontal' | 'vertical',
    });
  };

  const handlePlotsPerRowChange = (e: SelectChangeEvent<number>) => {
    handleSettingsChange({
      ...settings,
      plotsPerRow: Number(e.target.value),
    });
  };

  const addSubplot = () => {
    handleSettingsChange((prev) => ({
      ...prev,
      subplots: [
        ...prev.subplots,
        {
          id: prev.subplots.length + 1,
          type: 'bar',
          showRangeSlider: false,
          showArea: false,
          showMinLine: false,
          showMaxLine: false,
          showAvgLine: false,
          showSparkline: false,
          assetMeasures: [],
        },
      ],
    }));
  };

  return (
    <DataWidgetSettingsContainer
      settings={settings}
      setSettings={handleSettingsChange}
      dataTabChildren={
        <>
          <Box component={Card} p={2} my={2}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                {settings.subplots.length === 0 && (
                  <Typography>Add Subplot(s) to render on the widget.</Typography>
                )}
              </Box>
              <Button
                sx={{ mb: settings.subplots.length !== 0 ? 2 : 0 }}
                startIcon={<AddIcon />}
                color="primary"
                variant="contained"
                onClick={addSubplot}
              >
                Add Subplot
              </Button>
            </Box>

            {settings.subplots.map((subplot, subplotIndex) => (
              <SubplotSettingsCard
                key={subplot.id}
                subplot={subplot}
                subplotIndex={subplotIndex}
                handleSettingsChange={handleSettingsChange}
                settings={settings}
              />
            ))}
          </Box>
        </>
      }
      feelTabChidren={
        <Box>
          <Box display="flex" gap={2} alignItems="center" justifyContent="center">
            <FormControl fullWidth variant="outlined">
              <InputLabel>Layout Type</InputLabel>
              <Select
                value={showRangeSlider ? 'horizontal' : settings.layoutType}
                disabled={showRangeSlider}
                onChange={handleLayoutTypeChange}
                label="Layout Type"
              >
                <MenuItem value="vertical">Vertical</MenuItem>
                <MenuItem value="horizontal">Horizontal</MenuItem>
                <MenuItem value="grid">Grid</MenuItem>
              </Select>
              {showRangeSlider && (
                <FormHelperText>
                  To access Layout options, please disable all Range sliders to prevent chart
                  overlap.
                </FormHelperText>
              )}
            </FormControl>

            {settings.layoutType === 'grid' && !showRangeSlider && (
              <FormControl fullWidth variant="outlined">
                <InputLabel>Plots Per Row</InputLabel>
                <Select
                  value={settings.plotsPerRow}
                  onChange={handlePlotsPerRowChange}
                  label="Plots Per Row"
                >
                  {Array.from(
                    { length: Math.max(settings.subplots.length, 1) },
                    (_, i) => i + 1,
                  ).map((val) => (
                    <MenuItem key={val} value={val}>
                      {val}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          </Box>
          <Box sx={{ mt: 2 }}>
            <FormControl fullWidth variant="outlined">
              <TextField
                label="Legend Y Position"
                type="number"
                inputProps={{ step: 0.01, min: -1, max: 1 }}
                value={settings.legend?.y ?? -0.25}
                onChange={(e) => {
                  const value = parseFloat(e.target.value);
                  const clamped = Math.max(-1, Math.min(1, value)); // Clamp between -1 and 1
                  handleSettingsChange((prev) => ({
                    ...prev,
                    legend: {
                      ...prev.legend,
                      y: clamped,
                    },
                  }));
                }}
                helperText="Adjust vertical position of legend (allowed range: -1 to 1)"
              />
            </FormControl>
          </Box>
          <Box sx={{ mt: 2 }}>
            {settings.subplots.map((subplot, subplotIndex) => {
              return (
                <Box key={subplotIndex} component={Card} p={1.5} mt={1}>
                  <Typography variant="h5" mt={2} mb={1}>
                    Subplot {subplotIndex + 1}
                  </Typography>
                  <Divider />
                  <Box
                    sx={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: 3,
                    }}
                  >
                    {/* Row 1: Range Slider & Area */}
                    <FormGroup row>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={subplot.showRangeSlider ?? false}
                            onChange={(e) =>
                              handleSettingsChange((prev) => ({
                                ...prev,
                                subplots: prev.subplots.map((sp, idx) =>
                                  idx === subplotIndex
                                    ? { ...sp, showRangeSlider: e.target.checked }
                                    : sp,
                                ),
                              }))
                            }
                          />
                        }
                        label={'Range Slider for Subplot' + subplot.id}
                      />

                      {subplot.assetMeasures.some((am) => am.chartType === 'trend') && (
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={subplot.showArea ?? false}
                              onChange={(e) =>
                                handleSettingsChange((prev) => ({
                                  ...prev,
                                  subplots: prev.subplots.map((sp, idx) =>
                                    idx === subplotIndex
                                      ? { ...sp, showArea: e.target.checked }
                                      : sp,
                                  ),
                                }))
                              }
                            />
                          }
                          label="Show Area"
                        />
                      )}

                      {subplot.assetMeasures.some((am) => am.chartType === 'trend') && (
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={subplot.showSparkline ?? false}
                              onChange={(e) =>
                                handleSettingsChange((prev) => ({
                                  ...prev,
                                  subplots: prev.subplots.map((sp, idx) =>
                                    idx === subplotIndex
                                      ? { ...sp, showSparkline: e.target.checked }
                                      : sp,
                                  ),
                                }))
                              }
                            />
                          }
                          label="Sparkline"
                        />
                      )}
                    </FormGroup>
                  </Box>
                  {subplot.assetMeasures.map((measure, measureIndex) => (
                    <Box key={measure.id}>
                      {measure.measureId.length > 0 && (
                        <Box my={1.5}>
                          <Typography variant="h6" mt={2} mb={1}>
                            Measurement #{measureIndex + 1}
                          </Typography>
                          <Divider />
                          <FormGroup row sx={{ mb: 2 }}>
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={measure.showMinLine ?? false}
                                  onChange={(e) =>
                                    handleSettingsChange((prev) => {
                                      const updated = JSON.parse(JSON.stringify(prev));
                                      updated.subplots[subplotIndex].assetMeasures[
                                        measureIndex
                                      ].showMinLine = e.target.checked;
                                      return updated;
                                    })
                                  }
                                />
                              }
                              label="Show Min"
                            />
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={measure.showMaxLine ?? false}
                                  onChange={(e) =>
                                    handleSettingsChange((prev) => {
                                      const updated = JSON.parse(JSON.stringify(prev));
                                      updated.subplots[subplotIndex].assetMeasures[
                                        measureIndex
                                      ].showMaxLine = e.target.checked;
                                      return updated;
                                    })
                                  }
                                />
                              }
                              label="Show Max"
                            />
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={measure.showAvgLine ?? false}
                                  onChange={(e) =>
                                    handleSettingsChange((prev) => {
                                      const updated = JSON.parse(JSON.stringify(prev));
                                      updated.subplots[subplotIndex].assetMeasures[
                                        measureIndex
                                      ].showAvgLine = e.target.checked;
                                      return updated;
                                    })
                                  }
                                />
                              }
                              label="Show Avg"
                            />

                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={measure.showThresholdLine ?? false}
                                  onChange={(e) => {
                                    handleSettingsChange((prev) => {
                                      const updated = JSON.parse(JSON.stringify(prev));
                                      updated.subplots[subplotIndex].assetMeasures[
                                        measureIndex
                                      ].showThresholdLine = e.target.checked;
                                      return updated;
                                    });
                                    // updateMeasureField({ showThresholdLine: e.target.checked })
                                  }}
                                />
                              }
                              label="Threshold Line"
                            />
                          </FormGroup>

                          {measure.showThresholdLine && (
                            <Box p={1.5}>
                              <Grid container spacing={2}>
                                {/* Row 1 */}
                                <Grid container spacing={2}>
                                  <Grid item xs={12} sm={6}>
                                    <TextField
                                      label="Threshold Name"
                                      value={measure.thresholdName || ''}
                                      onChange={(e) => {
                                        handleSettingsChange((prev) => {
                                          const updated = JSON.parse(JSON.stringify(prev));
                                          updated.subplots[subplotIndex].assetMeasures[
                                            measureIndex
                                          ].thresholdName = e.target.value;
                                          return updated;
                                        });
                                      }}
                                      fullWidth
                                    />
                                  </Grid>

                                  <Grid item xs={12} sm={6}>
                                    <TextField
                                      label="Threshold Color"
                                      type="color"
                                      value={measure.thresholdColor || '#ff0000'}
                                      onChange={(e) => {
                                        handleSettingsChange((prev) => {
                                          const updated = JSON.parse(JSON.stringify(prev));
                                          updated.subplots[subplotIndex].assetMeasures[
                                            measureIndex
                                          ].thresholdColor = e.target.value;
                                          return updated;
                                        });
                                      }}
                                      fullWidth
                                      InputLabelProps={{ shrink: true }}
                                    />
                                  </Grid>
                                </Grid>

                                {/* Row 2 */}
                                <Grid container spacing={2} sx={{ mt: 0 }}>
                                  <Grid item xs={12} sm={6}>
                                    <TextField
                                      label="Threshold Value"
                                      type="number"
                                      value={measure.thresholdValue ?? ''}
                                      onChange={(e) => {
                                        handleSettingsChange((prev) => {
                                          const updated = JSON.parse(JSON.stringify(prev));
                                          updated.subplots[subplotIndex].assetMeasures[
                                            measureIndex
                                          ].thresholdValue = parseFloat(e.target.value);
                                          return updated;
                                        });
                                      }}
                                      fullWidth
                                    />
                                  </Grid>

                                  <Grid item xs={12} sm={6}>
                                    <FormControl fullWidth>
                                      <InputLabel>Line Style</InputLabel>
                                      <Select
                                        value={measure.thresholdStyle || 'solid'}
                                        label="Line Style"
                                        onChange={(e) => {
                                          handleSettingsChange((prev) => {
                                            const updated = JSON.parse(JSON.stringify(prev));
                                            updated.subplots[subplotIndex].assetMeasures[
                                              measureIndex
                                            ].thresholdStyle = e.target.value as
                                              | 'solid'
                                              | 'dash'
                                              | 'dot';
                                            return updated;
                                          });
                                        }}
                                      >
                                        <MenuItem value="solid">Solid</MenuItem>
                                        <MenuItem value="dash">Dashed</MenuItem>
                                        <MenuItem value="dot">Dotted</MenuItem>
                                      </Select>
                                    </FormControl>
                                  </Grid>
                                </Grid>
                              </Grid>
                            </Box>
                          )}

                          {subplot.assetMeasures.length === 1 && (
                            <>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={measure.ForecastSettings?.showForecast}
                                    onChange={(e) => {
                                      handleSettingsChange((prev) => {
                                        const updated = JSON.parse(JSON.stringify(prev));
                                        updated.subplots[subplotIndex].assetMeasures[
                                          measureIndex
                                        ].ForecastSettings.showForecast = e.target.checked;
                                        return updated;
                                      });
                                    }}
                                    name="showForecast"
                                  />
                                }
                                label="Show Forecast"
                              />

                              {measure.ForecastSettings?.showForecast && (
                                <Grid container spacing={2}>
                                  <Grid item xs={12} sm={6}>
                                    <FormControl fullWidth>
                                      <InputLabel id="Forecast-label">Forecast Period</InputLabel>
                                      <Select
                                        labelId="Forecast-label"
                                        id="ForecastPeriod"
                                        label="Forecast Period"
                                        value={measure.ForecastSettings.period}
                                        onChange={(e) => {
                                          handleSettingsChange((prev) => {
                                            const updated = JSON.parse(JSON.stringify(prev));
                                            updated.subplots[subplotIndex].assetMeasures[
                                              measureIndex
                                            ].ForecastSettings.period = e.target.value as
                                              | '24hr'
                                              | 'eom';
                                            return updated;
                                          });
                                        }}
                                      >
                                        {forecastPeriods.map((period) => {
                                          return (
                                            <MenuItem key={period} value={period}>
                                              {period}
                                            </MenuItem>
                                          );
                                        })}
                                      </Select>
                                    </FormControl>
                                  </Grid>

                                  <Grid item xs={12} sm={6}>
                                    <TextField
                                      name="forecastColor"
                                      type="color"
                                      label="Forecast Color"
                                      onChange={(e) => {
                                        handleSettingsChange((prev) => {
                                          const updated = JSON.parse(JSON.stringify(prev));
                                          updated.subplots[subplotIndex].assetMeasures[
                                            measureIndex
                                          ].ForecastSettings.forecastColor = e.target.value;
                                          return updated;
                                        });
                                      }}
                                      value={measure.ForecastSettings.forecastColor ?? '#000000'}
                                      variant="outlined"
                                      fullWidth
                                    />
                                  </Grid>
                                </Grid>
                              )}
                            </>
                          )}
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={measure.overrideChartColor ?? false}
                                onChange={(e) => {
                                  handleSettingsChange((prev) => {
                                    const updated = JSON.parse(JSON.stringify(prev));
                                    updated.subplots[subplotIndex].assetMeasures[
                                      measureIndex
                                    ].overrideChartColor = e.target.checked;
                                    return updated;
                                  });
                                }}
                              />
                            }
                            label="Override Chart Color"
                          />
                          {measure.overrideChartColor && (
                            <FormControl fullWidth sx={{ mb: 2 }}>
                              <FormLabel sx={{ mb: 1.5 }}>
                                {measure.chartType === 'trend' ? 'Trend' : 'Bar'} ({' '}
                                {formatMetricLabel(
                                  settings.dbMeasureIdToName?.[measure.measureId[0]] ??
                                    'Measurement',
                                )}
                                )
                              </FormLabel>
                              <TextField
                                label={`Chart Color`}
                                type="color"
                                value={measure.chartColor ?? '#000000'}
                                onChange={(e) => {
                                  handleSettingsChange((prev) => {
                                    const updated = JSON.parse(JSON.stringify(prev));
                                    updated.subplots[subplotIndex].assetMeasures[
                                      measureIndex
                                    ].chartColor = e.target.value ?? '#000000';
                                    return updated;
                                  });
                                }}
                                fullWidth
                              />
                            </FormControl>
                          )}
                        </Box>
                      )}
                    </Box>
                  ))}
                </Box>
              );
            })}
          </Box>
        </Box>
      }
    />
  );
};

export default MultiPlotSubplotsContainer;
