import { yupResolver } from '@hookform/resolvers/yup';
import { Alert, Autocomplete, Box, Button, TextField } from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import { AlertMessage } from '~/shared/forms/types';
import { Asset, assetSchema, AssetTypeOption, NewAsset, TimeZoneDto } from '~/types/asset';
import { ControlledTextField } from '~/shared/forms/components/ControlledTextField';
import { useEffect } from 'react';

type NewAssetFormProps = {
  loading: boolean;
  assetTypesWithPath: AssetTypeOption[];
  timeZoneList: TimeZoneDto[];
  alertMessage: AlertMessage | undefined;
  onValidSubmit: (data: NewAsset) => Promise<unknown>;
  parentId?: number;
  parentAsset?: Asset;
};

export default function NewAssetForm({
  loading,
  assetTypesWithPath,
  timeZoneList,
  alertMessage,
  onValidSubmit,
  parentId,
  parentAsset,
}: NewAssetFormProps): JSX.Element {
  const { control, handleSubmit, reset, trigger, setValue, getValues } = useForm<NewAsset>({
    defaultValues: {
      parentIds: parentId ? [parentId] : [],
      description: '',
      tag: '',
      latitude: null,
      longitude: null,
    },
    resolver: yupResolver(assetSchema),
  });

  useEffect(() => {
    if (parentAsset && parentAsset.time_zone) {
      setValue('timeZone', parentAsset.time_zone);
    }
  }, [parentAsset, parentId]);
  return (
    <form
      onSubmit={handleSubmit(async (data) => {
        try {
          await onValidSubmit(data);
          reset();
        } catch (err) {}
      })}
      noValidate
    >
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <ControlledTextField
          control={control}
          fieldName="tag"
          label="Tag"
          loading={loading}
          required
        />

        <Controller
          name="assetTypeId"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <Autocomplete
              options={assetTypesWithPath}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select an asset type"
                  error={!!fieldState.error}
                  helperText={fieldState.error?.message}
                />
              )}
              onChange={(_, value) => {
                onChange(value?.value);
                console.log(value);
              }}
              onBlur={onBlur}
              value={assetTypesWithPath.find((assetType) => assetType.value === value) ?? null}
              sx={{ mt: 2, mb: 1 }}
              fullWidth
            />
          )}
        />

        <ControlledTextField
          control={control}
          fieldName="description"
          label="Description"
          loading={loading}
          multiline
        />

        <Controller
          name="timeZone"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState }) => (
            <Autocomplete
              options={timeZoneList}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select a time zone"
                  error={!!fieldState.error}
                  helperText={fieldState.error?.message}
                />
              )}
              onChange={(_, value) => onChange(value)}
              onBlur={onBlur}
              value={value ?? null}
              sx={{ mt: 2, mb: 1 }}
              fullWidth
            />
          )}
        />

        <Controller
          name="latitude"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState, formState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={(e) => {
                onChange(e.target.value === '' ? null : e.target.value);
                if (formState.isSubmitted) {
                  trigger('longitude');
                }
              }}
              onBlur={onBlur}
              type="number"
              value={value ?? ''}
              label="Latitude"
              variant="outlined"
              margin="normal"
              disabled={loading}
              fullWidth
            />
          )}
        />
        <Controller
          name="longitude"
          control={control}
          render={({ field: { onChange, onBlur, value }, fieldState, formState }) => (
            <TextField
              error={!!fieldState.error}
              helperText={fieldState.error?.message}
              onChange={(e) => {
                onChange(e.target.value === '' ? null : e.target.value);
                if (formState.isSubmitted) {
                  trigger('latitude');
                }
              }}
              onBlur={onBlur}
              type="number"
              value={value ?? ''}
              label="Longitude"
              variant="outlined"
              margin="normal"
              disabled={loading}
              fullWidth
            />
          )}
        />
        <Button
          type="submit"
          variant="contained"
          size="large"
          sx={{ mt: 2, width: 200 }}
          disabled={loading}
        >
          Submit
        </Button>
        {alertMessage && (
          <Alert severity={alertMessage.severity} sx={{ mt: 3 }}>
            {alertMessage.message}
          </Alert>
        )}
      </Box>
    </form>
  );
}
