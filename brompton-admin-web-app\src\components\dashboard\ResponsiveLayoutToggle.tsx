import DesktopWindowsIcon from '@mui/icons-material/DesktopWindows';
import PhoneAndroidIcon from '@mui/icons-material/PhoneAndroid';
import { Paper, ToggleButton, ToggleButtonGroup, Tooltip } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { getDesktopMobileMode } from '~/redux/selectors/widgetSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';

interface ResponsiveLayoutToggleProps {
  disabled?: boolean;
}

const ResponsiveLayoutToggle: React.FC<ResponsiveLayoutToggleProps> = ({ disabled = false }) => {
  const dispatch = useDispatch();
  const currentMode = useSelector(getDesktopMobileMode);

  const handleModeChange = (event: React.MouseEvent<HTMLElement>, newMode: number | null) => {
    if (newMode !== null) {
      dispatch(dashboardSlice.actions.setDesktopMobileMode(newMode));
    }
  };

  return (
    <Paper
      elevation={0}
      sx={{
        borderRadius: 2,
        px: 0.5,
        py: 0.2,
        bgcolor: 'transparent',
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <ToggleButtonGroup
        value={currentMode}
        exclusive
        onChange={handleModeChange}
        size="small"
        disabled={disabled}
        sx={{
          '& .MuiToggleButton-root': {
            width: 32,
            height: 32,
            border: 'none',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            '& svg': {
              fontSize: 20,
            },
            // Remove global hover on selected
            '&.Mui-selected:hover': {
              backgroundColor: 'unset',
            },
          },
        }}
      >
        <Tooltip title="Desktop Layout" arrow>
          <ToggleButton
            value={0}
            aria-label="Desktop layout"
            sx={{
              bgcolor: currentMode === 0 ? 'primary.main' : 'transparent',
              color: currentMode === 0 ? 'primary.contrastText' : 'text.secondary',
              '&:hover': {
                bgcolor: currentMode === 0 ? 'primary.dark' : 'action.hover',
              },
            }}
          >
            <DesktopWindowsIcon />
          </ToggleButton>
        </Tooltip>

        <Tooltip title="Mobile Layout" arrow>
          <ToggleButton
            value={1}
            aria-label="Mobile layout"
            sx={{
              bgcolor: currentMode === 1 ? 'primary.main' : 'transparent',
              color: currentMode === 1 ? 'primary.contrastText' : 'text.secondary',
              '&:hover': {
                bgcolor: currentMode === 1 ? 'primary.dark' : 'action.hover',
              },
            }}
          >
            <PhoneAndroidIcon />
          </ToggleButton>
        </Tooltip>
      </ToggleButtonGroup>
    </Paper>
  );
};

export default ResponsiveLayoutToggle;
