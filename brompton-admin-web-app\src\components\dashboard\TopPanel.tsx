import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import ShareIcon from '@mui/icons-material/Share';
import {
  Alert,
  Box,
  Button,
  Divider,
  FormControlLabel,
  IconButton,
  List,
  ListItem,
  Popover,
  Snackbar,
  TextField,
  Tooltip,
  Typography,
  useTheme,
} from '@mui/material';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import { ChangeEvent, useEffect, useState } from 'react';
import 'react-date-range/dist/styles.css'; // main style file
import 'react-date-range/dist/theme/default.css'; // theme css file
import { useDispatch, useSelector } from 'react-redux';
import { useCustomTimeInterval } from '~/hooks/useCustomTimeInterval';
import { useHasPowerUserAccess } from '~/hooks/useHasPowerUserAccess';
import { useCreateDashboardMutation, useEditDashboardMutation } from '~/redux/api/dashboardApi';
import { getEndDate, getStartDate } from '~/redux/selectors/chartSelectors';
import { getActiveCustomer, getCustomerId } from '~/redux/selectors/customerSelectors';
import {
  getCurrentAssetTemplate,
  getCurrentAssetType,
  getCurrentDashboardId,
  getCurrentDashboardTitle,
  getDashboardCrumb,
  getDashboardTempalteTopPanel,
  getDashboardTemplateId,
  getDashboardTemplateName,
  getIsLeftPanelOpen,
  getMainPanel,
  isDashboardDirty,
  selectDashboardState,
} from '~/redux/selectors/dashboardSelectors';
import {
  getAssetTz,
  getDashboardTemplateEndDate,
  getDashboardTemplateStartDate,
  getGlobalTimeRangeType,
  getRefreshInterval,
} from '~/redux/selectors/topPanleSelectors';
import { getSelectedNodeIds } from '~/redux/selectors/treeSelectors';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';
import {
  getDeletedWidgets,
  getLastWidgetId,
  getWidgets,
  getWidgetsLayout,
} from '~/redux/selectors/widgetSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { Customer } from '~/types/customers';
import {
  DashboardCollection,
  DashboardState,
  SamplePeriodOptions,
  TimeRangeOptions,
} from '~/types/dashboard';
import { Widget } from '~/types/widgets';
import { getPreviousDate } from '~/utils/utils';
import IOSSwitch from '../Switch/Switch';
import Share from '../common/Share/Share';
import DashboardTitleDialog from './DashboardTitleDialog'; // Import the DashboardTitleDialog component
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
//@ts-ignore
import { DateRangePicker } from 'react-date-time-range-picker';
import { CustomError } from '~/errors/CustomerErrorResponse';
import useExportDashboardTemplate from '~/hooks/useExportDashboadTemplate';
import useTopPanelHelper from '~/hooks/useTopPanelHelper';
import ImportTemplate from '~/pages/dashboard-template/import';
import {
  useCreateDashboardTemplateMutation,
  useGetDashboardTemplatesQuery,
  useUpdateDashboardTemplateMutation,
} from '~/redux/api/dashboardTemplate';
import { useGetMeasuresByCustomersMutation } from '~/redux/api/measuresApi';
import CustomDialog from '../common/CustomDialog';
import DashboardTemplateTitleDialog from './DashboardTemplateTItleDialog';
import ImportDashboard from './ImportDashboard';
import TemplateCustomerAssetSelection from './TemplateCustomerAssetSelection';
export const TimeRangeRefreshOptions = [
  { label: 'Manual', interval: -1 },
  { label: '5 minute', interval: 300000 },
  { label: '10 minute', interval: 600000 },
  { label: '15 minute', interval: 900000 },
  { label: '30 minute', interval: 1800000 },
  { label: '1 hour', interval: 3600000 },
];
type TopPanelProps = {
  dashboardList: DashboardCollection;
  isLoadingDashboards: boolean;
  customerList: Customer[];
  isCustomerListLoading: boolean;
  isCustomerListSuccess: boolean;
  isSamplePeriod: boolean;
  isRefreshInterval: boolean;
};
export function TopPanel({
  dashboardList,
  isLoadingDashboards,
  isCustomerListSuccess,
  isCustomerListLoading,
  customerList,
  isSamplePeriod = true,
  isRefreshInterval = true,
}: TopPanelProps) {
  const dispatch = useDispatch();
  const router = useRouter();
  const isNotDashboardTemplate = router.pathname !== '/dashboard-template';
  const startDate = useSelector(getStartDate);
  const endDate = useSelector(getEndDate);
  const dashboardTemplateName = useSelector(getDashboardTemplateName);
  const dashboardTemplateId = useSelector(getDashboardTemplateId);
  const { setChartStartDate, setChartEndDate } = dashboardSlice.actions;
  const selectedTimeRange = useSelector(getGlobalTimeRangeType);
  const activeCustomer = useSelector(getActiveCustomer);
  const timeRefreshInterval = useSelector(getRefreshInterval);
  const assetTz = useSelector(getAssetTz);
  const customerId = useSelector(getCustomerId);
  const dashboardState = useSelector(selectDashboardState);
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();
  const currentDashboardId = useSelector(getCurrentDashboardId);
  const asset_template = useSelector(getCurrentAssetTemplate);
  const crumbs = useSelector(getDashboardCrumb);

  const [open, setOpen] = useState<boolean>(false);
  const [templateSaveOpen, setTemplateSaveOpen] = useState<boolean>(false);
  const [saveTemplateAsDashboard, setSaveTemplateAsDashboard] = useState<boolean>(false);
  const mainPanel = useSelector(getMainPanel);
  const selectedNodeIds = useSelector(getSelectedNodeIds);
  const currentDashboardTitle = useSelector(getCurrentDashboardTitle);
  const dateTimeFormat = useSelector(getDateTimeFormat);
  const hasPowerUserAccess = useHasPowerUserAccess();
  const [showShare, setShowShare] = useState<boolean>(false);
  const theme = useTheme();
  const isDashboardStateDirty = useSelector(isDashboardDirty);
  const widgets = useSelector(getWidgets);
  const deleteWidgets = useSelector(getDeletedWidgets);
  const widgetLayout = useSelector(getWidgetsLayout);
  const lastWidgetId = useSelector(getLastWidgetId);
  const [showPicker, setShowPicker] = useState(false);
  const isLeftPanelOpen = useSelector(getIsLeftPanelOpen);
  const dashboardTemplateTopPanel = useSelector(getDashboardTempalteTopPanel);
  const dashboardTemplateStartDate = useSelector(getDashboardTemplateStartDate);
  const dashboardTemplateEndDate = useSelector(getDashboardTemplateEndDate);
  const [assetType, setAssetType] = useState<number>(0);
  const [saveAsGlobal, setSaveAsGlobal] = useState<boolean>(false); // New state for checkbox
  const [assetTemplate, setAssetTemplate] = useState<number>(0);
  const [showImport, setShowImport] = useState<boolean>(false);
  const assetTypeTemplate = useSelector(getCurrentAssetType);
  const { exportTemplate, loading, message, setMessage } = useExportDashboardTemplate();
  const [warningForGlobalMonthly, setWarningForGlobalMonthly] = useState<{
    error: boolean;
    widgetTypes: string[];
  }>({
    error: false,
    widgetTypes: [],
  });
  const [warningForNone, setWarningForNone] = useState<{
    error: boolean;
    widgetTypes: string[];
  }>({
    error: false,
    widgetTypes: [],
  });
  // const mobile = useMediaQuery('@media (max-width: 600px)');
  // useEffect(() => {
  //   if (mobile) {
  //     dispatch(dashboardSlice.actions.setAssetTz(false));
  //   }
  // }, [mobile]);

  const { data: dashboardTemplates, refetch } = useGetDashboardTemplatesQuery(
    { assetTypeId: assetTypeTemplate },
    { skip: !assetTypeTemplate || assetTypeTemplate <= 0, refetchOnMountOrArgChange: true },
  );
  const [selectionTimeRange, setSelectionTimeRange] = useState<{
    startDate: Date;
    endDate: Date;
    key: string;
    selectedIndex: number;
  }>({
    startDate: new Date(),
    endDate: new Date(),
    key: 'selection',
    selectedIndex: 0,
  });
  const [templateTitleFromDashboard, setTemplateTitleFromDashboard] = useState<string>('');
  const { exportDashboard } = useTopPanelHelper({
    showErrorAlert,
    showSuccessAlert,
  });
  useEffect(() => {
    if (isNotDashboardTemplate) {
      setSelectionTimeRange({
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        key: 'selection',
        selectedIndex: selectedTimeRange,
      });
    }
  }, [startDate, endDate, selectedTimeRange]);

  useEffect(() => {
    if (!isNotDashboardTemplate) {
      setSelectionTimeRange({
        startDate: new Date(dashboardTemplateStartDate),
        endDate: new Date(dashboardTemplateEndDate),
        key: 'selection',
        selectedIndex: dashboardTemplateTopPanel.timeRangeType,
      });
    }
  }, [
    dashboardTemplateStartDate,
    dashboardTemplateEndDate,
    dashboardTemplateTopPanel.timeRangeType,
  ]);
  useCustomTimeInterval(() => {
    dispatch(dashboardSlice.actions.refreshTimeRange());
  }, timeRefreshInterval);

  const handleTimeRefreshChange = (event: SelectChangeEvent<number>) => {
    const value = event.target.value as number;
    if (isNotDashboardTemplate) {
      dispatch(dashboardSlice.actions.setRefreshTimeInterval(value));
      dispatch(dashboardSlice.actions.setIsDirty(true));
    } else {
      dispatch(dashboardSlice.actions.setTemplateRefreshInterval(value));
    }
  };
  const [createDashboard, { data: dashboard, isSuccess, isError, isLoading }] =
    useCreateDashboardMutation();
  const [
    getMeasuresByCustomer,
    {
      data: measurements,
      isSuccess: measurementsSuccess,
      isError: isMeasurementsError,
      error: measurementsError,
    },
  ] = useGetMeasuresByCustomersMutation();
  const [updateDashboard, { data: editDashboard, isSuccess: isEditSuccess, isError: isEditError }] =
    useEditDashboardMutation();
  const [
    createDashboardtemplate,
    { isError: createDashboardTempalteError, isSuccess: createDashboardSuccess, data: template },
  ] = useCreateDashboardTemplateMutation();
  const [
    updateDashboardTemplate,
    { isError: isupdatetemplateError, isSuccess: isTemplateUpdateSuccess },
  ] = useUpdateDashboardTemplateMutation();
  useEffect(() => {
    if (isMeasurementsError && measurementsError) {
      const error = measurementsError as CustomError;
      showErrorAlert(error.data.message ?? 'Error during fetching measurments data');
    }
    if (measurements && measurementsSuccess) {
      const measurements_metrics = measurements.items.reduce((acc, item) => {
        acc[item.id.toString()] = item.metric_id !== null ? item.metric_id.toString() : null;
        return acc;
      }, {} as Record<string, string | null>);
      const metric_name = measurements.items.reduce((acc, item) => {
        if (item?.metric_id !== undefined && item.metric_id !== null) {
          acc[item.metric_id.toString()] = item.metricName ? item.metricName.toString() : null;
        }
        return acc;
      }, {} as Record<string, string | null>);
      const widgets = dashboardState.widget.widgets.map((widget) => {
        const { type, settings } = widget;
        switch (type) {
          case 'kpi-bar-chart':
          case 'kpi-color-box':
          case 'kpi-percentage':
          case 'kpi-value-indicator':
          case 'kpi-sparkline':
          case 'image-stats':
          case 'stats': {
            let metric: string | null = null;
            settings.assetMeasure.measureId.forEach((measurement) => {
              if (measurements_metrics[measurement]) {
                metric = measurements_metrics[measurement];
              }
            });
            return {
              ...widget,
              settings: {
                ...settings,
                selectedDbMeasureId: metric ?? '',
                mode: 'template',
                assetMeasure: {
                  assetId: '',
                  measureId: [],
                },
              },
            };
          }
          case 'image': {
            const measures = settings.assetMeasure
              .flatMap((assetMeas) => assetMeas.measureId)
              .filter((measure) => measure.trim() !== '')
              .map((measurement) => measurements_metrics[measurement] ?? '')
              .filter((measure) => measure !== '');
            const dbMeasureIdToName = Object.fromEntries(
              measures
                .map((measure) => [measure, metric_name[measure]]) // Map key-value pairs
                .filter(([key, value]) => value !== undefined && value !== null), // Ensure valid entries
            );

            const measureIdToImageTextDetails = Object.fromEntries(
              measures
                .filter((measure) => measure && metric_name[measure])
                .map((measure) => [
                  measure,
                  {
                    label: metric_name[measure],
                    unit: '',
                    id: measure,
                    positionX: 100,
                    positionY: 100,
                    value: '',
                    dashboard: null,
                    openDashboardInNewTab: false,
                    dashboardOrTemplate: 'template',
                    assetOrAssetType: null,
                  },
                ]), // Map key-value pairs // Ensure valid entries
            );
            return {
              ...widget,
              settings: {
                ...settings,
                mode: 'template',
                selectedTitles: measures,
                dbMeasureIdToName,
                measureIdToImageTextDetails,
                assetMeasure: [],
              },
            };
          }
          case 'real-time': {
            let metric: string | null = null;
            settings.assetMeasure.measureId.forEach((measurement) => {
              if (measurements_metrics[measurement]) {
                metric = measurements_metrics[measurement];
              }
            });
            return {
              ...widget,
              settings: {
                ...settings,
                selectedDbMeasureId: metric,
                assetMeasure: {
                  assetId: '',
                  measureId: [],
                },
              },
            };
          }
          case 'map': {
            const updatedMarkers = settings.markers.map((marker) => {
              const measures = marker.assetMeasures
                .flatMap((assetMeas) => assetMeas.measureId)
                .filter((measure) => measure.trim() !== '')
                .map((measurement) => measurements_metrics[measurement] ?? '')
                .filter((measure) => measure !== '');
              const updatedLabelAndUnits = Object.fromEntries(
                measures.map((measurement) => {
                  const measurement_metric = measurements_metrics[measurement] ?? measurement;
                  return [
                    measurement_metric,
                    marker.labelAndUnits[measurement] || { label: '', unit: '', value: '' },
                  ];
                }),
              );
              return {
                ...marker,
                selectedTitles: measures,
                assetMeasures: [],
                labelAndUnits: updatedLabelAndUnits,
              };
            });
            return {
              ...widget,
              settings: {
                ...settings,
                mode: 'template',
                markers: updatedMarkers,
              },
            };
          }
          case 'table':
          case 'kpi-table': {
            const measures = settings.assetMeasure
              .flatMap((assetMeas) => assetMeas.measureId)
              .filter((measure) => measure.trim() !== '')
              .map((measurement) => measurements_metrics[measurement] ?? '')
              .filter((measure) => measure !== '');
            return {
              ...widget,
              settings: {
                ...settings,
                mode: 'template',
                selectedTitles: measures,
                selectedDbMeasureIds: measures,
                assetMeasure: [],
              },
            };
          }
          case 'alert-widget': {
            const measures = settings.assetMeasure
              .flatMap((assetMeas) => assetMeas.measureId)
              .filter((measure) => measure.trim() !== '')
              .map((measurement) => measurements_metrics[measurement] ?? '')
              .filter((measure) => measure !== '');
            return {
              ...widget,
              settings: {
                ...settings,
                mode: 'template',
                selectedTitles: measures,
                assetMeasure: [],
              },
            };
          }
          case 'dashboard-widget': {
            const measures = settings.assetMeasure
              .flatMap((assetMeas) => assetMeas.measureId)
              .filter((measure) => measure.trim() !== '')
              .map((measurement) => measurements_metrics[measurement] ?? '')
              .filter((measure) => measure !== '');
            return {
              ...widget,
              settings: {
                ...settings,
                mode: 'template',
                selectedTitles: measures,
                assetMeasure: [],
              },
            };
          }
          case 'chart': {
            const chartType = settings.chartType;
            switch (chartType) {
              case 'bar':
              case 'scatter': {
                const measures = settings.settings.assetMeasure
                  .flatMap((assetMeas) => assetMeas.measureId)
                  .filter((measure) => measure.trim() !== '')
                  .map((measurement) => measurements_metrics[measurement] ?? '')
                  .filter((measure) => measure !== '');
                //measurements_metrics
                const dbMeasureIdToNames: Record<string, string> = measures.reduce(
                  (acc, measure) => {
                    const value = metric_name[measure];
                    if (value !== null) {
                      // Only set if value exists
                      acc[measure] = value;
                    }
                    return acc;
                  },
                  {} as Record<string, string>,
                );
                return {
                  ...widget,
                  settings: {
                    ...settings,
                    settings: {
                      ...settings.settings,
                      mode: 'template',
                      selectedTitles: measures,
                      dbMeasureIdToName: dbMeasureIdToNames,
                      assetMeasure: [],
                    },
                  },
                };
              }
              case 'bullet':
              case 'heatmap':
              case 'indicator': {
                let metric: string | null = null;
                settings.settings.assetMeasure.measureId.forEach((measurement) => {
                  if (measurements_metrics[measurement]) {
                    metric = measurements_metrics[measurement];
                  }
                });
                return {
                  ...widget,
                  settings: {
                    ...settings,
                    settings: {
                      ...settings.settings,
                      mode: 'template',
                      selectedDbMeasureId: metric ?? '',
                      assetMeasure: {
                        assetId: '',
                        measureId: [],
                      },
                    },
                  },
                };
              }
              case 'sankey': {
                const updatedLabel = settings.settings.Label.map((label) => {
                  if (label.sourceFrom === 'Calculated') {
                    return label;
                  }
                  return {
                    ...label,
                    sourceName: label.sourceAssetMeasure?.measureId
                      ? measurements_metrics[label.sourceAssetMeasure.measureId] ?? ''
                      : '',
                    sourceLabel: label.sourceAssetMeasure?.measureId
                      ? metric_name[
                          measurements_metrics[label.sourceAssetMeasure.measureId] ?? ''
                        ] ?? ''
                      : '',
                    sourceAssetMeasure: {
                      assetId: '',
                      measureId: '',
                    },
                  };
                });
                return {
                  ...widget,
                  settings: {
                    ...settings,
                    settings: {
                      ...settings.settings,
                      mode: 'template',
                      Label: updatedLabel,
                    },
                  },
                };
              }
            }
          }
          default:
            return widget;
        }
      });
      createDashboardtemplate({
        title: templateTitleFromDashboard,
        asset_template: assetTemplate,
        data: JSON.stringify(
          {
            widget: {
              ...dashboardState.widget,
              widgets: widgets as DashboardState['widget']['widgets'],
            },
            topPanel: dashboardState.topPanel,
            chart: dashboardState.chart,
          },
          null,
        ),
        is_global: saveAsGlobal,
      });
      setAssetTemplate(0);
    }
  }, [measurements, isMeasurementsError, measurementsSuccess]);
  useEffect(() => {
    if (isTemplateUpdateSuccess) {
      showSuccessAlert('Dashboard Template updated');
    }
    if (isupdatetemplateError) {
      showErrorAlert('Error updating dashboard template');
    }
  }, [isupdatetemplateError, isTemplateUpdateSuccess]);
  useEffect(() => {
    if (createDashboardSuccess) {
      if (template) {
        dispatch(dashboardSlice.actions.setTemplateId(template.id ?? 0));
        dispatch(
          dashboardSlice.actions.setAssetType(Number(template?.asset_template?.assetType?.id ?? 0)),
        );
        dispatch(
          dashboardSlice.actions.setAssetTemplate(Number(template?.asset_template?.id ?? 0)),
        );
        dispatch(dashboardSlice.actions.setTemplateName(template?.title ?? ''));
      }
      showSuccessAlert('Dashboard Template created');
      if (router.pathname !== '/dashboard-template') {
        router.push('/dashboard-template');
      }
    }
    if (createDashboardTempalteError) {
      showErrorAlert('Error creating dashboard');
    }
  }, [createDashboardSuccess, createDashboardTempalteError, router]);
  useEffect(() => {
    if (isEditSuccess) {
      showSuccessAlert('Dashboard updated');
      dispatch(dashboardSlice.actions.setIsDirty(false));
      dispatch(dashboardSlice.actions.setIsDirty(false));
      dispatch(dashboardSlice.actions.setWidgetDirty(false));
    }
    if (isEditError) {
      showErrorAlert('Error updating dashboard');
    }
  }, [isEditSuccess, editDashboard, isEditError]);

  useEffect(() => {
    if (isSuccess && dashboard) {
      showSuccessAlert('Dashboard saved');
      dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
      router.push(`/customer/${customerId}/dashboard/${dashboard.id}`);
      dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
      dispatch(dashboardSlice.actions.setIsDirty(false));
      dispatch(dashboardSlice.actions.setIsDirty(false));
      dispatch(dashboardSlice.actions.setWidgetDirty(false));
      crumbs.map((_, index) => {
        dispatch(dashboardSlice.actions.removeDashboardCrumb(index));
      });
      dispatch(
        dashboardSlice.actions.setDashboardCrumb({
          dashboardId: dashboard.id,
          title: dashboard.title,
        }),
      );
      setShowImport(false);
    }
    if (isError) {
      showErrorAlert('Error saving dashboard');
    }
  }, [isSuccess, dashboard, isError]);
  const handleSamplePeriodChange = (event: SelectChangeEvent<number>) => {
    const index = event.target.value as number;
    if (isNotDashboardTemplate) {
      const widgetTypes: string[] = [];
      const STDCharts = widgets.filter(
        (widget) => widget.type === 'chart' && widget.settings.settings?.aggBy === 5,
      );
      widgetTypes.push(
        ...STDCharts.map((widget) =>
          'chartType' in widget.settings
            ? widget.type + '-' + widget.settings.chartType
            : widget.type,
        ),
      );
      const otherWidgets = widgets.filter(
        (widget) =>
          widget.type !== 'chart' && 'aggBy' in widget.settings && widget.settings.aggBy === 5,
      );
      widgetTypes.push(...otherWidgets.map((widget) => widget.type));
      if (index === 14 && (STDCharts.length > 0 || otherWidgets.length > 0)) {
        setWarningForGlobalMonthly({
          error: true,
          widgetTypes: widgetTypes,
        });
      } else {
        setWarningForGlobalMonthly({
          error: false,
          widgetTypes: [],
        });
        dispatch(dashboardSlice.actions.setSamplePeriod(index));
        dispatch(dashboardSlice.actions.setIsDirty(true));
      }
    } else {
      dispatch(dashboardSlice.actions.setTemplateSamplePeriod(index));
    }
  };
  const handleAssetTzChange = (event: ChangeEvent<HTMLInputElement>, checked: boolean) => {
    if (isNotDashboardTemplate) {
      dispatch(dashboardSlice.actions.setAssetTz(checked));
      dispatch(dashboardSlice.actions.setIsDirty(true));
    } else {
      dispatch(dashboardSlice.actions.setTemplateAssetTz(checked));
    }
  };
  const handleShare = () => {
    setShowShare(!showShare);
  };
  const handleSelection = (ranges: any) => {
    if (ranges.selection.selectedIndex === -1) {
      ranges.selection.selectedIndex = 0;
    }
    setSelectionTimeRange(ranges.selection);
  };
  const onSave = () => {
    const widgetTypes: string[] = [];
    const ids: Set<string> = new Set();
    const timeDifferenceHours =
      (selectionTimeRange.endDate.getTime() - selectionTimeRange.startDate.getTime()) /
      (1000 * 60 * 60);
    if (timeDifferenceHours > 6) {
      const diagramWidgets = widgets.filter(
        (widget) =>
          widget.type === 'Diagram' &&
          (widget.settings.elementVariable.length > 0 ||
            Object.keys(widget.settings.elementIdVariabels).length > 0),
      );
      const charts = widgets.filter(
        (widget) => widget.type === 'chart' && widget.settings.settings?.aggBy === 0,
      );
      const otherWidgets = widgets.filter(
        (widget) =>
          widget.type !== 'chart' && 'aggBy' in widget.settings && widget.settings.aggBy === 0,
      );

      const overRiddenCharts = charts.filter(
        (chart) =>
          chart.type === 'chart' &&
          chart.settings.settings.aggBy === 0 &&
          chart.settings.settings.overrideGlobalSettings &&
          chart.settings.settings.startDate &&
          chart.settings.settings.endDate &&
          (chart.settings.settings.endDate - chart.settings.settings.startDate) / (1000 * 60 * 60) >
            6,
      );
      overRiddenCharts.forEach((chart) => {
        if (!ids.has(chart.id) && chart.type === 'chart') {
          widgetTypes.push(`${chart.type} - ${chart.settings.chartType}`);
          ids.add(chart.id); // Ensure it doesn't get added again
        }
      });
      diagramWidgets.forEach((widget) => {
        if (widget.type !== 'Diagram') return;

        // Iterate over elementIdVariabels, which is a Record<string, elementVariable[]>
        Object.keys(widget.settings.elementIdVariabels ?? {}).forEach((key) => {
          const elementVariables = widget.settings.elementIdVariabels[key];

          // Iterate over the elementVariable array
          elementVariables.forEach((variable) => {
            // Validate or process each elementVariable object here
            const isNoneInvalid = (() => {
              if (variable?.aggBy !== 0 || variable?.aggBy === undefined) return false; // Skip if aggBy is not 0 or undefined
              const start = widget.settings.startDate; // Use widget's startDate for validation
              const end = widget.settings.endDate; // Use widget's endDate for validation
              const timeDifferenceHours = (end - start) / (1000 * 60 * 60); // Calculate time difference in hours
              return timeDifferenceHours > 6; // Invalid if the time difference exceeds 6 hours
            })();

            // If the variable is invalid, add the widget type to the warning list
            if (isNoneInvalid && !ids.has(widget.id)) {
              widgetTypes.push(`${widget.type}`);
              ids.add(widget.id); // Ensure it doesn't get added again
            }
          });
        });
      });

      const otherWidgetsList = otherWidgets.filter(
        (widget) =>
          widget.type !== 'chart' &&
          'aggBy' in widget.settings &&
          widget.settings.overrideGlobalSettings &&
          widget.settings.startDate &&
          widget.settings.endDate &&
          (widget.settings.endDate - widget.settings.startDate) / (1000 * 60 * 60) > 6,
      );
      otherWidgetsList.forEach((widget) => {
        if (!ids.has(widget.id)) {
          widgetTypes.push(widget.type);
          ids.add(widget.id);
        }
      });
      const nonOverriddenAggByZeroWidgets = otherWidgets.filter(
        (widget) =>
          'aggBy' in widget.settings &&
          widget.settings.aggBy === 0 &&
          !widget.settings.overrideGlobalSettings, // Ensure overrideGlobalSettings is **false** or missing
      );

      // Add non-overridden widgets if not already in ids
      nonOverriddenAggByZeroWidgets.forEach((widget) => {
        if (!ids.has(widget.id)) {
          widgetTypes.push(widget.type);
          ids.add(widget.id);
        }
      });
      const nonOverriddenCharts = charts.filter(
        (chart) =>
          chart.type === 'chart' &&
          chart.settings.settings.aggBy === 0 &&
          !chart.settings.settings.overrideGlobalSettings, // Ensure overrideGlobalSettings is **false** or missing
      );
      // ✅ Add non-overridden charts if not already in ids
      nonOverriddenCharts.forEach((chart) => {
        if (!ids.has(chart.id) && chart.type === 'chart') {
          widgetTypes.push(`${chart.type} - ${chart.settings.chartType}`);
          ids.add(chart.id);
        }
      });
      if (ids.size > 0 && widgetTypes.length > 0) {
        setWarningForNone({
          error: true,
          widgetTypes: widgetTypes,
        });
        return;
      }
    }
    if (isNotDashboardTemplate) {
      dispatch(dashboardSlice.actions.setTimeRangeType(selectionTimeRange.selectedIndex));
      dispatch(setChartStartDate(selectionTimeRange.startDate));
      dispatch(setChartEndDate(selectionTimeRange.endDate));
      dispatch(dashboardSlice.actions.setIsDirty(true));
    } else {
      dispatch(dashboardSlice.actions.setTemplateChartStartDate(selectionTimeRange.startDate));
      dispatch(dashboardSlice.actions.setTemplateChartEndDate(selectionTimeRange.endDate));
      dispatch(dashboardSlice.actions.setTemplateTimeRangeType(selectionTimeRange.selectedIndex));
    }
    setShowPicker(false);
  };
  const onCancel = () => {
    setShowPicker(false);
    setSelectionTimeRange({
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      key: 'selection',
      selectedIndex: selectedTimeRange,
    });
  };
  return (
    <>
      <AlertSnackbar {...snackbarState} />
      {!isLoadingDashboards && activeCustomer && currentDashboardId !== -1 && (
        <>
          <Popover
            open={showPicker}
            onClose={() => onCancel()}
            sx={{
              height: '650px',
              position: 'absolute',
              zIndex: 1300,
              top: 70,
              left: isLeftPanelOpen ? 120 : 120,
            }}
          >
            <DateRangePicker
              months={2}
              ranges={[selectionTimeRange]}
              onChange={handleSelection}
              // maxDate={assetTz ? new Date(new Date().getTime() + 24 * 60 * 60 * 1000) : undefined}
              direction="horizontal"
              showTime
              color={theme.palette.primary.main}
              rangeColors={[theme.palette.primary.main]}
              staticRanges={[
                ...TimeRangeOptions.map((option, index) => {
                  return {
                    label: option.label,
                    key: option.label,
                    range: () => ({
                      startDate: new Date(getPreviousDate(option.serverValue)),
                      endDate: new Date(),
                    }),
                    isSelected() {
                      return selectionTimeRange.selectedIndex === index;
                    },
                  };
                }),
              ]}
            />
            <Divider />
            <Box p={2} gap={2} display={'flex'} justifyContent={'end'}>
              <Button color="primary" variant="outlined" onClick={() => onCancel()}>
                Cancel
              </Button>
              <Button color="primary" variant="contained" onClick={onSave}>
                Apply
              </Button>
            </Box>
          </Popover>
          <Box display="flex">
            <FormControl
              size="small"
              sx={{
                width: selectionTimeRange.selectedIndex === 0 ? '400px' : 200,
                cursor: 'pointer',
                backgroundColor: theme.palette.background.paper,
                height: (theme) => theme.spacing(5),
              }}
            >
              <TextField
                size="small"
                label="Time Range"
                fullWidth
                onClick={() => setShowPicker(true)}
                contentEditable={false}
                sx={{ cursor: 'pointer' }}
                value={
                  isNotDashboardTemplate
                    ? selectionTimeRange.selectedIndex !== 0
                      ? TimeRangeOptions[selectionTimeRange.selectedIndex].label
                      : dayjs(startDate).format(dateTimeFormat) +
                        ' To ' +
                        dayjs(endDate).format(dateTimeFormat)
                    : dayjs(dashboardTemplateStartDate).format(dateTimeFormat) +
                      ' To ' +
                      dayjs(dashboardTemplateEndDate).format(dateTimeFormat)
                }
              />
            </FormControl>
          </Box>
          {isSamplePeriod && (
            <Box display="flex">
              <FormControl
                sx={{
                  width: '163px',
                  backgroundColor: theme.palette.background.paper,
                  height: (theme) => theme.spacing(5),
                }}
              >
                <InputLabel id="sample-period-select-label">Sample Period</InputLabel>
                <Select
                  labelId="sample-period-select-label"
                  id="sample-period-range-select"
                  value={
                    isNotDashboardTemplate
                      ? dashboardState.topPanel.samplePeriod
                      : dashboardTemplateTopPanel.samplePeriod
                  }
                  label="Sample Period"
                  onChange={handleSamplePeriodChange}
                  sx={{
                    height: (theme) => theme.spacing(5),
                  }}
                >
                  {SamplePeriodOptions.map((option, index) =>
                    index > 0 ? (
                      <MenuItem key={option.value} value={index}>
                        {option.label}
                      </MenuItem>
                    ) : null,
                  )}
                </Select>
              </FormControl>
            </Box>
          )}

          {isRefreshInterval && (
            <Box display="flex">
              <FormControl
                sx={{
                  width: '163px',
                  backgroundColor: theme.palette.background.paper,
                  height: (theme) => theme.spacing(5),
                }}
              >
                <InputLabel id="time-range-select-label">Refresh Interval</InputLabel>
                <Select
                  labelId="time-range-select-label"
                  id="time-range-select"
                  value={
                    isNotDashboardTemplate
                      ? timeRefreshInterval
                      : dashboardTemplateTopPanel.refreshInterval
                  }
                  disabled={
                    isNotDashboardTemplate
                      ? selectedTimeRange === 0
                      : dashboardTemplateTopPanel.timeRangeType === 0
                  }
                  label="Refresh Interval"
                  onChange={handleTimeRefreshChange}
                  sx={{ height: (theme) => theme.spacing(5) }}
                >
                  {TimeRangeRefreshOptions.map(({ label, interval }) => (
                    <MenuItem key={interval} value={interval}>
                      {label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          )}

          <Box sx={{ ml: !(isSamplePeriod && isRefreshInterval) ? 3 : 0 }}>
            <FormControlLabel
              sx={{ height: (theme) => theme.spacing(5), width: 'max-content' }}
              control={
                <IOSSwitch
                  sx={{ m: 1 }}
                  onChange={handleAssetTzChange}
                  checked={isNotDashboardTemplate ? assetTz : dashboardTemplateTopPanel.assetTz}
                />
              }
              label="Asset Timezone"
            />
          </Box>
        </>
      )}
      <Snackbar
        open={!!message}
        autoHideDuration={3000}
        onClose={() => setMessage(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity={message?.type}>{message?.text}</Alert>
      </Snackbar>
      {mainPanel === 'chart' &&
      currentDashboardId !== -1 &&
      (activeCustomer !== null || selectedNodeIds.length > 1) ? (
        <Box
          sx={{
            display: 'flex',
            width: '100%',
            justifyContent: 'end',
            alignSelf: 'center',
          }}
        >
          {router.pathname !== '/dashboard-template' ? (
            <>
              <Tooltip title="Share" placement="top" sx={{ mr: 1 }}>
                <IconButton onClick={handleShare}>
                  <ShareIcon color="primary" />
                </IconButton>
              </Tooltip>

              {hasPowerUserAccess && (
                <>
                  <Tooltip title="Import" placement="top" sx={{ mr: 1 }}>
                    <IconButton
                      onClick={() => {
                        setShowImport(true);
                      }}
                    >
                      <FileDownloadIcon color="primary" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Export" placement="top" sx={{ mr: 1 }}>
                    <IconButton onClick={exportDashboard}>
                      <FileUploadIcon color="primary" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Save" placement="top">
                    <IconButton
                      onClick={() => {
                        if (!isLoading) {
                          setOpen(!open);
                        }
                      }}
                      // disabled={
                      //   !(
                      //     widgets.filter(
                      //       (widget) => widget.type !== 'chart' && widget.settings.isDirty,
                      //     ).length > 0 ||
                      //     deleteWidgets.length > 0 ||
                      //     widgets.filter(
                      //       (widget) =>
                      //         widget.type === 'chart' && widget.settings?.settings?.isDirty,
                      //     ).length > 0 ||
                      //     isDashboardStateDirty
                      //   )
                      // }
                    >
                      <SaveAsIcon
                        sx={{
                          color: theme.palette.primary.main,
                          // color: (() => {
                          //   const hasDirtyWidgets =
                          //     widgets.some(
                          //       (widget) => widget.type !== 'chart' && widget.settings.isDirty,
                          //     ) ||
                          //     widgets.some(
                          //       (widget) =>
                          //         widget.type === 'chart' && widget.settings?.settings?.isDirty,
                          //     );

                          //   return !hasDirtyWidgets &&
                          //     deleteWidgets.length === 0 &&
                          //     !isDashboardStateDirty
                          //     ? 'gray' // Color when disabled
                          //     : 'primary.main'; // Color when enabled
                          // })(),
                        }}
                      />
                    </IconButton>
                  </Tooltip>
                </>
              )}
            </>
          ) : (
            <>
              <ImportTemplate />
              <Tooltip title="Export Template">
                <Button color="primary" onClick={() => exportTemplate(dashboardTemplateId)}>
                  <FileDownloadIcon />
                </Button>
              </Tooltip>
              <Button
                variant="contained"
                disabled={
                  asset_template === null || asset_template === undefined || asset_template === 0
                }
                onClick={() => {
                  setTemplateSaveOpen(true);
                }}
              >
                Save
              </Button>
            </>
          )}
        </Box>
      ) : null}
      <TemplateCustomerAssetSelection
        saveTemplateAsDashboard={saveTemplateAsDashboard}
        setSaveTemplateAsDashboard={setSaveTemplateAsDashboard}
      />
      <ImportDashboard
        open={showImport}
        setShowImport={setShowImport}
        createDashboard={createDashboard}
      />
      <DashboardTemplateTitleDialog
        open={templateSaveOpen}
        onClose={() => setTemplateSaveOpen(!templateSaveOpen)}
        initialTitle={dashboardTemplateName}
        flow={dashboardTemplateId <= 0 ? 'save' : 'update'}
        onSave={async ({ title, description, save_as_global_dashboard_template }, flow) => {
          const widgets = JSON.parse(JSON.stringify(dashboardState)).widget.widgets.map(
            (widget: Widget) => {
              if (widget.type === 'chart') {
                widget.settings.settings.isDirty = false;
              } else {
                widget.settings.isDirty = false;
              }
              return widget;
            },
          );
          const templateWidgets: DashboardState['widget'] = {
            widgets: widgets,
            deleteWidgets: deleteWidgets,
            widgetLayout: widgetLayout,
            lastWidgetId: lastWidgetId,
          };
          const templateData: DashboardState['template'] = {
            assetTemplate: asset_template ?? 0,
            idToName: {},
            assetType: 0,
            metrics: [],
            templateId: dashboardTemplateId,
            templateName: dashboardTemplateName,
            topPanel: {
              timeRangeType: dashboardTemplateTopPanel.timeRangeType,
              refreshInterval: dashboardTemplateTopPanel.refreshInterval,
              samplePeriod: dashboardTemplateTopPanel.samplePeriod,
              assetTz: dashboardTemplateTopPanel.assetTz,
            },
            chart: {
              startDate: dashboardTemplateStartDate,
              endDate: dashboardTemplateEndDate,
            },
          };
          if (dashboardTemplateId <= 0) {
            await createDashboardtemplate({
              asset_template: asset_template,
              title: title,
              data: JSON.stringify(
                {
                  // ...dashboardState,
                  widget: templateWidgets,
                  topPanel: dashboardTemplateTopPanel,
                  chart: templateData.chart,
                  // template: templateData,
                },
                null,
              ),
              is_global: save_as_global_dashboard_template,
            });
            refetch();
            // create dashboard template
          } else {
            if (flow === 'save') {
              await createDashboardtemplate({
                asset_template: asset_template,
                title: title,
                data: JSON.stringify(
                  {
                    // ...dashboardState,
                    widget: templateWidgets,
                    topPanel: dashboardTemplateTopPanel,
                    chart: templateData.chart,
                    // template: templateData,
                  },
                  null,
                ),
                is_global: save_as_global_dashboard_template,
              });
              await refetch();
              //create dashboard template
            } else {
              await updateDashboardTemplate({
                title: title,
                data: JSON.stringify(
                  {
                    widget: templateWidgets,
                    topPanel: dashboardTemplateTopPanel,
                    chart: templateData.chart,
                  },
                  null,
                ),
                id: dashboardTemplateId,
                asset_template: asset_template,
                is_global: save_as_global_dashboard_template ?? false,
              });
              await refetch();
            }
          }
        }}
      />
      <DashboardTitleDialog
        open={open}
        assetType={assetType}
        setAssetType={setAssetType}
        saveAsGlobal={saveAsGlobal}
        setSaveAsGlobal={setSaveAsGlobal}
        onClose={() => {
          setOpen(!open);
          setTemplateTitleFromDashboard('');
          setAssetType(0);
        }}
        assetTemplate={assetTemplate}
        setAssetTemplate={setAssetTemplate}
        initialTitle={currentDashboardTitle}
        flow={currentDashboardId <= 0 ? 'save' : 'update'}
        onSave={({ title, description }, flow) => {
          const widgets = JSON.parse(JSON.stringify(dashboardState)).widget.widgets.map(
            (widget: Widget) => {
              if (widget.type === 'chart') {
                widget.settings.settings.isDirty = false;
              } else {
                widget.settings.isDirty = false;
              }
              return widget;
            },
          );
          const dashboardStateToSave: DashboardState = {
            ...dashboardState,
            dashboardTitle: title,
            isDirty: false,
            topPanel: {
              ...dashboardState.topPanel,
            },
            widget: {
              ...dashboardState.widget,
              deleteWidgets: [],
              widgets: widgets,
            },
            tree: {
              ...dashboardState.tree,
            },
          };
          if (flow !== 'template') {
            if (currentDashboardId <= 0) {
              createDashboard({
                title: title,
                data: JSON.stringify(dashboardStateToSave, null),
                customerId: customerId,
                description: description,
              });
            } else {
              if (flow === 'save') {
                createDashboard({
                  title: title,
                  data: JSON.stringify(dashboardStateToSave, null),
                  customerId: customerId,
                  description: description,
                });
              } else {
                updateDashboard({
                  title: title,
                  data: dashboardStateToSave,
                  id: currentDashboardId,
                  description: description,
                  customerId: activeCustomer?.id ?? 0,
                });
                dispatch(dashboardSlice.actions.setCurrentDashboardTitle(title));
              }
            }
          }
          if (flow === 'template') {
            setTemplateTitleFromDashboard(title);
            const measurements: Set<string> = new Set();
            dashboardState.widget.widgets.forEach(({ type, settings }) => {
              switch (type) {
                case 'kpi-bar-chart':
                case 'kpi-color-box':
                case 'kpi-percentage':
                case 'kpi-value-indicator':
                case 'kpi-sparkline':
                case 'image-stats':
                case 'stats': {
                  if (
                    settings?.assetMeasure?.measureId?.length > 0 &&
                    settings.assetMeasure.measureId.some((metric) => metric !== '')
                  ) {
                    settings.assetMeasure.measureId.forEach((measure) => {
                      measurements.add(measure);
                    });
                  }
                  break;
                }
                case 'table':
                case 'kpi-table': {
                  const measures = settings?.assetMeasure
                    ?.flatMap((assetMeas) => assetMeas.measureId)
                    .filter((measure) => measure.trim() !== '');
                  measures?.forEach((measurement) => {
                    measurements.add(measurement);
                  });
                  break;
                }
                case 'chart': {
                  const chartType = settings.chartType;
                  switch (chartType) {
                    case 'bar':
                    case 'scatter': {
                      const measures = settings.settings.assetMeasure
                        ?.flatMap((assetMeas) => assetMeas.measureId)
                        .filter((measure) => measure.trim() !== '');
                      measures?.forEach((measurement) => {
                        measurements.add(measurement);
                      });
                      break;
                    }
                    case 'sankey': {
                      break;
                    }
                    case 'bullet':
                    case 'heatmap':
                    case 'indicator': {
                      if (
                        settings.settings.assetMeasure.assetId !== '' &&
                        settings.settings.assetMeasure.measureId.some((measure) => measure !== '')
                      ) {
                        settings.settings.assetMeasure.measureId.forEach((measure) => {
                          measurements.add(measure);
                        });
                      }
                    }
                    default:
                      break;
                  }
                  break;
                }
                case 'map': {
                  settings.markers.forEach((marker) => {
                    const measures = marker.assetMeasures
                      ?.flatMap((assetMeas) => assetMeas.measureId)
                      .filter((measure) => measure.trim() !== '');
                    measures?.forEach((measurement) => {
                      measurements.add(measurement);
                    });
                  });
                  break;
                }
                default:
                  break;
              }
            });
            getMeasuresByCustomer({
              customerId: activeCustomer?.id ?? 0,
              measurements: Array.from(measurements).map(Number),
            });
          }
        }}
      />
      <CustomDialog
        open={warningForGlobalMonthly.error}
        onClose={() =>
          setWarningForGlobalMonthly({
            error: false,
            widgetTypes: [],
          })
        }
        content={
          <Box>
            <Typography variant="body1" sx={{ mb: 1 }}>
              Reset the aggregate option in widget settings where <strong>STD.P</strong> is
              selected.
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
              Affected Widgets:
            </Typography>
            {warningForGlobalMonthly.widgetTypes.length > 0 ? (
              <List sx={{ pl: 2 }}>
                {warningForGlobalMonthly.widgetTypes.map((name, index) => (
                  <ListItem key={index} sx={{ py: 0.5, px: 0 }}>
                    🔹 {name}
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body2" color="textSecondary">
                No widgets found.
              </Typography>
            )}
          </Box>
        }
        dialogActions={
          <Button
            variant="contained"
            onClick={() =>
              setWarningForGlobalMonthly({
                error: false,
                widgetTypes: [],
              })
            }
          >
            Ok
          </Button>
        }
        title={
          <Box display="flex" alignItems="center" gap={1}>
            <ErrorOutlineIcon color="error" fontSize="large" />
            <Typography color="error" variant="h6">
              <strong>Action Required</strong>
            </Typography>
          </Box>
        }
        showCloseOnTop
      />
      <CustomDialog
        open={warningForNone.error}
        onClose={() =>
          setWarningForNone({
            error: false,
            widgetTypes: [],
          })
        }
        content={
          <Box>
            <Typography variant="body1" sx={{ mb: 1 }}>
              Time range is more than <strong>6 hours</strong> and is not allowed for widgets where
              the aggregate is <strong>None</strong>.
            </Typography>

            <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
              Affected Widgets:
            </Typography>
            {warningForNone.widgetTypes.length > 0 ? (
              <List sx={{ pl: 2 }}>
                {warningForNone.widgetTypes.map((name, index) => (
                  <ListItem key={index} sx={{ py: 0.5, px: 0 }}>
                    🔹 {name}
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body2" color="textSecondary">
                No widgets found.
              </Typography>
            )}
          </Box>
        }
        dialogActions={
          <Button
            variant="contained"
            onClick={() =>
              setWarningForNone({
                error: false,
                widgetTypes: [],
              })
            }
          >
            Ok
          </Button>
        }
        title={
          <Box display="flex" alignItems="center" gap={1}>
            <ErrorOutlineIcon color="error" fontSize="large" />
            <Typography color="error" variant="h6">
              <strong>Action Required</strong>
            </Typography>
          </Box>
        }
        showCloseOnTop
      />
      <Share open={showShare} onClose={handleShare} />
    </>
  );
}
