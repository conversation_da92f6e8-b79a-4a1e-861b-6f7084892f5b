import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import ResponsiveLayoutToggle from '../ResponsiveLayoutToggle';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';

// Mock theme
const theme = createTheme();

// Create a mock store with initial state
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      dashboard: dashboardSlice.reducer,
    },
    preloadedState: {
      dashboard: {
        ...dashboardSlice.getInitialState(),
        desktopMobile: 0, // Default to desktop
        responsiveLayouts: {
          desktop: { widgetLayout: [] },
          mobile: { widgetLayout: [] }
        },
        ...initialState,
      },
    },
  });
};

const renderWithProviders = (component: React.ReactElement, initialState = {}) => {
  const store = createMockStore(initialState);
  return {
    ...render(
      <Provider store={store}>
        <ThemeProvider theme={theme}>
          {component}
        </ThemeProvider>
      </Provider>
    ),
    store,
  };
};

describe('ResponsiveLayoutToggle', () => {
  it('renders desktop and mobile toggle buttons', () => {
    renderWithProviders(<ResponsiveLayoutToggle />);
    
    expect(screen.getByLabelText('desktop layout')).toBeInTheDocument();
    expect(screen.getByLabelText('mobile layout')).toBeInTheDocument();
  });

  it('shows desktop mode as selected by default', () => {
    renderWithProviders(<ResponsiveLayoutToggle />);
    
    const desktopButton = screen.getByLabelText('desktop layout');
    const mobileButton = screen.getByLabelText('mobile layout');
    
    expect(desktopButton).toHaveClass('Mui-selected');
    expect(mobileButton).not.toHaveClass('Mui-selected');
  });

  it('shows mobile mode as selected when desktopMobile is 1', () => {
    renderWithProviders(<ResponsiveLayoutToggle />, { desktopMobile: 1 });
    
    const desktopButton = screen.getByLabelText('desktop layout');
    const mobileButton = screen.getByLabelText('mobile layout');
    
    expect(desktopButton).not.toHaveClass('Mui-selected');
    expect(mobileButton).toHaveClass('Mui-selected');
  });

  it('dispatches setDesktopMobileMode action when mobile button is clicked', () => {
    const { store } = renderWithProviders(<ResponsiveLayoutToggle />);
    
    const mobileButton = screen.getByLabelText('mobile layout');
    fireEvent.click(mobileButton);
    
    const state = store.getState();
    expect(state.dashboard.desktopMobile).toBe(1);
  });

  it('dispatches setDesktopMobileMode action when desktop button is clicked from mobile mode', () => {
    const { store } = renderWithProviders(<ResponsiveLayoutToggle />, { desktopMobile: 1 });
    
    const desktopButton = screen.getByLabelText('desktop layout');
    fireEvent.click(desktopButton);
    
    const state = store.getState();
    expect(state.dashboard.desktopMobile).toBe(0);
  });

  it('is disabled when disabled prop is true', () => {
    renderWithProviders(<ResponsiveLayoutToggle disabled={true} />);
    
    const desktopButton = screen.getByLabelText('desktop layout');
    const mobileButton = screen.getByLabelText('mobile layout');
    
    expect(desktopButton).toBeDisabled();
    expect(mobileButton).toBeDisabled();
  });

  it('shows tooltip on hover', async () => {
    renderWithProviders(<ResponsiveLayoutToggle />);
    
    const toggleGroup = screen.getByRole('group', { name: 'layout mode' });
    fireEvent.mouseEnter(toggleGroup);
    
    // Note: Testing tooltip visibility might require additional setup
    // depending on your testing environment and MUI version
  });

  it('does not dispatch action when clicking the already selected button', () => {
    const { store } = renderWithProviders(<ResponsiveLayoutToggle />);
    
    const initialState = store.getState();
    const desktopButton = screen.getByLabelText('desktop layout');
    
    // Click the already selected desktop button
    fireEvent.click(desktopButton);
    
    const newState = store.getState();
    expect(newState.dashboard.desktopMobile).toBe(initialState.dashboard.desktopMobile);
  });

  it('has correct ARIA attributes', () => {
    renderWithProviders(<ResponsiveLayoutToggle />);
    
    const toggleGroup = screen.getByRole('group', { name: 'layout mode' });
    expect(toggleGroup).toBeInTheDocument();
    
    const desktopButton = screen.getByLabelText('desktop layout');
    const mobileButton = screen.getByLabelText('mobile layout');
    
    expect(desktopButton).toHaveAttribute('aria-label', 'desktop layout');
    expect(mobileButton).toHaveAttribute('aria-label', 'mobile layout');
  });
});
