// npm test -- --testPathPattern=TopPanel.test.tsx --verbose --watchAll=false
// npm test -- --watch ResponsiveLayoutToggle.test.ts
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import { TopPanel, TimeRangeRefreshOptions } from '../TopPanel';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { Customer } from '~/types/customers';
import { DashboardCollection } from '~/types/dashboard';

// Mock external dependencies
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

jest.mock('~/hooks/useCustomTimeInterval', () => ({
  useCustomTimeInterval: jest.fn(),
}));

jest.mock('~/hooks/useHasPowerUserAccess', () => ({
  useHasPowerUserAccess: jest.fn(),
}));

jest.mock('~/hooks/useExportDashboadTemplate', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    exportTemplate: jest.fn(),
    loading: false,
    message: null,
    setMessage: jest.fn(),
  })),
}));

jest.mock('~/hooks/useTopPanelHelper', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    exportDashboard: jest.fn(),
  })),
}));

jest.mock('~/shared/snackbars/snackbar-hooks', () => ({
  useSnackbar: jest.fn(() => [
    { open: false, message: '', severity: 'success' },
    jest.fn(),
    jest.fn(),
  ]),
}));

// Mock API hooks
jest.mock('~/redux/api/dashboardApi', () => ({
  useCreateDashboardMutation: jest.fn(() => [
    jest.fn(),
    { data: null, isSuccess: false, isError: false, isLoading: false },
  ]),
  useEditDashboardMutation: jest.fn(() => [
    jest.fn(),
    { data: null, isSuccess: false, isError: false },
  ]),
}));

jest.mock('~/redux/api/dashboardTemplate', () => ({
  useCreateDashboardTemplateMutation: jest.fn(() => [
    jest.fn(),
    { isError: false, isSuccess: false, data: null },
  ]),
  useGetDashboardTemplatesQuery: jest.fn(() => ({
    data: [],
    refetch: jest.fn(),
  })),
  useUpdateDashboardTemplateMutation: jest.fn(() => [
    jest.fn(),
    { isError: false, isSuccess: false },
  ]),
}));

jest.mock('~/redux/api/measuresApi', () => ({
  useGetMeasuresByCustomersMutation: jest.fn(() => [
    jest.fn(),
    {
      data: null,
      isSuccess: false,
      isError: false,
      error: null,
    },
  ]),
}));

// Mock child components
jest.mock('../ResponsiveLayoutToggle', () => {
  return function MockResponsiveLayoutToggle({ disabled }: { disabled?: boolean }) {
    return <div data-testid="responsive-layout-toggle" data-disabled={disabled} />;
  };
});

jest.mock('../DashboardTitleDialog', () => {
  return function MockDashboardTitleDialog({ open, onClose, onSave }: any) {
    return open ? (
      <div data-testid="dashboard-title-dialog">
        <button onClick={() => onSave({ title: 'Test Dashboard', description: 'Test' }, 'save')}>
          Save
        </button>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null;
  };
});

jest.mock('../DashboardTemplateTItleDialog', () => {
  return function MockDashboardTemplateTitleDialog({ open, onClose, onSave }: any) {
    return open ? (
      <div data-testid="dashboard-template-title-dialog">
        <button
          onClick={() =>
            onSave({ title: 'Test Template', save_as_global_dashboard_template: false }, 'save')
          }
        >
          Save Template
        </button>
        <button onClick={onClose}>Close Template</button>
      </div>
    ) : null;
  };
});

jest.mock('../ImportDashboard', () => {
  return function MockImportDashboard({ open, setShowImport }: any) {
    return open ? (
      <div data-testid="import-dashboard">
        <button onClick={() => setShowImport(false)}>Close Import</button>
      </div>
    ) : null;
  };
});

jest.mock('../TemplateCustomerAssetSelection', () => {
  return function MockTemplateCustomerAssetSelection() {
    return <div data-testid="template-customer-asset-selection" />;
  };
});

jest.mock('~/pages/dashboard-template/import', () => {
  return function MockImportTemplate() {
    return <div data-testid="import-template" />;
  };
});

// Mock date range picker
jest.mock('react-date-time-range-picker', () => ({
  DateRangePicker: function MockDateRangePicker({ onChange }: any) {
    return (
      <div data-testid="date-range-picker">
        <button
          onClick={() =>
            onChange({
              selection: {
                startDate: new Date('2023-01-01'),
                endDate: new Date('2023-01-02'),
                key: 'selection',
                selectedIndex: 1,
              },
            })
          }
        >
          Select Date Range
        </button>
      </div>
    );
  },
}));

const mockRouter = {
  pathname: '/customer/1/dashboard/1',
  push: jest.fn(),
  query: {},
  asPath: '',
  route: '',
  back: jest.fn(),
  beforePopState: jest.fn(),
  prefetch: jest.fn(),
  reload: jest.fn(),
  replace: jest.fn(),
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  defaultLocale: 'en',
  domainLocales: [],
  isPreview: false,
};

const mockCustomer: Customer = {
  id: 1,
  name: 'Test Customer',
  nameId: 'test-customer',
  address: 'Test Address',
  logo: 'data:image/png;base64,test',
};

const mockDashboardList: DashboardCollection = {
  items: [
    {
      id: 1,
      title: 'Test Dashboard',
      data: '{}',
      default: false,
      favorite: false,
      dashboardTemplate: null,
      asset: null,
    },
  ],
  total: 1,
};

const defaultProps = {
  dashboardList: mockDashboardList,
  isLoadingDashboards: false,
  customerList: [mockCustomer],
  isCustomerListLoading: false,
  isCustomerListSuccess: true,
  isSamplePeriod: true,
  isRefreshInterval: true,
};

// Helper function to create a test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      dashboard: dashboardSlice.reducer,
    },
    preloadedState: {
      dashboard: {
        ...dashboardSlice.getInitialState(),
        currentDashboardId: 1,
        dashboardTitle: 'Test Dashboard',
        userDetails: null,
        userToken: null,
        customer: mockCustomer,
        enableZoom: false,
        userPreferences: {
          DATE_FORMAT: 'DD-MM-YYYY HH:mm:ss',
          DEFAULT_CUSTOMER: '',
          THOUSAND_SEPARATOR: 'enabled',
        },
        dashboardCrumb: [],
        mainPanel: 'chart',
        isLeftPanelOpen: true,
        isDirty: false,
        kisok: false,
        fullScreen: false,
        rightSideBar: false,
        dateFormat: 0,
        newMeasureId: 0,
        rightSideBarActiveTab: '/icons/alerts.svg',
        topPanel: {
          isVisible: true,
          timeRangeType: 6,
          refreshInterval: -1,
          samplePeriod: 2,
          assetTz: true,
        },
        tree: {
          currentSelectedNodeId: '1',
          selectedViewMeasureId: '1',
          selectedNodeIds: ['1'],
          expandedNodeIds: ['1'],
          dbMeasureIdToName: {},
        },
        chart: {
          startDate: new Date('2023-01-01').getTime(),
          endDate: new Date('2023-01-02').getTime(),
        },
        widget: {
          widgets: [],
          widgetLayout: [],
          deleteWidgets: [],
          lastWidgetId: 0,
        },
        desktopMobile: 0,
        responsiveLayouts: {
          desktop: { widgetLayout: [], widgets: [] },
          mobile: { widgetLayout: [], widgets: [] },
        },
        template: {
          assetTemplate: 0,
          templateId: 0,
          templateName: '',
          assetType: 0,
          metrics: [],
          idToName: {},
          topPanel: {
            timeRangeType: 6,
            refreshInterval: -1,
            samplePeriod: 2,
            assetTz: true,
          },
          chart: {
            startDate: new Date().getTime(),
            endDate: new Date().getTime(),
          },
        },
        metricMeasurements: {},
        ...initialState,
      },
    },
  });
};

const theme = createTheme();

const renderWithProviders = (ui: React.ReactElement, { store = createTestStore() } = {}) => {
  return render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>{ui}</ThemeProvider>
    </Provider>,
  );
};

describe('TopPanel Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (require('~/hooks/useHasPowerUserAccess').useHasPowerUserAccess as jest.Mock).mockReturnValue(
      true,
    );
  });

  describe('Component Rendering', () => {
    it('should render without crashing', () => {
      renderWithProviders(<TopPanel {...defaultProps} />);
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
    });

    it('should render time range field', () => {
      renderWithProviders(<TopPanel {...defaultProps} />);
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
    });

    it('should render sample period dropdown when isSamplePeriod is true', () => {
      renderWithProviders(<TopPanel {...defaultProps} isSamplePeriod={true} />);
      expect(screen.getByLabelText('Sample Period')).toBeInTheDocument();
    });

    it('should not render sample period dropdown when isSamplePeriod is false', () => {
      renderWithProviders(<TopPanel {...defaultProps} isSamplePeriod={false} />);
      expect(screen.queryByLabelText('Sample Period')).not.toBeInTheDocument();
    });

    it('should render refresh interval dropdown when isRefreshInterval is true', () => {
      renderWithProviders(<TopPanel {...defaultProps} isRefreshInterval={true} />);
      expect(screen.getByLabelText('Refresh Interval')).toBeInTheDocument();
    });

    it('should not render refresh interval dropdown when isRefreshInterval is false', () => {
      renderWithProviders(<TopPanel {...defaultProps} isRefreshInterval={false} />);
      expect(screen.queryByLabelText('Refresh Interval')).not.toBeInTheDocument();
    });

    it('should render asset timezone switch', () => {
      renderWithProviders(<TopPanel {...defaultProps} />);
      expect(screen.getByLabelText('Asset Timezone')).toBeInTheDocument();
    });
  });

  describe('Action Buttons', () => {
    it('should render basic controls when component loads', () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      // These are the main controls that should always be present
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
      expect(screen.getByLabelText('Sample Period')).toBeInTheDocument();
      expect(screen.getByLabelText('Refresh Interval')).toBeInTheDocument();
      expect(screen.getByLabelText('Asset Timezone')).toBeInTheDocument();
    });

    it('should render responsive layout toggle when conditions are met', () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      // The responsive layout toggle should be present
      expect(screen.getByTestId('responsive-layout-toggle')).toBeInTheDocument();
    });

    it('should handle component interactions without crashing', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      // Test that basic interactions work
      const timeRangeField = screen.getByLabelText('Time Range');
      expect(timeRangeField).toBeInTheDocument();

      // Click on time range field should not crash
      await userEvent.click(timeRangeField);
      expect(timeRangeField).toBeInTheDocument();
    });

    it('should render different content based on power user access', () => {
      // Test with power user access
      (require('~/hooks/useHasPowerUserAccess').useHasPowerUserAccess as jest.Mock).mockReturnValue(
        true,
      );
      const { unmount } = renderWithProviders(<TopPanel {...defaultProps} />);

      // Basic controls should be present
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();

      // Unmount and test without power user access
      unmount();
      (require('~/hooks/useHasPowerUserAccess').useHasPowerUserAccess as jest.Mock).mockReturnValue(
        false,
      );
      renderWithProviders(<TopPanel {...defaultProps} />);

      // Basic controls should still be present
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
    });

    it('should handle user interactions gracefully', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      // Test asset timezone switch
      const assetTzSwitch = screen.getByLabelText('Asset Timezone');
      await userEvent.click(assetTzSwitch);

      // Should not crash and element should still be present
      expect(assetTzSwitch).toBeInTheDocument();
    });
  });

  describe('Time Range Functionality', () => {
    it('should open date picker when time range field is clicked', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const timeRangeField = screen.getByLabelText('Time Range');
      await userEvent.click(timeRangeField);

      await waitFor(() => {
        expect(screen.getByTestId('date-range-picker')).toBeInTheDocument();
      });
    });

    it('should display correct time range format', () => {
      const store = createTestStore({
        chart: {
          startDate: new Date('2023-01-01').getTime(),
          endDate: new Date('2023-01-02').getTime(),
        },
        topPanel: {
          timeRangeType: 0, // Custom time range
          refreshInterval: -1,
          samplePeriod: 2,
          assetTz: true,
        },
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      const timeRangeField = screen.getByLabelText('Time Range');
      // The component formats dates as "DD-MM-YYYY HH:mm:ss To DD-MM-YYYY HH:mm:ss"
      expect(timeRangeField).toHaveValue('01-01-2023 05:30:00 To 02-01-2023 05:30:00');
    });

    it('should handle date range selection', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const timeRangeField = screen.getByLabelText('Time Range');
      await userEvent.click(timeRangeField);

      await waitFor(() => {
        expect(screen.getByTestId('date-range-picker')).toBeInTheDocument();
      });

      const selectButton = screen.getByText('Select Date Range');
      await userEvent.click(selectButton);

      const applyButton = screen.getByText('Apply');
      await userEvent.click(applyButton);

      // Date picker should close after applying
      await waitFor(() => {
        expect(screen.queryByTestId('date-range-picker')).not.toBeInTheDocument();
      });
    });

    it('should handle date range cancellation', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const timeRangeField = screen.getByLabelText('Time Range');
      await userEvent.click(timeRangeField);

      await waitFor(() => {
        expect(screen.getByTestId('date-range-picker')).toBeInTheDocument();
      });

      const cancelButton = screen.getByText('Cancel');
      await userEvent.click(cancelButton);

      // Date picker should close after cancelling
      await waitFor(() => {
        expect(screen.queryByTestId('date-range-picker')).not.toBeInTheDocument();
      });
    });
  });

  describe('Sample Period Functionality', () => {
    it('should handle sample period change', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const samplePeriodSelect = screen.getByLabelText('Sample Period');

      // Open the select dropdown
      fireEvent.mouseDown(samplePeriodSelect);

      await waitFor(() => {
        // Look for a specific sample period option
        const options = screen.getAllByRole('option');
        expect(options.length).toBeGreaterThan(0);

        // Click on the first option that's not already selected
        const firstOption = options.find((option) => !option.classList.contains('Mui-selected'));
        if (firstOption) {
          fireEvent.click(firstOption);
        }
      });
    });

    it('should display current sample period value', () => {
      const store = createTestStore({
        topPanel: {
          samplePeriod: 2,
          timeRangeType: 6,
          refreshInterval: -1,
          assetTz: true,
        },
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      const samplePeriodSelect = screen.getByLabelText('Sample Period');
      expect(samplePeriodSelect).toBeInTheDocument();
    });
  });

  describe('Refresh Interval Functionality', () => {
    it('should handle refresh interval change', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const refreshIntervalSelect = screen.getByLabelText('Refresh Interval');

      // Open the select dropdown
      fireEvent.mouseDown(refreshIntervalSelect);

      await waitFor(() => {
        // Look for a refresh interval option
        const option = screen.getByRole('option', { name: '5 minute' });
        if (option) {
          fireEvent.click(option);
        }
      });
    });

    it('should disable refresh interval when custom time range is selected', () => {
      const store = createTestStore({
        topPanel: {
          timeRangeType: 0, // Custom time range
          refreshInterval: -1,
          samplePeriod: 2,
          assetTz: true,
        },
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      const refreshIntervalSelect = screen.getByLabelText('Refresh Interval');
      // Check if the select has the disabled attribute or aria-disabled
      expect(refreshIntervalSelect).toHaveAttribute('aria-disabled', 'true');
    });

    it('should enable refresh interval when predefined time range is selected', () => {
      const store = createTestStore({
        topPanel: {
          timeRangeType: 6, // Predefined time range
          refreshInterval: -1,
          samplePeriod: 2,
          assetTz: true,
        },
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      const refreshIntervalSelect = screen.getByLabelText('Refresh Interval');
      expect(refreshIntervalSelect).not.toBeDisabled();
    });
  });

  describe('Asset Timezone Switch', () => {
    it('should handle asset timezone toggle', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const assetTzSwitch = screen.getByLabelText('Asset Timezone');
      await userEvent.click(assetTzSwitch);

      // The switch should be interactive
      expect(assetTzSwitch).toBeInTheDocument();
    });

    it('should reflect current asset timezone state', () => {
      const store = createTestStore({
        topPanel: {
          assetTz: false,
          timeRangeType: 6,
          refreshInterval: -1,
          samplePeriod: 2,
        },
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      const assetTzSwitch = screen.getByLabelText('Asset Timezone');
      expect(assetTzSwitch).toBeInTheDocument();
    });
  });

  describe('Dashboard Template Mode', () => {
    beforeEach(() => {
      (useRouter as jest.Mock).mockReturnValue({
        ...mockRouter,
        pathname: '/dashboard-template',
      });
    });

    it('should render template customer asset selection in template mode', () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      // Template mode should show the customer asset selection component
      expect(screen.getByTestId('template-customer-asset-selection')).toBeInTheDocument();
    });

    it('should render basic controls in template mode', () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      // Basic controls should still be present in template mode
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
      expect(screen.getByLabelText('Sample Period')).toBeInTheDocument();
      expect(screen.getByLabelText('Refresh Interval')).toBeInTheDocument();
      expect(screen.getByLabelText('Asset Timezone')).toBeInTheDocument();
    });

    it('should handle template mode without crashing', () => {
      const store = createTestStore({
        template: {
          assetTemplate: 1,
          templateId: 0,
          templateName: '',
          assetType: 0,
          metrics: [],
          idToName: {},
          topPanel: {
            timeRangeType: 6,
            refreshInterval: -1,
            samplePeriod: 2,
            assetTz: true,
          },
          chart: {
            startDate: new Date().getTime(),
            endDate: new Date().getTime(),
          },
        },
      });

      expect(() => {
        renderWithProviders(<TopPanel {...defaultProps} />, { store });
      }).not.toThrow();

      // Should render template components
      expect(screen.getByTestId('template-customer-asset-selection')).toBeInTheDocument();
    });

    it('should handle template mode with different asset template values', () => {
      const store = createTestStore({
        template: {
          assetTemplate: 0, // No asset template selected
          templateId: 0,
          templateName: '',
          assetType: 0,
          metrics: [],
          idToName: {},
          topPanel: {
            timeRangeType: 6,
            refreshInterval: -1,
            samplePeriod: 2,
            assetTz: true,
          },
          chart: {
            startDate: new Date().getTime(),
            endDate: new Date().getTime(),
          },
        },
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      // Should still render template components even with no asset template
      expect(screen.getByTestId('template-customer-asset-selection')).toBeInTheDocument();
    });
  });

  describe('Dialog Interactions', () => {
    it('should handle date picker dialog interactions', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const timeRangeField = screen.getByLabelText('Time Range');
      await userEvent.click(timeRangeField);

      await waitFor(() => {
        expect(screen.getByTestId('date-range-picker')).toBeInTheDocument();
      });

      // Test date selection
      const selectButton = screen.getByText('Select Date Range');
      await userEvent.click(selectButton);

      // Apply the selection
      const applyButton = screen.getByText('Apply');
      await userEvent.click(applyButton);

      // Dialog should close after applying
      await waitFor(() => {
        expect(screen.queryByTestId('date-range-picker')).not.toBeInTheDocument();
      });
    });

    it('should handle date picker cancellation', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const timeRangeField = screen.getByLabelText('Time Range');
      await userEvent.click(timeRangeField);

      await waitFor(() => {
        expect(screen.getByTestId('date-range-picker')).toBeInTheDocument();
      });

      const cancelButton = screen.getByText('Cancel');
      await userEvent.click(cancelButton);

      await waitFor(() => {
        expect(screen.queryByTestId('date-range-picker')).not.toBeInTheDocument();
      });
    });

    it('should handle component state changes without dialogs', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      // Test sample period dropdown
      const samplePeriodSelect = screen.getByLabelText('Sample Period');
      fireEvent.mouseDown(samplePeriodSelect);

      // Should not crash when interacting with dropdowns
      expect(samplePeriodSelect).toBeInTheDocument();

      // Test refresh interval dropdown
      const refreshIntervalSelect = screen.getByLabelText('Refresh Interval');
      fireEvent.mouseDown(refreshIntervalSelect);

      expect(refreshIntervalSelect).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('should handle loading dashboards state gracefully', () => {
      renderWithProviders(<TopPanel {...defaultProps} isLoadingDashboards={true} />);

      // Component should render without crashing during loading
      expect(screen.getByTestId('responsive-layout-toggle')).toBeInTheDocument();
    });

    it('should handle loading state transitions', () => {
      const { unmount } = renderWithProviders(
        <TopPanel {...defaultProps} isLoadingDashboards={true} />,
      );

      // Should render during loading
      expect(screen.getByTestId('responsive-layout-toggle')).toBeInTheDocument();

      // Unmount and render after loading completes
      unmount();
      renderWithProviders(<TopPanel {...defaultProps} isLoadingDashboards={false} />);
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
    });

    it('should handle customer list loading states', () => {
      renderWithProviders(<TopPanel {...defaultProps} isCustomerListLoading={true} />);

      // Component should handle customer loading state
      expect(screen.getByTestId('responsive-layout-toggle')).toBeInTheDocument();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle missing customer data', () => {
      const store = createTestStore({
        customer: null,
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      // Component should not crash with missing customer
      expect(screen.queryByLabelText('Time Range')).not.toBeInTheDocument();
    });

    it('should handle invalid dashboard ID', () => {
      const store = createTestStore({
        currentDashboardId: -1,
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      // Component should not render main controls with invalid dashboard ID
      expect(screen.queryByLabelText('Time Range')).not.toBeInTheDocument();
    });

    it('should handle empty dashboard list', () => {
      const emptyDashboardList: DashboardCollection = {
        items: [],
        total: 0,
      };

      renderWithProviders(<TopPanel {...defaultProps} dashboardList={emptyDashboardList} />);

      // Component should still render with empty dashboard list
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
    });

    it('should handle empty customer list', () => {
      renderWithProviders(<TopPanel {...defaultProps} customerList={[]} />);

      // Component should handle empty customer list gracefully
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
    });
  });

  describe('TimeRangeRefreshOptions', () => {
    it('should export TimeRangeRefreshOptions with correct structure', () => {
      expect(TimeRangeRefreshOptions).toBeDefined();
      expect(Array.isArray(TimeRangeRefreshOptions)).toBe(true);
      expect(TimeRangeRefreshOptions.length).toBeGreaterThan(0);

      TimeRangeRefreshOptions.forEach((option) => {
        expect(option).toHaveProperty('label');
        expect(option).toHaveProperty('interval');
        expect(typeof option.label).toBe('string');
        expect(typeof option.interval).toBe('number');
      });
    });

    it('should include Manual option with -1 interval', () => {
      const manualOption = TimeRangeRefreshOptions.find((option) => option.label === 'Manual');
      expect(manualOption).toBeDefined();
      expect(manualOption?.interval).toBe(-1);
    });

    it('should include time-based options with positive intervals', () => {
      const timeOptions = TimeRangeRefreshOptions.filter((option) => option.interval > 0);
      expect(timeOptions.length).toBeGreaterThan(0);

      timeOptions.forEach((option) => {
        expect(option.interval).toBeGreaterThan(0);
        expect(option.label).toMatch(/minute|hour/i);
      });
    });
  });
});
