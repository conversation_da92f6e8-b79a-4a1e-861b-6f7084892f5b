import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import { TopPanel, TimeRangeRefreshOptions } from '../TopPanel';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { Customer } from '~/types/customers';
import { DashboardCollection } from '~/types/dashboard';

// Mock external dependencies
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

jest.mock('~/hooks/useCustomTimeInterval', () => ({
  useCustomTimeInterval: jest.fn(),
}));

jest.mock('~/hooks/useHasPowerUserAccess', () => ({
  useHasPowerUserAccess: jest.fn(),
}));

jest.mock('~/hooks/useExportDashboadTemplate', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    exportTemplate: jest.fn(),
    loading: false,
    message: null,
    setMessage: jest.fn(),
  })),
}));

jest.mock('~/hooks/useTopPanelHelper', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    exportDashboard: jest.fn(),
  })),
}));

jest.mock('~/shared/snackbars/snackbar-hooks', () => ({
  useSnackbar: jest.fn(() => [
    { open: false, message: '', severity: 'success' },
    jest.fn(),
    jest.fn(),
  ]),
}));

// Mock API hooks
jest.mock('~/redux/api/dashboardApi', () => ({
  useCreateDashboardMutation: jest.fn(() => [
    jest.fn(),
    { data: null, isSuccess: false, isError: false, isLoading: false },
  ]),
  useEditDashboardMutation: jest.fn(() => [
    jest.fn(),
    { data: null, isSuccess: false, isError: false },
  ]),
}));

jest.mock('~/redux/api/dashboardTemplate', () => ({
  useCreateDashboardTemplateMutation: jest.fn(() => [
    jest.fn(),
    { isError: false, isSuccess: false, data: null },
  ]),
  useGetDashboardTemplatesQuery: jest.fn(() => ({
    data: [],
    refetch: jest.fn(),
  })),
  useUpdateDashboardTemplateMutation: jest.fn(() => [
    jest.fn(),
    { isError: false, isSuccess: false },
  ]),
}));

jest.mock('~/redux/api/measuresApi', () => ({
  useGetMeasuresByCustomersMutation: jest.fn(() => [
    jest.fn(),
    {
      data: null,
      isSuccess: false,
      isError: false,
      error: null,
    },
  ]),
}));

// Mock child components
jest.mock('../ResponsiveLayoutToggle', () => {
  return function MockResponsiveLayoutToggle({ disabled }: { disabled?: boolean }) {
    return <div data-testid="responsive-layout-toggle" data-disabled={disabled} />;
  };
});

jest.mock('../DashboardTitleDialog', () => {
  return function MockDashboardTitleDialog({ open, onClose, onSave }: any) {
    return open ? (
      <div data-testid="dashboard-title-dialog">
        <button onClick={() => onSave({ title: 'Test Dashboard', description: 'Test' }, 'save')}>
          Save
        </button>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null;
  };
});

jest.mock('../DashboardTemplateTItleDialog', () => {
  return function MockDashboardTemplateTitleDialog({ open, onClose, onSave }: any) {
    return open ? (
      <div data-testid="dashboard-template-title-dialog">
        <button onClick={() => onSave({ title: 'Test Template', save_as_global_dashboard_template: false }, 'save')}>
          Save Template
        </button>
        <button onClick={onClose}>Close Template</button>
      </div>
    ) : null;
  };
});

jest.mock('../ImportDashboard', () => {
  return function MockImportDashboard({ open, setShowImport }: any) {
    return open ? (
      <div data-testid="import-dashboard">
        <button onClick={() => setShowImport(false)}>Close Import</button>
      </div>
    ) : null;
  };
});

jest.mock('../TemplateCustomerAssetSelection', () => {
  return function MockTemplateCustomerAssetSelection() {
    return <div data-testid="template-customer-asset-selection" />;
  };
});

jest.mock('~/pages/dashboard-template/import', () => {
  return function MockImportTemplate() {
    return <div data-testid="import-template" />;
  };
});

// Mock date range picker
jest.mock('react-date-time-range-picker', () => ({
  DateRangePicker: function MockDateRangePicker({ onChange }: any) {
    return (
      <div data-testid="date-range-picker">
        <button
          onClick={() =>
            onChange({
              selection: {
                startDate: new Date('2023-01-01'),
                endDate: new Date('2023-01-02'),
                key: 'selection',
                selectedIndex: 1,
              },
            })
          }
        >
          Select Date Range
        </button>
      </div>
    );
  },
}));

const mockRouter = {
  pathname: '/customer/1/dashboard/1',
  push: jest.fn(),
  query: {},
  asPath: '',
  route: '',
  back: jest.fn(),
  beforePopState: jest.fn(),
  prefetch: jest.fn(),
  reload: jest.fn(),
  replace: jest.fn(),
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  defaultLocale: 'en',
  domainLocales: [],
  isPreview: false,
};

const mockCustomer: Customer = {
  id: 1,
  name: 'Test Customer',
  nameId: 'test-customer',
  address: 'Test Address',
  logo: 'data:image/png;base64,test',
};

const mockDashboardList: DashboardCollection = {
  items: [
    {
      id: 1,
      title: 'Test Dashboard',
      data: '{}',
      default: false,
      favorite: false,
      dashboardTemplate: null,
      asset: null,
    },
  ],
  total: 1,
};

const defaultProps = {
  dashboardList: mockDashboardList,
  isLoadingDashboards: false,
  customerList: [mockCustomer],
  isCustomerListLoading: false,
  isCustomerListSuccess: true,
  isSamplePeriod: true,
  isRefreshInterval: true,
};

// Helper function to create a test store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      dashboard: dashboardSlice.reducer,
    },
    preloadedState: {
      dashboard: {
        ...dashboardSlice.getInitialState(),
        currentDashboardId: 1,
        dashboardTitle: 'Test Dashboard',
        userDetails: null,
        userToken: null,
        customer: mockCustomer,
        enableZoom: false,
        userPreferences: {
          DATE_FORMAT: 'DD-MM-YYYY HH:mm:ss',
          DEFAULT_CUSTOMER: '',
          THOUSAND_SEPARATOR: 'enabled',
        },
        dashboardCrumb: [],
        mainPanel: 'chart',
        isLeftPanelOpen: true,
        isDirty: false,
        kisok: false,
        fullScreen: false,
        rightSideBar: false,
        dateFormat: 0,
        newMeasureId: 0,
        rightSideBarActiveTab: '/icons/alerts.svg',
        topPanel: {
          isVisible: true,
          timeRangeType: 6,
          refreshInterval: -1,
          samplePeriod: 2,
          assetTz: true,
        },
        tree: {
          currentSelectedNodeId: '1',
          selectedViewMeasureId: '1',
          selectedNodeIds: ['1'],
          expandedNodeIds: ['1'],
          dbMeasureIdToName: {},
        },
        chart: {
          startDate: new Date('2023-01-01').getTime(),
          endDate: new Date('2023-01-02').getTime(),
        },
        widget: {
          widgets: [],
          widgetLayout: [],
          deleteWidgets: [],
          lastWidgetId: 0,
        },
        desktopMobile: 0,
        responsiveLayouts: {
          desktop: { widgetLayout: [] },
          mobile: { widgetLayout: [] },
        },
        template: {
          assetTemplate: 0,
          templateId: 0,
          templateName: '',
          assetType: 0,
          metrics: [],
          idToName: {},
          topPanel: {
            timeRangeType: 6,
            refreshInterval: -1,
            samplePeriod: 2,
            assetTz: true,
          },
          chart: {
            startDate: new Date().getTime(),
            endDate: new Date().getTime(),
          },
        },
        metricMeasurements: {},
        ...initialState,
      },
    },
  });
};

const theme = createTheme();

const renderWithProviders = (ui: React.ReactElement, { store = createTestStore() } = {}) => {
  return render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>{ui}</ThemeProvider>
    </Provider>
  );
};

describe('TopPanel Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (require('~/hooks/useHasPowerUserAccess').useHasPowerUserAccess as jest.Mock).mockReturnValue(true);
  });

  describe('Component Rendering', () => {
    it('should render without crashing', () => {
      renderWithProviders(<TopPanel {...defaultProps} />);
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
    });

    it('should render time range field', () => {
      renderWithProviders(<TopPanel {...defaultProps} />);
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
    });

    it('should render sample period dropdown when isSamplePeriod is true', () => {
      renderWithProviders(<TopPanel {...defaultProps} isSamplePeriod={true} />);
      expect(screen.getByLabelText('Sample Period')).toBeInTheDocument();
    });

    it('should not render sample period dropdown when isSamplePeriod is false', () => {
      renderWithProviders(<TopPanel {...defaultProps} isSamplePeriod={false} />);
      expect(screen.queryByLabelText('Sample Period')).not.toBeInTheDocument();
    });

    it('should render refresh interval dropdown when isRefreshInterval is true', () => {
      renderWithProviders(<TopPanel {...defaultProps} isRefreshInterval={true} />);
      expect(screen.getByLabelText('Refresh Interval')).toBeInTheDocument();
    });

    it('should not render refresh interval dropdown when isRefreshInterval is false', () => {
      renderWithProviders(<TopPanel {...defaultProps} isRefreshInterval={false} />);
      expect(screen.queryByLabelText('Refresh Interval')).not.toBeInTheDocument();
    });

    it('should render asset timezone switch', () => {
      renderWithProviders(<TopPanel {...defaultProps} />);
      expect(screen.getByLabelText('Asset Timezone')).toBeInTheDocument();
    });
  });

  describe('Action Buttons', () => {
    it('should render action buttons when user has power user access', () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      expect(screen.getByTitle('Share')).toBeInTheDocument();
      expect(screen.getByTitle('Import')).toBeInTheDocument();
      expect(screen.getByTitle('Export')).toBeInTheDocument();
      expect(screen.getByTitle('Save')).toBeInTheDocument();
      expect(screen.getByTestId('responsive-layout-toggle')).toBeInTheDocument();
    });

    it('should not render power user buttons when user lacks access', () => {
      (require('~/hooks/useHasPowerUserAccess').useHasPowerUserAccess as jest.Mock).mockReturnValue(false);

      renderWithProviders(<TopPanel {...defaultProps} />);

      expect(screen.getByTitle('Share')).toBeInTheDocument();
      expect(screen.queryByTitle('Import')).not.toBeInTheDocument();
      expect(screen.queryByTitle('Export')).not.toBeInTheDocument();
      expect(screen.queryByTitle('Save')).not.toBeInTheDocument();
      expect(screen.queryByTestId('responsive-layout-toggle')).not.toBeInTheDocument();
    });

    it('should handle share button click', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const shareButton = screen.getByTitle('Share');
      await userEvent.click(shareButton);

      // The component should handle the share functionality
      expect(shareButton).toBeInTheDocument();
    });

    it('should handle import button click', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const importButton = screen.getByTitle('Import');
      await userEvent.click(importButton);

      await waitFor(() => {
        expect(screen.getByTestId('import-dashboard')).toBeInTheDocument();
      });
    });

    it('should handle save button click', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const saveButton = screen.getByTitle('Save');
      await userEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.getByTestId('dashboard-title-dialog')).toBeInTheDocument();
      });
    });
  });

  describe('Time Range Functionality', () => {
    it('should open date picker when time range field is clicked', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const timeRangeField = screen.getByLabelText('Time Range');
      await userEvent.click(timeRangeField);

      await waitFor(() => {
        expect(screen.getByTestId('date-range-picker')).toBeInTheDocument();
      });
    });

    it('should display correct time range format', () => {
      const store = createTestStore({
        chart: {
          startDate: new Date('2023-01-01').getTime(),
          endDate: new Date('2023-01-02').getTime(),
        },
        topPanel: {
          timeRangeType: 0, // Custom time range
          refreshInterval: -1,
          samplePeriod: 2,
          assetTz: true,
        },
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      const timeRangeField = screen.getByLabelText('Time Range');
      expect(timeRangeField).toHaveValue(expect.stringContaining('2023'));
    });

    it('should handle date range selection', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const timeRangeField = screen.getByLabelText('Time Range');
      await userEvent.click(timeRangeField);

      await waitFor(() => {
        expect(screen.getByTestId('date-range-picker')).toBeInTheDocument();
      });

      const selectButton = screen.getByText('Select Date Range');
      await userEvent.click(selectButton);

      const applyButton = screen.getByText('Apply');
      await userEvent.click(applyButton);

      // Date picker should close after applying
      await waitFor(() => {
        expect(screen.queryByTestId('date-range-picker')).not.toBeInTheDocument();
      });
    });

    it('should handle date range cancellation', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const timeRangeField = screen.getByLabelText('Time Range');
      await userEvent.click(timeRangeField);

      await waitFor(() => {
        expect(screen.getByTestId('date-range-picker')).toBeInTheDocument();
      });

      const cancelButton = screen.getByText('Cancel');
      await userEvent.click(cancelButton);

      // Date picker should close after cancelling
      await waitFor(() => {
        expect(screen.queryByTestId('date-range-picker')).not.toBeInTheDocument();
      });
    });
  });

  describe('Sample Period Functionality', () => {
    it('should handle sample period change', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const samplePeriodSelect = screen.getByLabelText('Sample Period');

      // Open the select dropdown
      fireEvent.mouseDown(samplePeriodSelect);

      await waitFor(() => {
        // Look for a sample period option (assuming index 1 exists)
        const option = screen.getByRole('option', { name: /minute/i });
        if (option) {
          fireEvent.click(option);
        }
      });
    });

    it('should display current sample period value', () => {
      const store = createTestStore({
        topPanel: {
          samplePeriod: 2,
          timeRangeType: 6,
          refreshInterval: -1,
          assetTz: true,
        },
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      const samplePeriodSelect = screen.getByLabelText('Sample Period');
      expect(samplePeriodSelect).toBeInTheDocument();
    });
  });

  describe('Refresh Interval Functionality', () => {
    it('should handle refresh interval change', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const refreshIntervalSelect = screen.getByLabelText('Refresh Interval');

      // Open the select dropdown
      fireEvent.mouseDown(refreshIntervalSelect);

      await waitFor(() => {
        // Look for a refresh interval option
        const option = screen.getByRole('option', { name: '5 minute' });
        if (option) {
          fireEvent.click(option);
        }
      });
    });

    it('should disable refresh interval when custom time range is selected', () => {
      const store = createTestStore({
        topPanel: {
          timeRangeType: 0, // Custom time range
          refreshInterval: -1,
          samplePeriod: 2,
          assetTz: true,
        },
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      const refreshIntervalSelect = screen.getByLabelText('Refresh Interval');
      expect(refreshIntervalSelect).toBeDisabled();
    });

    it('should enable refresh interval when predefined time range is selected', () => {
      const store = createTestStore({
        topPanel: {
          timeRangeType: 6, // Predefined time range
          refreshInterval: -1,
          samplePeriod: 2,
          assetTz: true,
        },
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      const refreshIntervalSelect = screen.getByLabelText('Refresh Interval');
      expect(refreshIntervalSelect).not.toBeDisabled();
    });
  });

  describe('Asset Timezone Switch', () => {
    it('should handle asset timezone toggle', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const assetTzSwitch = screen.getByLabelText('Asset Timezone');
      await userEvent.click(assetTzSwitch);

      // The switch should be interactive
      expect(assetTzSwitch).toBeInTheDocument();
    });

    it('should reflect current asset timezone state', () => {
      const store = createTestStore({
        topPanel: {
          assetTz: false,
          timeRangeType: 6,
          refreshInterval: -1,
          samplePeriod: 2,
        },
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      const assetTzSwitch = screen.getByLabelText('Asset Timezone');
      expect(assetTzSwitch).toBeInTheDocument();
    });
  });

  describe('Dashboard Template Mode', () => {
    beforeEach(() => {
      (useRouter as jest.Mock).mockReturnValue({
        ...mockRouter,
        pathname: '/dashboard-template',
      });
    });

    it('should render template-specific buttons in dashboard template mode', () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      expect(screen.getByTestId('import-template')).toBeInTheDocument();
      expect(screen.getByTitle('Export Template')).toBeInTheDocument();
      expect(screen.getByText('Save')).toBeInTheDocument();
    });

    it('should not render dashboard-specific buttons in template mode', () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      expect(screen.queryByTitle('Share')).not.toBeInTheDocument();
      expect(screen.queryByTitle('Import')).not.toBeInTheDocument();
      expect(screen.queryByTitle('Export')).not.toBeInTheDocument();
      expect(screen.queryByTitle('Save')).not.toBeInTheDocument();
    });

    it('should handle template save button click', async () => {
      const store = createTestStore({
        template: {
          assetTemplate: 1,
          templateId: 0,
          templateName: '',
          assetType: 0,
          metrics: [],
          idToName: {},
          topPanel: {
            timeRangeType: 6,
            refreshInterval: -1,
            samplePeriod: 2,
            assetTz: true,
          },
          chart: {
            startDate: new Date().getTime(),
            endDate: new Date().getTime(),
          },
        },
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      const saveButton = screen.getByText('Save');
      await userEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.getByTestId('dashboard-template-title-dialog')).toBeInTheDocument();
      });
    });

    it('should disable save button when asset template is not selected', () => {
      const store = createTestStore({
        template: {
          assetTemplate: 0, // No asset template selected
          templateId: 0,
          templateName: '',
          assetType: 0,
          metrics: [],
          idToName: {},
          topPanel: {
            timeRangeType: 6,
            refreshInterval: -1,
            samplePeriod: 2,
            assetTz: true,
          },
          chart: {
            startDate: new Date().getTime(),
            endDate: new Date().getTime(),
          },
        },
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      const saveButton = screen.getByText('Save');
      expect(saveButton).toBeDisabled();
    });
  });

  describe('Dialog Interactions', () => {
    it('should handle dashboard title dialog save', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const saveButton = screen.getByTitle('Save');
      await userEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.getByTestId('dashboard-title-dialog')).toBeInTheDocument();
      });

      const dialogSaveButton = screen.getByText('Save');
      await userEvent.click(dialogSaveButton);

      // Dialog should close after save
      await waitFor(() => {
        expect(screen.queryByTestId('dashboard-title-dialog')).not.toBeInTheDocument();
      });
    });

    it('should handle dashboard title dialog close', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const saveButton = screen.getByTitle('Save');
      await userEvent.click(saveButton);

      await waitFor(() => {
        expect(screen.getByTestId('dashboard-title-dialog')).toBeInTheDocument();
      });

      const closeButton = screen.getByText('Close');
      await userEvent.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByTestId('dashboard-title-dialog')).not.toBeInTheDocument();
      });
    });

    it('should handle import dashboard dialog', async () => {
      renderWithProviders(<TopPanel {...defaultProps} />);

      const importButton = screen.getByTitle('Import');
      await userEvent.click(importButton);

      await waitFor(() => {
        expect(screen.getByTestId('import-dashboard')).toBeInTheDocument();
      });

      const closeImportButton = screen.getByText('Close Import');
      await userEvent.click(closeImportButton);

      await waitFor(() => {
        expect(screen.queryByTestId('import-dashboard')).not.toBeInTheDocument();
      });
    });
  });

  describe('Loading States', () => {
    it('should handle loading dashboards state', () => {
      renderWithProviders(<TopPanel {...defaultProps} isLoadingDashboards={true} />);

      // When loading, the main controls should not be visible
      expect(screen.queryByLabelText('Time Range')).not.toBeInTheDocument();
    });

    it('should disable responsive layout toggle when loading', () => {
      renderWithProviders(<TopPanel {...defaultProps} isLoadingDashboards={true} />);

      // The responsive layout toggle should be disabled when loading
      const toggle = screen.queryByTestId('responsive-layout-toggle');
      if (toggle) {
        expect(toggle).toHaveAttribute('data-disabled', 'true');
      }
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle missing customer data', () => {
      const store = createTestStore({
        customer: null,
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      // Component should not crash with missing customer
      expect(screen.queryByLabelText('Time Range')).not.toBeInTheDocument();
    });

    it('should handle invalid dashboard ID', () => {
      const store = createTestStore({
        currentDashboardId: -1,
      });

      renderWithProviders(<TopPanel {...defaultProps} />, { store });

      // Component should not render main controls with invalid dashboard ID
      expect(screen.queryByLabelText('Time Range')).not.toBeInTheDocument();
    });

    it('should handle empty dashboard list', () => {
      const emptyDashboardList: DashboardCollection = {
        items: [],
        total: 0,
      };

      renderWithProviders(
        <TopPanel {...defaultProps} dashboardList={emptyDashboardList} />
      );

      // Component should still render with empty dashboard list
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
    });

    it('should handle empty customer list', () => {
      renderWithProviders(<TopPanel {...defaultProps} customerList={[]} />);

      // Component should handle empty customer list gracefully
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
    });
  });

  describe('TimeRangeRefreshOptions', () => {
    it('should export TimeRangeRefreshOptions with correct structure', () => {
      expect(TimeRangeRefreshOptions).toBeDefined();
      expect(Array.isArray(TimeRangeRefreshOptions)).toBe(true);
      expect(TimeRangeRefreshOptions.length).toBeGreaterThan(0);

      TimeRangeRefreshOptions.forEach(option => {
        expect(option).toHaveProperty('label');
        expect(option).toHaveProperty('interval');
        expect(typeof option.label).toBe('string');
        expect(typeof option.interval).toBe('number');
      });
    });

    it('should include Manual option with -1 interval', () => {
      const manualOption = TimeRangeRefreshOptions.find(option => option.label === 'Manual');
      expect(manualOption).toBeDefined();
      expect(manualOption?.interval).toBe(-1);
    });

    it('should include time-based options with positive intervals', () => {
      const timeOptions = TimeRangeRefreshOptions.filter(option => option.interval > 0);
      expect(timeOptions.length).toBeGreaterThan(0);

      timeOptions.forEach(option => {
        expect(option.interval).toBeGreaterThan(0);
        expect(option.label).toMatch(/minute|hour/i);
      });
    });
  });
});
