'use client';
import { Box } from '@mui/material';
import { useSelector } from 'react-redux';
import { RootState } from '~/redux/store';
import Application from './Application';
import BodyWidget from './Drag and Drop/BodyWidget';

const Diagram = () => {
  const application = new Application();
  const value = useSelector((state: RootState) => state.solar.solarEnergy);

  return (
    <div className=" ">
      <Box height="calc(100vh - 71px)" display="flex" justifyContent="center">
        <BodyWidget app={application} value={value}></BodyWidget>
      </Box>
    </div>
  );
};

export default Diagram;
