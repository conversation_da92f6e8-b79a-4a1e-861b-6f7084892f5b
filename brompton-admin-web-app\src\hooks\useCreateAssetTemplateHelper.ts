import { yupResolver } from '@hookform/resolvers/yup';
import { SetStateAction, useMemo, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import {
  createAssetTemplateFirstStep,
  createAssetTemplateFirstStepData,
  MeasurementSchema,
  measurementSchemaData,
} from '~/measurements/domain/types';
import {
  useGetAllBackOfficeAssetTypesMetricsQuery,
  useGetAllBackOfficeAssetTypesQuery,
} from '~/redux/api/assetsApi';
import {
  useGetCalculationEngineTemplatesQuery,
  useGetPollPeriodsQuery,
} from '~/redux/api/calculationEngine';
import {
  useGetAllDatasourcesQuery,
  useGetAllDataTypesQuery,
  useGetAllLocationsQuery,
  useGetAllMeasureTypesQuery,
  useGetAllValueTypesQuery,
} from '~/redux/api/measuresApi';
import {
  calculationMeasurementSchema,
  CalculationMeasurementSchemaData,
} from '~/types/calc_engine';
import { mapListToOptions } from '~/utils/utils';

const useCreateAssetTemplateHelper = ({
  activeStep,
  measurements,
  currentMeausreIndex,
  setActiveStep,
}: {
  activeStep: number;
  currentMeausreIndex: number;
  measurements: measurementSchemaData[];
  setActiveStep: (value: SetStateAction<number>) => void;
}) => {
  const [calcMeasurements, setCalcMeasurements] = useState<
    {
      metric_id: string;
      calcMeasurementData: CalculationMeasurementSchemaData;
    }[]
  >([]);

  const { data: valueTypeList } = useGetAllValueTypesQuery();
  const { data: datasourceList } = useGetAllDatasourcesQuery({});
  const { data: locationsList } = useGetAllLocationsQuery();
  const { data: dataTypeList } = useGetAllDataTypesQuery();
  const { data: measurementTypeList } = useGetAllMeasureTypesQuery();
  const { data: pollPeriods } = useGetPollPeriodsQuery(undefined, {
    skip: activeStep !== 2,
  });
  const { data: expressionTemplates, isLoading: fetchingExpressionTemplates } =
    useGetCalculationEngineTemplatesQuery(undefined, { skip: activeStep !== 2 });
  const {
    data: assetTypeListData,
    isSuccess: isSuccessfullBackOffieAssetTypes,
    isFetching: isFetchingBackOfficeAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery();

  const { control, handleSubmit, getValues, watch, reset } =
    useForm<createAssetTemplateFirstStepData>({
      defaultValues: {
        manufacturer: '',
        model_number: '',
        assetTypeId: undefined,
        save_as_global_asset_template: false,
      },
      resolver: yupResolver(createAssetTemplateFirstStep),
    });
  const {
    control: measurementsControl,
    getValues: getMeasureValues,
    handleSubmit: measurementSubmit,
    formState: { errors: measureErrors },
    setValue: setMeasureValue,
    reset: resetMeasure,
  } = useForm<measurementSchemaData>({
    defaultValues:
      currentMeausreIndex >= 0
        ? measurements[currentMeausreIndex]
        : {
            type_id: undefined,
            data_type_id: undefined,
            value_type_id: undefined,
            metric_id: undefined,
            description: '',
            location_id: undefined,
            datasource_id: undefined,
            meter_factor: undefined,
          },
    resolver: yupResolver(MeasurementSchema),
  });

  const {
    control: calcMeasurementController,
    handleSubmit: calcMeasureHandleSubmit,
    getValues: calcMeasureGetValues,
    reset: resetCalcMeasureValues,
    watch: calcMeasureWatch,
    setValue: calcMeasureSetValue,
    formState: { errors: calcMeasureError },
  } = useForm<CalculationMeasurementSchemaData>({
    defaultValues: {
      is_persisted: false,
      poll_period: null,
      writeback: false,
      expression_template_id: undefined,
      variable_inputs: [],
    },
    resolver: yupResolver(calculationMeasurementSchema),
  });
  const { fields } = useFieldArray({
    control: calcMeasurementController,
    name: 'variable_inputs',
  });

  const valueTypeOptions = useMemo(() => mapListToOptions(valueTypeList ?? []), [valueTypeList]);
  const datasourceOptions = useMemo(
    () =>
      mapListToOptions(
        datasourceList?.items.filter((source) => source.name !== 'TimeVaryingFactor') ?? [],
      ),
    [datasourceList],
  );
  const locationsListOption = useMemo(
    () => mapListToOptions(locationsList?.items ?? []),
    [locationsList],
  );
  const dataTypesListOptions = useMemo(() => mapListToOptions(dataTypeList ?? []), [dataTypeList]);
  const measurementTypeListOptions = useMemo(
    () => mapListToOptions(measurementTypeList ?? []),
    [measurementTypeList],
  );

  const calculationSource = datasourceList?.items?.find(
    (datasource) => datasource.name === 'Calculation',
  );
  const calculatedMeasures = useMemo(() => {
    return calculationSource
      ? measurements.filter((metric) => metric.datasource_id === calculationSource.id)
      : [];
  }, [measurements, calculationSource]);
  const steps = [
    'Asset Details',
    'Measurements',
    calculatedMeasures.length > 0 ? 'Calculated measurements' : undefined,
  ];
  const isLastStep = activeStep === steps.filter(Boolean).length - 1;
  const assetTypeId = watch('assetTypeId');
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };
  const { data: assetTypeMetrics } = useGetAllBackOfficeAssetTypesMetricsQuery(
    {
      assetId: assetTypeId ? assetTypeId.toString() : '',
    },
    {
      skip: assetTypeId === undefined || assetTypeId === 0,
      refetchOnMountOrArgChange: true,
    },
  );

  const assetTypeMetricsListOptions = useMemo(
    () => mapListToOptions(assetTypeMetrics?.items ?? []),
    [assetTypeMetrics],
  );
  const hasDuplicates = () => {
    const values = measurements.map((measure) => measure.metric_id);
    const uniqueValues = new Set(values);
    return values.length !== uniqueValues.size;
  };
  return {
    steps,
    isLastStep,
    calculatedMeasures,
    valueTypeList,
    datasourceList,
    locationsList,
    dataTypeList,
    measurementTypeList,
    assetTypeListData,
    isSuccessfullBackOffieAssetTypes,
    isFetchingBackOfficeAssetTypes,
    valueTypeOptions,
    datasourceOptions,
    pollPeriods,
    fetchingExpressionTemplates,
    locationsListOption,
    dataTypesListOptions,
    measurementTypeListOptions,
    assetTypeMetricsListOptions,
    expressionTemplates,
    calculationSource,
    hasDuplicates,
    handleBack,
    firstStep: { control, handleSubmit, getValues, watch, reset },
    measurements: {
      measurementsControl,
      getMeasureValues,
      measurementSubmit,
      measureErrors,
      setMeasureValue,
      resetMeasure,
    },
    calculationMeasurement: {
      calcMeasurementController,
      calcMeasureHandleSubmit,
      calcMeasureGetValues,
      calcMeasureError,
      calcMeasureWatch,
      resetCalcMeasureValues,
      calcMeasureSetValue,
      fields,
    },
    calcMeasurements,
    setCalcMeasurements,
  };
};

export default useCreateAssetTemplateHelper;
