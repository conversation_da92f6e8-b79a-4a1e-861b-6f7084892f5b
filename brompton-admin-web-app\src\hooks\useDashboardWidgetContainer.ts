import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { ItemCallback, Layout } from 'react-grid-layout';
import { useDispatch } from 'react-redux';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { useHasPowerUserAccess } from './useHasPowerUserAccess';
import { useHasAdminAccess } from './useHasAdminAccess';

type DashboardWidgetContainerProps = {
  widgetLayout: Layout[];
};
export const useDashboardWidgetContainer = ({ widgetLayout }: DashboardWidgetContainerProps) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [compactType, setCompactType] = useState<'vertical' | 'horizontal'>('vertical');
  const [mounted, setMounted] = useState<boolean>(false);
  const { admin, globalAdmin } = useHasAdminAccess();
  const hasPowerUserAccess = useHasPowerUserAccess();
  const onLayoutChange = (layout: Layout[]) => {
    // if (layout.find((layoutToFind) => layoutToFind.i === 'a') === undefined) {
    //   // console.log('if condition')
    //   dispatch(dashboardSlice.actions.setWidgetsLayout(layout));
    // }
  };

  const onDrop = (layout: Layout[], layoutItem: Layout, event: any) => {
    const type = event.dataTransfer.getData('text/plain');
    const mode = router.pathname === '/dashboard-template' ? 'template' : 'dashboard';
    dispatch(
      dashboardSlice.actions.addWidget({
        widgetMode: mode,
        type: type,
        layout,
        layoutItem: layoutItem,
      }),
    );
  };
  const checkIsLayoutEditable = (widgetId: string): 'disabled-resize' | '' => {
    const isUserAdmin = admin || globalAdmin || hasPowerUserAccess;
    const widgetIsStatic = widgetLayout.find((widget) => widget.i === widgetId)?.static;
    return !isUserAdmin || widgetIsStatic ? 'disabled-resize' : '';
  };
  useEffect(() => {
    setMounted(true);
  }, []);
  const resizeHandler: ItemCallback = (
    layout: Layout[],
    oldItem: Layout,
    newItem: Layout,
    placeholder: Layout,
    event: MouseEvent,
    element: HTMLElement,
  ) => {
    dispatch(dashboardSlice.actions.updateLayout(layout));
  };
  return {
    compactType,
    setCompactType,
    onLayoutChange,
    onDrop,
    checkIsLayoutEditable,
    mounted,
    resizeHandler,
  };
};
