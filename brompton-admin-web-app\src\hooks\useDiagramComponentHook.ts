import { dia, shapes, util } from '@joint/core';
import { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { ConicTank } from '~/components/JointJs/DiagramComponent/ConicalTank';
import LiquidTank from '~/components/JointJs/DiagramComponent/LiquidTank';
import { useGetDiagramDetailsQuery } from '~/redux/api/diagramApi';
import {
  getBase64Image,
  getDiagramId,
  getEditingElementId,
  getEditLinkDialogOpen,
  getElementAttrs,
  getElementName,
  getElementsVariables,
  getElementType,
  getIconPosition,
  getIconVisible,
  getImageDropPosition,
  getImageUploaded,
  getImageUploadModalOpen,
  getLinkAttrs,
  getOpacity,
  getSelectedElement,
  getSelectedLink,
} from '~/redux/selectors/diagramSelector';
import { diagramSlice } from '~/redux/slices/diagramSlice';
import { cellNamespace, shapeMap } from '~/types/diagram';
import { useDiagramHelper } from './useDiagramHelper';
import Progress from '~/components/CreateElement/Progress';
import { formatDiagramElementName } from '~/utils/utils';

type DiagramComponentHookProps = {
  paperRef: React.MutableRefObject<HTMLDivElement | null>;
  rotateIconRef: React.MutableRefObject<HTMLButtonElement | null>;
  deleteIconPositionRef: React.MutableRefObject<{
    x: number;
    y: number;
  }>;
  resizeDotsRef: React.MutableRefObject<HTMLDivElement[]>;
  graphRef: React.MutableRefObject<dia.Graph<dia.Graph.Attributes, dia.ModelSetOptions> | null>;
  hideIconTimeoutRef: React.MutableRefObject<NodeJS.Timeout | null>;
};

export const predefinedImages = [
  { id: 'img1', src: 'https://cdn-icons-png.flaticon.com/256/2051/2051528.png', name: 'Image 1' },
  {
    id: 'img2',
    src: 'https://img.freepik.com/premium-vector/cutting-fingers-rotating-blades-symbol-sign-vector-illustration-isolate-white-background-label-eps10_112413-841.jpg?semt=ais_hybrid',
    name: 'Image 2',
  },
  {
    id: 'img3',
    src: 'https://images.pond5.com/industrial-workers-icon-construction-workers-illustration-169809917_iconl_nowm.jpeg',
    name: 'Image 3',
  },
];

const shapeImageMap: Record<string, string> = {
  gateValve: '/icons/gate-valve.svg',
  compressor: '/icons/compressor.svg',
  flarePlain: '/icons/flare-plain.svg',
  grayTextBox: '/icons/gray-text-box.svg',
  hashHut: '/icons/hash-hut.svg',
  hydraulicMotorValve: '/icons/hydraulic-motor-valve.svg',
  hydraulicMotorValveDark: '/icons/hydraulic-motor-valve-dark.svg',
  hydraulicMotorValveWhite: '/icons/hydraulic-motor-valve-white.svg',
  insetFieldBordered: '/icons/inset-field-bordered.svg',
  outsetFieldBordered: '/icons/outset-field-bordered.svg',
  pidArrow: '/icons/pid-arrow.svg',
  pneumaticValve: '/icons/pneumatic-valve.svg',
  scrubber: '/icons/scrubber.svg',
  tank: '/icons/tank.svg',
  vru: '/icons/vru.svg',
  whiteTextBox: '/icons/white-text-box.svg',
};

export const useDiagramComponentHook = ({
  paperRef,
  rotateIconRef,
  deleteIconPositionRef,
  resizeDotsRef,
  graphRef,
  hideIconTimeoutRef,
}: DiagramComponentHookProps) => {
  const dispatch = useDispatch();

  const sourceElementRef = useRef<dia.Element | null>(null);
  const rotationDotsRef = useRef<HTMLDivElement[]>([]);
  const [fieldErrors, setFieldErrors] = useState<{ [key: number]: string | null }>({});
  const [editedLabels, setEditedLabels] = useState<string[]>([]);
  const [portCount, setPortCount] = useState(1);
  const [x, setX] = useState<number>(0);
  const [y, setY] = useState<number>(0);

  const [errors, setErrors] = useState({
    lineWidth: false,
    strokeWidth: false,
    liquidStrokeWidth: false,
  });

  const {
    setEditDialogOpen,
    setEditingElementId,
    setBase64Image,
    setOpacity,
    setElementAttrs,
    setElementName,
    setElementType,
    setImageUploaded,
    setIconVisible,
    setImageUploadModalOpen,
    setImageDropPosition,
    setEditLinkDialogOpen,
    setLinkAttrs,
    setDiagram,
    setElementVairiables,
    setZoomLevel,
  } = diagramSlice.actions;

  const selectedElement = useSelector(getSelectedElement);
  const editingElementId = useSelector(getEditingElementId);
  const base64Image = useSelector(getBase64Image);
  const opacity = useSelector(getOpacity);
  const elementAttrs = useSelector(getElementAttrs);
  const elementName = useSelector(getElementName);
  const elementType = useSelector(getElementType);
  const imageUploaded = useSelector(getImageUploaded);
  const iconVisible = useSelector(getIconVisible);
  const iconPosition = useSelector(getIconPosition);
  const imageUploadModalOpen = useSelector(getImageUploadModalOpen);
  const imageDropPosition = useSelector(getImageDropPosition);
  const selectedLink = useSelector(getSelectedLink);
  const editLinkDialogOpen = useSelector(getEditLinkDialogOpen);
  const linkAttrs = useSelector(getLinkAttrs);
  const diagramId = useSelector(getDiagramId);
  const variables = useSelector(getElementsVariables);
  const {
    deleteSelectedElement,
    resetZoom,
    zoomIn,
    zoomOut,
    handleOpenLinkEditDialog,
    removeAllVertexTools,
    paperInstanceRef,
    highlightSelectedPort,
    setSelectedPortId,
    selectedPortId,
    visiblePorts,
    setVisiblePorts,
  } = useDiagramHelper({
    deleteIconPositionRef,
    graphRef,
    paperRef,
    resizeDotsRef,
    sourceElementRef,
    hideIconTimeoutRef,
    rotationDotsRef,
  });

  const { data } = useGetDiagramDetailsQuery(
    {
      id: diagramId,
    },
    {
      skip: diagramId <= 0 || diagramId === undefined || diagramId === null,
      refetchOnMountOrArgChange: true,
    },
  );
  useEffect(() => {
    if (data) {
      const json = JSON.parse(data.data as string);
      const zoomLevel: number = json.zoomLevel ?? 1;
      const elementVariables = json.elementsVariables as Record<
        string,
        { label: string; variable: string }[]
      >;
      dispatch(setZoomLevel(zoomLevel));
      if (zoomLevel !== 1) {
        paperInstanceRef.current?.scale(zoomLevel, zoomLevel);
      }
      dispatch(setElementVairiables(elementVariables));
      graphRef.current!.fromJSON(json.diagram, {
        cellNamespace: cellNamespace,
      });
      json.diagram.cells.forEach((cell: any) => {
        if (cell) {
          const cellInfo = graphRef.current!.getCell(cell.id);
          const { range = [], variables = [], conditionalRule = [] } = cellInfo.get('data') || {};
          cellInfo.prop('data/range', range ?? []);
          cellInfo.prop('data/variables', variables ?? []);
          cellInfo.prop('data/conditionalRule', conditionalRule ?? []);
          if (cellInfo instanceof Progress) {
            cellInfo.style = cellInfo.prop('data/style') ?? 'bar';
          }
          if (cellInfo instanceof ConicTank) {
            const { maxCapacity } = cellInfo.get('data') ?? {};
            cellInfo.updateMaxCapacity(Number(maxCapacity ?? 100));
          }
          if (cellInfo instanceof LiquidTank) {
            const direction = cellInfo.get('data')?.direction ?? 'horizontal';
            cellInfo.startProgress(true);
            cellInfo.level = 10;
            cellInfo.updateDirection(direction);
            cellInfo.prop('data/range', cellInfo.get('data').range ?? []);
            cellInfo.updateRangePicker(cellInfo.get('data').range ?? []);
            cellInfo.updateMaxCapacity(cellInfo.get('data').maxCapacity ?? 100);
          }
        }
        if (cell.base64Image) {
          const element = graphRef.current!.getCell(cell.id) as dia.Element;
          if (element) {
            injectPatternIntoDefs(cell.id, cell.base64Image);
            element.attr('body/fill', `url(#pattern-${cell.id})`);
          }
        }
      });
      dispatch(diagramSlice.actions.setDiagram(graphRef.current as dia.Graph));
    }
  }, [data, diagramId, dispatch]);
  useEffect(() => {
    if (graphRef && graphRef.current) {
      dispatch(setDiagram(graphRef.current));
    }
  }, [graphRef]);

  useEffect(() => {
    if (!graphRef.current) {
      return;
    }

    const links = graphRef.current.getLinks();

    const animationIntervals: Record<string, NodeJS.Timeout> = {};

    links.forEach((link) => {
      const lineAttrs = link.attr('line');
      const hasAnimation =
        lineAttrs?.strokeDasharray &&
        lineAttrs?.strokeDasharray !== 'none' &&
        typeof lineAttrs.strokeDashoffset !== 'undefined';

      if (hasAnimation) {
        let dashOffset = 0;
        const animationSpeed = lineAttrs?.animationSpeed || 75; // Default speed if not provided
        const intervalTime = Math.max(10, 1000 - animationSpeed * 12);

        animationIntervals[link.id] = setInterval(() => {
          if (!graphRef.current) {
            clearInterval(animationIntervals[link.id]);
            delete animationIntervals[link.id];
            return;
          }

          const existingLink = graphRef.current.getCell(link.id);
          if (!existingLink) {
            clearInterval(animationIntervals[link.id]);
            delete animationIntervals[link.id];
            return;
          }

          dashOffset -= 2;
          link.attr('line/strokeDashoffset', dashOffset.toString());
        }, intervalTime);
      } else {
        console.warn(`[Link Animation] No animation applied for link ID: ${link.id}`);
      }
    });

    return () => {
      Object.values(animationIntervals).forEach(clearInterval);
    };
  }, [graphRef.current ? graphRef.current.getLinks() : []]);

  // const handleAttrChange = (
  //   attr: string,
  //   value: string | number | { enabled?: boolean; speed?: number },
  // ) => {
  //   if (attr === 'width') {
  //     const numericValue = Number(value);
  //     dispatch(
  //       setLinkAttrs({
  //         ...linkAttrs,
  //         [attr]: isNaN(numericValue) ? 1 : numericValue, // Default width fallback
  //       }),
  //     );
  //   } else if (attr === 'animation' && typeof value === 'object') {
  //     // Handle animation property changes
  //     dispatch(
  //       setLinkAttrs({
  //         ...linkAttrs,
  //         animation: {
  //           ...linkAttrs.animation,
  //           ...value, // Merge with current animation values
  //         },
  //       }),
  //     );
  //   } else if (attr === 'endStyle' && typeof value === 'string') {
  //     // Ensure endStyle updates correctly (none, arrow, triangle, dot)
  //     dispatch(
  //       setLinkAttrs({
  //         ...linkAttrs,
  //         endStyle: value,
  //       }),
  //     );

  //     // Find the selected link in JointJS graph and update its marker dynamically
  //     if (selectedLink && graphRef.current) {
  //       selectedLink.attr(
  //         'line/targetMarker',
  //         value === 'arrow'
  //           ? { type: 'arrow' } // Solid arrow
  //           : value === 'triangle'
  //           ? {
  //               type: 'path',
  //               d: 'M 10 -5 L 0 0 L 10 5 Z', // Hollow Arrow Shape
  //               fill: 'white', // Keeps it hollow
  //               stroke: 'black', // Ensures only the outline is visible
  //               strokeWidth: 2,
  //               strokeLinejoin: 'miter',
  //             }
  //           : value === 'dot'
  //           ? {
  //               type: 'circle',
  //               r: 4, // Dot radius
  //               fill: 'black', // Solid dot
  //               stroke: 'black',
  //               strokeWidth: 2,
  //             }
  //           : {},
  //       );

  //       // Prevent the stroke from extending into the marker
  //       if (value === 'triangle' || value === 'dot') {
  //         selectedLink.attr('line/strokeLinecap', 'butt'); // Prevents line extension
  //       } else {
  //         selectedLink.removeAttr('line/strokeLinecap');
  //       }
  //     }
  //   } else {
  //     dispatch(
  //       setLinkAttrs({
  //         ...linkAttrs,
  //         [attr]: value,
  //       }),
  //     );
  //   }
  // };

  // const handleUpdateLink = () => {
  //   if (selectedLink && graphRef.current) {
  //     // Validate input values
  //     const newErrors = {
  //       lineWidth: linkAttrs.width > 30, // True if line width > 30
  //       strokeWidth: linkAttrs.outlineStrokeWidth > 30, // True if stroke width > 30
  //       liquidStrokeWidth: linkAttrs.liquidStrokeWidth > 30, // True if liquid stroke width > 30
  //     };

  //     setErrors(newErrors); // Update the errors state

  //     // If any validation fails, block the update
  //     if (Object.values(newErrors).some((error) => error)) {
  //       return;
  //     }

  //     // Reset errors if validation passes
  //     setErrors({
  //       lineWidth: false,
  //       strokeWidth: false,
  //       liquidStrokeWidth: false,
  //     });

  //     // Update line attributes
  //     selectedLink.attr('line/strokeWidth', linkAttrs.width || 3);
  //     selectedLink.attr(
  //       'line/strokeDasharray',
  //       linkAttrs.style === 'solid' ? '0' : linkAttrs.style === 'dash' ? '5,5' : '10,5,2,5',
  //     );
  //     selectedLink.attr('line/stroke', linkAttrs.color || '#000000');

  //     // Handle endStyle (targetMarker)
  //     if (linkAttrs.endStyle === 'arrow') {
  //       selectedLink.attr('line/targetMarker', {
  //         type: 'path',
  //         d: 'M 10 -5 0 0 10 5 Z', // Arrow shape
  //       });
  //     } else if (linkAttrs.endStyle === 'none') {
  //       selectedLink.removeAttr('line/targetMarker'); // Remove marker for 'none'
  //     }

  //     // Handle bidirectional or backward flow sourceMarker
  //     if (linkAttrs.direction === 'backward' || linkAttrs.direction === 'bidirectional') {
  //       selectedLink.attr('line/sourceMarker', {
  //         type: 'path',
  //         d: 'M 10 -5 0 0 10 5 Z', // Arrow shape
  //       });
  //     } else {
  //       selectedLink.removeAttr('line/sourceMarker'); // Remove source marker for forward flow
  //     }

  //     // Update other attributes (liquid, outline, animation, direction)
  //     selectedLink.attr('liquid/strokeDasharray', linkAttrs.liquidDashArray || '0');
  //     selectedLink.attr('liquid/stroke', linkAttrs.liquidStrokeColor || '#000000');
  //     selectedLink.attr('liquid/strokeWidth', linkAttrs.liquidStrokeWidth || 0);

  //     selectedLink.attr('outline/strokeWidth', linkAttrs.outlineStrokeWidth || 0);
  //     selectedLink.attr('outline/stroke', linkAttrs.outlineStrokeColor || '#010101');

  //     selectedLink.set('animation', {
  //       enabled: linkAttrs.animation.enabled || false,
  //       speed: linkAttrs.animation.speed || 0,
  //     });

  //     selectedLink.set('direction', linkAttrs.direction || 'forward');

  //     // Close the modal
  //     dispatch(setEditLinkDialogOpen(false));
  //   }
  // };

  const portMarkup = [
    {
      tagName: 'circle',
      selector: 'portBody',
      attributes: {
        r: 6,
        magnet: true,
        fill: '#C1C1C1',
        stroke: '#C1C1C1',
        strokeWidth: 1,
      },
    },
  ];

  const portGroups: Record<string, dia.Element.PortGroup> = {
    in: {
      position: { name: 'absolute' },
      attrs: { portBody: { magnet: true } },
      markup: portMarkup,
    },
    out: {
      position: { name: 'absolute' },
      attrs: { portBody: { magnet: true } },
      markup: portMarkup,
    },
    custom: {
      position: { name: 'absolute' },
      attrs: { portBody: { magnet: true, fill: '#fff' } },
      markup: portMarkup,
    },
  };

  const addInitialPorts = (element: dia.Element) => {
    const { width, height } = element.size();
    element.prop('ports/groups', portGroups);
    element.addPorts([
      { id: 'in', group: 'in', args: { x: 0, y: height / 2 } },
      { id: 'out', group: 'out', args: { x: width, y: height / 2 } },
    ]);
  };

  const handleAddPort = () => {
    if (!selectedElement) return;

    // const totalPorts = selectedElement.getPorts().length;
    // if (totalPorts >= 8) {
    //   alert('You can only add up to 6 custom ports per element.');
    //   return;
    // }

    const { width, height } = selectedElement.size();
    const randomX = Math.floor(Math.random() * width);
    const randomY = Math.floor(Math.random() * height);
    const portId = `custom-${portCount}`;

    const newPort: dia.Element.Port = {
      id: portId,
      group: 'custom',
      attrs: {
        portBody: { r: 6, magnet: true, fill: '#C1C1C1', stroke: '#C1C1C1', strokeWidth: 1 },
      },
      args: { x: randomX, y: randomY },
    };

    selectedElement.addPort(newPort);
    setSelectedPortId(portId);
    setX(randomX);
    setY(randomY);
    setVisiblePorts((prev) => [...prev, newPort]);
    setPortCount((prev) => prev + 1);
  };

  const handleDeletePort = (portId: string) => {
    if (!selectedElement) return;
    if (portId === 'in' || portId === 'out') return; // prevent deletion

    const updatedPorts = selectedElement.getPorts().filter((p) => p.id !== portId);
    selectedElement.removePorts();
    selectedElement.addPorts(updatedPorts);
    setVisiblePorts(updatedPorts);
    if (selectedPortId === portId) setSelectedPortId(null);
  };

  const handleEditPort = (portId: string) => {
    if (!selectedElement) return;
    const port = selectedElement.getPorts().find((p) => p.id === portId);
    if (!port) return;
    setSelectedPortId(portId);
    setX(port.args?.x || 0);
    setY(port.args?.y || 0);
    highlightSelectedPort(selectedElement, portId);
  };

  const handlePortSliderChange = (axis: 'x' | 'y', value: number) => {
    if (!selectedElement || !selectedPortId) return;
    const port = selectedElement.getPorts().find((p) => p.id === selectedPortId);
    if (!port) return;
    const updatedArgs = { ...port.args, [axis]: value };
    selectedElement.portProp(selectedPortId, 'args', updatedArgs);
    axis === 'x' ? setX(value) : setY(value);
  };

  const onDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();

    const shapeType = event.dataTransfer.getData('shape');
    const customShapeType = event.dataTransfer.getData('custom');

    if (graphRef.current) {
      const canvasRect = paperRef.current!.getBoundingClientRect();
      const position = {
        x: event.clientX - canvasRect.left,
        y: event.clientY - canvasRect.top,
      };

      if (customShapeType) {
        try {
          const json = JSON.parse(customShapeType);

          // Generate unique IDs for each cell
          const idMapping = new Map();
          json.cells.forEach((cell: any) => {
            const newId = util.uuid(); // Generate a new unique ID for the cell
            idMapping.set(cell.id, newId);
            cell.id = newId;

            // Update parent if applicable
            if (cell.parentId) {
              cell.parent = idMapping.get(cell.parentId); // Set `parent` instead of `parentId`
            }

            // Update embedded elements if applicable
            if (cell.embeds) {
              cell.embeds = cell.embeds
                .filter((embedId: any) => idMapping.has(embedId)) // Ensure valid embeds
                .map((embedId: any) => idMapping.get(embedId));
            }

            // Update ports, if applicable
            if (cell.ports && cell.ports.items) {
              cell.ports.items.forEach((port: any) => {
                port.id = util.uuid(); // Assign unique IDs to ports
              });
            }
          });

          // Ensure `parent` relationship is intact for `TextBlock` cells
          json.cells.forEach((cell: any) => {
            if (cell.type === 'standard.TextBlock') {
              cell.parent = idMapping.get(cell.parent); // Ensure the parent ID is updated
            }
          });

          // Filter out selectionHighlight cells
          const filteredCells = json.cells.filter(
            (cell: any) => cell.type !== 'selectionHighlight',
          );

          // Extract the updated group element after ID mapping
          const groupElement = filteredCells.find((cell: any) => cell.type === 'groupElement');
          const groupId = groupElement ? groupElement.id : null; // Use the updated ID

          // Extract other required values
          const textBlocks = filteredCells.filter(
            (cell: any) => cell.type === 'standard.TextBlock',
          );
          const textLabels = textBlocks.map((block: any) => block.attrs?.label?.text || '');
          const relatedVariables = textBlocks.map((block: any) => block.relatedVariable || '');

          // Dispatch extracted values to the store with updated groupId
          dispatch(
            diagramSlice.actions.setCustomElementData({
              groupId,
              textLabels,
              relatedVariables,
            }),
          );

          // Update positions and add cells to the graph
          const updatedCells = filteredCells.map((cell: any) => {
            if (cell.position) {
              cell.position.x += position.x;
              cell.position.y += position.y;
            }
            return cell;
          });

          updatedCells.forEach((cell: any) => {
            const existingCell = graphRef.current!.getCell(cell.id);
            if (!existingCell) {
              graphRef.current!.addCell(cell, { cellNamespace });
            }
          });

          // Reapply parent-child relationships to ensure grouping
          updatedCells.forEach((cell: any) => {
            if (cell.parent) {
              const parentCell = graphRef.current!.getCell(cell.parent);
              if (parentCell) {
                parentCell.embed(graphRef.current!.getCell(cell.id));
              }
            }
          });
        } catch (error) {
          console.error('Failed to parse and render JSON diagram:', error);
        }
      } else if (shapeType) {
        const createShape = shapeMap[shapeType];
        if (createShape) {
          const newShape = createShape(shapeType);
          newShape.attr({
            body: {
              name: shapeType,
              borderStyle: 'solid',
            },
          });
          if (shapeType === 'image') {
            newShape.attr({
              border: {
                stroke: 'transparent',
              },
              background: {
                fill: 'transparent',
              },
              image: {
                'xlink:href': '/placeholder-svg.svg',
              },
              label: { text: '' },
            });
          }

          if (shapeImageMap[shapeType]) {
            newShape.attr({
              border: {
                stroke: 'transparent',
              },
              background: {
                fill: 'transparent',
              },
              image: {
                'xlink:href': shapeImageMap[shapeType],
              },
              label: { text: formatDiagramElementName(shapeType) },
            });
          }
          addInitialPorts(newShape);
          newShape.position(position.x, position.y);
          newShape.on('change:size', () => {
            const { width, height } = newShape.size();
            newShape.portProp('in', 'args', { x: 0, y: height / 2 });
            newShape.portProp('out', 'args', { x: width, y: height / 2 });
          });
          newShape.addTo(graphRef.current);
        } else {
          console.warn(`No shape creator found for type: ${shapeType}`);
        }
      } else {
        console.warn('No valid shapeType or customShapeType found during drop event.');
      }
    }
  };

  const applyImageToElement = (imageSrc: string) => {
    if (graphRef.current && imageDropPosition) {
      const newImageElement = new shapes.standard.BorderedImage({
        attrs: {
          image: { 'xlink:href': imageSrc },
          label: { text: '' },
        },
      });

      newImageElement.position(imageDropPosition.x, imageDropPosition.y);
      newImageElement.addTo(graphRef.current);
      // Clear drop position and close the modal
      dispatch(setImageDropPosition(null));
    }
    dispatch(setImageUploadModalOpen(false));
  };

  const onDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleImageUploadInternal = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleImageUpload(e);
    dispatch(setImageUploaded(true));
  };

  const handleOpacityChange = (event: Event, newValue: number | number[]) => {
    dispatch(setOpacity(newValue as number));
  };

  const clearHandlers = () => {
    resizeDotsRef.current.forEach((dot) => dot.remove());
    resizeDotsRef.current = [];

    rotationDotsRef.current.forEach((dot) => dot.remove());
    rotationDotsRef.current = [];

    dispatch(diagramSlice.actions.setIconVisible(false));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        dispatch(setBase64Image(base64String));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleOpenEditDialog = (element: dia.Element) => {
    dispatch(setEditingElementId(element.id));

    const currentAttrs = element.attr();
    const fillColor = typeof currentAttrs.body.fill === 'string' ? currentAttrs.body.fill : '';

    dispatch(
      setElementAttrs({
        color: fillColor,
        borderColor: currentAttrs.body.stroke || '',
        borderWidth: currentAttrs.body.strokeWidth || '',
        name: currentAttrs.body.name || '',
        borderStyle: currentAttrs.body.currentStyle || 'solid',
      }),
    );

    const imageUrl =
      typeof currentAttrs.body.fill === 'string' && currentAttrs.body.fill.includes('url')
        ? currentAttrs.body.fill.match(/url\(#(.*)\)/)?.[1]
        : null;

    if (imageUrl) {
      const imageElement = document.querySelector(`#${imageUrl} image`) as SVGImageElement;
      if (imageElement) {
        const href = imageElement.getAttributeNS('', 'href');
        dispatch(setBase64Image(href));

        const opacity = imageElement.getAttribute('opacity') || '1';
        dispatch(setOpacity(parseFloat(opacity)));
      }
      dispatch(setImageUploaded(true));
    } else {
      dispatch(setBase64Image(null));
      dispatch(setOpacity(1));
      dispatch(setImageUploaded(false));
    }

    const elementType = element.attributes.elementType;
    dispatch(setElementType(elementType));
    dispatch(
      setElementName(
        element?.attributes?.attrs?.body?.name ??
          (element.attributes.type.split('.').pop() || 'Element'),
      ),
    );
    dispatch(setEditDialogOpen(true));
  };

  const injectPatternIntoDefs = (elementId: string, base64Image: string) => {
    const svgDefs = document.querySelector('svg defs');
    if (!svgDefs) return;

    let pattern = document.getElementById(
      `pattern-${elementId}`,
    ) as unknown as SVGPatternElement | null;

    if (!pattern) {
      pattern = document.createElementNS(
        'http://www.w3.org/2000/svg',
        'pattern',
      ) as SVGPatternElement;
      pattern.id = `pattern-${elementId}`;
      pattern.setAttribute('patternUnits', 'userSpaceOnUse');
      svgDefs.appendChild(pattern);
    }

    const element = graphRef.current?.getCell(elementId) as dia.Element;
    const { width, height } = element.size();

    pattern.setAttribute('width', `${width}`);
    pattern.setAttribute('height', `${height}`);

    let image: SVGImageElement | null = pattern.querySelector('image');
    if (!image) {
      image = document.createElementNS('http://www.w3.org/2000/svg', 'image') as SVGImageElement;
      pattern.appendChild(image);
    }

    image.setAttributeNS('', 'href', base64Image);
    image.setAttribute('x', '0');
    image.setAttribute('y', '0');
    image.setAttribute('width', `${width}`);
    image.setAttribute('height', `${height}`);
  };

  const getBorderStyle = (borderStyle: string) => {
    switch (borderStyle) {
      case 'solid':
        return {
          'stroke-dasharray': '', // No dash for solid border
        };
      case 'dashed':
        return {
          'stroke-dasharray': '5, 5', // Dashed line
        };
      case 'dotted':
        return {
          'stroke-dasharray': '2, 2', // Dotted line
        };
      case 'double':
        // Double line approximation
        return {
          'stroke-dasharray': '6, 2', // Adjust dash and gap to simulate a double border
          'stroke-width': 4, // Make it wider to simulate a double line
        };
      case 'none':
      case 'hidden':
        return {
          'stroke-width': 0, // No border
        };
      default:
        return {
          'stroke-dasharray': '', // Fallback to solid for unsupported styles
        };
    }
  };

  // const handleUpdateElement = (selectedOpacity: number) => {
  //   if (!editingElementId || !graphRef.current) return;

  //   const element = graphRef.current.getCell(editingElementId) as dia.Element;
  //   const elementVariables = variables[editingElementId] || [];
  //   const textBlocks = graphRef.current.getCells().filter(
  //     (cell) =>
  //       cell.isElement() &&
  //       cell.attributes.type === 'standard.TextBlock' &&
  //       cell.attributes.parent === editingElementId, // Ensure the text block belongs to the editing element
  //   ) as dia.Element[];

  //   const errors: { [key: number]: string | null } = {};

  //   // Validate labels
  //   const names = textBlocks.map((block) => block.attr('label/text').trim());
  //   textBlocks.forEach((block, index) => {
  //     const labelText = block.attr('label/text') || '';
  //     if (!labelText.trim()) {
  //       errors[index] = 'Label cannot be empty';
  //     } else if (names.indexOf(labelText.trim()) !== index) {
  //       errors[index] = 'Label must be unique';
  //     } else {
  //       errors[index] = null;
  //     }
  //   });

  //   setFieldErrors(errors);

  //   // Prevent saving if there are errors
  //   if (Object.values(errors).some((error) => error !== null)) {
  //     return;
  //   }

  //   // Update the main element's attributes if necessary
  //   if (element) {
  //     const currentAttrs = element.attr();
  //     const updatedAttrs: dia.Element.Attributes = {
  //       body: {
  //         fill: elementAttrs.color || currentAttrs.body.fill || 'white',
  //         stroke: elementAttrs.borderColor || currentAttrs.body.stroke || '#000000',
  //         strokeWidth: elementAttrs.borderWidth || currentAttrs.body.strokeWidth || 1,
  //         name: elementAttrs.name || currentAttrs.body.name || currentAttrs.label.text || 'title',
  //         ...getBorderStyle(elementAttrs.borderStyle || currentAttrs.body.borderStyle || 'solid'),
  //         borderStyle: elementAttrs.borderStyle || currentAttrs.body.borderStyle || 'solid',
  //       },
  //     };

  //     if (base64Image) {
  //       injectPatternIntoDefs(editingElementId.toString(), base64Image);
  //       updatedAttrs.body.fill = `url(#pattern-${editingElementId})`;

  //       const svgImage = document.querySelector(
  //         `#pattern-${editingElementId} image`,
  //       ) as SVGImageElement;
  //       if (svgImage) {
  //         svgImage.setAttribute('opacity', selectedOpacity.toString());
  //       }
  //     } else {
  //       const patternElement = document.getElementById(`pattern-${editingElementId}`);
  //       if (patternElement) {
  //         patternElement.remove();
  //       }
  //     }

  //     element.attr(updatedAttrs);
  //   }

  //   // Update the labels of the `standard.TextBlock` elements
  //   textBlocks.forEach((block, index) => {
  //     const labelText = block.attr('label/text');
  //     const updatedLabel = elementVariables[index]?.label || labelText || 'Untitled';

  //     if (labelText !== updatedLabel) {
  //       block.attr('label/text', updatedLabel);
  //     }
  //   });

  //   dispatch(setEditDialogOpen(false));
  // };

  const handleUpdateElement = (selectedOpacity: number) => {
    if (!editingElementId || !graphRef.current) return;

    const element = graphRef.current.getCell(editingElementId) as dia.Element;

    if (!element) {
      console.error(`No element found with ID: ${editingElementId}`);
      return;
    }

    // Get embedded TextBlocks from the parent element
    const embeddedIds = element.get('embeds') || [];
    const textBlocks = embeddedIds
      .map((id: string) => graphRef.current && graphRef.current.getCell(id))
      .filter(
        (cell: dia.Cell) => cell && cell.attributes.type === 'standard.TextBlock',
      ) as dia.Element[];

    const elementVariables = variables[editingElementId] || [];
    const errors: { [key: number]: string | null } = {};
    const names = editedLabels.map((label) => label.trim());

    // Revalidate editedLabels
    editedLabels.forEach((label, index) => {
      if (!label.trim()) {
        errors[index] = 'Label cannot be empty';
      } else if (names.indexOf(label.trim()) !== index) {
        errors[index] = 'Label must be unique';
      } else {
        errors[index] = null;
      }
    });

    setFieldErrors(errors);

    // Prevent saving if there are errors
    if (Object.values(errors).some((error) => error !== null)) {
      return;
    }

    // Update the main element's attributes if necessary
    if (element) {
      const currentAttrs = element.attr();
      const updatedAttrs: dia.Element.Attributes = {
        body: {
          fill: elementAttrs.color || currentAttrs.body.fill || 'white',
          stroke: elementAttrs.borderColor || currentAttrs.body.stroke || '#000000',
          strokeWidth: elementAttrs.borderWidth || currentAttrs.body.strokeWidth || 1,
          name: elementAttrs.name || currentAttrs.body.name || currentAttrs.label.text || 'title',
          ...getBorderStyle(elementAttrs.borderStyle || currentAttrs.body.borderStyle || 'solid'),
          borderStyle: elementAttrs.borderStyle || currentAttrs.body.borderStyle || 'solid',
        },
      };

      if (base64Image) {
        injectPatternIntoDefs(editingElementId.toString(), base64Image);
        updatedAttrs.body.fill = `url(#pattern-${editingElementId})`;

        const svgImage = document.querySelector(
          `#pattern-${editingElementId} image`,
        ) as SVGImageElement;
        if (svgImage) {
          svgImage.setAttribute('opacity', selectedOpacity.toString());
        }
      } else {
        const patternElement = document.getElementById(`pattern-${editingElementId}`);
        if (patternElement) {
          patternElement.remove();
        }
      }

      element.attr(updatedAttrs);
    }

    // Apply changes to `TextBlock` elements
    textBlocks.forEach((block, index) => {
      const updatedLabel = editedLabels[index] || 'Untitled';
      block.attr('label/text', updatedLabel);

      // Optionally update associated data
      block.prop('data/label', updatedLabel);

      // Dispatch updated label to Redux
      dispatch(
        diagramSlice.actions.updateCustomElementLabel({
          groupId: String(editingElementId),
          index,
          newLabel: updatedLabel,
        }),
      );
    });

    // Update the labels of the `standard.TextBlock` elements
    textBlocks.forEach((block, index) => {
      const labelText = block.attr('label/text');
      const updatedLabel = elementVariables[index]?.label || labelText || 'Untitled';

      if (labelText !== updatedLabel) {
        block.attr('label/text', updatedLabel);
      }
    });

    // Close the modal after saving
    dispatch(setEditDialogOpen(false));
  };

  const handleRemoveImage = () => {
    dispatch(setBase64Image(null));
    dispatch(setImageUploaded(false));
  };

  const handleAttrChangeElement = (attr: string, value: string) => {
    dispatch(
      setElementAttrs({
        ...elementAttrs,
        [attr]: value,
      }),
    );

    if (selectedElement) {
      if (attr === 'name') {
        const updatedAttrs = {
          ...selectedElement.attr(),
          label: { ...selectedElement.attr('label'), text: value },
        };
        selectedElement.attr(updatedAttrs);
      } else if (attr === 'borderColor') {
        selectedElement.attr('body/stroke', value);
        selectedElement.attr('top/stroke', value);
        selectedElement.attr('bottom/stroke', value);
        selectedElement.attr('border/stroke', value);
      } else if (attr === 'borderWidth') {
        selectedElement.attr('body/strokeWidth', parseInt(value, 10));
        selectedElement.attr('top/strokeWidth', parseInt(value, 10));
        selectedElement.attr('bottom/strokeWidth', parseInt(value, 10));
        selectedElement.attr('border/strokeWidth', parseInt(value, 10));
      }
    }
  };

  const removeAllLinks = () => {
    if (graphRef.current) {
      const links = graphRef.current.getLinks();
      links.forEach((link) => link.remove());
    }
  };

  const handlePredefinedImageSelect = (src: string, elementID: string) => {
    if (graphRef.current) {
      const element = graphRef.current.getCell(elementID ?? '') as dia.Element;
      if (element) {
        injectPatternIntoDefs(String(elementID), src);
        element.attr({
          image: { 'xlink:href': src },
          label: { text: 'image' },
        });
      }
    }
    dispatch(setBase64Image(src));
  };

  return {
    onDrop,
    onDragOver,
    selectedElement,
    handleOpenEditDialog,
    elementName,
    handleAttrChangeElement,
    elementAttrs,
    elementType,
    handleImageUpload,
    base64Image,
    handleUpdateElement,
    editLinkDialogOpen,
    linkAttrs,
    deleteSelectedElement,
    removeAllLinks,
    imageUploaded,
    opacity,
    handleImageUploadInternal,
    handleOpacityChange,
    iconVisible,
    iconPosition,
    setIconVisible,
    handleRemoveImage,
    imageUploadModalOpen,
    applyImageToElement,
    zoomIn,
    zoomOut,
    resetZoom,
    setFieldErrors,
    fieldErrors,
    editedLabels,
    setEditedLabels,
    handleOpenLinkEditDialog,
    errors,
    handlePredefinedImageSelect,
    removeAllVertexTools,
    handleAddPort,
    handleEditPort,
    handleDeletePort,
    handlePortSliderChange,
    selectedPortId,
    visiblePorts,
    x,
    y,
  };
};
