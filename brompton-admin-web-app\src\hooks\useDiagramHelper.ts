import { dia, linkTools } from '@joint/core';
import { MutableRefObject, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Pipe from '~/components/JointJs/DiagramComponent/Pipe';
import Pump from '~/components/JointJs/DiagramComponent/Pump';
import { theme } from '~/pages/_app';
import {
  getLinkAttrs,
  getSelectedElement,
  getSelectedElements,
  getSelectedLink,
  getZoomLevel,
} from '~/redux/selectors/diagramSelector';
import { diagramSlice } from '~/redux/slices/diagramSlice';
import { cellNamespace, elementSettings } from '~/types/diagram';
type useDiagramHelperProps = {
  deleteIconPositionRef: MutableRefObject<{
    x: number;
    y: number;
  }>;
  graphRef: MutableRefObject<dia.Graph<dia.Graph.Attributes, dia.ModelSetOptions> | null>;
  paperRef: MutableRefObject<HTMLDivElement | null>;
  sourceElementRef: MutableRefObject<dia.Element<
    dia.Element.Attributes,
    dia.ModelSetOptions
  > | null>;
  resizeDotsRef: MutableRefObject<HTMLDivElement[]>;
  hideIconTimeoutRef: MutableRefObject<NodeJS.Timeout | null>;
  rotationDotsRef: MutableRefObject<HTMLDivElement[]>;
};

const MIN_ZOOM = 0.5;
const MAX_ZOOM = 2.5;
const ZOOM_STEP = 0.1;

export const useDiagramHelper = ({
  deleteIconPositionRef,
  graphRef,
  paperRef,
  sourceElementRef,
  resizeDotsRef,
  hideIconTimeoutRef,
  rotationDotsRef,
}: useDiagramHelperProps) => {
  const dispatch = useDispatch();
  const zoomLevel = useSelector(getZoomLevel);
  const {
    setSelectedElement,
    setIconVisible,
    setSelectedLink,
    setHoveredLink,
    setIconPosition,
    setLinkAttrs,
    setEditLinkDialogOpen,
    addSelectedElement,
    removeAllSlectedElemts,
  } = diagramSlice.actions;
  const selectedElement = useSelector(getSelectedElement);
  const selectedElementsList = useSelector(getSelectedElements);
  const linkAttrs = useSelector(getLinkAttrs);
  const selectedLink = useSelector(getSelectedLink);
  const paperInstanceRef = useRef<dia.Paper | null>(null);
  const selectedElementsRef = useRef<dia.Element[]>([...selectedElementsList]);
  const linkHideTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const elementHideTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [selectedPortId, setSelectedPortId] = useState<string | null>(null);
  const [visiblePorts, setVisiblePorts] = useState<dia.Element.Port[]>([]);

  const clearHandlers = () => {
    resizeDotsRef.current.forEach((dot) => dot.remove());
    resizeDotsRef.current = [];

    rotationDotsRef.current.forEach((dot) => dot.remove());
    rotationDotsRef.current = [];

    dispatch(setIconVisible(false));
  };

  const updateHandlerPositions = () => {
    if (selectedElement) {
      resizeDotsRef.current.forEach((dot) => dot.remove());
      resizeDotsRef.current = addResizeDots(selectedElement);

      rotationDotsRef.current.forEach((dot) => dot.remove());
      rotationDotsRef.current = addRotationDots(selectedElement);
    }
  };

  const zoomIn = () => {
    if (paperInstanceRef.current) {
      const currentScale = paperInstanceRef.current.scale();
      const newScale = Math.min(currentScale.sx + ZOOM_STEP, MAX_ZOOM);
      paperInstanceRef.current.scale(newScale, newScale);
      dispatch(diagramSlice.actions.setZoomLevel(paperInstanceRef.current?.scale().sx || 1));
      updateHandlerPositions();
    }
  };

  const zoomOut = () => {
    if (paperInstanceRef.current) {
      const currentScale = paperInstanceRef.current.scale();
      const newScale = Math.max(currentScale.sx - ZOOM_STEP, MIN_ZOOM);
      paperInstanceRef.current.scale(newScale, newScale);
      dispatch(diagramSlice.actions.setZoomLevel(paperInstanceRef.current?.scale().sx || 1));
      updateHandlerPositions();
    }
  };

  const resetZoom = () => {
    if (paperInstanceRef.current) {
      paperInstanceRef.current.scale(1, 1);
      updateHandlerPositions();
    }
  };

  const getDynamicAnchor = (source: dia.Element, target: dia.Element): string => {
    const sourceBBox = source.getBBox();
    const targetBBox = target.getBBox();

    if (targetBBox.y + targetBBox.height < sourceBBox.y) {
      return 'top';
    } else if (targetBBox.y > sourceBBox.y + sourceBBox.height) {
      return 'bottom';
    } else if (targetBBox.x > sourceBBox.x + sourceBBox.width) {
      return 'right';
    } else {
      return 'left';
    }
  };

  const createPipeLink = (source: dia.Element, target: dia.Element) => {
    if (graphRef.current) {
      if (
        source.get('type') === 'standard.TextBlock' ||
        target.get('type') === 'standard.TextBlock'
      ) {
        console.log('DraggableLabel elements cannot be connected.');
        return;
      }

      const existingLinks = graphRef.current.getLinks();

      const linkExists = existingLinks.some((link) => {
        const linkSourceId = link.get('source').id;
        const linkTargetId = link.get('target').id;

        return (
          (linkSourceId === source.id && linkTargetId === target.id) ||
          (linkSourceId === target.id && linkTargetId === source.id)
        );
      });

      if (linkExists) {
        console.log('A link between the source and target already exists.');
        return;
      }

      // Determine dynamic anchor points
      const sourceAnchor = getDynamicAnchor(source, target);
      const targetAnchor = getDynamicAnchor(target, source);

      const pipeLink = new Pipe();
      pipeLink.source(
        { id: source.id },
        {
          anchor: { name: sourceAnchor },
          connectionPoint: { name: 'boundary' },
        },
      );
      pipeLink.target(
        { id: target.id },
        {
          anchor: { name: targetAnchor },
          connectionPoint: { name: 'boundary' },
        },
      );
      pipeLink.router('manhattan', { step: 20 });
      pipeLink.connector('normal');
      pipeLink.attr({
        line: {
          stroke: '#000000', // Black color
          strokeWidth: 3, // Default width
          strokeDasharray: '0', // Solid line
        },
        outline: {
          strokeWidth: 0, // No outline by default
        },
        liquid: {
          strokeWidth: 0, // No liquid stroke by default
          strokeDasharray: '', // No dash pattern
          strokeDashoffset: 0, // No dash offset
        },
      });

      pipeLink.set('animation', { enabled: false, speed: 0 });
      pipeLink.set('direction', 'forward');

      pipeLink?.addTo(graphRef.current);

      source.removeAttr('body/filter');
      sourceElementRef.current = null;
    }
  };

  const removeAllVertexTools = () => {
    if (graphRef.current) {
      const links = graphRef.current.getLinks();
      links.forEach((link) => {
        const linkView = link.findView(paperInstanceRef.current!);
        if (linkView) {
          linkView.removeTools();
        }
      });
    }
  };

  const handleResizeStart = (
    e: MouseEvent,
    element: dia.Element,
    index: number,
    maintainAspectRatio: boolean,
  ) => {
    e.preventDefault();
    const startX = e.clientX;
    const startY = e.clientY;
    const startSize = element.size();
    const startPos = element.position();
    const aspectRatio = startSize.width / startSize.height;

    const onMouseMove = (moveEvent: MouseEvent) => {
      const deltaX = moveEvent.clientX - startX;
      const deltaY = moveEvent.clientY - startY;
      let newWidth = startSize.width;
      let newHeight = startSize.height;
      const newPosition = { ...startPos };
      // Adjust based on dot index
      if (maintainAspectRatio) {
        // Ensuring 1:1 aspect ratio by taking the larger movement delta
        const maxDelta = Math.max(deltaX, deltaY);
        newWidth = startSize.width + maxDelta;
        newHeight = startSize.height + maxDelta; // Keep square proportion

        switch (index) {
          case 0: // Top-left
            newPosition.x = startPos.x - maxDelta;
            newPosition.y = startPos.y - maxDelta;
            break;
          case 1: // Top-right
            newPosition.y = startPos.y - maxDelta;
            break;
          case 2: // Bottom-left
            newPosition.x = startPos.x - maxDelta;
            break;
          case 3: // Bottom-right
            break;
        }
      } else {
        // Normal resizing logic for non-image elements
        switch (index) {
          case 0: // Top-left
            newWidth = startSize.width - deltaX;
            newHeight = startSize.height - deltaY;
            newPosition.x = startPos.x + deltaX;
            newPosition.y = startPos.y + deltaY;
            break;
          case 1: // Top-center
            newHeight = startSize.height - deltaY;
            newPosition.y = startPos.y + deltaY;
            break;
          case 2: // Top-right
            newWidth = startSize.width + deltaX;
            newHeight = startSize.height - deltaY;
            newPosition.y = startPos.y + deltaY;
            break;
          case 3: // Right-center
            newWidth = startSize.width + deltaX;
            break;
          case 4: // Bottom-right
            newWidth = startSize.width + deltaX;
            newHeight = startSize.height + deltaY;
            break;
          case 5: // Bottom-center
            newHeight = startSize.height + deltaY;
            break;
          case 6: // Bottom-left
            newWidth = startSize.width - deltaX;
            newHeight = startSize.height + deltaY;
            newPosition.x = startPos.x + deltaX;
            break;
          case 7: // Left-center
            newWidth = startSize.width - deltaX;
            newPosition.x = startPos.x + deltaX;
            break;
        }
      }

      // Apply minimum width/height constraints
      newWidth = Math.max(newWidth, 20);
      newHeight = Math.max(newHeight, 20);

      // Update element's size and position
      element.resize(newWidth, newHeight);
      element.position(newPosition.x, newPosition.y);
    };

    const onMouseUp = () => {
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  };

  const addResizeDots = (element: dia.Element): HTMLDivElement[] => {
    const dotSize = 8;
    const dotColor = '#0077b6';
    const bbox = element.getBBox();
    const scale = paperInstanceRef.current?.scale().sx || 1; // Get the current scale
    const translation = paperInstanceRef.current?.translate() || { tx: 0, ty: 0 }; // Get the canvas translation

    const isImageElement =
      element.get('type') === 'standard.Image' ||
      element.get('type') === 'standard.BorderedImage' ||
      element.get('type') === 'standard.Circle' ||
      element.get('type') === 'HandValve' ||
      element.get('type') === 'ControlValve' ||
      element.get('type') === 'Pump';

    const positions = isImageElement
      ? [
          // Only 4 dots (corner positions)
          {
            x: bbox.x * scale + translation.tx,
            y: bbox.y * scale + translation.ty,
            cursor: 'nw-resize',
          },
          {
            x: (bbox.x + bbox.width) * scale + translation.tx,
            y: bbox.y * scale + translation.ty,
            cursor: 'ne-resize',
          },
          {
            x: bbox.x * scale + translation.tx,
            y: (bbox.y + bbox.height) * scale + translation.ty,
            cursor: 'sw-resize',
          },
          {
            x: (bbox.x + bbox.width) * scale + translation.tx,
            y: (bbox.y + bbox.height) * scale + translation.ty,
            cursor: 'se-resize',
          },
        ]
      : [
          // 8 dots (full resize for other elements)
          {
            x: bbox.x * scale + translation.tx,
            y: bbox.y * scale + translation.ty,
            cursor: 'nw-resize',
          },
          {
            x: (bbox.x + bbox.width / 2) * scale + translation.tx,
            y: bbox.y * scale + translation.ty,
            cursor: 'n-resize',
          },
          {
            x: (bbox.x + bbox.width) * scale + translation.tx,
            y: bbox.y * scale + translation.ty,
            cursor: 'ne-resize',
          },
          {
            x: (bbox.x + bbox.width) * scale + translation.tx,
            y: (bbox.y + bbox.height / 2) * scale + translation.ty,
            cursor: 'e-resize',
          },
          {
            x: (bbox.x + bbox.width) * scale + translation.tx,
            y: (bbox.y + bbox.height) * scale + translation.ty,
            cursor: 'se-resize',
          },
          {
            x: (bbox.x + bbox.width / 2) * scale + translation.tx,
            y: (bbox.y + bbox.height) * scale + translation.ty,
            cursor: 's-resize',
          },
          {
            x: bbox.x * scale + translation.tx,
            y: (bbox.y + bbox.height) * scale + translation.ty,
            cursor: 'sw-resize',
          },
          {
            x: bbox.x * scale + translation.tx,
            y: (bbox.y + bbox.height / 2) * scale + translation.ty,
            cursor: 'w-resize',
          },
        ];

    return positions.map((pos, index) => {
      const dot = document.createElement('div');
      dot.style.position = 'absolute';
      dot.style.width = `${dotSize}px`;
      dot.style.height = `${dotSize}px`;
      dot.style.backgroundColor = dotColor;
      dot.style.borderRadius = '50%';
      dot.style.cursor = pos.cursor;
      dot.style.left = `${pos.x}px`;
      dot.style.top = `${pos.y}px`;
      dot.style.boxShadow = '0 0 3px rgba(0,0,0,0.3)';
      dot.style.zIndex = '1000';

      dot.onmousedown = (e) => handleResizeStart(e, element, index, isImageElement);

      paperRef.current?.appendChild(dot);

      return dot;
    });
  };

  const deleteSelectedElement = () => {
    if (selectedElement && graphRef.current) {
      const data = selectedElement.get('data') as elementSettings;
      const connectedLinks = graphRef.current.getConnectedLinks(selectedElement);
      connectedLinks.forEach((link) => link.remove());
      selectedElement.remove();
      (data?.variableLabels || [])
        .map((labelId) => graphRef.current!.getCell(labelId))
        .filter(Boolean)
        .forEach((cell) => cell.remove());
      clearHandlers();
      dispatch(setSelectedElement(null));
      sourceElementRef.current = null;
    }
  };

  const addRotationDots = (element: dia.Element): HTMLDivElement[] => {
    const dotSize = 8;
    const rotationDotColor = '#ff8c00';
    const bbox = element.getBBox();
    const scale = paperInstanceRef.current?.scale().sx || 1; // Get current scale
    const translation = paperInstanceRef.current?.translate() || { tx: 0, ty: 0 }; // Get canvas translation

    const rotationPositions = [
      {
        x: (bbox.x + bbox.width / 2) * scale + translation.tx,
        y: (bbox.y - 20) * scale + translation.ty,
        cursor: 'grab',
        id: 'top',
      },
      {
        x: (bbox.x + bbox.width + 20) * scale + translation.tx,
        y: (bbox.y + bbox.height / 2) * scale + translation.ty,
        cursor: 'grab',
        id: 'right',
      },
      {
        x: (bbox.x + bbox.width / 2) * scale + translation.tx,
        y: (bbox.y + bbox.height + 20) * scale + translation.ty,
        cursor: 'grab',
        id: 'bottom',
      },
      {
        x: (bbox.x - 20) * scale + translation.tx,
        y: (bbox.y + bbox.height / 2) * scale + translation.ty,
        cursor: 'grab',
        id: 'left',
      },
    ];

    return rotationPositions.map((pos) => {
      const dot = document.createElement('div');
      dot.style.position = 'absolute';
      dot.style.width = `${dotSize}px`;
      dot.style.height = `${dotSize}px`;
      dot.style.backgroundColor = rotationDotColor;
      dot.style.borderRadius = '50%';
      dot.style.cursor = pos.cursor;
      dot.style.left = `${pos.x}px`;
      dot.style.top = `${pos.y}px`;
      dot.style.boxShadow = '0 0 3px rgba(0,0,0,0.3)';
      dot.style.zIndex = '1000';

      dot.onmousedown = (e: MouseEvent) => handleRotationStart(e, element, pos.id);

      paperRef.current?.appendChild(dot);
      return dot;
    });
  };

  // Rotation handler logic
  const handleRotationStart = (e: MouseEvent, element: dia.Element, handlerId: string) => {
    e.preventDefault();

    // Set grabbing cursor on drag start
    document.body.style.cursor = 'grabbing';

    // Get the center coordinates of the element for rotation calculations
    const centerX = element.position().x + element.size().width / 2;
    const centerY = element.position().y + element.size().height / 2;

    // Store the starting mouse position and initial rotation angle
    const initialAngle = element.get('angle') || 0;
    const startMousePos = { x: e.clientX, y: e.clientY };
    let previousAngle = initialAngle;

    // Helper functions similar to the provided code
    function getAngle(
      a: { x: number; y: number },
      b: { x: number; y: number },
      c: { x: number; y: number },
    ) {
      const AB = Math.sqrt(Math.pow(b.x - a.x, 2) + Math.pow(b.y - a.y, 2));
      const BC = Math.sqrt(Math.pow(b.x - c.x, 2) + Math.pow(b.y - c.y, 2));
      const AC = Math.sqrt(Math.pow(c.x - a.x, 2) + Math.pow(c.y - a.y, 2));
      return Math.acos((BC * BC + AB * AB - AC * AC) / (2 * BC * AB));
    }

    function ccw(
      a: { x: number; y: number },
      b: { x: number; y: number },
      c: { x: number; y: number },
    ) {
      const area2 = (b.x - a.x) * (c.y - a.y) - (b.y - a.y) * (c.x - a.x);
      if (area2 < 0) return -1;
      if (area2 > 0) return +1;
      return 0;
    }

    const onMouseMove = (moveEvent: MouseEvent) => {
      const currentMousePos = { x: moveEvent.clientX, y: moveEvent.clientY };
      const direction = ccw(startMousePos, { x: centerX, y: centerY }, currentMousePos);
      const angle = getAngle(startMousePos, { x: centerX, y: centerY }, currentMousePos);

      // Calculate new rotation angle based on direction
      const rotationAngle = direction === -1 ? previousAngle - angle : previousAngle + angle;

      // Apply the rotation to the element
      element.rotate(rotationAngle, true);

      // Update the previous angle for smooth incremental rotation
      previousAngle = rotationAngle;
    };

    const onMouseUp = () => {
      document.body.style.cursor = ''; // Reset cursor
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  };

  const handleOpenLinkEditDialog = (link: dia.Link) => {
    dispatch(setSelectedLink(link));
    dispatch(setEditLinkDialogOpen(true));
  };

  const updateHandlersForPanning = () => {
    if (selectedElement) {
      resizeDotsRef.current.forEach((dot) => dot.remove());
      rotationDotsRef.current.forEach((dot) => dot.remove());

      resizeDotsRef.current = addResizeDots(selectedElement);
      rotationDotsRef.current = addRotationDots(selectedElement);
    }
  };

  const mapSliderValueToSpeed = (sliderValue: number): number => {
    return Math.max(0.1, sliderValue / 10); // Scale linearly from 0.1 (slow) to 10 (fast)
  };

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Delete' && selectedElement && graphRef.current) {
        deleteSelectedElement();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedElement]);

  useEffect(() => {
    let animationFrameId: number;

    const animateFlow = () => {
      const pipes = graphRef.current?.getLinks().filter((link) => link instanceof Pipe);
      pipes?.forEach((pipe) => {
        const liquidAttrs = pipe.attr('liquid');

        // Skip animation if not enabled
        const animation = pipe.get('animation') || { enabled: false, speed: 0 };
        if (!animation.enabled) return;

        const currentOffset = parseFloat(liquidAttrs.strokeDashoffset) || 0;

        // Map slider value to speed multiplier
        const speedMultiplier = mapSliderValueToSpeed(animation.speed || 0);

        // Update strokeDashoffset based on direction and speed
        if (linkAttrs.direction === 'forward') {
          pipe.attr('liquid/strokeDashoffset', currentOffset - 2 * speedMultiplier);
        } else if (linkAttrs.direction === 'backward') {
          pipe.attr('liquid/strokeDashoffset', currentOffset + 2 * speedMultiplier);
        } else if (linkAttrs.direction === 'bidirectional') {
          pipe.attr('liquid/strokeDashoffset', Math.sin(Date.now() / 100) * 20);
        }
      });

      animationFrameId = requestAnimationFrame(animateFlow);
    };

    animateFlow();

    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, [linkAttrs]);

  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      e.preventDefault();
      if (e.deltaY < 0) {
        zoomIn();
      } else {
        zoomOut();
      }
    };

    if (paperInstanceRef.current) {
      paperInstanceRef.current.el.addEventListener('wheel', handleWheel);
    }

    return () => {
      if (paperInstanceRef.current) {
        paperInstanceRef.current.el.removeEventListener('wheel', handleWheel);
      }
    };
  }, [zoomIn, zoomOut]);

  const handleElementSelect = (element: dia.Element, addToSelection: boolean) => {
    if (addToSelection) {
      // Add element to selection if it's not already selected
      if (!selectedElementsRef.current.includes(element)) {
        selectedElementsRef.current.push(element);
        dispatch(addSelectedElement({ element }));
      }
    } else {
      // Select only the clicked element
      selectedElementsRef.current = [element];
    }
    dispatch(setSelectedElement(element));
  };

  const resetAllPortHighlights = () => {
    graphRef.current?.getElements().forEach((el) => {
      el.getPorts().forEach((port) => {
        el.portProp(String(port.id), 'attrs/portBody/stroke', '#000000');
        el.portProp(String(port.id), 'attrs/portBody/strokeWidth', 1);
      });
    });
  };

  const highlightSelectedPort = (element: dia.Element | null, portId: string | null) => {
    if (!element || !portId) return;
    resetAllPortHighlights();
    element.portProp(portId, 'attrs/portBody/stroke', theme.palette.primary.main);
    element.portProp(portId, 'attrs/portBody/strokeWidth', 3);
  };

  useEffect(() => {
    const handleShiftPan = (
      paper: dia.Paper,
      paperRef: MutableRefObject<HTMLDivElement | null>,
    ) => {
      paper.on('blank:pointerdown', (evt) => {
        if (evt.shiftKey && paperRef.current) {
          const startPosition = { x: evt.clientX ?? 0, y: evt.clientY ?? 0 };
          const initialOrigin = paper.translate();

          // Change cursor to grabbing hand on the canvas
          paperRef.current.style.cursor = 'grabbing';

          const onPointerMove = (moveEvt: any) => {
            const dx = moveEvt.clientX - startPosition.x;
            const dy = moveEvt.clientY - startPosition.y;
            paper.translate(initialOrigin.tx + dx, initialOrigin.ty + dy);
          };

          const onPointerUp = () => {
            // Reset cursor to default on the canvas
            if (paperRef.current) {
              paperRef.current.style.cursor = 'default';
            }

            document.removeEventListener('mousemove', onPointerMove);
            document.removeEventListener('mouseup', onPointerUp);
          };

          document.addEventListener('mousemove', onPointerMove);
          document.addEventListener('mouseup', onPointerUp);
        }
      });
    };

    if (paperRef.current && !graphRef.current) {
      const graph = new dia.Graph(
        {},
        {
          cellNamespace: cellNamespace,
        },
      );
      graphRef.current = graph;

      const paper = new dia.Paper({
        el: paperRef.current,
        model: graph,
        width: '100%',
        height: '100vh',
        gridSize: 10,
        drawGrid: true,
        cellViewNamespace: cellNamespace,
        interactive: { linkMove: true },
        snapLinks: { radius: 30 },
        linkPinning: false,
        defaultConnectionPoint: { name: 'rectangle' },
        validateConnection: function (cellViewS, magnetS, cellViewT, magnetT) {
          if (cellViewS.model === cellViewT.model) {
            return false;
          }
          return !!(magnetS && magnetT);
        },
      });

      paperInstanceRef.current = paper;
      handleShiftPan(paper, paperRef);

      paper.on('translate', updateHandlersForPanning);

      paper.on('link:mouseenter', (linkView) => {
        const tools = new dia.ToolsView({
          tools: [
            new linkTools.Vertices(), // Allows vertex control
            new linkTools.Segments(), // Enables segment editing
            new linkTools.Remove({ distance: '50%' }), // Delete button
            new linkTools.Button({
              distance: '40%', // Position button at the middle of the link
              action: (_evt, linkView) => {
                const link = linkView.model;
                handleOpenLinkEditDialog(link);
              },
              markup: [
                {
                  tagName: 'circle',
                  selector: 'button',
                  attributes: {
                    r: 7,
                    fill: theme.palette.primary.main,
                    cursor: 'pointer',
                  },
                },
                {
                  tagName: 'text',
                  selector: 'label',
                  attributes: {
                    'font-size': 10,
                    'font-weight': 'bold',
                    'text-anchor': 'middle', // Centers horizontally
                    'dominant-baseline': 'middle', // Centers vertically
                    fill: '#FFFFFF',
                    cursor: 'pointer',
                    x: 0, // Keeps it centered within the circle
                    y: 1, // Adjusted for better alignment
                  },
                  textContent: 'E',
                },
              ],
            }),
          ],
        });
        linkView.addTools(tools);
      });

      paper.on('link:mouseleave', (linkView) => {
        linkView.removeTools();
      });

      paper.on('element:mouseenter', (elementView: dia.ElementView) => {
        const element = elementView.model;
        resizeDotsRef.current.forEach((dot) => dot.remove());
        resizeDotsRef.current = addResizeDots(element);

        rotationDotsRef.current.forEach((dot) => dot.remove());
        rotationDotsRef.current = addRotationDots(element);

        // dispatch(setSelectedElement(element as dia.Element));
        clearTimeout(hideIconTimeoutRef.current!);
        dispatch(setIconVisible(true));
      });

      paper.on('element:pointerdown', (elementView, evt) => {
        const element = elementView.model;

        // Multi-selection logic with Ctrl key
        const addToSelection = evt.ctrlKey; // Use Ctrl key for multi-select
        if (addToSelection) {
          // Add the clicked element to the selection
          handleElementSelect(element, true);

          // Apply drop shadow to the selected element
          element?.attr('body/filter', {
            name: 'dropShadow',
            args: { dx: 3, dy: 3, blur: 5, color: 'rgba(0, 0, 0, 0.6)' },
          });

          if (element.get('type') === 'standard.BorderedImage') {
            element.attr('image/filter', {
              name: 'dropShadow',
              args: { dx: 3, dy: 3, blur: 5, color: 'rgba(0, 0, 0, 0.6)' },
            });
          }
        } else {
          // Single selection logic

          sourceElementRef.current = null;

          // Clear all previous selections
          dispatch(removeAllSlectedElemts());

          // Remove drop shadow from all elements for single selection
          graphRef.current?.getElements().forEach((el) => {
            el.attr('body/filter', null);
            if (el.get('type') === 'standard.BorderedImage') {
              el.attr('image/filter', null);
            }
          });

          // Set the clicked element as the new source
          sourceElementRef.current = element;
          element.attr('body/filter', {
            name: 'dropShadow',
            args: { dx: 3, dy: 3, blur: 5, color: 'rgba(0, 0, 0, 0.6)' },
          });

          if (element.get('type') === 'standard.BorderedImage') {
            element.attr('image/filter', {
              name: 'dropShadow',
              args: { dx: 3, dy: 3, blur: 5, color: 'rgba(0, 0, 0, 0.6)' },
            });
          }
        }

        // Update Redux state with the selected element
        dispatch(setSelectedElement(element as dia.Element));

        // Handle Pump-specific logic
        if (element instanceof Pump) {
          element.changeRotation();
        }

        // Dispatch the selected element's attributes to the Redux store
        dispatch(diagramSlice.actions.setElementAttrs(element.attr().body));
      });

      paper.on('blank:pointerdown', () => {
        if (sourceElementRef.current) {
          sourceElementRef.current.attr('body/filter', null); // Remove shadow
          sourceElementRef.current.attr('image/filter', null); // Remove shadow
          sourceElementRef.current = null;
        }

        // Reset Redux selections
        dispatch(setSelectedElement(null));
        dispatch(removeAllSlectedElemts());

        // Remove drop shadow from all elements
        graphRef.current?.getElements().forEach((el) => {
          el.attr('body/filter', null);
          el.attr('image/filter', null);
        });

        // Optionally clear handlers or additional UI indicators
        clearHandlers();
      });

      paper.on('element:mouseenter', (elementView) => {
        const element = elementView.model;
        if (resizeDotsRef.current.length > 0) {
          resizeDotsRef.current.forEach((dot) => dot.remove());
          resizeDotsRef.current = []; // Clear the ref after removal
        }
        resizeDotsRef.current = addResizeDots(element);
        // dispatch(setSelectedElement(element as dia.Element));
        // dispatch(diagramSlice.actions.setElementAttrs(element.attr().body));
        clearTimeout(hideIconTimeoutRef.current!); // Cancel any hide timeout
        dispatch(setIconVisible(true)); // Show the icon
      });

      paper.on('link:pointerdown', (linkView) => {
        const link = linkView.model as dia.Link;
        dispatch(setSelectedLink(link));

        const vertexTool = new linkTools.Vertices({
          snapRadius: 10,
        });

        const toolsView = new dia.ToolsView({
          tools: [vertexTool],
        });

        linkView.addTools(toolsView);

        linkView.on('link:unfocus', () => {
          linkView.removeTools();
        });
      });

      paper.on('link:mouseenter', (linkView) => {
        const link = linkView.model as dia.Link;
        if (hideIconTimeoutRef.current) clearTimeout(hideIconTimeoutRef.current);
        dispatch(setHoveredLink(link));

        // Calculate a point along the link’s path for icon positioning
        const pathLength = linkView.getConnectionLength();
        const midpoint = linkView.getPointAtLength(pathLength / 2);
        deleteIconPositionRef.current = {
          x: midpoint.x + 10, // Offset for a closer position
          y: midpoint.y - 10,
        };
      });

      paper.on('link:mouseleave', () => {
        if (linkHideTimeoutRef.current) clearTimeout(linkHideTimeoutRef.current);
        linkHideTimeoutRef.current = setTimeout(() => {
          dispatch(setHoveredLink(null));
          linkHideTimeoutRef.current = null;
        }, 1000);
      });

      paper.on('element:mouseleave', () => {
        if (elementHideTimeoutRef.current) clearTimeout(elementHideTimeoutRef.current);
        elementHideTimeoutRef.current = setTimeout(() => {
          dispatch(setIconVisible(false));
          dispatch(setHoveredLink(null));
          clearHandlers();
          resizeDotsRef.current.forEach((dot) => dot.remove());
          resizeDotsRef.current = [];
          rotationDotsRef.current.forEach((dot) => dot.remove());
          rotationDotsRef.current = [];
          elementHideTimeoutRef.current = null;
        }, 1000);
      });

      paper.on('element:pointerclick', (elementView) => {
        const model = elementView.model;
        resetAllPortHighlights();
        setSelectedElement(model);
        setSelectedPortId(null);
        setVisiblePorts(model.getPorts());
      });

      paper.on('element:port:pointerdown', ({ model }, _evt, portId) => {
        setSelectedElement(model);
        setSelectedPortId(portId);
        highlightSelectedPort(model, portId);
      });

      paper.on('blank:pointerdown', () => {
        resetAllPortHighlights();
        setSelectedElement(null);
        setSelectedPortId(null);
      });

      graph?.on('change:position', (element: dia.Element) => {
        const parent = element.getParentCell();
        if (parent) {
          const parentBBox = parent.getBBox();
          const elementBBox = element.getBBox();
          const newX = Math.max(
            parentBBox.x,
            Math.min(parentBBox.x + parentBBox.width - elementBBox.width, elementBBox.x),
          );
          const newY = Math.max(
            parentBBox.y,
            Math.min(parentBBox.y + parentBBox.height - elementBBox.height, elementBBox.y),
          );
          if (elementBBox.x !== newX || elementBBox.y !== newY) {
            element.position(newX, newY);
          }
        }
      });

      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Delete' && selectedElement) {
          deleteSelectedElement();
        }
      };
      document.addEventListener('keydown', handleKeyDown);

      return () => {
        document.removeEventListener('keydown', handleKeyDown);
        paper.off('translate', updateHandlersForPanning);
      };
    }
  }, [selectedElement, selectedLink]);

  return {
    deleteSelectedElement,
    zoomIn,
    zoomOut,
    resetZoom,
    handleOpenLinkEditDialog,
    removeAllVertexTools,
    paperInstanceRef,
    resetAllPortHighlights,
    highlightSelectedPort,
    setSelectedPortId,
    selectedPortId,
    visiblePorts,
    setVisiblePorts,
  };
};
