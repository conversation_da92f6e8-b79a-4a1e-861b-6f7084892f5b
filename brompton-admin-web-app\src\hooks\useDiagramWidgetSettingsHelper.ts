import { dia } from '@joint/core';
import { MutableRefObject, useEffect, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useGetDashboardByCustomerIdQuery } from '~/redux/api/dashboardApi';
import { useGetAllDiagramsQuery, useGetDiagramDetailsQuery } from '~/redux/api/diagramApi';
import { getEndDate, getStartDate } from '~/redux/selectors/chartSelectors';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getAssetTz } from '~/redux/selectors/topPanleSelectors';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';
import { TimeRangeOptions } from '~/types/dashboard';
import { elementVariable } from '~/types/diagram';
import { DiagramWidget } from '~/types/widgets';
import { getPreviousDate } from '~/utils/utils';
type useDiagramWidgetSettingsHelperProps = {
  graphVersion: number;
  settings: DiagramWidget;
  graphRef: MutableRefObject<dia.Graph<dia.Graph.Attributes, dia.ModelSetOptions> | null>;
  handleSettingsChange: React.Dispatch<React.SetStateAction<DiagramWidget>>;
  setGrpahVersion: React.Dispatch<React.SetStateAction<number>>;
};
const useDiagramWidgetSettingsHelper = ({
  graphVersion,
  settings,
  graphRef,
  handleSettingsChange,
  setGrpahVersion,
}: useDiagramWidgetSettingsHelperProps) => {
  const activeCustomer = useSelector(getActiveCustomer);
  const dateTimeFormat = useSelector(getDateTimeFormat);
  const globalStartDate = useSelector(getStartDate);
  const assetTz = useSelector(getAssetTz);
  const globalendDate = useSelector(getEndDate);
  const { data: dashboardList, isLoading: isLoadingDashboards } = useGetDashboardByCustomerIdQuery(
    {
      customerId: activeCustomer?.id ?? 0,
      search: null,
    },
    {
      skip: !activeCustomer?.id,
      refetchOnMountOrArgChange: true,
    },
  );
  const { data: diagramsList, isFetching: fetchingDiagrams } = useGetAllDiagramsQuery();
  const { data: diagramData, isFetching: fetchNewData } = useGetDiagramDetailsQuery(
    {
      id: settings.selectedDiagram?.id ?? 0,
    },
    {
      skip: !settings.selectedDiagram?.id || settings.jsonFile !== '',
      refetchOnMountOrArgChange: true,
    },
  );
  const dashboardOptions = useMemo(
    () =>
      dashboardList?.items?.map((dashboard) => ({
        id: dashboard.id,
        title: dashboard.title,
      })) ?? [],
    [dashboardList],
  );
  const diagramOptions = useMemo(
    () =>
      diagramsList?.items?.map((item) => ({
        id: item.id,
        label: item.name,
      })) ?? [],
    [diagramsList],
  );
  useEffect(() => {
    if (settings.jsonFile !== '') {
      const jsonData = JSON.parse(settings.jsonFile);
      const graphData = jsonData?.graph ?? {};
      if (graphRef.current!.getCells().length > 0) {
        graphRef.current!.clear();
      }
      graphRef.current!.fromJSON(graphData);
    }
  }, [settings.jsonFile]);
  useEffect(() => {
    if (diagramData?.data && settings.jsonFile === '') {
      if (diagramData?.data) {
        try {
          graphRef.current!.clear();
          const parsedData = JSON.parse(diagramData.data);
          const diagram = parsedData?.diagram || {};
          if (graphRef.current!.getCells().length > 0) {
            graphRef.current!.clear();
          }
          graphRef.current!.fromJSON(diagram);
          const elementVariables: elementVariable[] = [];
          const elementIdVariabels: Record<string, elementVariable[]> = {};
          const cellIds: string[] = [];
          graphRef
            .current!.getCells()
            .filter((cell) => cell.get('type') !== 'Pipe')
            .forEach((cell) => {
              const cellData = cell.get('data'); // Get 'data' object from cell
              const variables = (cellData?.variables ?? []) as elementVariable[];
              if (variables.length > 0) {
                elementVariables.push(...variables);
                elementIdVariabels[cell.id.toString()] = variables;
                cellIds.push(cell.id.toString());
              }
              return null;
            });

          const settingsElementIdVariables: Record<string, elementVariable[]> = {
            ...settings.elementIdVariabels,
          };

          cellIds.forEach((cell) => {
            if (
              elementIdVariabels.hasOwnProperty(cell) &&
              !settingsElementIdVariables.hasOwnProperty(cell)
            ) {
              // Add the cell to settingsElementIdVariables from elementIdVariabels
              settingsElementIdVariables[cell] = elementIdVariabels[cell];
            }

            // If the cell exists in settingsElementIdVariables but does not exist in elementIdVariabels
            if (
              settingsElementIdVariables.hasOwnProperty(cell) &&
              !elementIdVariabels.hasOwnProperty(cell)
            ) {
              // Remove the cell from settingsElementIdVariables
              delete settingsElementIdVariables[cell];
            }
            // If the cell exists in both, and the number of variables is different
            if (
              settingsElementIdVariables.hasOwnProperty(cell) &&
              elementIdVariabels.hasOwnProperty(cell) &&
              settingsElementIdVariables[cell].length !== elementIdVariabels[cell].length
            ) {
              // this should sync  all variables one by one if variables is already exist then leave if variale is not there on settings and
              // elementIdVariables then update on settigs and vice-versa
              const settingsVars = [...settingsElementIdVariables[cell]];
              const elementVars = [...elementIdVariabels[cell]];

              // Add missing variables from elementIdVariabels to settingsElementIdVariables
              elementVars.forEach((varItem) => {
                if (
                  !settingsVars.some(
                    (settingsVar) => settingsVar.variableName === varItem.variableName,
                  )
                ) {
                  settingsVars.push(varItem);
                }
              });

              // Add missing variables from settingsElementIdVariables to elementIdVariabels
              settingsVars.forEach((varItem) => {
                if (
                  !elementVars.some(
                    (elementVar) => elementVar.variableName === varItem.variableName,
                  )
                ) {
                  elementVars.push(varItem);
                }
              });
              settingsElementIdVariables[cell] = settingsVars;
            }
          });
          Object.keys(settingsElementIdVariables).forEach((key) => {
            if (!cellIds.includes(key)) {
              delete settingsElementIdVariables[key];
            }
          });

          cellIds.forEach((cellId) => {
            if (!settingsElementIdVariables.hasOwnProperty(cellId)) {
              settingsElementIdVariables[cellId] = [];
            }
          });
          const uniqueVariables: elementVariable[] = Object.values(settingsElementIdVariables)
            .map((variable) => {
              return variable;
            })
            .flat();
          graphRef.current!.getCells().forEach((cell) => {
            const cellData = cell.get('data');
            const cellId = cell.id.toString();
            const variables = settingsElementIdVariables[cellId];

            if (variables) {
              // Update the cell's 'data' with the new variables
              cell.set('data', {
                ...cellData,
                variables, // Updated variables for the cell
              });
            }
          });
          const timeRange = 8;
          for (const [elementId, variables] of Object.entries(settingsElementIdVariables)) {
            settingsElementIdVariables[elementId] = variables.map((v) => ({
              ...v,
              aggBy: v?.aggBy,
              samplePeriod: v?.samplePeriod === undefined ? 1 : v?.samplePeriod,
              timeRange: v?.timeRange === undefined ? timeRange : v?.timeRange,
              startDate: getPreviousDate(
                TimeRangeOptions[v?.timeRange === undefined ? timeRange : v?.timeRange].serverValue,
              ),
              overrideAssetTz: v?.overrideAssetTz ?? false,
              overrideAssetTzValue: v?.overrideAssetTz ? v?.overrideAssetTzValue ?? false : false,
              endDate: new Date().getTime(),
              isRelativeToGlboalEndTime:
                v?.isRelativeToGlboalEndTime === undefined ? false : v.isRelativeToGlboalEndTime,
              globalSamplePeriod:
                v?.globalSamplePeriod === undefined ? false : v.globalSamplePeriod,
            }));
          }
          handleSettingsChange({
            ...settings,
            elementIdVariabels: settingsElementIdVariables,
            elementVariable: uniqueVariables,
            jsonFile: JSON.stringify({ graph: graphRef.current }, null),
          });
        } catch (error) {
          console.error('Error parsing diagram data:', error);
        }
      }
    }
  }, [diagramData]);
  const handleChangeDashboard = (
    _: React.SyntheticEvent,
    value: { id: number; title: string } | null,
  ) => {
    if (!value) return;
    handleSettingsChange((prevState) => ({
      ...prevState,
      dashboard: value,
      isDirty: true,
    }));
  };
  const handleChangeDiagramDropdown = (value: { id: number; label: string } | null) => {
    handleSettingsChange((prevState) => ({
      ...prevState,
      selectedDiagram: {
        id: value?.id ?? 0,
        name: value?.label ?? '',
      },
      jsonFile: '',
      isDirty: true,
    }));
    graphRef.current!.clear();
    setGrpahVersion(graphVersion + 1);
  };
  return {
    diagramsList,
    fetchingDiagrams,
    dashboardList,
    isLoadingDashboards,
    dashboardOptions,
    diagramOptions,
    fetchNewData,
    handleChangeDashboard,
    handleChangeDiagramDropdown,
  };
};
export default useDiagramWidgetSettingsHelper;
