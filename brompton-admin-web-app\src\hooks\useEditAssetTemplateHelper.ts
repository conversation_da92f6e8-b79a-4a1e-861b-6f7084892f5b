import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect, useMemo, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import {
  EditAssetTemplate,
  EditAssetTemplateSchema,
  getAssetTemplateDataWithMetricName,
  MeasurementSchema,
  measurementSchemaEdit,
} from '~/measurements/domain/types';
import {
  useGetAllBackOfficeAssetTypesMetricsQuery,
  useGetAllBackOfficeAssetTypesQuery,
} from '~/redux/api/assetsApi';
import {
  useGetCalculationEngineTemplatesQuery,
  useGetPollPeriodsQuery,
} from '~/redux/api/calculationEngine';
import {
  useGetAllDatasourcesQuery,
  useGetAllDataTypesQuery,
  useGetAllLocationsQuery,
  useGetAllMeasureTypesQuery,
  useGetAllValueTypesQuery,
} from '~/redux/api/measuresApi';
import {
  calculationMeasurementSchema,
  CalculationMeasurementSchemaData,
} from '~/types/calc_engine';
import { mapListToOptions } from '~/utils/utils';
import useGetAffectedAssetWithTemplates from './useGetAffectedAssetWithTemplates';

const useEditAssetTemplateHelper = ({
  data,
  currentMeausreIndex,
  activeStep,
}: {
  activeStep: number;
  data: getAssetTemplateDataWithMetricName | undefined;
  currentMeausreIndex: number;
}) => {
  const [calcMeasurements, setCalcMeasurements] = useState<
    {
      metric_id: string;
      existing: boolean;
      calcMeasurementData: CalculationMeasurementSchemaData;
    }[]
  >([]);

  const assets = useGetAffectedAssetWithTemplates({ assets: data?.asset ?? [] });

  const { data: assetTypeMetrics } = useGetAllBackOfficeAssetTypesMetricsQuery(
    {
      assetId: data?.asset_type_id ? data?.asset_type_id?.toString() : '',
    },
    {
      skip: data?.asset_type_id === undefined || data?.asset_type_id === 0,
      refetchOnMountOrArgChange: true,
    },
  );
  const { data: assetTypeListData } = useGetAllBackOfficeAssetTypesQuery();
  const { data: valueTypeList } = useGetAllValueTypesQuery();
  const { data: datasourceList } = useGetAllDatasourcesQuery({});
  const { data: locationsList } = useGetAllLocationsQuery();
  const { data: dataTypeList } = useGetAllDataTypesQuery();
  const { data: measurementTypeList } = useGetAllMeasureTypesQuery();
  const { data: pollPeriods } = useGetPollPeriodsQuery(undefined);
  const { data: expressionTemplates, isLoading: fetchingExpressionTemplates } =
    useGetCalculationEngineTemplatesQuery(undefined, { skip: activeStep !== 2 });
  useEffect(() => {
    if (!data) return;
    const resultMap = new Map<
      number,
      {
        metric_id: string;
        existing: boolean;
        calcMeasurementData: CalculationMeasurementSchemaData;
      }
    >();

    for (const instance of data.calculatedMetrics) {
      const metricId = instance.outputMetric.id;

      if (!resultMap.has(metricId)) {
        const pollPeriod = pollPeriods?.items?.find((poll) => poll.id === instance.pollPeriod);

        resultMap.set(metricId, {
          metric_id: String(metricId),
          existing: true,
          calcMeasurementData: {
            expression_template_id: instance.calculation || null,
            is_persisted: instance.ispersisted ?? false,
            writeback: null,
            poll_period:
              instance.pollPeriod && pollPeriod
                ? {
                    id: pollPeriod?.id,
                    value: pollPeriod?.value,
                    label: pollPeriod?.value,
                  }
                : null,
            variable_inputs: [],
          },
        });
      }
    }

    for (const input of data.calculatedMetricsInputs) {
      const instance = data.calculatedMetrics.find((i) => i.id === input.calculationMetricInstance);
      if (instance) {
        const metricId = instance.outputMetric.id;
        const entry = resultMap.get(metricId);
        if (entry) {
          entry.calcMeasurementData.variable_inputs.push({
            variable: input.inputLabel,
            type: input.metric !== null ? 'measurement' : 'constant',
            metric_id: input.metric?.id ?? null,
            constant_value: input.metric !== null ? null : input.constantNumber,
            comment: input.comment ?? null,
          });
        }
      }
    }

    setCalcMeasurements(Array.from(resultMap.values()));
  }, [data, pollPeriods]);
  const valueTypeOptions = useMemo(() => mapListToOptions(valueTypeList ?? []), [valueTypeList]);
  const datasourceOptions = useMemo(
    () =>
      mapListToOptions(
        datasourceList?.items.filter((source) => source.name !== 'TimeVaryingFactor') ?? [],
      ),
    [datasourceList],
  );
  const assetTypeMetricsListOptions = useMemo(
    () => mapListToOptions(assetTypeMetrics?.items ?? []),
    [assetTypeMetrics],
  );
  const locationsListOption = useMemo(
    () => mapListToOptions(locationsList?.items ?? []),
    [locationsList],
  );
  const dataTypesListOptions = useMemo(() => mapListToOptions(dataTypeList ?? []), [dataTypeList]);
  const measurementTypeListOptions = useMemo(
    () => mapListToOptions(measurementTypeList ?? []),
    [measurementTypeList],
  );

  const {
    control,
    handleSubmit,
    formState: { errors },
    getValues: formValues,
    trigger, // Added to manually trigger validation
    setValue, // To set specific values to the form
  } = useForm<EditAssetTemplate>({
    resolver: yupResolver(EditAssetTemplateSchema),
    defaultValues: {
      save_as_global_asset_template: false,
      measurements: [], // Default values for measurements
    },
  });
  const { fields, append, remove, update } = useFieldArray({
    control,
    rules: {
      required: true,
    },
    name: 'measurements',
  });
  const calculationSource = datasourceList?.items?.find(
    (datasource) => datasource.name === 'Calculation',
  );
  const calculatedMeasures = useMemo(() => {
    return calculationSource
      ? fields.filter((metric) => metric.datasource_id === calculationSource.id)
      : [];
  }, [fields, calculationSource]);
  const steps = [
    'Asset Template Information',
    'Measurement Details',
    calculatedMeasures.length > 0 ? 'Calculated measurements' : undefined,
  ];
  const {
    control: measurementsControl,
    handleSubmit: measurementSubmit,
    getValues: measurementGetValues,
    formState: { errors: measureErrors },
    setValue: setMeasureValue,
    reset: resetMeasure,
    clearErrors: measureClearError,
  } = useForm<measurementSchemaEdit>({
    defaultValues:
      currentMeausreIndex >= 0
        ? fields[currentMeausreIndex]
        : {
            id: undefined,
            type_id: undefined,
            data_type_id: undefined,
            value_type_id: undefined,
            metric_id: undefined,
            description: '',
            location_id: undefined,
            datasource_id: undefined,
            meter_factor: undefined,
          },
    resolver: yupResolver(MeasurementSchema),
  });

  const {
    control: calcMeasurementController,
    handleSubmit: calcMeasureHandleSubmit,
    getValues: calcMeasureGetValues,
    reset: resetCalcMeasureValues,
    watch: calcMeasureWatch,
    setValue: calcMeasureSetValue,
    formState: { errors: calcMeasureError },
  } = useForm<CalculationMeasurementSchemaData>({
    defaultValues: {
      is_persisted: false,
      poll_period: null,
      writeback: false,
      expression_template_id: undefined,
      variable_inputs: [],
    },
    resolver: yupResolver(calculationMeasurementSchema),
  });
  const { fields: variableFields } = useFieldArray({
    control: calcMeasurementController,
    name: 'variable_inputs',
  });
  const hasDuplicates = () => {
    const values = fields.map((measure) => measure.metric_id?.toString());
    const uniqueValues = new Set(values);
    return values.length !== uniqueValues.size;
  };
  return {
    steps,
    valueTypeOptions,
    assetTypeListData,
    datasourceOptions,
    assetTypeMetricsListOptions,
    locationsListOption,
    dataTypesListOptions,
    measurementTypeListOptions,
    assets,
    hasDuplicates,
    editAssetTemplate: {
      handleSubmit,
      errors,
      trigger,
      setValue,
      control,
      formValues,
    },
    append,
    remove,
    update,
    fields,
    measurementsEdits: {
      measurementSubmit,
      measurementGetValues,
      measureErrors,
      setMeasureValue,
      resetMeasure,
      measureClearError,
      measurementsControl,
    },
    calculatedMeasures,
    calculationSource,
    pollPeriods,
    expressionTemplates,
    fetchingExpressionTemplates,
    calcMeasurements,
    setCalcMeasurements,
    calculationMeasurement: {
      calcMeasurementController,
      calcMeasureHandleSubmit,
      calcMeasureGetValues,
      calcMeasureError,
      calcMeasureWatch,
      resetCalcMeasureValues,
      calcMeasureSetValue,
      variableFields,
    },
  };
};

export default useEditAssetTemplateHelper;
