import { ThunkDispatch } from '@reduxjs/toolkit';
import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AssetMeasurement } from '~/measurements/domain/types';
import { measuresApi } from '~/redux/api/measuresApi';
import { timeseriesApi } from '~/redux/api/timeseriesApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { RootState } from '~/redux/store';
import { SingleScatterTimeSeriesData } from '~/types/timeseries';

export type ExtendedAssetMeasurement = AssetMeasurement & {
  tagId: number;
  tag_meta: {
    uom: string;
  };
  error?: string;
  'ts,val': [number, number][];
};

const transformMeasureDataToMeasurements = (
  baseMeasurements: AssetMeasurement[],
  tsData: Record<number, SingleScatterTimeSeriesData>,
): ExtendedAssetMeasurement[] => {
  return baseMeasurements.map((measurement) => {
    const timeseries = tsData[measurement.measurementId];
    return {
      ...measurement,
      tagId: measurement.measurementId,
      tag_meta: timeseries?.tag_meta || { uom: '' },
      error: timeseries ? undefined : 'No timeseries data available',
      'ts,val': timeseries ? timeseries['ts,val'] || [] : [],
    };
  });
};
const initialState = {
  error: false,
  errorMessage: '',
  isLoading: false,
  measureData: {} as Record<number, SingleScatterTimeSeriesData>,
  assetMeasurementsHistoryData: [] as ExtendedAssetMeasurement[] | undefined,
};
const useFetchAssetMeausrementsHistory = ({ assetId }: { assetId: number }) => {
  const activeCustomer = useSelector(getActiveCustomer);
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();

  const [data, setData] = useState(initialState);

  const fetchHistoryTimeseriesData = useCallback(
    async (
      tsDbMeasureIds: number[],
    ): Promise<{ error: boolean; tsData: Record<number, SingleScatterTimeSeriesData> }> => {
      if (tsDbMeasureIds.length === 0) {
        return { error: false, tsData: {} };
      }

      const { data: fetchedTsData, isSuccess: isTsSuccess } = await dispatch(
        timeseriesApi.endpoints.getMultipleLastReadingsMeasurementSeries.initiate({
          customerId: activeCustomer?.id ?? 0,
          measId: tsDbMeasureIds.filter(Boolean).sort().join(','),
        }),
      );

      if (!isTsSuccess || !fetchedTsData) {
        return { error: true, tsData: {} };
      }

      return { error: false, tsData: fetchedTsData };
    },
    [dispatch, activeCustomer?.id],
  );

  const fetchMeasuresData = useCallback(async () => {
    const { data: measureData, isSuccess: isMeasureDataSuccess } = await dispatch(
      measuresApi.endpoints.getAllMeasurements.initiate({
        customerId: activeCustomer?.id ?? 0,
        assetId: assetId,
      }),
    );

    if (!isMeasureDataSuccess || !measureData) {
      throw new Error(`Error fetching measure data for assetId: ${assetId}`);
    }

    return measureData;
  }, [activeCustomer?.id, dispatch, assetId]);

  useEffect(() => {
    // Reset state to default when assetId changes.
    setData({
      error: false,
      errorMessage: '',
      isLoading: true,
      measureData: {},
      assetMeasurementsHistoryData: [],
    });

    const fetchData = async () => {
      try {
        const baseMeasureData = await fetchMeasuresData();
        const tsDbMeasureIds = baseMeasureData.map((measure) => measure.measurementId);
        const { error, tsData: fetchedTsData } = await fetchHistoryTimeseriesData(tsDbMeasureIds);

        if (error) {
          const extendedMeasurements = baseMeasureData.map((measurement) => ({
            ...measurement,
            tagId: measurement.measurementId,
            tag_meta: { uom: '' },
            error: 'Failed to fetch timeseries data',
            'ts,val': [],
          }));
          setData((prev) => ({
            ...prev,
            isLoading: false,
            error: true,
            errorMessage: 'Failed to fetch measurement timeseries data',
            measureData: {},
            assetMeasurementsHistoryData: extendedMeasurements,
          }));
        } else {
          const extendedMeasurements = transformMeasureDataToMeasurements(
            baseMeasureData,
            fetchedTsData,
          );
          setData((prev) => ({
            ...prev,
            isLoading: false,
            measureData: fetchedTsData,
            assetMeasurementsHistoryData: extendedMeasurements,
          }));
        }
      } catch (error: any) {
        console.error(error);
        setData((prev) => ({
          ...prev,
          isLoading: false,
          error: true,
          errorMessage: error.message || 'Error fetching data',
        }));
      }
    };

    fetchData();
  }, [assetId, fetchHistoryTimeseriesData, fetchMeasuresData]);

  return data;
};

export default useFetchAssetMeausrementsHistory;
