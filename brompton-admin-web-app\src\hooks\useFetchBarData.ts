import { useRouter } from 'next/router';
import { Data, Layout, TypedArray } from 'plotly.js';
import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { getAssetTz } from '~/redux/selectors/topPanleSelectors';
import { getDateTimeFormatForChart, getThousandSeparator } from '~/redux/selectors/userPreferences';
import { AssetMeasurementDetails } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchTime,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { BarChartWidget, ChartMeasureSetting, MeasureColorPair } from '~/types/widgets';
import {
  calculateSumAndDelta,
  calulationXaxisValue,
  calulationYaxisValue,
  formatMetricLabel,
  formatMetricTag,
  formatNumber,
  roundNumber,
  showMeanValue,
  showMinMaxAvg,
  showThresholdValue,
} from '~/utils/utils';
import { useGetMeasuresTsData } from './useGetMeasuresTsData';
type Stats = {
  min: string;
  max: string;
  avg: string;
  total: string;
  unit: UnitOfMeasure | undefined;
};

type ChartData = {
  data: Data[];
  removedResults: AssetMeasurementDetailsWithLastFetchTime[];
  layout: Partial<Layout>;
  stats: Stats;
  result: {
    delta: string | undefined;
    sum: string | undefined;
  };
};

type TrendResult = {
  isError: boolean;
  error?: string;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
  lastFetchTime: number;
};

type SingleChartData = {
  layout: Partial<Layout>;
  stats: Stats;
};

function getChartDataForSingleTrace(
  filteredResults: TrendResult[],
  minMaxAvg: {
    min: {
      show: boolean;
    };
    max: {
      show: boolean;
    };
    avg: {
      show: boolean;
    };
  },
  thousandSeparator: boolean,
): SingleChartData {
  const singleSeries = filteredResults[0];
  const { 'ts,val': values } = singleSeries.tsData;
  if (!values) {
    return { layout: {}, stats: { avg: '', max: '', min: '', total: '', unit: undefined } };
  }
  const unit = singleSeries.unitOfMeasures.filter(
    (data) => data.id == singleSeries.measureData.unitOfMeasureId,
  )[0];
  const yValues = values.map((value) => value[1]);

  let max: number | string = -Infinity;
  let min: number | string = Infinity;
  let tempTotal = 0;

  for (const val of yValues) {
    if (val > max) max = val;
    if (val < min) min = val;
    tempTotal += val;
  }

  // Using optimized rounding function
  max = thousandSeparator ? formatNumber(max) : roundNumber(max);
  min = thousandSeparator ? formatNumber(min) : roundNumber(min);
  tempTotal = yValues.reduce((a, b) => a + b, 0);
  const total = tempTotal.toFixed(2);
  const avg = (tempTotal / yValues.length).toFixed(2);
  const layout: Partial<Layout> = showMinMaxAvg(
    {
      layout: {},
    } as Partial<Layout>,
    minMaxAvg,
    min,
    max,
    avg,
  );
  return { layout, stats: { min, max, avg, total, unit } };
}

function transformBarDataForPlotly(
  results: TrendResult[],
  dbMeasureIdToAnnotation: Record<string, boolean>,
  dbMeasureIdToSetting: Record<string, ChartMeasureSetting>,
  selectedSamplePeriod: number,
  selectedTitles: string[],
  title: {
    value: string;
    isVisible: boolean;
    color: string;
    fontSize?: number;
    fontWeight?: string;
  },
  barColors: MeasureColorPair[],
  overrideGlobalBarColor: boolean,
  legendY: number,
  showThreshold: boolean,
  treshdold: {
    thresholdName: string;
    thresholdValue: number;
    thresholdColor: string;
    thresholdStyle: string;
  },
  min: {
    show: boolean;
  },
  max: {
    show: boolean;
  },
  avg: {
    show: boolean;
  },
  useAssetTz: boolean,
  forecastedResults: TrendResult[] | undefined,
  settings: BarChartWidget,
  isDashboardTemplate: boolean,
  thousandSeparator: boolean,
): ChartData {
  const traces: Data[] = [];
  const yData: number[] = [];
  const layout: Partial<Layout> = {
    showlegend: true,
    // title: numberOfCharts > 1 ? 'Trends' : formatMetricLabel(results[0]?.measureData.tag),
    title: title.isVisible ? title.value : undefined,
    // results.map((res) => formatMetricLabel(res?.measureData?.tag || '')).join(' Vs. '),
    titlefont: title.isVisible
      ? {
          size: title.fontSize,
          color: title.color,
        }
      : undefined,
    annotations: [],
    barmode: 'group',
    yaxis: {
      // title: 'Unit Values',
      side: 'left',
    },
    yaxis2: {
      // title: 'Unit Values',
      side: 'right',
      overlaying: 'y',
    },
  };

  let chartNumber = 1;
  const filteredResults: TrendResult[] = [];
  const removedResults: TrendResult[] = [];
  if (isDashboardTemplate) {
    filteredResults.push(...results);
  } else {
    removedResults.push(
      ...results.filter((result) => !result || !result.tsData || result.isError || result.error),
    );
    filteredResults.push(...results.filter((result) => result && result.tsData));
  }

  // Legend Y-axis position logic
  let dynamicLegendY: number | undefined = undefined;

  // Manual override from user
  if (settings.legendY) {
    const legendCount = filteredResults.length;

    if (legendCount <= 5) {
      dynamicLegendY = -0.4;
    } else if (legendCount <= 8) {
      dynamicLegendY = -0.6;
    } else if (legendCount <= 10) {
      dynamicLegendY = -0.75;
    } else {
      dynamicLegendY = -0.8; // Optional: adjust if needed for large counts
    }
  }

  // Apply the final legend position if defined
  if (dynamicLegendY !== undefined) {
    layout.legend = {
      x: 0,
      y: dynamicLegendY,
      xanchor: 'left',
      yanchor: 'top',
      orientation: 'h',
    };
  }

  const filteredForecatResults = forecastedResults?.filter((result) => result && result.tsData);
  let singleChartData: SingleChartData = {
    stats: { avg: '', max: '', min: '', total: '', unit: undefined },
    layout: {
      annotations: [],
      shapes: [],
    },
  };
  for (let i = 0; i < filteredResults.length; i++) {
    const result = filteredResults[i];

    if (result && result.tsData) {
      const seriesData = result.tsData;
      const measureData = result.measureData;
      if (
        !isDashboardTemplate &&
        selectedTitles.length > 0 &&
        selectedTitles.includes(measureData.id?.toString()) === false
      ) {
        continue;
      }
      const unitOfMeasures = result.unitOfMeasures;
      const values = seriesData['ts,val'];
      // if (!values) continue;
      const x = calulationXaxisValue(seriesData, useAssetTz);
      const y = calulationYaxisValue(seriesData);

      const unitsOfMeasure = unitOfMeasures.find(
        (data) => data.id === measureData.unitOfMeasureId,
      ) || { name: '', id: 0 };

      const title = formatMetricLabel(measureData.tag);
      const yAxisLabel =
        chartNumber == 1
          ? 'Left'
          : dbMeasureIdToSetting && dbMeasureIdToSetting[measureData.id]?.yAxisSide == 'right'
          ? 'Right'
          : 'Left';
      if (y && y.length > 0) {
        yData?.push(...y);
      }
      traces.push({
        type: 'bar',
        x: x && x.length > 0 ? x : [new Date().toISOString()],
        y:
          y && y.length > 0
            ? y.map((value) => (thousandSeparator ? formatNumber(value) : roundNumber(value)))
            : [['']],
        ...(overrideGlobalBarColor
          ? {
              marker: {
                color: barColors?.find((item) => item.measureId === measureData.id.toString())
                  ?.color,
              },
            }
          : undefined),
        // marker: { color: 'red' },
        hovertemplate: `${title}: %{y} ${unitsOfMeasure.name}<br>Time: %{x}<extra></extra>'`,
        name: `${formatMetricTag(measureData.tag)} (${unitsOfMeasure.name})(${yAxisLabel})`,
        // yaxis:
        //   chartNumber == 1
        //     ? 'y'
        //     : dbMeasureIdToSetting && dbMeasureIdToSetting[measureData.id]?.yAxisSide == 'right'
        //     ? 'y2'
        //     : 'y',
        yaxis:
          dbMeasureIdToSetting && dbMeasureIdToSetting[measureData.id]?.yAxisSide == 'right'
            ? 'y2'
            : 'y',
        mode: 'lines',
        customdata:
          !isDashboardTemplate &&
          dbMeasureIdToAnnotation &&
          dbMeasureIdToAnnotation[measureData.id.toString()]
            ? [measureData.measurementId.toString()] // Assign custom data here
            : undefined,
        // marker: { size: 4 }, // Set the marker size here
      });

      chartNumber++;
    }
  }
  if (filteredForecatResults && settings.showForecast && selectedTitles.length === 1) {
    for (let i = 0; i < filteredForecatResults.length; i++) {
      const result = filteredForecatResults[i];

      if (result && result.tsData) {
        const seriesData = result.tsData;
        const measureData = result.measureData;
        if (
          !isDashboardTemplate &&
          selectedTitles.length > 0 &&
          selectedTitles.includes(measureData.id?.toString()) === false
        )
          continue;
        const unitOfMeasures = result.unitOfMeasures;
        const values = seriesData['ts,val'];
        if (!values) continue;
        const x = calulationXaxisValue(seriesData, useAssetTz);
        const y = calulationYaxisValue(seriesData);

        const unitsOfMeasure = unitOfMeasures.find(
          (data) => data.id === measureData.unitOfMeasureId,
        ) || { name: '', id: 0 };
        if (chartNumber != 1) {
          // debugger;
        }

        const title = formatMetricLabel(measureData.tag);
        const yAxisLabel =
          chartNumber == 1
            ? 'Left'
            : dbMeasureIdToSetting && dbMeasureIdToSetting[measureData.id]?.yAxisSide == 'right'
            ? 'Right'
            : 'Left';
        traces.push({
          type: 'bar',
          x: x.length > 0 ? x : [new Date().toISOString()],
          y:
            y.length > 0
              ? y.map((value) => (thousandSeparator ? formatNumber(value) : roundNumber(value)))
              : [['']],
          // y.map((value) => (thousandSeparator ? formatNumber(value) : roundNumber(value))),
          marker: { color: settings.forecastColor ?? 'blue' },
          hovertemplate: `${title}: %{y} ${unitsOfMeasure.name}<br>Time: %{x}<extra></extra>'`,
          name: `${formatMetricTag(measureData.tag)} (${
            unitsOfMeasure.name
          })(${yAxisLabel}) - Forecast`,
          yaxis:
            chartNumber == 1
              ? 'y'
              : dbMeasureIdToSetting && dbMeasureIdToSetting[measureData.id]?.yAxisSide == 'right'
              ? 'y2'
              : 'y',
          mode: 'lines',
          // marker: { size: 4 }, // Set the marker size here
        });

        chartNumber++;
      }
    }
    if (settings.showMean) {
      singleChartData.layout = showMeanValue(singleChartData, traces, {
        meanColor: settings.meanColor,
        meanStyle: settings.meanStyle,
        meanName: settings.meanName,
      }).layout;
    }
  }
  if (traces.length === 1 && filteredResults.length === 1) {
    singleChartData = getChartDataForSingleTrace(
      filteredResults,
      { min, max, avg },
      thousandSeparator,
    );
  }
  if (showThreshold) {
    const threshold = showThresholdValue(singleChartData, treshdold);
    singleChartData.layout = threshold.layout;
  }
  const result: {
    delta: number | undefined;
    sum: number | undefined;
  } = { delta: undefined, sum: undefined };
  let unit = '';
  if (selectedTitles.length === 1 && filteredResults && filteredResults.length === 1) {
    const unitsOfMeasure = filteredResults[0].unitOfMeasures.find(
      (data) => data.id === filteredResults[0].measureData.unitOfMeasureId,
    ) || { name: '', id: 0 };
    unit = unitsOfMeasure.name;
    calculateSumAndDelta(
      unitsOfMeasure,
      {
        showDelta: settings.showDelta ?? false,
        deltaLabel: settings.deltaLabel ?? 'Delta',
        showSum: settings.showSum ?? false,
        sumLabel: settings.sumLabel ?? 'Delta',
      },
      traces,
      layout,
      yData,
    );
  }

  return {
    data: traces,
    removedResults: removedResults.map((res) => {
      return {
        ...res.measureData,
        lastFetchTime: res.lastFetchTime,
        partialFailed: removedResults.length !== results.length,
      };
    }),
    layout: {
      ...layout,
      ...singleChartData.layout,
      barmode: settings.showStacked.show ? 'stack' : 'group',
    },
    stats: { ...singleChartData.stats },
    result: { ...result, delta: result.delta + ' ' + unit, sum: result.sum + ' ' + unit },
  };
}

export function useFetchBarData(widgetId: string, state: BarChartWidget) {
  const selectedSamplePeriod = state.samplePeriod || 0;
  const selectedAggBy = state.aggBy;
  const router = useRouter();
  const dbMeasureIdToSetting = state.dbMeasureIdToSetting;
  const selectedTitles = state.selectedTitles;
  const thousandSeparator = useSelector(getThousandSeparator);
  const dateFormats = useSelector(getDateTimeFormatForChart);
  const [chartResults, setChartResults] = useState<TrendResult[] | undefined>(undefined);
  const useAssetTz = useSelector(getAssetTz);
  const [forecastedResults, setForcastedResult] = useState<TrendResult[] | undefined>(undefined);
  const [allDataFetched, setAllDataFetched] = useState({
    removedResults: [] as AssetMeasurementDetailsWithLastFetchTime[],
    chartData: [] as Data[],
    isLoading: true,
    isError: false,
    stats: {
      min: '',
      max: '',
      avg: '',
    } as Stats,
    layoutData: {
      showlegend: true,
      title: 'Chart',
    } as Partial<Layout>,
    results: {
      delta: '',
      sum: '',
    },
  });
  const [selectedMeasures, setSelectedMeasures] = useState<string[]>([]);
  const prevResultsRef = useRef<TrendResult[]>([]);

  useEffect(() => {
    if (state.mode === 'dashboard') {
      const hasValidAssetMeasure = state.assetMeasure.some(
        (assetMeas) =>
          assetMeas.assetId.trim() !== '' &&
          assetMeas.measureId.some((measure) => measure.trim() !== ''),
      );

      if (hasValidAssetMeasure) {
        const titles = state.assetMeasure
          .flatMap((assetMeas) => assetMeas.measureId)
          .filter((measure) => measure.trim() !== '');

        setSelectedMeasures(titles);
      } else {
        // Optionally, clear selectedTitles if no valid asset measures
        setSelectedMeasures([]);
      }
    }
    if (state.mode === 'template') {
      const selectedMesures = selectedTitles.map((title) => title);
      setSelectedMeasures([...selectedMesures]);
    }
  }, [state.assetMeasure, selectedTitles]);
  const {
    data: measureData,
    isLoading: isMeasureDataLoading,
    forcastedData,
    successAndFailedMeasurements,
    isError,
  } = useGetMeasuresTsData({
    selectedTitles: selectedMeasures,
    dataFetchSettings: state,
    assetMeasure: state.assetMeasure,
  });
  useEffect(() => {
    if (isMeasureDataLoading) {
      setChartResults(undefined);
      setForcastedResult(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isLoading: true,
          isError: false,
        };
      });
    } else if (measureData) {
      const updated: TrendResult[] = [];
      (measureData || []).forEach((result) => {
        if (!result.isError && result.tsData) {
          updated.push(result);
        } else {
          const existing = prevResultsRef.current.find(
            (r) => r.measureData.measurementId === result?.measureData?.measurementId,
          );
          if (existing) {
            updated.push({
              ...existing,
              lastFetchTime: result.lastFetchTime,
              isError: result.isError,
              error: result.error,
              tsData: {
                ...existing.tsData,
                error: result.error,
              },
            });
          } else {
            updated.push(result);
          }
        }
      });
      prevResultsRef.current = updated; // Keep latest working state
      setChartResults(updated as TrendResult[]);
      const forecastFilter = forcastedData?.filter(({ isError }) => !isError);
      setForcastedResult(forecastFilter as TrendResult[]);
    } else if (isError) {
      setChartResults(undefined);
      setForcastedResult(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isError: true,
          isLoading: false,
        };
      });
    }
  }, [isMeasureDataLoading, measureData, forcastedData, isError]);

  useEffect(() => {
    if (chartResults) {
      const chartData = transformBarDataForPlotly(
        chartResults,
        state.dbMeasureIdToAnnotation,
        dbMeasureIdToSetting,
        selectedSamplePeriod,
        selectedMeasures,
        state.title,
        state.barColors,
        state.overrideGlobalBarColor,
        state.legendY,
        state.showThreshold,
        state.treshdold,
        state.min,
        state.max,
        state.avg,
        useAssetTz,
        forecastedResults,
        state,
        router.pathname === '/dashboard-template',
        thousandSeparator,
      );
      setAllDataFetched({
        removedResults: chartData.removedResults,
        chartData: chartData.data,
        isLoading: false,
        isError: false,
        layoutData: chartData.layout,
        stats: chartData.stats,
        results: {
          delta: chartData.result.delta ?? '',
          sum: chartData.result.sum ?? '',
        },
      });
    }
  }, [
    chartResults,
    state.showThreshold,
    state.treshdold,
    state.title.value,
    state.title.isVisible,
    selectedTitles,
    dbMeasureIdToSetting,
    selectedSamplePeriod,
    selectedAggBy,
    state.dbMeasureIdToAnnotation,
    state.overrideGlobalSettings,
    state.startDate,
    state.endDate,
    state.timeRange,
    state.barColors,
    state.overrideGlobalBarColor,
    state.globalSamplePeriod,
    state.legendY,
    state.title,
    state.min,
    state.max,
    state.avg,
    dateFormats,
    state.period,
    state.showForecast,
    forecastedResults,
    state.forecastColor,
    state.meanColor,
    state.meanName,
    state.meanStyle,
    state.showMean,
    state.showDelta,
    state.showSum,
    state.sumLabel,
    state.showStacked,
    state.deltaLabel,
    router.pathname,
    thousandSeparator,
  ]);
  return { ...allDataFetched, successAndFailedMeasurements };
}
