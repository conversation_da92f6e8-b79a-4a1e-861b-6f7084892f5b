import { Data, Layout, PlotData } from 'plotly.js';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { getDbMeasureIdToName } from '~/redux/selectors/treeSelectors';
import { AssetMeasurementDetails } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchTime,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { BulletChartWidget } from '~/types/widgets';
import { useGetMeasuresTsData } from './useGetMeasuresTsData';

type TrendResult = {
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
  lastFetchTime: number;
};

type ChartData = {
  data: Data[];
  removedResults?: AssetMeasurementDetailsWithLastFetchTime;
  layout: Partial<Layout>;
  tsData?: number;
};
const transformBulletData = (
  results: TrendResult[],
  state: BulletChartWidget,
  selectedMeasureName: string,
): ChartData => {
  if (results.length === 0) {
    return {
      data: [],
      layout: {},
      removedResults: undefined,
    };
  }
  const firstResult = results[0];
  if (!firstResult?.tsData?.['ts,val']) {
    return {
      data: [],
      layout: {},
      tsData: undefined,
      removedResults: {
        ...firstResult?.measureData,
        lastFetchTime: firstResult?.lastFetchTime,
        partialFailed: false,
      },
    };
  }
  const res = results[0]?.tsData?.['ts,val']?.slice(-2);
  if (!results[0]?.tsData?.['ts,val'] || res.length < 2) {
    return {
      data: [],
      layout: {},
      removedResults: firstResult
        ? {
            ...firstResult.measureData,
            lastFetchTime: firstResult.lastFetchTime,
            partialFailed: false,
          }
        : undefined,
    };
  }
  const { measureData, unitOfMeasures } = results[0];
  const unit = unitOfMeasures?.find((data) => data.id === measureData.unitOfMeasureId) || null;
  const layout: Partial<Layout> = {
    margin: { ...state.margin },
    title: {
      text: state.title.isVisible ? state.title.value + ' (' + unit?.name + ')' : undefined,
      //  formatMetricLabel(selectedMeasureName) + ' (' + unit?.name + ')',
      font: state.title.isVisible
        ? {
            size: state.title.fontSize,
            color: state.title.color,
          }
        : undefined,
    },
  };
  const value: number = res[res?.length - 1][1];
  const referenceValue = res[res?.length - 2]?.[1];
  const maxValue = Number(state.maxValue === -999 ? value / 0.9 : state.maxValue);
  const minValue = Number(state.minValue >= 0 ? state.minValue : null);
  const traces: Data[] = [
    {
      domain: { x: [0, 1], y: [0, 1] },
      value: value,
      type: 'indicator',
      mode: 'gauge+number+delta',
      delta: { reference: referenceValue },
      gauge: {
        axis: { range: [minValue, maxValue] },
        steps: [],
        bar: {
          color: state.barColor,
        },
        shape: 'bullet',
      },
      orientation: 'v',
    },
  ];
  const trace = traces[0] as PlotData;
  if (state.showThreshHoldValue) {
    const thresholdMark: number =
      Number(state.threshHoldValue) === 0 ? 490 / 500 : Number(state.threshHoldValue);
    trace.gauge.threshold = {
      line: { color: state.threshHoldColor, width: 4 },
      thickness: 0.75,
      value: thresholdMark,
    };
  }
  if (state.showIndicator1) {
    const indicator1 = state.indicator1Value === 0 ? value / 2 : Number(state.indicator1Value);
    trace.gauge.steps?.push({ range: [minValue ?? 0, indicator1], color: state.indicator1Color });
    if (state.showIndicator2) {
      const indicator2 = state.indicator2Value === 0 ? value * 0.8 : Number(state.indicator2Value);
      trace.gauge.steps?.push({ range: [indicator1, indicator2], color: state.indicator2Color });
    }
  }

  return {
    data: traces,
    removedResults:
      firstResult && (!firstResult.tsData?.['ts,val'] || firstResult.tsData.error)
        ? {
            ...firstResult.measureData,
            lastFetchTime: firstResult.lastFetchTime,
            partialFailed: false,
          }
        : undefined,
    layout: layout,
    tsData: res[res?.length - 1][0],
  };
};
export function useFetchBulletChartData(widgetId: string, state: BulletChartWidget) {
  const selectedDbMeasureId = state.selectedDbMeasureId;
  const dbMeasureIdToName = useSelector(getDbMeasureIdToName);
  const [allDataFetched, setAllDataFetched] = useState({
    removedResults: undefined as undefined | AssetMeasurementDetailsWithLastFetchTime,
    chartData: [] as Data[],
    tsData: undefined as number | undefined,
    isLoading: true,
    layoutData: {
      showlegend: true,
      title: 'Chart',
    } as Partial<Layout>,
  });
  const [chartResults, setChartResults] = useState<TrendResult[] | undefined>(undefined);
  const [selectedTitles, setSelectedTitles] = useState<string[]>([]);
  // useEffect(() => {
  //   if (selectedDbMeasureId !== '') setSelectedTitles([selectedDbMeasureId]);
  // }, [selectedDbMeasureId]);
  useEffect(() => {
    if (
      state.mode === 'dashboard' &&
      state.assetMeasure.assetId !== '' &&
      state.assetMeasure.measureId.some((measure) => measure !== '')
    ) {
      setSelectedTitles(state.assetMeasure.measureId);
    }
    if (state.mode === 'template') {
      if (state.selectedDbMeasureId !== '') {
        setSelectedTitles([state.selectedDbMeasureId]);
      } else {
        setSelectedTitles([]);
      }
    }
  }, [state.assetMeasure, state.selectedDbMeasureId]);

  const {
    data: measureData,
    isLoading: isMeasureDataLoading,
    successAndFailedMeasurements,
  } = useGetMeasuresTsData({
    selectedTitles,
    dataFetchSettings: state,
    assetMeasure: [state.assetMeasure],
  });
  useEffect(() => {
    if (isMeasureDataLoading) {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isLoading: true,
          isError: false,
        };
      });
    } else if (measureData) {
      const filteredList = measureData;
      setChartResults(filteredList as TrendResult[]);
    } else {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isError: true,
          isLoading: false,
        };
      });
    }
  }, [isMeasureDataLoading, measureData]);

  useEffect(() => {
    if (chartResults) {
      const chartData = transformBulletData(
        chartResults,
        state,
        dbMeasureIdToName[selectedDbMeasureId],
      );
      setAllDataFetched({
        chartData: chartData.data,
        removedResults: chartData.removedResults,
        isLoading: false,
        layoutData: chartData.layout,
        tsData: chartData.tsData,
      });
    }
  }, [
    chartResults,
    state.title.isVisible,
    state.title.value,
    state.barColor,
    state.threshHoldColor,
    state.showThreshHoldValue,
    state.threshHoldValue,
    state.showIndicator1,
    state.indicator1Color,
    state.indicator1Value,
    state.showIndicator2,
    state.indicator2Color,
    state.indicator2Value,
    state.minValue,
    state.maxValue,
    state.margin,
    state.overrideGlobalSettings,
    state.startDate,
    state.endDate,
    state.timeRange,
    state.globalSamplePeriod,
    state,
  ]);
  return { ...allDataFetched, successAndFailedMeasurements };
}
