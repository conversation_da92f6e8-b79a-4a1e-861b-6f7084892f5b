import { Data, Layout, PlotData } from 'plotly.js';
import { useEffect, useRef, useState } from 'react';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { AssetMeasurementDetails } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchTime,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { GaugeChartWidget } from '~/types/widgets';
import { useGetMeasuresTsData } from './useGetMeasuresTsData';

type TrendResult = {
  isError: boolean;
  error?: string;
  lastFetchTime: number;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};

type ChartData = {
  data: Data[];
  layout: Partial<Layout>;
  tsData?: number;
  removedResults: AssetMeasurementDetailsWithLastFetchTime[];
};
const transformGaugeData = (
  results: TrendResult[],
  state: GaugeChartWidget,
  selectedMeasureName: string,
  selectedTitles: string[],
): ChartData => {
  if (results.length === 0) {
    return {
      data: [],
      layout: {},
      removedResults: [],
    };
  }
  const firstResult = results[0];
  if (!firstResult?.tsData?.['ts,val']) {
    return {
      data: [],
      layout: {},
      removedResults:
        firstResult && (firstResult.isError || !firstResult.tsData || !firstResult.tsData['ts,val'])
          ? [
              {
                ...firstResult.measureData,
                lastFetchTime: firstResult.lastFetchTime,
                partialFailed: false,
              },
            ]
          : [],
    };
  }
  const res = results[0]?.tsData?.['ts,val']?.slice(-2);
  if (res.length < 2) {
    return {
      data: [],
      layout: {},
      removedResults: results.map((res) => {
        return {
          ...res.measureData,
          lastFetchTime: res.lastFetchTime,
          partialFailed: false,
        };
      }),
    };
  }
  const { measureData, unitOfMeasures } = results[0];
  const unit = unitOfMeasures?.find((data) => data.id === measureData.unitOfMeasureId) || null;
  const layout: Partial<Layout> = { margin: { ...state.margin } };
  const value: number = res[res?.length - 1][1];
  const referenceValue = res[res?.length - 2]?.[1];
  const maxValue = Number(state.maxValue === -999 ? value / 0.9 : state.maxValue);
  const minValue = Number(state.minValue >= 0 ? state.minValue : null);
  const traces: Data[] = [
    {
      domain: { x: [0, 1], y: [0, 1] },
      value: value,
      title: {
        text: state.title.isVisible ? state.title.value + ` (${unit?.name})` : undefined,
        //formatMetricLabel(selectedMeasureName) + ` (${unit?.name})`,
        font: state.title.isVisible
          ? {
              size: state.title.fontSize,
              color: state.title.color,
            }
          : undefined,
      },
      type: 'indicator',
      mode: 'gauge+number+delta',
      delta: { reference: referenceValue },
      gauge: {
        axis: { range: [minValue, maxValue] },
        steps: [],
        bar: {
          color: state.barColor,
        },
      },
    },
  ];
  const trace = traces[0] as PlotData;
  if (state.showThreshHoldValue) {
    const thresholdMark: number =
      Number(state.threshHoldValue) === 0 ? 490 / 500 : Number(state.threshHoldValue);
    trace.gauge.threshold = {
      line: { color: state.threshHoldColor, width: 4 },
      thickness: 0.75,
      value: thresholdMark,
    };
  }
  if (state.showIndicator1) {
    const indicator1 = state.indicator1Value === 0 ? value / 2 : Number(state.indicator1Value);
    trace.gauge.steps?.push({ range: [minValue ?? 0, indicator1], color: state.indicator1Color });
    if (state.showIndicator2) {
      const indicator2 = state.indicator2Value === 0 ? value * 0.8 : Number(state.indicator2Value);
      trace.gauge.steps?.push({ range: [indicator1, indicator2], color: state.indicator2Color });
    }
  }
  return {
    data: traces,
    layout: layout,
    tsData: res[res?.length - 1][0],
    removedResults:
      firstResult && (firstResult.isError || !firstResult.tsData || !firstResult.tsData['ts,val'])
        ? [
            {
              ...firstResult.measureData,
              lastFetchTime: firstResult.lastFetchTime,
              partialFailed: false,
            },
          ]
        : [],
  };
};
export function useFetchGaugeData(widgetId: string, state: GaugeChartWidget) {
  const selectedDbMeasureId = state.selectedDbMeasureId;
  const prevResultsRef = useRef<TrendResult[]>([]);
  const [selectedTitles, setSelectedTitles] = useState<string[]>([]);
  const [allDataFetched, setAllDataFetched] = useState({
    chartData: [] as Data[],
    tsData: undefined as number | undefined,
    isLoading: true,
    layoutData: {
      showlegend: true,
      title: 'Chart',
    } as Partial<Layout>,
    removedResults: [] as AssetMeasurementDetailsWithLastFetchTime[],
  });
  useEffect(() => {
    if (
      state.mode === 'dashboard' &&
      state.assetMeasure.assetId !== '' &&
      state.assetMeasure.measureId.some((measure) => measure !== '')
    ) {
      setSelectedTitles(state.assetMeasure.measureId);
    }
    if (state.mode === 'template') {
      if (state.selectedDbMeasureId !== '') {
        setSelectedTitles([state.selectedDbMeasureId]);
      } else {
        setSelectedTitles([]);
      }
    }
  }, [state.assetMeasure, state.selectedDbMeasureId]);
  const [chartResults, setChartResults] = useState<TrendResult[] | undefined>(undefined);
  const {
    data: measureData,
    isLoading: isMeasureDataLoading,
    successAndFailedMeasurements,
  } = useGetMeasuresTsData({
    selectedTitles,
    dataFetchSettings: state,
    assetMeasure: [state.assetMeasure],
  });
  useEffect(() => {
    if (isMeasureDataLoading) {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isLoading: true,
          isError: false,
        };
      });
    } else if (measureData) {
      const updated: TrendResult[] = [];
      (measureData || []).forEach((result) => {
        if (!result.isError && result.tsData) {
          updated.push(result);
        } else {
          const existing = prevResultsRef.current.find(
            (r) => r.measureData.measurementId === result?.measureData?.measurementId,
          );
          if (existing) {
            updated.push({
              ...existing,
              lastFetchTime: result.lastFetchTime,
              isError: result.isError,
              error: result.error,
              tsData: {
                ...existing.tsData,
                error: result.error,
              },
            });
          } else {
            updated.push(result);
          }
        }
      });
      prevResultsRef.current = updated; // Keep latest working state
      setChartResults(updated as TrendResult[]);
    } else {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isError: true,
          isLoading: false,
        };
      });
    }
  }, [isMeasureDataLoading, measureData]);

  useEffect(() => {
    if (chartResults) {
      const chartData = transformGaugeData(
        chartResults,
        state,
        state.dbMeasureIdToName[selectedDbMeasureId],
        selectedTitles,
      );
      setAllDataFetched({
        chartData: chartData.data,
        isLoading: false,
        layoutData: chartData.layout,
        tsData: chartData.tsData,
        removedResults: chartData.removedResults,
      });
    }
  }, [
    chartResults,
    state.title.isVisible,
    state.title.value,
    state.barColor,
    state.threshHoldColor,
    state.showThreshHoldValue,
    state.threshHoldValue,
    state.showIndicator1,
    state.indicator1Color,
    state.indicator1Value,
    state.showIndicator2,
    state.indicator2Color,
    state.indicator2Value,
    state.minValue,
    state.maxValue,
    state.margin,
    state.overrideGlobalSettings,
    state.startDate,
    state.endDate,
    state.timeRange,
    state.globalSamplePeriod,
    state,
  ]);
  return { ...allDataFetched, successAndFailedMeasurements };
}
