import { useRouter } from 'next/router';
import { Data, Layout } from 'plotly.js';
import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { getGlobalTimeRangeType } from '~/redux/selectors/topPanleSelectors';
import { getThousandSeparator } from '~/redux/selectors/userPreferences';
import { AssetMeasurementDetails } from '~/types/measures';
import { SingleHeatMapTimeSeriesData } from '~/types/timeseries';
import { HeatmapChartWidget, HeatMapPastelColor } from '~/types/widgets';
import { formatMetricLabel, formatMetricTag, formatNumber, roundNumber } from '~/utils/utils';
import { useGetHeatMapMeasuresTsData } from './useGetHeatMapMeasuresTsData';

type ChartData = {
  data: Data[];
  layout: Partial<Layout>;
};

type HeatMapResult = {
  tsData: SingleHeatMapTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
  lastFetchTime?: number;
  isError?: boolean;
  error?: string;
};

type SingleChartData = {
  layout: Partial<Layout>;
};

export function useFetchHeatmapData(widgetId: string, state: HeatmapChartWidget) {
  const prevResultsRef = useRef<HeatMapResult[]>([]);
  const [selectedTitles, setSelectedTitles] = useState<string[]>([]);
  const router = useRouter();
  const selectedSamplePeriod = state.samplePeriod || 0;
  const selectedHeatMapSeries = state.selectedDbMeasureId;
  const timeRangeType = useSelector(getGlobalTimeRangeType);
  const thousandSeparator = useSelector(getThousandSeparator);

  const [allDataFetched, setAllDataFetched] = useState({
    chartData: [] as Data[],
    isLoading: true,
    layoutData: {
      showlegend: true,
      title: 'Chart',
    } as Partial<Layout>,
  });
  const [chartResults, setChartResults] = useState<HeatMapResult[] | undefined>(undefined);

  function transformHeatmapDataForPlotly(
    results: HeatMapResult[],
    selectedHeatMapSeries: string[],
    title: {
      value: string;
      isVisible: boolean;
      color: string;
      fontSize?: number;
      fontWeight?: string;
    },
    selectedMeasureName: string,
    pastelColor: HeatMapPastelColor,
    isDashboardTemplatePage: boolean,
    thousandSeparator: boolean,
  ): ChartData {
    const traces: Data[] = [];
    const filteredResults = results.filter((result) => result && result.tsData);
    let result1 = filteredResults;
    if (!isDashboardTemplatePage) {
      result1 = filteredResults.filter(
        (result) => result && selectedHeatMapSeries.includes(result?.measureData?.id.toString()),
      );
    }
    if (result1.length === 0) {
      return {
        data: [],
        layout: {
          showlegend: true,
          title: 'No Data Available',
        },
      };
    }
    const result = result1[0];
    const { measureData, unitOfMeasures } = result;
    const unit = unitOfMeasures?.find((data) => data.id === measureData.unitOfMeasureId) || null;

    const layout: Partial<Layout> = {
      showlegend: true,
      title: title.isVisible
        ? title.value + ' (' + unit?.name + ')'
        : formatMetricLabel(selectedMeasureName) + ' (' + unit?.name + ')',
      titlefont: title.isVisible
        ? {
            size: title.fontSize,
            color: title.color,
          }
        : undefined,
      annotations: [],
      xaxis: {
        zeroline: false,
        title: state.groupX, // Add X-axis title based on groupX selection
      },
      yaxis: {
        zeroline: false,
        linecolor: 'white',
        title: state.groupY, // Add Y-axis title based on groupY selection
      },
    };

    if (result && result.tsData) {
      const seriesData = result.tsData;
      const measureData = result.measureData;
      const unitOfMeasures = result.unitOfMeasures;
      const { 'gb0,gb1,val': values } = seriesData;
      const x: string[] = [];
      const y: string[] = [];
      const z: number[] = [];
      values?.forEach(([xVal, yVal, zVal]) => {
        x.push(xVal);
        y.push(yVal);
        z.push(Number(thousandSeparator ? formatNumber(zVal) : roundNumber(zVal)));
      });

      const unitsOfMeasure = unitOfMeasures.find(
        (data) => data.id === measureData.unitOfMeasureId,
      ) || { name: '', id: 0 };

      const title = formatMetricLabel(measureData.tag);

      traces.push({
        type: 'heatmap',
        colorscale: pastelColor ?? 'Jet',
        x: x,
        y: y,
        z: z,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        hoverongaps: false,
        hovertemplate: `${title}: %{z} ${unitsOfMeasure.name}<br>${state.groupX}: %{x}<br>${state.groupY}: %{y}<extra></extra>`,
        name: `${formatMetricTag(measureData.tag)} (${unitsOfMeasure.name})`,
        yaxis: 'y',
      });
    }

    const singleChartData: SingleChartData = {
      layout: {},
    };
    return {
      data: traces,
      layout: { ...layout, ...singleChartData.layout },
    };
  }

  useEffect(() => {
    if (
      state.mode === 'dashboard' &&
      state.assetMeasure.assetId !== '' &&
      state.assetMeasure.measureId.some((measure) => measure !== '')
    ) {
      setSelectedTitles(state.assetMeasure.measureId);
    }
    if (state.mode === 'template') {
      if (state.selectedDbMeasureId !== '') {
        setSelectedTitles([state.selectedDbMeasureId]);
      } else {
        setSelectedTitles([]);
      }
    }
  }, [state.assetMeasure, state.selectedDbMeasureId]);

  const {
    data: measureData,
    isLoading: isMeasureDataLoading,
    successAndFailedMeasurements,
  } = useGetHeatMapMeasuresTsData({
    selectedTitles,
    dataFetchSettings: state,
    assetMeasure: [state.assetMeasure],
  });
  useEffect(() => {
    if (isMeasureDataLoading) {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isLoading: true,
          isError: false,
        };
      });
    } else if (measureData) {
      const filteredList = measureData.filter(({ isError }) => !isError);
      setChartResults(filteredList);
    } else {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isError: true,
          isLoading: false,
        };
      });
    }
  }, [isMeasureDataLoading, measureData]);
  useEffect(() => {
    if (chartResults) {
      const chartData = transformHeatmapDataForPlotly(
        chartResults,
        selectedTitles,
        state.title,
        state.dbMeasureIdToName[selectedHeatMapSeries],
        state.pastelColor,
        router.pathname === '/dashboard-template',
        thousandSeparator,
      );
      setAllDataFetched({
        chartData: chartData.data,
        isLoading: false,
        layoutData: chartData.layout,
      });
    }
  }, [
    chartResults,
    state.title.value,
    state.title.isVisible,
    selectedSamplePeriod,
    timeRangeType,
    state.aggBy,
    state.groupX,
    state.groupY,
    state.overrideGlobalSettings,
    state.startDate,
    state.endDate,
    state.timeRange,
    selectedHeatMapSeries,
    state.title,
    state.globalSamplePeriod,
    state.pastelColor,
    thousandSeparator,
  ]);
  return { ...allDataFetched, successAndFailedMeasurements };
}
