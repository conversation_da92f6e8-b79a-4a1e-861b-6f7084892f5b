import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { AssetMeasurementDetails } from '~/types/measures';
import { SingleScatterTimeSeriesData } from '~/types/timeseries';
import { ImageWidget, SVGTexts } from '~/types/widgets';
import { formatNumber, roundNumber } from '~/utils/utils';
import { useGetMeasuresTsData } from './useGetMeasuresTsData';
import { getThousandSeparator } from '~/redux/selectors/userPreferences';
type TrendResult = {
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};

const transformData = (
  results: TrendResult[],
  selectedTitles: string[],
  isDashboardTemplate: boolean,
  thousandSeparator: boolean,
): Record<string, string> => {
  const data: Record<string, string> = {};
  const filteredResults = isDashboardTemplate
    ? [...results]
    : results.filter((result) => result && result.tsData);
  for (let i = 0; i < filteredResults.length; i++) {
    const result = filteredResults[i];

    const seriesData = result.tsData;
    const measureData = result.measureData;
    if (
      !isDashboardTemplate &&
      selectedTitles.length > 0 &&
      selectedTitles.includes(measureData.id?.toString()) === false
    )
      continue;
    const values = seriesData['ts,val'] ?? [];
    const x = values.length == 0 ? [0, 0] : values[values.length - 1];
    if (!isDashboardTemplate) {
      data[measureData.id] = thousandSeparator ? formatNumber(x[1]) : roundNumber(x[1]);
    }
    if (isDashboardTemplate) {
      data[selectedTitles[i]] = thousandSeparator ? formatNumber(x[1]) : roundNumber(x[1]);
    }
  }
  return data;
};
const useFetchImageWidget = (id: string, state: ImageWidget) => {
  const dispatch = useDispatch();
  const thousandSeparator = useSelector(getThousandSeparator);
  const [allDataFetched, setAllDataFetched] = useState<{
    data: Record<string, string>;
    svgTexts: SVGTexts[];
    isLoading: boolean;
    isError: boolean;
  }>({ data: {}, isLoading: true, isError: false, svgTexts: [] });
  const [measureIdUOM, setMeasureIdUOM] = useState<Record<string, string>>({});
  const router = useRouter();
  const [widgetResults, setWidgetResults] = useState<TrendResult[] | undefined>(undefined);
  const [selectedMeasures, setSelectedMeasures] = useState<string[]>([]);
  useEffect(() => {
    if (state.mode === 'dashboard') {
      const hasValidAssetMeasure = state.assetMeasure.some(
        (assetMeas) =>
          assetMeas.assetId.trim() !== '' &&
          assetMeas.measureId.some((measure) => measure.trim() !== ''),
      );

      if (hasValidAssetMeasure) {
        const titles = state.assetMeasure
          .flatMap((assetMeas) => assetMeas.measureId)
          .filter((measure) => measure.trim() !== '');

        setSelectedMeasures(titles);
      } else {
        // Optionally, clear selectedTitles if no valid asset measures
        setSelectedMeasures([]);
      }
    }
    if (state.mode === 'template') {
      const metrics = state.selectedTitles;
      if (metrics.length > 0) {
        setSelectedMeasures(metrics);
      } else {
        setSelectedMeasures([]);
      }
    }
  }, [state.assetMeasure, state.selectedTitles]);
  const { data: measureData, isLoading: isMeasureDataLoading } = useGetMeasuresTsData({
    selectedTitles: selectedMeasures,
    dataFetchSettings: state,
    assetMeasure: state.assetMeasure,
  });
  useEffect(() => {
    if (isMeasureDataLoading) {
      setWidgetResults(undefined);
      setMeasureIdUOM({});
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isLoading: true,
          isError: false,
        };
      });
    } else if (measureData) {
      const filteredList = measureData.filter(({ isError }) => !isError);
      setWidgetResults(filteredList);
    } else {
      setWidgetResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isError: true,
          isLoading: false,
        };
      });
    }
  }, [isMeasureDataLoading, measureData]);
  useEffect(() => {
    if (widgetResults) {
      const data = transformData(
        widgetResults,
        state.selectedTitles,
        router.pathname === '/dashboard-template',
        thousandSeparator,
      );
      const keyValuePair = widgetResults.reduce<Record<string, string>>((acc, result) => {
        acc[result.measureData.id.toString()] = result.tsData.tag_meta.uom;
        return acc;
      }, {});
      setMeasureIdUOM(keyValuePair);
      setAllDataFetched({ data: data, isLoading: false, isError: false, svgTexts: [] });
    }
  }, [id, widgetResults, state.selectedTitles, state.font, state.title]);
  return { ...allDataFetched, measureIdUOM };
};

export default useFetchImageWidget;
