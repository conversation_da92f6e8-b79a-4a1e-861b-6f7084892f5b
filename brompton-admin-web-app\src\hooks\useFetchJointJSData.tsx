import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { ThunkDispatch } from 'redux-thunk';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { measuresApi } from '~/redux/api/measuresApi';
import { timeseriesApi } from '~/redux/api/timeseriesApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { getCurrentDashboardId, selectDashboardState } from '~/redux/selectors/dashboardSelectors';
import { getAssetTz, getGlobalTimeRangeType } from '~/redux/selectors/topPanleSelectors';
import { getAssetIdByDbMeasureId } from '~/redux/selectors/treeSelectors';
import { getGlobalEndDate, getGlobalStartDate } from '~/redux/selectors/widgetSelectors';
import { RootState } from '~/redux/store';
import { AggByOptions, SamplePeriodOptions } from '~/types/dashboard';
import { AssetMeasurementDetails } from '~/types/measures';
import { SingleScatterTimeSeriesData } from '~/types/timeseries';
import { DiagramWidget } from '~/types/widgets';

type Stats = {
  current: string;
};

type TrendResult = {
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};

function getChartDataForSingleTrace(
  singleSeries: TrendResult | null,
  selectedSamplePeriod: number,
): Stats | null {
  if (!singleSeries) {
    return null;
  }

  const { 'ts,val': values, error } = singleSeries.tsData;
  if (error || !values) {
    return null;
  }
  const unit = singleSeries.unitOfMeasures.filter(
    (data) => data.id == singleSeries.measureData.unitOfMeasureId,
  )[0];
  const yValues = values.map((value) => value[1]);
  const current = yValues.slice(-1).pop()?.toFixed(2) || ''; // Get the last value without using indexing and square braces
  return { current };
}

export function useFetchDiagramData(state: DiagramWidget) {
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const customerId = useSelector(getCustomerId);
  const dashboardId = useSelector(getCurrentDashboardId);
  const startDate = useSelector(getGlobalStartDate);
  const endDate = useSelector(getGlobalEndDate);
  const selectedSamplePeriod = state.samplePeriod || 0;
  const selectedAggBy = state.aggBy || 0;
  const { selectedDbMeasureId } = state;
  const assetId = useSelector(getAssetIdByDbMeasureId(selectedDbMeasureId));
  const timeRangeType = useSelector(getGlobalTimeRangeType);
  const dashboardState = useSelector(selectDashboardState);
  const [statsResults, setStatsResults] = useState<TrendResult | undefined>(undefined);
  const assetTz = useSelector(getAssetTz);

  const [allDataFetched, setAllDataFetched] = useState<{
    isLoading: boolean;
    stats: Stats | null;
  }>({
    isLoading: false,
    stats: null,
  });

  useEffect(() => {
    const fetchTwoDData = async () => {
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isLoading: true,
        };
      });
      const promise = dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate({
          customerId,
          assetId,
          measId: selectedDbMeasureId,
        }),
      ).then(({ data: measureData, isSuccess }) => {
        if (isSuccess && measureData && measureData.measurementId) {
          return dispatch(
            timeseriesApi.endpoints.getMeasurementSeries.initiate({
              customerId,
              measId: measureData.measurementId.toString(),
              start: state.overrideGlobalSettings ? state.startDate : startDate,
              end: state.overrideGlobalSettings ? state.endDate : endDate,
              agg: AggByOptions[selectedAggBy].serverValue,
              agg_period: state.globalSamplePeriod
                ? SamplePeriodOptions[selectedSamplePeriod].serverValue
                : SamplePeriodOptions[dashboardState.topPanel.samplePeriod].serverValue,
              timeRangeType: state.overrideGlobalSettings ? state.timeRange : timeRangeType,
              assetTz,
            }),
          ).then(({ data: tsData, isSuccess: isTsSuccess }) => {
            if (isTsSuccess && tsData) {
              return dispatch(
                measuresApi.endpoints?.getUnitsOfMeasure.initiate({
                  measurementTypeId: measureData.typeId,
                }),
              ).then(({ data: unitOfMeasures, isSuccess }) => {
                if (isSuccess && unitOfMeasures) {
                  return {
                    tsData: tsData,
                    measureData,
                    unitOfMeasures,
                  } as TrendResult;
                }
                return null;
              });
            }
            return null;
          });
        } else {
          return null;
        }
        // return result.data;
      });
      const result = await promise;
      if (result !== null) {
        setStatsResults(result);
      } else {
        setAllDataFetched((prev) => {
          return {
            ...prev,
            isLoading: false,
          };
        });
      }
    };
    if (dashboardId && selectedDbMeasureId && assetId) {
      fetchTwoDData();
    }
  }, [
    customerId,
    assetId,
    startDate,
    endDate,
    selectedAggBy,
    selectedSamplePeriod,
    selectedDbMeasureId,
    state.overrideGlobalSettings,
    state.startDate,
    state.endDate,
    state.timeRange,
    state.globalSamplePeriod,
    dispatch,
    dashboardState.topPanel.samplePeriod,
    timeRangeType,
    dashboardId,
    assetTz,
  ]);

  useEffect(() => {
    if (statsResults) {
      const stats = getChartDataForSingleTrace(statsResults, selectedSamplePeriod);
      setAllDataFetched({
        isLoading: false,
        stats,
      });
    }
  }, [
    statsResults,
    state.current,
    state.overrideGlobalSettings,
    state.startDate,
    state.endDate,
    state.timeRange,
    state.globalSamplePeriod,
    selectedSamplePeriod,
  ]);

  return allDataFetched;
}
