import { useRouter } from 'next/router';
import { Data, Layout } from 'plotly.js';
import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { getDateTimeFormatForChart } from '~/redux/selectors/userPreferences';
import { AssetMeasurementDetails } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchTime,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { ChartMeasureSetting, KPIPercentage } from '~/types/widgets';
import {
  calulationXaxisValue,
  calulationYaxisValue,
  formatMetricLabel,
  formatMetricTag,
  roundNumber,
} from '~/utils/utils';
import { useGetMeasuresTsData } from './useGetMeasuresTsData';
type Stats = {
  min: string;
  max: string;
  avg: string;
  total: string;
  unit: UnitOfMeasure | undefined;
};

type ChartData = {
  data: Data[];
  removedResults: AssetMeasurementDetailsWithLastFetchTime[];
  layout: Partial<Layout>;
};

type TrendResult = {
  isError: boolean;
  error?: string;
  lastFetchTime: number;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};

type SingleChartData = {
  layout: Partial<Layout>;
  stats: Stats;
};

function transformBarDataForPlotly(
  results: TrendResult[],
  dbMeasureIdToSetting: Record<string, ChartMeasureSetting>,
  selectedSamplePeriod: number,
  selectedDbMeasureId: string,
  title: {
    value: string;
    isVisible: boolean;
    color: string;
    fontSize?: number;
    fontWeight?: string;
  },
  positiveColor: string,
  negativeColor: string,
  dateFormats: string,
  selectedTitles: string[],
  isDashboardTemplate: boolean,
): ChartData {
  const traces: Data[] = [];
  const layout: Partial<Layout> = {
    showlegend: false,
    title: title.isVisible ? title.value : undefined,
    titlefont: title.isVisible
      ? {
          size: title.fontSize,
          color: title.color,
        }
      : undefined,
    annotations: [],
    margin: {
      l: 80,
      r: 50,
      b: 50,
      t: 50,
    },
    barmode: 'group',
    yaxis: {
      side: 'left',
    },
    yaxis2: {
      side: 'right',
      overlaying: 'y',
    },
  };

  let chartNumber = 1;
  const removedResults: TrendResult[] = [];
  const filteredResults: TrendResult[] = [];
  if (isDashboardTemplate) {
    filteredResults.push(...results);
  } else {
    removedResults.push(
      ...results.filter((result) => !result || !result.tsData || result.isError || result.error),
    );
    filteredResults.push(...results.filter((result) => result && result.tsData));
  }
  for (let i = 0; i < filteredResults.length; i++) {
    const result = filteredResults[i];

    if (result && result.tsData) {
      const seriesData = result.tsData;
      const measureData = result.measureData;
      if (
        !isDashboardTemplate &&
        selectedTitles.length > 0 &&
        selectedTitles.includes(measureData.id?.toString()) === false
      )
        continue;
      const unitOfMeasures = result.unitOfMeasures;
      const values = seriesData['ts,val'];
      if (!values) continue;
      const x = calulationXaxisValue(seriesData, false);
      const y = calulationYaxisValue(seriesData);
      const unitsOfMeasure = unitOfMeasures.find(
        (data) => data.id === measureData.unitOfMeasureId,
      ) || { name: '', id: 0 };

      const title = formatMetricLabel(measureData.tag);
      const yAxisLabel =
        chartNumber == 1
          ? 'Left'
          : dbMeasureIdToSetting && dbMeasureIdToSetting[measureData.id]?.yAxisSide == 'right'
          ? 'Right'
          : 'Left';
      traces.push({
        type: 'bar',
        x: y.map((value) => roundNumber(value)),
        orientation: 'h',
        y: x,
        marker: { color: y.map((value) => (value > 0 ? positiveColor : negativeColor)) },
        hovertemplate: `${title}: %{x} ${unitsOfMeasure.name}<br>Time: %{y}<extra></extra>'`,
        name: `${formatMetricTag(measureData.tag)} (${unitsOfMeasure.name})(${yAxisLabel})`,
        yaxis:
          chartNumber == 1
            ? 'y'
            : dbMeasureIdToSetting && dbMeasureIdToSetting[measureData.id]?.yAxisSide == 'right'
            ? 'y2'
            : 'y',
        mode: 'lines',
      });

      chartNumber++;
    }
  }

  const singleChartData: SingleChartData = {
    stats: { avg: '', max: '', min: '', total: '', unit: undefined },
    layout: {
      annotations: [],
      shapes: [],
    },
  };
  return {
    data: traces,
    removedResults: removedResults.map((res) => {
      return {
        ...res.measureData,
        lastFetchTime: res.lastFetchTime,
        partialFailed: removedResults.length !== results.length,
      };
    }),
    layout: { ...layout, ...singleChartData.layout, barmode: 'group' },
  };
}

export function useFetchKPIBars(widgetId: string, state: KPIPercentage) {
  const router = useRouter();
  const selectedSamplePeriod = state.samplePeriod || 0;
  const selectedAggBy = state.aggBy;
  const prevResultsRef = useRef<TrendResult[]>([]); // Keep latest working state
  const dbMeasureIdToSetting = state.dbMeasureIdToSetting;
  const dateFormats = useSelector(getDateTimeFormatForChart);
  const [chartResults, setChartResults] = useState<TrendResult[] | undefined>(undefined);
  const [selectedTitles, setSelectedTitles] = useState<string[]>([]);
  const [allDataFetched, setAllDataFetched] = useState<{
    chartData: Data[];
    isLoading: boolean;
    isError: boolean;
    layoutData: Partial<Layout>;
    removedResults: AssetMeasurementDetailsWithLastFetchTime[];
  }>({
    chartData: [],
    isLoading: true,
    isError: false,
    removedResults: [],
    layoutData: {
      showlegend: true,
      title: 'Chart',
    } as Partial<Layout>,
  });

  useEffect(() => {
    if (
      state.mode === 'dashboard' &&
      state.assetMeasure?.assetId !== '' &&
      state.assetMeasure.measureId.some((measure) => measure !== '')
    ) {
      setSelectedTitles(state.assetMeasure.measureId);
    }
    if (state.mode === 'template') {
      const metric = state.selectedDbMeasureId;
      if (metric && metric !== '') {
        setSelectedTitles([metric]);
      } else {
        setSelectedTitles([]);
      }
    }
  }, [state.assetMeasure, state.selectedDbMeasureId, state.mode]);

  const {
    data: measureData,
    isLoading: isMeasureDataLoading,
    successAndFailedMeasurements,
  } = useGetMeasuresTsData({
    selectedTitles,
    dataFetchSettings: state,
    assetMeasure: [state.assetMeasure],
  });
  useEffect(() => {
    if (isMeasureDataLoading) {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isLoading: true,
          isError: false,
        };
      });
    } else if (measureData) {
      const updated: TrendResult[] = [];
      (measureData || []).forEach((result) => {
        if (!result.isError && result.tsData) {
          updated.push(result);
        } else {
          const existing = prevResultsRef.current.find(
            (r) => r.measureData.measurementId === result?.measureData?.measurementId,
          );
          if (existing) {
            updated.push({
              ...existing,
              lastFetchTime: result.lastFetchTime,
              isError: result.isError,
              error: result.error,
              tsData: {
                ...existing.tsData,
                error: result.error,
              },
            });
          } else {
            updated.push(result);
          }
        }
      });
      prevResultsRef.current = updated; // Keep latest working state
      setChartResults(updated as TrendResult[]);
    } else {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isError: true,
          isLoading: false,
        };
      });
    }
  }, [isMeasureDataLoading, measureData]);

  useEffect(() => {
    if (chartResults) {
      const chartData = transformBarDataForPlotly(
        chartResults,
        dbMeasureIdToSetting,
        selectedSamplePeriod,
        state.selectedDbMeasureId,
        state.title,
        state.positiveColor,
        state.negativeColor,
        dateFormats,
        selectedTitles,
        router.pathname === '/dashboard-template',
      );
      setAllDataFetched({
        chartData: chartData.data,
        isLoading: false,
        isError: false,
        removedResults: chartData.removedResults,
        layoutData: chartData.layout,
      });
    }
  }, [
    chartResults,
    state.title?.value,
    state.title?.isVisible,
    state.selectedDbMeasureId,
    dbMeasureIdToSetting,
    selectedSamplePeriod,
    selectedAggBy,
    state.overrideGlobalSettings,
    state.startDate,
    state.endDate,
    state.timeRange,
    state.globalSamplePeriod,
    state.positiveColor,
    state.negativeColor,
    dateFormats,
  ]);
  return { ...allDataFetched, successAndFailedMeasurements };
}
