import { Data, Layout } from 'plotly.js';
import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { getAssetTz } from '~/redux/selectors/topPanleSelectors';
import { getDbMeasureIdToName } from '~/redux/selectors/treeSelectors';
import { getThousandSeparator } from '~/redux/selectors/userPreferences';
import { AssetMeasurementDetails } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchTime,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { KPIColorBox } from '~/types/widgets';
import {
  formatChartDate,
  formatChartDateToAssetTz,
  formatMetricLabel,
  formatMetricTag,
  formatNumber,
  roundNumber,
  showMeanValue,
} from '~/utils/utils';
import { useGetMeasuresTsData } from './useGetMeasuresTsData';

type TrendResult = {
  isError: boolean;
  error?: string;
  lastFetchTime: number;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};
type KPIData = {
  trendUp: boolean;
  unit: string;
  lastValue: string;
  prevValue: string;
  differencePercentage: number;
  delta: string;
  isTrendSame: boolean;
  data: Data[];
  layout: Partial<Layout>;
  removedResult?: AssetMeasurementDetailsWithLastFetchTime;
};
const transformKPIColorData = (
  results: TrendResult[],
  settings: KPIColorBox,
  useAssetTz: boolean,
  selectedDbMeasureIdToName: Record<string, string>,
  forcastedData: TrendResult[] | undefined,
  thousandSeparator: boolean,
): KPIData => {
  const traces: Data[] = [];
  let layout: Partial<Layout> = {
    showlegend: true,

    annotations: [],
    yaxis: {
      side: 'left',
    },
    yaxis2: {
      // title: 'Unit Values',
      side: 'right',
      overlaying: 'y',
    },
    margin: {
      b: 0,
      l: 0,
      pad: 0,
      r: 0,
      t: 0,
    },
  };

  if (results.length === 0) {
    return {
      trendUp: false,
      unit: '-',
      lastValue: '-',
      differencePercentage: 0,
      prevValue: '-',
      delta: '-',
      isTrendSame: false,
      data: [],
      layout: {
        ...layout,
      },
    };
  }
  const res = results[0]?.tsData?.['ts,val']?.slice(-2);
  if (results.length === 0 || !results[0]?.tsData?.['ts,val'] || res.length < 2) {
    return {
      trendUp: false,
      unit: '',
      lastValue: '-',
      differencePercentage: 0,
      prevValue: '-',
      delta: '-',
      isTrendSame: false,
      data: [],
      layout: {
        ...layout,
      },
      removedResult: {
        ...results[0]?.measureData,
        lastFetchTime: results[0]?.lastFetchTime,
        partialFailed: true,
      },
    };
  }
  const value: number = res[res?.length - 1][1];
  const referenceValue = res[res?.length - 2]?.[1];
  const isTrendUp = value > referenceValue;
  const differencePercentage = ((value - referenceValue) / referenceValue) * 100;
  const delta = value - referenceValue;
  const trendUp = isTrendUp;
  const isTrendSame = value === referenceValue;
  const filteredResults = results.filter((result) => result && result.tsData);
  const removedResult = results.filter(
    (result) => !result || !result.tsData || result.isError || result.error,
  );
  const filteredForecatResults = forcastedData?.filter((result) => result && result.tsData);
  let chartNumber = 1;
  let unit = '';
  for (let i = 0; i < filteredResults.length; i++) {
    const result = filteredResults[i];

    if (result && result.tsData) {
      const seriesData = result.tsData;
      const measureData = result.measureData;
      const unitOfMeasures = result.unitOfMeasures;
      const values = seriesData['ts,val'];
      if (!values) continue;
      const x = values.map(([timestamp]) =>
        useAssetTz
          ? formatChartDateToAssetTz(new Date(timestamp))
          : formatChartDate(new Date(timestamp)),
      );
      const y = values.map(([, value]) => value);

      const unitsOfMeasure = unitOfMeasures.find(
        (data) => data.id === measureData.unitOfMeasureId,
      ) || { name: '', id: 0 };
      unit = unitsOfMeasure.name;
      const title = formatMetricLabel(measureData.tag);
      traces.push({
        type: 'scatter',
        fill: 'tozeroy',
        marker: { color: settings.sparkLineColor },
        x,
        y: y.map((value) => (thousandSeparator ? formatNumber(value) : roundNumber(value))),
        hovertemplate: `${title}: %{y} ${unitsOfMeasure.name}<br>Time: %{x}<extra></extra>'`,
        name: `${formatMetricTag(measureData.tag)} (${unitsOfMeasure.name})`,
        // yaxis: dbMeasureIdToSetting[result.measureData.id]?.yAxisSide === 'right' ? 'y2' : 'y',
        mode: 'lines',
      });

      chartNumber++;
    }
  }
  if (filteredForecatResults && settings.showForecast) {
    for (let i = 0; i < filteredForecatResults.length; i++) {
      const result = filteredForecatResults[i];

      if (result && result.tsData) {
        const seriesData = result.tsData;
        const measureData = result.measureData;
        const unitOfMeasures = result.unitOfMeasures;
        const values = seriesData['ts,val'];
        if (!values) continue;
        const x = values.map(([timestamp]) =>
          useAssetTz
            ? formatChartDateToAssetTz(new Date(timestamp))
            : formatChartDate(new Date(timestamp)),
        );
        const y = values.map(([, value]) => value);

        const unitsOfMeasure = unitOfMeasures.find(
          (data) => data.id === measureData.unitOfMeasureId,
        ) || { name: '', id: 0 };
        unit = unitsOfMeasure.name;
        const title = formatMetricLabel(measureData.tag);
        traces.push({
          type: 'scatter',
          fill: 'none',
          x,
          y: y.map((value) => (thousandSeparator ? formatNumber(value) : roundNumber(value))),
          marker: { color: settings.forecastColor ?? '#000000' },
          hovertemplate: `${title}: %{y} ${unitsOfMeasure.name}<br>Time: %{x}<extra></extra>'`,
          name: `${formatMetricTag(measureData.tag)} (${unitsOfMeasure.name}) - Forecast`,
          mode: 'lines',
          line: {
            dash: 'dot',
          },
        });

        chartNumber++;
      }
    }
    if (settings.showMean) {
      layout = showMeanValue(
        {
          layout: layout,
        },
        traces,
        {
          meanColor: settings.meanColor,
          meanStyle: settings.meanStyle,
          meanName: settings.meanName,
        },
      ).layout;
    }
  }
  layout.showlegend = false;
  layout.xaxis = {
    showline: false,
    showgrid: false,
    showticklabels: false,
    autotick: false,
  };
  layout.xaxis = {
    showline: false,
    showgrid: false,
    showticklabels: false,
    autotick: false,
  };
  layout.yaxis = {
    showline: false,
    showgrid: false,
    showticklabels: false,
    autotick: false,
  };
  layout.yaxis = {
    showgrid: false,
    zeroline: false,
    showline: false,
    showticklabels: false,
  };
  if (!settings.showForecast) {
    layout.annotations = [];
    layout.shapes = [];
  }
  return {
    unit,
    trendUp,
    lastValue: value.toString(),
    prevValue: referenceValue.toString(),
    differencePercentage,
    delta: delta.toString(),
    isTrendSame,
    data: traces,
    layout: {
      ...layout,
    },
    removedResult:
      removedResult.length > 0 && removedResult[0]
        ? {
            ...removedResult[0].measureData,
            lastFetchTime: removedResult[0].lastFetchTime,
            partialFailed: false,
          }
        : undefined,
  };
};
export function useFetchKPIColorBoxdata(state: KPIColorBox) {
  const prevResultsRef = useRef<TrendResult[]>([]);

  const [allDataFetched, setAllDataFetched] = useState<{
    isLoading: boolean;
    data: null | KPIData;
    removedResult?: AssetMeasurementDetailsWithLastFetchTime;
  }>({
    data: null,
    isLoading: true,
    removedResult: undefined,
  });
  const thousandSeparator = useSelector(getThousandSeparator);
  const [chartResults, setChartResults] = useState<TrendResult[] | undefined>(undefined);
  const [forecastedResults, setForcastedResult] = useState<TrendResult[] | undefined>(undefined);
  const [selectedTitles, setSelectedTitles] = useState<string[]>([]);
  const useAssetTz = useSelector(getAssetTz);
  const selectedDbMeasureIdToName = useSelector(getDbMeasureIdToName);

  useEffect(() => {
    if (
      state.mode === 'dashboard' &&
      state.assetMeasure.assetId !== '' &&
      state.assetMeasure.measureId.some((measure) => measure !== '')
    ) {
      setSelectedTitles(state.assetMeasure.measureId);
    }
    if (state.mode === 'template') {
      if (state.selectedDbMeasureId !== '') {
        setSelectedTitles([state.selectedDbMeasureId]);
      } else {
        setSelectedTitles([]);
      }
    }
  }, [state.assetMeasure, state.selectedDbMeasureId, state.mode]);
  const {
    data: measureData,
    isLoading: isMeasureDataLoading,
    forcastedData,
    successAndFailedMeasurements,
  } = useGetMeasuresTsData({
    selectedTitles,
    dataFetchSettings: state,
    assetMeasure: [state.assetMeasure],
  });
  useEffect(() => {
    if (isMeasureDataLoading) {
      setChartResults(undefined);
      setForcastedResult(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isLoading: true,
          isError: false,
        };
      });
    } else if (measureData) {
      const updated: TrendResult[] = [];
      (measureData || []).forEach((result) => {
        if (!result.isError && result.tsData) {
          updated.push(result);
        } else {
          const existing = prevResultsRef.current.find(
            (r) => r.measureData.measurementId === result?.measureData?.measurementId,
          );
          if (existing) {
            updated.push({
              ...existing,
              lastFetchTime: result.lastFetchTime,
              isError: result.isError,
              error: result.error,
              tsData: {
                ...existing.tsData,
                error: result.error,
              },
            });
          } else {
            updated.push(result);
          }
        }
      });
      prevResultsRef.current = updated; // Keep latest working state
      setChartResults(updated as TrendResult[]);
      const forecastFilter = forcastedData?.filter(({ isError }) => !isError);
      setForcastedResult(forecastFilter as TrendResult[]);
    } else {
      setChartResults(undefined);
      setForcastedResult(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isError: true,
          isLoading: false,
        };
      });
    }
  }, [isMeasureDataLoading, measureData, forcastedData]);

  useEffect(() => {
    if (chartResults) {
      const colorBoxData = transformKPIColorData(
        chartResults,
        state,
        useAssetTz,
        selectedDbMeasureIdToName,
        forecastedResults,
        thousandSeparator,
      );
      setAllDataFetched({
        removedResult: colorBoxData.removedResult,
        data: colorBoxData,
        isLoading: false,
      });
    }
  }, [
    chartResults,
    state.title.isVisible,
    state.title.value,
    state.overrideGlobalSettings,
    state.startDate,
    state.endDate,
    state.timeRange,
    state.globalSamplePeriod,
    state,
    useAssetTz,
    state.sparkLineColor,
    state.period,
    forecastedResults,
    state.showForecast,
    state.forecastColor,
    state.showForecast,
    state.meanColor,
    state.meanName,
    state.meanStyle,
    state.showMean,
    thousandSeparator,
  ]);
  return { ...allDataFetched, successAndFailedMeasurements };
}
