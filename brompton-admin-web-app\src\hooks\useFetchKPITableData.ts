import { useRouter } from 'next/router';
import { Data, Layout } from 'plotly.js';
import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { getAssetTz } from '~/redux/selectors/topPanleSelectors';
import { getDateTimeFormat, getThousandSeparator } from '~/redux/selectors/userPreferences';
import { AssetMeasurementDetails } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchTime,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { ChartMeasureSetting, KPITable } from '~/types/widgets';
import {
  formatChartDate,
  formatChartDateToAssetTz,
  formatMetricLabel,
  formatMetricTag,
  formatNumber,
  roundNumber,
  showMinMaxAvg,
} from '~/utils/utils';
import { useGetMeasuresTsData } from './useGetMeasuresTsData';

type Stats = {
  min: string;
  max: string;
  avg: string;
  total: string;
  delta: string;
  current: string;
  sum: string;
  unit: UnitOfMeasure | undefined;
};

type ChartData = {
  res: {
    id: string;
    name: string;
    chartData: Data[] | undefined;
    stats: SingleChartData;
  }[];
  removedResults: AssetMeasurementDetailsWithLastFetchTime[];
};

type TrendResult = {
  isError: boolean;
  error?: string;
  lastFetchTime: number;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};

export type SingleChartData = {
  layout: Partial<Layout>;
  stats: Stats;
};

function getChartDataForSingleTrace(
  filteredResults: TrendResult[],
  thousandSeparator: boolean,
): SingleChartData {
  const singleSeries = filteredResults[0];
  const { 'ts,val': values } = singleSeries.tsData;
  if (!values) {
    return {
      layout: {},
      stats: {
        avg: '-',
        max: '-',
        min: '-',
        sum: '-',
        delta: '-',
        current: '-',
        total: '-',
        unit: undefined,
      },
    };
  }
  const unit = singleSeries.unitOfMeasures.filter(
    (data) => data.id == singleSeries.measureData.unitOfMeasureId,
  )[0];
  const yValues = values.map((value) => value[1]);
  const max = thousandSeparator
    ? formatNumber(Math.max(...yValues))
    : roundNumber(Math.max(...yValues));
  const min = thousandSeparator
    ? formatNumber(Math.min(...yValues))
    : roundNumber(Math.min(...yValues));
  const tempTotal = yValues.reduce((a, b) => a + b, 0);
  const total = thousandSeparator ? formatNumber(Math.min(tempTotal)) : roundNumber(tempTotal);
  const avg = thousandSeparator
    ? formatNumber(tempTotal / yValues.length)
    : roundNumber(tempTotal / yValues.length);
  const sum =
    yValues.length > 0
      ? thousandSeparator
        ? formatNumber(yValues.reduce((a, b) => a + b, 0))
        : roundNumber(yValues.reduce((a, b) => a + b, 0))
      : '-';
  const current =
    yValues.length > 0
      ? thousandSeparator
        ? formatNumber(yValues.slice(-1).pop() ?? 0) || '-'
        : roundNumber(yValues.slice(-1).pop() ?? 0) || '-'
      : '-';

  const firstValue = yValues[0] ?? 0;
  const lastValue = yValues[yValues.length - 1] ?? 0;

  const delta =
    yValues.length > 0
      ? thousandSeparator
        ? formatNumber(lastValue - firstValue)
        : roundNumber(lastValue - firstValue)
      : '-';

  const layout: Partial<Layout> = showMinMaxAvg(
    {
      layout: {},
    } as Partial<Layout>,
    {
      avg: {
        show: false,
      },
      max: {
        show: false,
      },
      min: {
        show: false,
      },
    },
    min,
    max,
    avg,
  );
  layout.showlegend = false;
  layout.showlegend = false;
  layout.margin = {
    b: 0,
    l: 0,
    r: 0,
    t: 0,
  };
  layout.xaxis = {
    showline: false,
    showgrid: false,
    showticklabels: false,
    autotick: false,
  };
  layout.xaxis = {
    showline: false,
    showgrid: false,
    showticklabels: false,
    autotick: false,
  };
  layout.yaxis = {
    showline: false,
    showgrid: false,
    showticklabels: false,
    autotick: false,
  };
  layout.yaxis = {
    showgrid: false,
    zeroline: false,
    showline: false,
    showticklabels: false,
  };
  return { layout, stats: { min, max, avg, delta, sum, current, total, unit } };
}

function transformBarDataForPlotly(
  results: TrendResult[],
  dbMeasureIdToSetting: Record<string, ChartMeasureSetting>,
  selectedSamplePeriod: number,
  selectedTitles: string[],
  title: {
    value: string;
    isVisible: boolean;
    color: string;
    fontSize?: number;
    fontWeight?: string;
  },
  dateFormat: string,
  useAssetTz: boolean,
  isDashboardTemplate: boolean,
  thousandSeparator: boolean,
): ChartData {
  const traces: Data[] = [];
  const layout: Partial<Layout> = {
    showlegend: true,
    title: title.isVisible ? title.value : undefined,
    titlefont: {
      size: title.fontSize,
      color: title.color,
    },
    annotations: [],
    barmode: 'group',
    yaxis: {
      side: 'left',
    },
    yaxis2: {
      side: 'right',
      overlaying: 'y',
    },
  };
  let chartNumber = 1;
  const filteredResults: TrendResult[] = [];
  const removedResults: TrendResult[] = [];
  if (isDashboardTemplate) {
    filteredResults.push(...results);
  } else {
    removedResults.push(
      ...results.filter((result) => !result || result.error || result.isError || !result.tsData),
    );
    filteredResults.push(
      ...results.filter(
        (result) =>
          result && result.tsData && selectedTitles.includes(result.measureData.id?.toString()),
      ),
    );
  }
  const selectedMeasures: {
    [x: string]: Data[];
  }[] = [];
  for (let i = 0; i < filteredResults.length; i++) {
    const result = filteredResults[i];

    if (result && result.tsData) {
      const seriesData = result.tsData;
      const measureData = result.measureData;
      if (
        !isDashboardTemplate &&
        selectedTitles.length > 0 &&
        selectedTitles.includes(measureData.id?.toString()) === false
      )
        continue;
      const unitOfMeasures = result.unitOfMeasures;
      const values = seriesData['ts,val'];
      if (!values) continue;
      const x = values.map(([timestamp]) =>
        useAssetTz
          ? formatChartDateToAssetTz(new Date(timestamp))
          : formatChartDate(new Date(timestamp)),
      );
      const y = values.map(([, value]) => value);
      const unitsOfMeasure = unitOfMeasures.find(
        (data) => data.id === measureData.unitOfMeasureId,
      ) || { name: '', id: 0 };
      const title = formatMetricLabel(measureData.tag);
      const yAxisLabel =
        chartNumber == 1
          ? 'Left'
          : dbMeasureIdToSetting && dbMeasureIdToSetting[measureData.id]?.yAxisSide == 'right'
          ? 'Right'
          : 'Left';
      const trace: Data = {
        type: 'scatter',
        x,
        y,
        hovertemplate: `${title}: %{y} ${unitsOfMeasure.name}<br>Time: %{x}<extra></extra>'`,
        name: `${formatMetricTag(measureData.tag)} (${unitsOfMeasure.name})(${yAxisLabel})`,
        yaxis:
          chartNumber == 1
            ? 'y'
            : dbMeasureIdToSetting && dbMeasureIdToSetting[measureData.id]?.yAxisSide == 'right'
            ? 'y2'
            : 'y',
        mode: 'lines',
      };
      if (!isDashboardTemplate) {
        selectedMeasures.push({ [measureData.id.toString()]: [trace] });
      } else {
        selectedMeasures.push({ [measureData.tag.toLowerCase()]: [trace] });
      }
      traces.push(trace);
      chartNumber++;
    }
  }

  const finalRes = filteredResults.map((res, i) => {
    const { measureData, unitOfMeasures } = res;
    const unit = unitOfMeasures.find((data) => data.id === measureData.unitOfMeasureId) || null;
    return {
      id: !isDashboardTemplate ? res.measureData.id.toString() : i.toString(),
      name: formatMetricTag(res.measureData.tag) + ' (' + unit?.name + ')',
      chartData: !isDashboardTemplate
        ? selectedMeasures.find((data) => data[res.measureData.id.toString()])?.[
            res.measureData.id.toString()
          ]
        : selectedMeasures.find((data) => data[res.measureData.tag.toLowerCase()])?.[
            res.measureData.tag.toLowerCase()
          ],
      stats: getChartDataForSingleTrace([res], thousandSeparator),
    };
  });
  return {
    res: finalRes,
    removedResults: removedResults.map((res) => {
      return {
        ...res.measureData,
        lastFetchTime: res.lastFetchTime,
        partialFailed: removedResults.length !== results.length,
      };
    }),
  };
}

export function useFetchKPITableData(widgetId: string, state: KPITable) {
  const router = useRouter();
  const selectedSamplePeriod = state.samplePeriod || 0;
  const selectedAggBy = state.aggBy;
  const dbMeasureIdToSetting = state.dbMeasureIdToSetting;
  const prevResultsRef = useRef<TrendResult[]>([]);
  const selectedTitles = state.selectedTitles;
  const dateFormats = useSelector(getDateTimeFormat);
  const useAssetTz = useSelector(getAssetTz);
  const thousandSeparator = useSelector(getThousandSeparator);
  const [chartResults, setChartResults] = useState<TrendResult[] | undefined>(undefined);
  const [allDataFetched, setAllDataFetched] = useState({
    removedResults: [] as AssetMeasurementDetailsWithLastFetchTime[],
    isLoading: true,
    isError: false,
    res: [] as {
      name: string;
      chartData: Data[] | undefined;
      stats: SingleChartData;
      id: string;
    }[],
  });

  const [selectedMeasures, setSelectedMeasures] = useState<string[]>([]);
  useEffect(() => {
    if (state.mode === 'dashboard') {
      const hasValidAssetMeasure = state.assetMeasure.some(
        (assetMeas) =>
          assetMeas.assetId.trim() !== '' &&
          assetMeas.measureId.some((measure) => measure.trim() !== ''),
      );
      if (hasValidAssetMeasure) {
        const titles = state.assetMeasure
          .flatMap((assetMeas) => assetMeas.measureId)
          .filter((measure) => measure.trim() !== '');

        setSelectedMeasures(titles);
      } else {
        setSelectedMeasures([]);
      }
    }
    if (state.mode === 'template') {
      const selectedMesures = selectedTitles.map((title) => title);
      setSelectedMeasures([...selectedMesures]);
    }
  }, [state.assetMeasure, selectedTitles]);

  const {
    data: measureData,
    isLoading: isMeasureDataLoading,
    successAndFailedMeasurements,
  } = useGetMeasuresTsData({
    selectedTitles: selectedMeasures,
    dataFetchSettings: state,
    assetMeasure: state.assetMeasure,
  });
  useEffect(() => {
    if (isMeasureDataLoading) {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isLoading: true,
          isError: false,
        };
      });
    } else if (measureData) {
      const updated: TrendResult[] = [];
      (measureData || []).forEach((result) => {
        if (!result.isError && result.tsData) {
          updated.push(result);
        } else {
          const existing = prevResultsRef.current.find(
            (r) => r.measureData.measurementId === result?.measureData?.measurementId,
          );
          if (existing) {
            updated.push({
              ...existing,
              lastFetchTime: result.lastFetchTime,
              isError: result.isError,
              error: result.error,
              tsData: {
                ...existing.tsData,
                error: result.error,
              },
            });
          } else {
            updated.push(result);
          }
        }
      });
      prevResultsRef.current = updated; // Keep latest working state
      setChartResults(updated as TrendResult[]);
    } else {
      setChartResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isError: true,
          isLoading: false,
        };
      });
    }
  }, [isMeasureDataLoading, measureData]);

  useEffect(() => {
    if (chartResults) {
      const chartData = transformBarDataForPlotly(
        chartResults,
        dbMeasureIdToSetting,
        selectedSamplePeriod,
        state.mode === 'dashboard' ? selectedMeasures : selectedTitles,
        state.title,
        dateFormats,
        useAssetTz,
        router.pathname === '/dashboard-template',
        thousandSeparator,
      );
      setAllDataFetched({
        isLoading: false,
        isError: false,
        removedResults: chartData.removedResults,
        res: chartData.res,
      });
    }
  }, [
    chartResults,
    state.title.value,
    state.title.isVisible,
    selectedTitles,
    dbMeasureIdToSetting,
    selectedSamplePeriod,
    selectedAggBy,
    state.overrideGlobalSettings,
    state.startDate,
    state.endDate,
    state.timeRange,
    state.globalSamplePeriod,
    state.title,
    dateFormats,
    state.min,
    state.max,
    state.mean,
    state.sum,
    state.delta,
    state.current,
    router.pathname,
  ]);
  return { ...allDataFetched, successAndFailedMeasurements };
}
