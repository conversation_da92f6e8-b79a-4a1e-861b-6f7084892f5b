import { ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { Data, Layout } from 'plotly.js';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { measuresApi } from '~/redux/api/measuresApi';
import { timeseriesApi } from '~/redux/api/timeseriesApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { selectDashboardState } from '~/redux/selectors/dashboardSelectors';
import { getAssetTz, getGlobalTimeRangeType } from '~/redux/selectors/topPanleSelectors';
import { getThousandSeparator } from '~/redux/selectors/userPreferences';
import { getGlobalEndDate, getGlobalStartDate } from '~/redux/selectors/widgetSelectors';
import { RootState } from '~/redux/store';
import { AggByOptions, SamplePeriodOptions } from '~/types/dashboard';
import { AssetMeasurementDetails, measurementsUnitsDTO } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchAndSucess,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { MapWidget } from '~/types/widgets';
import { formatNumber, roundNumber } from '~/utils/utils';

type MeasuresData = {
  isLoading: boolean;
  isError: boolean;
  error: string;
  tsData: SingleScatterTimeSeriesData;
  lastFetchTime: number;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
  // partial_full_error?: 'partial' | 'full';
};
type TrendResult = {
  isError: boolean;
  error?: string;
  lastFetchTime?: number;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};
export const trensFormMapData = (
  mapResults: TrendResult[],
  settings: MapWidget,
  isDashboardTemplate: boolean,
  thousandSeparator: boolean,
): {
  data: Data[];
  mapLayout: Partial<Layout>;
} => {
  const mapLayout: Partial<Layout> = {
    autosize: true,
    margin: { l: 10, r: 10, b: 10, t: 10 },
    hovermode: 'closest',
    showlegend: false,
  };
  const data: Data[] = [];
  let counter = 0;

  settings.markers
    .filter((marker) => marker.assetMeasures.length === 0)
    .forEach((markerWithoutMeasures) => {
      data.push({
        type: 'scattermapbox',
        lon: [markerWithoutMeasures.location.lon],
        lat: [markerWithoutMeasures.location.lat],
        mode: 'markers',
        name: markerWithoutMeasures.markerName,
        marker: {
          size: 20,
          color: markerWithoutMeasures.color,
        },
        text: markerWithoutMeasures.markerName,
        value: counter++,
      });
    });

  mapLayout.mapbox = {
    style: 'open-street-map',
    center:
      data.length > 0 && !settings.changeMapCenter
        ? // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          { lat: data[0]?.lat[0], lon: data[0]?.lon[0] }
        : settings.mapCenter,
    zoom: settings.zoomLevel || 10,
  };
  if (mapResults.length === 0) {
    return {
      data: data,
      mapLayout: mapLayout,
    };
  }
  const results = mapResults.filter((res) => !res.isError && res.tsData);
  const mapResultsMeasures = results.reduce((acc, map) => {
    const measureIdKey = map.measureData.id.toString();
    const lastReading = thousandSeparator
      ? formatNumber(map.tsData?.['ts,val']?.slice(-1)?.[0]?.[1] ?? 0)?.toString()
      : roundNumber(map.tsData?.['ts,val']?.slice(-1)?.[0]?.[1] ?? 0)?.toString();
    acc[measureIdKey] = lastReading + ' ' + map?.tsData?.tag_meta?.uom || '';
    return acc;
  }, {} as { [measureId: string]: string });

  const markersWithMeasures = settings.markers
    .filter((marker) => marker.assetMeasures.length > 0)
    .map((marker) => {
      const newLabelAndUnits = { ...marker.labelAndUnits };
      marker.assetMeasures.forEach(({ measureId }) => {
        const measureIdStr = measureId.toString();
        if (measureIdStr in mapResultsMeasures) {
          newLabelAndUnits[measureIdStr] = {
            ...newLabelAndUnits[measureIdStr],
            value: mapResultsMeasures[measureIdStr],
          };
        }
      });

      return {
        ...marker,
        labelAndUnits: newLabelAndUnits,
      };
    });
  markersWithMeasures.forEach((marker) => {
    data.push({
      type: 'scattermapbox',
      lon: [marker.location.lon],
      lat: [marker.location.lat],
      mode: 'markers',
      name: marker.markerName,
      marker: {
        size: 20,
        color: marker.color,
      },
      text: [
        marker.markerName,
        ...marker.assetMeasures.map(({ measureId }) => {
          const idStr = measureId.toString();
          const label = marker.labelAndUnits[idStr]?.label || idStr;
          const val = marker.labelAndUnits[idStr]?.value || '0';
          return `${label}: ${val}`;
        }),
      ].join('<br>'),
      value: counter++,
    });
  });

  mapLayout.mapbox = {
    style: 'open-street-map',
    center:
      data.length > 0 && !settings.changeMapCenter
        ? // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          //@ts-ignore
          { lat: data[0]?.lat[0], lon: data[0]?.lon[0] }
        : settings.mapCenter,
    zoom: settings.zoomLevel || 10,
  };
  if (isDashboardTemplate) {
    return {
      mapLayout,
      data: settings.markers.map((marker) => {
        return {
          type: 'scattermapbox',
          lon: [marker.location.lon],
          lat: [marker.location.lat],
          mode: 'markers',
          marker: {
            size: 20,
            color: marker.color || 'gray', // Default to 'gray' if no color is provided
          },
          name: marker.selectedTitles.join(','),
          text:
            marker.markerName +
            '<br>' +
            mapResults
              .map((res) =>
                res.measureData.description + ':' + thousandSeparator
                  ? formatNumber(Math.random() * 100)
                  : roundNumber(Math.random() * 100),
              )
              .join('<br>'),
        };
      }),
    };
  }
  return {
    data,
    mapLayout,
  };
};
export const useFetchMapData = (settings: MapWidget) => {
  const [allDataFetched, setAllDataFetched] = useState<{
    data: Data[];
    layout: Partial<Layout>;
    isLoading: boolean;
  }>({
    isLoading: true,
    layout: {}, // Add a comma after the layout property
    data: [],
  });
  const assetTz = useSelector(getAssetTz);
  const router = useRouter();
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const customerId = useSelector(getCustomerId);
  const startDate = useSelector(getGlobalStartDate);
  const endDate = useSelector(getGlobalEndDate);
  const selectedAggBy = settings.aggBy || 0;
  const selectedSamplePeriod = settings.samplePeriod || 0;
  const timeRangeType = useSelector(getGlobalTimeRangeType);
  const dashboardState = useSelector(selectDashboardState);
  const thousandSeparator = useSelector(getThousandSeparator);
  const prevResultsRef = useRef<TrendResult[]>([]);
  const [mapResults, setMapResults] = useState<TrendResult[] | undefined>(undefined);
  const [assetMeasures, setAssetMeasures] = useState<{ assetId: string; measureId: string[] }[]>(
    [],
  );
  const [successAndFailedMeasurements, setSuccessAndFailedMeasurements] = useState<
    AssetMeasurementDetailsWithLastFetchAndSucess[]
  >([]);
  const [selectedTitles, setSelectedTitles] = useState<string[]>([]);
  useEffect(() => {
    if (settings.mode === 'template') return;
    if (!settings.markers?.length) return;

    // Create a dictionary to group measureIds by assetId
    const measureMap: Record<string, string[]> = {};

    settings.markers.forEach((marker) => {
      if (marker.assetMeasures.length > 0) {
        marker.assetMeasures.forEach((am) => {
          // If this assetId hasn't been seen, initialize it
          if (!measureMap[am.assetId]) {
            measureMap[am.assetId] = [];
          }
          // Push this measureId onto the array
          measureMap[am.assetId].push(am.measureId);
        });
      }
    });
    // Transform the dictionary into your desired array of objects
    const newAssetMeasures = Object.entries(measureMap).map(([assetId, measureIds]) => ({
      assetId,
      measureId: measureIds,
    }));

    setAssetMeasures(newAssetMeasures);
  }, [settings.markers]);

  useEffect(() => {
    if (settings.mode === 'template' && settings.markers.length === 0) return;
    const uniqueTitles = settings.markers.reduce<string[]>((acc, marker) => {
      marker.selectedTitles.forEach((title) => {
        if (!acc.includes(title)) {
          acc.push(title);
        }
      });
      return acc;
    }, []);
    setSelectedTitles(uniqueTitles);
  }, [settings.mode, settings.markers]);

  const generateRandomData = (
    numPoints: number,
    startTime: number,
    interval: number,
    minValue: number,
    maxValue: number,
  ): [number, number][] => {
    const data: [number, number][] = [];
    for (let i = 0; i < numPoints; i++) {
      const timestamp = startTime + i * interval;
      const value = Math.random() * (maxValue - minValue) + minValue;
      data.push([timestamp, value]);
    }
    return data;
  };
  const fetchAllAssetMeasures = useCallback(
    async (
      customerId: number,
      assetsWithMeasures: {
        asset_id: number;
        measurement_ids: number[];
      }[],
    ): Promise<measurementsUnitsDTO> => {
      if (!assetsWithMeasures || assetsWithMeasures.length === 0) {
        return {
          items: [],
          total: 0,
        };
      }

      const result = await dispatch(
        measuresApi.endpoints.getMeasuresWithAssetMeasures.initiate({
          customerId,
          data: assetsWithMeasures,
        }),
      );
      const { error, isError, data } = result;
      if (isError || error || !data) {
        console.error('Error fetching measures with asset measures:', error);
        return {
          items: [],
          total: 0,
        };
      }
      return data;
    },
    [customerId, dispatch],
  );
  useEffect(() => {
    const fetchData = async () => {
      setAllDataFetched((prevState) => ({ ...prevState, isLoading: true }));
      const assetMeasure = await fetchAllAssetMeasures(
        customerId,
        assetMeasures.map((am) => ({
          asset_id: Number(am.assetId),
          measurement_ids: am.measureId.map((id) => Number(id)),
        })),
      );
      if (!assetMeasure || !assetMeasure.items || assetMeasure.items.length === 0) {
        setAllDataFetched((prevState) => ({
          ...prevState,
          isLoading: false,
          data: [],
          layout: {},
        }));
        setMapResults([]);
        return;
      }
      const allPromisesData: MeasuresData[] = await Promise.all(
        assetMeasure.items.map(async (measureData) => {
          return {
            isLoading: false,
            isError: false,
            error: '',
            lastFetchTime: Date.now(),
            measureData: {
              ...measureData,
              id: measureData.id,
              measurementId: measureData.measurement_id,
              metricId: measureData.metricId ?? null, // FIXED: use correct property name
              tag: measureData.tag ?? '', // FIXED: use correct property name
            },
            unitOfMeasures: measureData.unitOfMeasure ? [measureData.unitOfMeasure] : [],
            tsData: {
              tag: measureData.id,
              period: '',
              'ts,val': [],
              tag_meta: { uom: measureData.unitOfMeasure?.name ?? '' },
            },
          } as MeasuresData;
        }),
      );
      const measureIDs = allPromisesData.map((measure) => measure.measureData.measurementId);
      const { data: tsData, isSuccess: isTsSuccess } = await dispatch(
        timeseriesApi.endpoints.getMultiMeasurementSeries.initiate({
          customerId,
          measId: measureIDs.join(','),
          start: settings.overrideGlobalSettings ? settings.startDate : startDate,
          end: settings.overrideGlobalSettings ? settings.endDate : endDate,
          agg: AggByOptions[selectedAggBy].serverValue,
          agg_period: settings.globalSamplePeriod
            ? SamplePeriodOptions[selectedSamplePeriod].serverValue
            : SamplePeriodOptions[dashboardState.topPanel.samplePeriod].serverValue,
          timeRangeType: settings.overrideGlobalSettings ? settings.timeRange : timeRangeType,
          assetTz: settings.overrideAssetTz ? settings.overrideAssetTzValue : assetTz,
        }),
      );

      if (!isTsSuccess || !tsData) {
        allPromisesData.forEach((result) => {
          result.isLoading = false;
          result.isError = true;
          result.error = 'Error fetching timeseries data';
          result.tsData = {
            tag: result.measureData.id,
            period: '',
            'ts,val': [],
            tag_meta: { uom: result.unitOfMeasures[0]?.name || '' },
            error: 'Error fetching timeseries data',
          };
        });
        setMapResults(allPromisesData);
        setAllDataFetched((prevState) => ({ ...prevState, isLoading: false }));
        return;
      }

      allPromisesData.forEach((result) => {
        const seriesData = tsData[result.measureData?.measurementId];
        if (seriesData?.error || seriesData?.['ts,val'] === undefined) {
          result.isLoading = false;
          result.isError = true;
          // result.lastFetchTime = Date.now();
          result.error = seriesData?.error || 'No data available';
        } else {
          result.isLoading = false;
          result.isError = false;
          result.lastFetchTime = Date.now();
          result.tsData = seriesData;
        }
      });
      const promisedResults = allPromisesData;
      const updatedSuccessFailedMeasures: AssetMeasurementDetailsWithLastFetchAndSucess[] = [];

      (promisedResults || []).forEach((res) => {
        if (!res.isError && res.tsData) {
          updatedSuccessFailedMeasures.push({
            ...res.measureData,
            lastFetchTime: res.lastFetchTime,
            isSuccess: !res.isError,
          });
        } else {
          const existing = successAndFailedMeasurements.find(
            (r) => r.measurementId === res?.measureData?.measurementId,
          );

          if (existing) {
            updatedSuccessFailedMeasures.push({
              ...existing,
              lastFetchTime: existing.lastFetchTime,
              isSuccess: !res.isError,
            });
          } else {
            updatedSuccessFailedMeasures.push({
              ...res.measureData,
              lastFetchTime: res.lastFetchTime,
              isSuccess: !res.isError,
            }); // fallback to current failed result
          }
        }
      });
      setSuccessAndFailedMeasurements(updatedSuccessFailedMeasures);
      const updated: TrendResult[] = [];
      (promisedResults || []).forEach((result) => {
        if (!result.isError && result.tsData) {
          updated.push(result);
        } else {
          const existing = prevResultsRef.current.find(
            (r) => r.measureData.measurementId === result?.measureData?.measurementId,
          );
          if (existing) {
            updated.push({
              ...existing,
              lastFetchTime: result.lastFetchTime,
              isError: result.isError,
              error: result.error,
              tsData: {
                ...existing.tsData,
                error: result.error,
              },
            });
          } else {
            updated.push(result);
          }
        }
      });
      setMapResults(promisedResults);
    };
    if (settings.mode === 'dashboard' && router.pathname !== '/dashboard-template') {
      fetchData();
    } else if (settings.mode === 'template' && router.pathname === '/dashboard-template') {
      const mockMapResults: TrendResult[] = selectedTitles.map(() => ({
        tsData: {
          tag: 18578,
          period: '1min',
          'ts,val': generateRandomData(24, Date.now(), 60000, 20, 100),
          error: '',
          tag_meta: { uom: 'volts' },
        },
        lastFetchTime: Date.now(),
        isError: false,
        error: '',
        measureData: {
          id: 11284,
          tag: 'Mock Data',
          dataTypeId: 2,
          measurementId: 18578,
          typeId: 23,
          description: 'PHASEB:VOLTAGE',
          locationId: null,
          unitOfMeasureId: 70,
          meterFactor: null,
          datasourceId: null,
          valueTypeId: 1,
        },
        unitOfMeasures: [
          { id: 115, name: '%' },
          { id: 69, name: 'mV' },
          { id: 70, name: 'volts' },
        ],
      }));
      setMapResults(mockMapResults);
    }
  }, [
    settings.zoomLevel,
    settings.mapCenter,
    settings.changeMapCenter,
    settings.markers,
    selectedAggBy,
    settings.globalSamplePeriod,
    selectedSamplePeriod,
    settings.overrideGlobalSettings,
    settings.startDate,
    settings.endDate,
    assetTz,
    settings.overrideAssetTz,
    settings.overrideAssetTzValue,
    assetMeasures,
  ]);

  useEffect(() => {
    if (mapResults) {
      const { data, mapLayout } = trensFormMapData(
        mapResults,
        settings,
        router.pathname === '/dashboard-template',
        thousandSeparator,
      );
      setAllDataFetched({
        ...allDataFetched,
        data: data,
        layout: mapLayout,
        isLoading: false,
      });
    }
  }, [
    router.pathname,
    mapResults,
    settings.zoomLevel,
    thousandSeparator,
    settings.mapCenter,
    settings.changeMapCenter,
    settings.markers,
    selectedAggBy,
    selectedSamplePeriod,
    settings.globalSamplePeriod,
    settings.overrideGlobalSettings,
    settings.startDate,
    settings.endDate,
    settings.overrideAssetTz,
    settings.overrideAssetTzValue,
    assetMeasures,
  ]);
  return { ...allDataFetched, successAndFailedMeasurements };
};
