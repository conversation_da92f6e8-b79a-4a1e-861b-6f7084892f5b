import { useRouter } from 'next/router';
import { Data, Layout } from 'plotly.js';
import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { Datasource, UnitOfMeasure } from '~/measurements/domain/types';
import { useGetAllDatasourcesQuery } from '~/redux/api/measuresApi';
import { getAssetTz } from '~/redux/selectors/topPanleSelectors';
import { getDateTimeFormatForChart, getThousandSeparator } from '~/redux/selectors/userPreferences';
import { AssetMeasurementDetails } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchTime,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { ChartMeasureSetting, MeasureColorPair, ScatterChartWidget } from '~/types/widgets';
import {
  calculateSumAndDelta,
  calulationXaxisValue,
  calulationYaxisValue,
  formatMetricLabel,
  formatMetricTag,
  formatNumber,
  getIsMeasureTimeVaringFactor,
  roundNumber,
  showMeanValue,
  showMinMaxAvg,
  showThresholdValue,
} from '~/utils/utils';
import { useGetMeasuresTsData } from './useGetMeasuresTsData';

type Stats = {
  min: string;
  max: string;
  avg: string;
  total: string;
  unit: UnitOfMeasure | undefined;
};

type ChartData = {
  data: Data[];
  removedResults: AssetMeasurementDetailsWithLastFetchTime[];
  layout: Partial<Layout>;
  stats: Stats;
  result: {
    delta: string | undefined;
    sum: string | undefined;
  };
};

type TrendResult = {
  isError: boolean;
  lastFetchTime: number;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
  error?: string;
  // partial_full_error?: 'partial' | 'full';
};

type SingleChartData = {
  layout: Partial<Layout>;
  stats: Stats;
};

function getChartDataForSingleTrace(
  filteredResults: TrendResult[],
  showThreshold: boolean,
  treshdold: {
    thresholdName: string;
    thresholdValue: number;
    thresholdColor: string;
    thresholdStyle: string;
  },
  minMaxAvg: {
    min: {
      show: boolean;
    };
    max: {
      show: boolean;
    };
    avg: {
      show: boolean;
    };
  },
  thousandSeparator: boolean,
): SingleChartData {
  const singleSeries = filteredResults[0];
  const { 'ts,val': values } = singleSeries?.tsData || {};

  const unit = singleSeries.unitOfMeasures.filter(
    (data) => data.id == singleSeries.measureData.unitOfMeasureId,
  )[0];
  if (!values) {
    return { layout: {}, stats: { min: '0', max: '0', avg: '0', total: '0', unit: undefined } };
  }
  const yValues = values.map((value) => value[1]);
  const max = thousandSeparator
    ? formatNumber(Math.max(...yValues))
    : roundNumber(Math.max(...yValues));
  const min = thousandSeparator
    ? formatNumber(Math.min(...yValues))
    : roundNumber(Math.min(...yValues));
  const tempTotal = yValues.reduce((a, b) => a + b, 0);
  const total = tempTotal.toFixed(2);
  const avg = (tempTotal / yValues.length).toFixed(2);
  const layout: Partial<Layout> = showMinMaxAvg(
    {
      layout: {},
    } as Partial<Layout>,
    minMaxAvg,
    min,
    max,
    avg,
  );
  return { layout, stats: { min, max, avg, total, unit } };
}

function transformScatterDataForPlotly(
  results: TrendResult[],
  dbMeasureIdToSetting: Record<string, ChartMeasureSetting>,
  dbMeasureIdToAnnotation: Record<string, boolean>,
  selectedSamplePeriod: number,
  selectedTitles: string[],
  selectedSparkTitle: string | undefined,
  showStacked: boolean,
  showArea: boolean,
  titleInfo: {
    value: string;
    isVisible: boolean;
    color: string;
    fontSize?: number;
    fontWeight?: string;
  },
  barColors: MeasureColorPair[],
  overrideGlobalBarColor: boolean,
  legendY: number,
  showThreshold: boolean,
  treshdold: {
    thresholdName: string;
    thresholdValue: number;
    thresholdColor: string;
    thresholdStyle: string;
  },
  showSparkLine: boolean,
  min: {
    show: boolean;
  },
  max: {
    show: boolean;
  },
  avg: {
    show: boolean;
  },
  dateFormats: string,
  useAssetTz: boolean,
  forecastedResults: TrendResult[] | undefined,
  state: ScatterChartWidget,
  dataSource: Datasource[],
  isDashboardTemplate: boolean,
  thousandSeparator: boolean,
): ChartData {
  const traces: Data[] = [];
  const layout: Partial<Layout> = {
    showlegend: true,
    // title: numberOfCharts == 1 ? 'Trends' : formatMetricLabel(results[0]?.measureData.tag),
    title: titleInfo?.isVisible ? titleInfo?.value : undefined,
    // : results.map((res) => formatMetricLabel(res.measureData.tag)).join(' Vs. '),
    titlefont: titleInfo.isVisible
      ? {
          size: titleInfo.fontSize,
          color: titleInfo.color,
        }
      : undefined,
    annotations: [],
    grid: {
      rows: 2,
      columns: 1,
      pattern: 'independent',
      //eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      subplots: [['xy'], ['xy3']],
      // pattern: 'coupled'
    },
    yaxis: {
      // title: 'Unit Values',
      side: 'left',
    },
    yaxis2: {
      // title: 'Unit Values',
      side: 'right',
      overlaying: 'y',
    },
    yaxis3: {
      // title: 'Unit Values',
      // side: 'right',
      // overlaying: 'y',
    },
  };

  let chartNumber = 1;
  const filteredResults: TrendResult[] = [];
  const removedResults: TrendResult[] = [];
  if (isDashboardTemplate) {
    filteredResults.push(...results);
  } else {
    removedResults.push(
      ...results.filter(
        (result) =>
          !result ||
          result.error ||
          result.isError ||
          !result.tsData ||
          (!showStacked &&
            (selectedTitles.length === 0 ||
              !selectedTitles.includes(result.measureData.id?.toString()))),
      ),
    );

    filteredResults.push(
      ...results.filter(
        (result) =>
          result &&
          result.tsData &&
          (showStacked ||
            (selectedTitles.length > 0 &&
              selectedTitles.includes(result.measureData.id?.toString()))),
      ),
    );
  }

  // Legend Y-axis position logic
  let dynamicLegendY: number | undefined = undefined;

  // Manual override from user
  if (state.legendY) {
    const legendCount = filteredResults.length;

    if (legendCount <= 5) {
      dynamicLegendY = -0.4;
    } else if (legendCount <= 8) {
      dynamicLegendY = -0.6;
    } else if (legendCount <= 10) {
      dynamicLegendY = -0.75;
    } else {
      dynamicLegendY = -0.8; // Optional: adjust if needed for large counts
    }
  }

  // Apply the final legend position if defined
  if (dynamicLegendY !== undefined) {
    layout.legend = {
      x: 0,
      y: dynamicLegendY,
      xanchor: 'left',
      yanchor: 'top',
      orientation: 'h',
    };
  }
  const filteredForecatResults = forecastedResults?.filter((result) => result && result.tsData);
  let singleChartData: SingleChartData = {
    stats: { avg: '', max: '', min: '', total: '', unit: undefined },
    layout: {
      annotations: [],
      shapes: [],
    },
  };
  const yData: number[] = [];
  for (let i = 0; i < filteredResults.length; i++) {
    const result = filteredResults[i];

    if (result && result.tsData) {
      const seriesData = result.tsData;
      const measureData = result.measureData;
      const unitOfMeasures = result.unitOfMeasures;
      const values = seriesData['ts,val'];
      if (!values) continue;
      const x = calulationXaxisValue(seriesData, useAssetTz);
      const y = calulationYaxisValue(seriesData);

      const unitsOfMeasure = unitOfMeasures.find(
        (data) => data.id === measureData.unitOfMeasureId,
      ) || { name: '', id: 0 };
      const title = formatMetricLabel(measureData.tag);
      const areaChartIsSpark =
        showStacked && result.measureData.id?.toString() === selectedSparkTitle;

      let yAxis = 'y';
      if (areaChartIsSpark && !showSparkLine) {
        yAxis = 'y3';
        layout.grid = {
          ...layout.grid,
          rows: 2,
        };
      } else if (dbMeasureIdToSetting[result.measureData.id]?.yAxisSide === 'right') {
        yAxis = 'y2';
        layout.grid = {
          ...layout.grid,
          rows: 1,
        };
      } else {
        yAxis = 'y';
        layout.grid = {
          ...layout.grid,
          rows: 1,
        };
      }

      const checkMesurementIsAlreadyExists = traces.find(
        (item) => item.name === `${formatMetricTag(measureData.tag)} (${unitsOfMeasure.name})`,
      );
      yData.push(...y);
      traces.push({
        type: 'scatter',
        fill:
          !checkMesurementIsAlreadyExists && areaChartIsSpark
            ? 'toself'
            : showArea && !showSparkLine
            ? 'tozeroy'
            : 'none',
        x: x.length > 0 ? x : [new Date().toISOString()],
        y:
          y.length > 0
            ? y.map((value) => (thousandSeparator ? formatNumber(value) : roundNumber(value)))
            : [['']],
        // y.map((value) => (thousandSeparator ? formatNumber(value) : roundNumber(value))),
        customdata:
          !isDashboardTemplate &&
          dbMeasureIdToAnnotation &&
          dbMeasureIdToAnnotation[measureData.id.toString()]
            ? [measureData.measurementId.toString()] // Only include the id as a string if the condition is true
            : undefined,
        ...(overrideGlobalBarColor
          ? {
              marker: {
                color: barColors?.find((item) => item.measureId === measureData.id.toString())
                  ?.color,
              },
            }
          : undefined),
        ids: [measureData.id.toString()],
        hovertemplate: `${title}: %{y} ${unitsOfMeasure.name}<br>Time: %{x}<extra></extra>'`,
        name: `${formatMetricTag(measureData.tag)} (${unitsOfMeasure.name})`,
        yaxis: checkMesurementIsAlreadyExists && areaChartIsSpark ? 'y' : yAxis,
        mode: 'lines',
        line: {
          shape: getIsMeasureTimeVaringFactor(dataSource, measureData) ? 'hv' : undefined,
        },
      });
      chartNumber++;
    }
  }
  if (
    filteredForecatResults &&
    state.showForecast &&
    selectedTitles.length === 1 &&
    !showSparkLine
    //  &&
    // state.selectedSparkTitle !== ''
  ) {
    for (let i = 0; i < filteredForecatResults.length; i++) {
      const result = filteredForecatResults[i];

      if (result && result.tsData) {
        const seriesData = result.tsData;
        const measureData = result.measureData;
        if (
          !isDashboardTemplate &&
          selectedTitles.length > 0 &&
          selectedTitles.includes(measureData.id?.toString()) === false
        )
          continue;
        const unitOfMeasures = result.unitOfMeasures;
        const values = seriesData['ts,val'];
        if (!values) continue;
        const x = calulationXaxisValue(seriesData, useAssetTz);
        const y = calulationYaxisValue(seriesData);
        const unitsOfMeasure = unitOfMeasures.find(
          (data) => data.id === measureData.unitOfMeasureId,
        ) || { name: '', id: 0 };

        const title = formatMetricLabel(measureData.tag);

        const areaChartIsSpark =
          showStacked && result.measureData.id?.toString() === selectedSparkTitle;

        let yAxis = 'y';
        if (areaChartIsSpark) {
          yAxis = 'y3';
          layout.grid = {
            ...layout.grid,
            rows: 2,
          };
        } else if (dbMeasureIdToSetting[result.measureData.id]?.yAxisSide === 'right') {
          yAxis = 'y2';
          layout.grid = {
            ...layout.grid,
            rows: 1,
          };
        } else {
          yAxis = 'y';
          layout.grid = {
            ...layout.grid,
            rows: 1,
          };
        }
        traces.push({
          type: 'scatter',
          fill: 'none',
          x: x.length > 0 ? x : [''],
          y:
            y.length > 0
              ? y.map((value) => (thousandSeparator ? formatNumber(value) : roundNumber(value)))
              : [''],
          // y.map((value) => (thousandSeparator ? formatNumber(value) : roundNumber(value))),
          marker: { color: state.forecastColor ?? '#000000' },
          hovertemplate: `${title}: %{y} ${unitsOfMeasure.name}<br>Time: %{x}<extra></extra>'`,
          name: `${formatMetricTag(measureData.tag)} (${unitsOfMeasure.name}) - Forecast`,
          yaxis: yAxis,
          mode: 'lines',
          line: {
            dash: 'dot',
          },
        });

        chartNumber++;
      }
    }
  }
  if (selectedTitles.length === 1 && filteredResults.length === 1) {
    singleChartData = getChartDataForSingleTrace(
      filteredResults,
      showThreshold,
      treshdold,
      {
        min,
        max,
        avg,
      },
      thousandSeparator,
    );
  }
  if (
    state.showMean &&
    selectedTitles.length === 1 &&
    filteredResults.length > 0 &&
    state.showForecast &&
    !showSparkLine
  ) {
    singleChartData.layout = showMeanValue(singleChartData, traces, {
      meanColor: state.meanColor,
      meanStyle: state.meanStyle,
      meanName: state.meanName,
    }).layout;
  }
  const result: {
    delta: number | undefined;
    sum: number | undefined;
  } = {
    delta: undefined,
    sum: undefined,
  };
  let unit = '';
  if (selectedTitles.length === 1 && filteredResults && filteredResults.length === 1) {
    const unitsOfMeasure = filteredResults[0].unitOfMeasures.find(
      (data) => data.id === filteredResults[0].measureData.unitOfMeasureId,
    ) || { name: '', id: 0 };
    unit = unitsOfMeasure.name;
    calculateSumAndDelta(
      unitsOfMeasure,
      {
        showDelta: state.showDelta ?? false,
        deltaLabel: state.deltaLabel ?? 'Delta',
        showSum: state.showSum ?? false,
        sumLabel: state.sumLabel ?? 'Sum',
      },
      traces,
      layout,
      yData,
    );
  }
  if (showThreshold) {
    const threshold = showThresholdValue(singleChartData, treshdold);
    singleChartData.layout = threshold.layout;
  }
  if (showSparkLine) {
    layout.showlegend = false;
    layout.annotations = [];
    layout.shapes = [];
    singleChartData.layout.showlegend = false;
    singleChartData.layout.annotations = [];
    singleChartData.layout.shapes = [];
    layout.xaxis = {
      visible: false,
      showline: false,
      showgrid: false,
      showticklabels: false,
      zeroline: false,
      autotick: false,
    };
    singleChartData.layout.xaxis = {
      visible: false,
      showline: false,
      showgrid: false,
      showticklabels: false,
      zeroline: false,
      autotick: false,
    };
    singleChartData.layout.yaxis = {
      visible: false,
      showline: false,
      showgrid: false,
      showticklabels: false,
      zeroline: false,
      autotick: false,
    };
    layout.yaxis = {
      visible: false,
      showline: false,
      showgrid: false,
      showticklabels: false,
      zeroline: false,
      autotick: false,
    };
  }
  return {
    data: traces,
    removedResults: removedResults.map((res) => {
      return {
        ...res.measureData,
        lastFetchTime: res.lastFetchTime,
        partialFailed: removedResults.length !== results.length,
      };
    }),
    layout: { ...layout, ...singleChartData.layout },
    stats: { ...singleChartData.stats },
    result: { ...result, delta: result.delta + ' ' + unit, sum: result.sum + ' ' + unit },
  };
}

export function useFetchScatterData(widgetId: string, state: ScatterChartWidget) {
  const selectedSamplePeriod = state.samplePeriod || 0;
  const showArea = state.showArea;
  const router = useRouter();
  const {
    dbMeasureIdToSetting,
    dbMeasureIdToAnnotation,
    selectedTitles,
    selectedSparkTitle,
    showStacked,
  } = state;
  const [selectedSparkMeasure, setSeletedSparkMeasure] = useState<string>('');
  const useAssetTz = useSelector(getAssetTz);
  const thousandSeparator = useSelector(getThousandSeparator);
  const dateFormats = useSelector(getDateTimeFormatForChart);
  const [forecastedResults, setForcastedResult] = useState<TrendResult[] | undefined>(undefined);
  const [allDataFetched, setAllDataFetched] = useState({
    removedResults: [] as AssetMeasurementDetailsWithLastFetchTime[],
    successfulResults: [] as AssetMeasurementDetailsWithLastFetchTime[],
    chartData: [] as Data[],
    isLoading: true,
    stats: {
      min: '',
      max: '',
      avg: '',
    } as Stats,
    layoutData: {
      showlegend: true,
      title: 'Chart',
    } as Partial<Layout>,
    results: {
      delta: '',
      sum: '',
    },
  });
  const prevResultsRef = useRef<TrendResult[]>([]);
  const { data: datasourceList } = useGetAllDatasourcesQuery({});
  const [chartResults, setChartResults] = useState<TrendResult[] | undefined>(undefined);
  const [selectedMesures, setSelectedMesure] = useState<string[]>([]);
  useEffect(() => {
    if (state.mode === 'dashboard') {
      const validMeasures = state.assetMeasure
        .flatMap((assetMeas) => assetMeas.measureId)
        .filter((measure) => measure.trim() !== '');
      if (
        state.showStacked &&
        state.selectedSparkMeasure?.assetId?.trim() !== '' &&
        state.selectedSparkMeasure?.measureId?.trim() !== ''
      ) {
        const measureId = state.selectedSparkMeasure?.measureId ?? '';
        if (measureId.trim() !== '') {
          validMeasures.push(measureId);
          setSeletedSparkMeasure(measureId);
        }
      } else {
        setSeletedSparkMeasure('');
      }
      setSelectedMesure(validMeasures);
    }
    if (state.mode === 'template') {
      const selectedMesures = selectedTitles.map((title) => title);
      setSelectedMesure([...selectedMesures]);
    }
  }, [state.assetMeasure, state.selectedSparkMeasure, state.showStacked, selectedTitles]);

  const {
    data: measureData,
    isLoading: isMeasureDataLoading,
    forcastedData,
    successAndFailedMeasurements,
    isError,
  } = useGetMeasuresTsData({
    selectedTitles: selectedMesures,
    dataFetchSettings: state,
    assetMeasure:
      state.selectedSparkMeasure && state.showStacked
        ? [
            ...state.assetMeasure,
            {
              assetId: state.selectedSparkMeasure.assetId,
              measureId: [state.selectedSparkMeasure.measureId],
            },
          ]
        : state.assetMeasure,
  });
  useEffect(() => {
    if (isMeasureDataLoading) {
      setChartResults(undefined);
      setForcastedResult(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isLoading: true,
          isError: false,
        };
      });
    } else if (measureData) {
      const updated: TrendResult[] = [];
      (measureData || []).forEach((result) => {
        if (!result.isError && result.tsData) {
          updated.push(result);
        } else {
          const existing = prevResultsRef.current.find(
            (r) => r.measureData.measurementId === result?.measureData?.measurementId,
          );
          if (existing) {
            updated.push({
              ...existing,
              lastFetchTime: existing.lastFetchTime,
              isError: result.isError,
              error: result.error,
              tsData: {
                ...existing.tsData,
                error: result.error,
              },
            });
          } else {
            updated.push(result);
          }
        }
      });
      prevResultsRef.current = updated; // Keep latest working state
      setChartResults(updated as TrendResult[]);
      const forecastFilter = forcastedData?.filter(({ isError }) => !isError);
      setForcastedResult(forecastFilter as TrendResult[]);
    } else if (isError) {
      setChartResults(undefined);
      setForcastedResult(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isError: true,
          isLoading: false,
        };
      });
    }
  }, [isMeasureDataLoading, measureData, forcastedData, isError]);

  useEffect(() => {
    if (chartResults) {
      const chartData = transformScatterDataForPlotly(
        chartResults,
        dbMeasureIdToSetting,
        dbMeasureIdToAnnotation,
        selectedSamplePeriod,
        selectedMesures,
        // selectedSparkTitle,
        selectedSparkMeasure,
        showStacked ?? false,
        showArea ?? false,
        state.title,
        state.barColors,
        state.overrideGlobalBarColor,
        state.legendY,
        state.showThreshold,
        state.treshdold,
        state.showSparkLine ?? false,
        state.min,
        state.max,
        state.avg,
        dateFormats,
        useAssetTz,
        forecastedResults,
        state,
        datasourceList?.items ?? [],
        router.pathname === '/dashboard-template',
        thousandSeparator,
      );
      setAllDataFetched({
        removedResults: chartData.removedResults,
        successfulResults: prevResultsRef.current
          .filter((res) => !res.isError)
          // .filter((res) => res.tsData)
          .map((res) => {
            return {
              ...res.measureData,
              lastFetchTime: res.lastFetchTime,
              partialFailed: false,
            };
          }),
        chartData: chartData.data,
        isLoading: false,
        layoutData: chartData.layout,
        stats: chartData.stats,
        results: {
          delta: chartData.result.delta ?? '',
          sum: chartData.result.sum ?? '',
        },
      });
    }
  }, [
    selectedSparkMeasure,
    state.showSparkLine,
    state.showThreshold,
    state.treshdold,
    chartResults,
    dbMeasureIdToAnnotation,
    state.title.value,
    state.title.isVisible,
    state.title.fontSize,
    state.title.fontWeight,
    state.title.color,
    state.showArea,
    selectedSamplePeriod,
    selectedTitles,
    selectedSparkTitle,
    showStacked,
    dbMeasureIdToSetting,
    state.overrideGlobalSettings,
    state.startDate,
    state.endDate,
    state.timeRange,
    state.barColors,
    state.overrideGlobalBarColor,
    state.globalSamplePeriod,
    state.legendY,
    showArea,
    state.title,
    state.min,
    state.max,
    state.avg,
    dateFormats,
    forecastedResults,
    state.period,
    state.showForecast,
    state.forecastColor,
    state.showMean,
    state.meanColor,
    state.meanName,
    state.meanStyle,
    state.showDelta,
    state.deltaLabel,
    state.showSum,
    state.sumLabel,
    datasourceList,
    router.pathname,
    thousandSeparator,
  ]);
  return { ...allDataFetched, successAndFailedMeasurements };
}
