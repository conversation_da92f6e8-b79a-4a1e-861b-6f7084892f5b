import { useEffect, useRef, useState } from 'react';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { AssetMeasurementDetails } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchTime,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { StatsWidget } from '~/types/widgets';
import { roundNumber } from '~/utils/utils';
import { useGetMeasuresTsData } from './useGetMeasuresTsData';

type Stats = {
  min: string;
  max: string;
  avg: string;
  sum: string;
  total: string;
  unit: UnitOfMeasure | undefined;
  current: string;
  data?: AssetMeasurementDetailsWithLastFetchTime;
};

type TrendResult = {
  isError: boolean;
  error?: string;
  tsData: SingleScatterTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
  lastFetchTime: number;
};

function getChartDataForSingleTrace(singleSeries: TrendResult | null): Stats {
  const measureData = singleSeries?.measureData;
  const lastFetchTime = singleSeries?.lastFetchTime || 0;
  if (!singleSeries || !singleSeries.tsData) {
    return {
      data: {
        ...measureData,
        lastFetchTime,
        partialFailed: false,
      } as AssetMeasurementDetailsWithLastFetchTime,
      min: '',
      max: '',
      avg: '',
      sum: '',
      total: '',
      unit: undefined,
      current: '',
    };
  }
  const { 'ts,val': values, error } = singleSeries.tsData;
  if (error || !values) {
    return {
      data: {
        ...measureData,
        lastFetchTime,
        partialFailed: false,
      } as AssetMeasurementDetailsWithLastFetchTime,
      min: '',
      max: '',
      avg: '',
      sum: '',
      total: '',
      unit: undefined,
      current: '',
    };
  }
  const unit = singleSeries.unitOfMeasures.filter(
    (data) => data.id == singleSeries.measureData.unitOfMeasureId,
  )[0];
  const yValues = values.map((value) => value[1]);
  const max = roundNumber(Math.max(...yValues));
  const min = roundNumber(Math.min(...yValues));
  const tempTotal = yValues.reduce((a, b) => a + b, 0);
  const total = roundNumber(tempTotal);
  const avg = roundNumber(tempTotal / yValues.length);
  const sum = yValues.length > 0 ? roundNumber(yValues.reduce((a, b) => a + b, 0)) : '-';
  const current =
    yValues.length > 0 ? roundNumber(yValues.slice(-1).pop() ?? Infinity) || '-' : '-'; // Get the last value without using indexing and square braces
  return {
    min,
    max,
    avg,
    sum,
    total,
    unit,
    current,
  };
}

export function useFetchStatsData(widgetId: string, state: StatsWidget) {
  const selectedSamplePeriod = state.samplePeriod || 0;
  const [statsResults, setStatsResults] = useState<TrendResult | undefined>(undefined);
  const [selectedTitles, setSelectedTitles] = useState<string[]>([]);

  useEffect(() => {
    if (
      state.assetMeasure?.assetId !== '' &&
      state.assetMeasure?.measureId.some((measure) => measure !== '') &&
      state.mode === 'dashboard'
    ) {
      setSelectedTitles(state.assetMeasure.measureId);
    }
    if (state.mode === 'template') {
      if (state.mode === 'template') {
        if (state.selectedDbMeasureId !== '') {
          setSelectedTitles([state.selectedDbMeasureId]);
        } else {
          setSelectedTitles([]);
        }
      }
    }
  }, [state.assetMeasure, state.selectedDbMeasureId, state.mode]);
  const [allDataFetched, setAllDataFetched] = useState<{
    isLoading: boolean;
    stats: Stats | null;
    isError: boolean;
  }>({
    isLoading: true,
    stats: null,
    isError: false,
  });
  const {
    data: measureData,
    isLoading: isMeasureDataLoading,
    successAndFailedMeasurements,
    isError,
  } = useGetMeasuresTsData({
    selectedTitles,
    dataFetchSettings: state,
    assetMeasure: [state.assetMeasure],
  });
  useEffect(() => {
    if (isMeasureDataLoading) {
      setStatsResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isLoading: true,
          isError: false,
        };
      });
    } else if (measureData) {
      setStatsResults(measureData.length > 0 ? (measureData[0] as TrendResult) : undefined);
    } else if (isError) {
      setStatsResults(undefined);
      setAllDataFetched((prev) => {
        return {
          ...prev,
          isError: true,
          isLoading: false,
        };
      });
    }
  }, [isMeasureDataLoading, measureData, isError]);

  useEffect(() => {
    if (statsResults) {
      const stats = getChartDataForSingleTrace(statsResults);
      setAllDataFetched({
        isLoading: false,
        stats: stats,
        isError: false,
      });
    }
  }, [
    statsResults,
    state.min,
    state.max,
    state.avg,
    state.delta,
    state.sum,
    state.total,
    state.overrideGlobalSettings,
    state.startDate,
    state.endDate,
    state.timeRange,
    state.globalSamplePeriod,
    selectedSamplePeriod,
  ]);

  return { ...allDataFetched, successAndFailedMeasurements };
}
