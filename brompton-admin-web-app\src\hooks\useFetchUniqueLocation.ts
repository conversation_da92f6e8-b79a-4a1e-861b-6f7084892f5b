import { ThunkDispatch } from '@reduxjs/toolkit';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { LocationDTO } from '~/measurements/domain/types';
import { measuresApi } from '~/redux/api/measuresApi';
import { RootState } from '~/redux/store';
import { MapWidget } from '~/types/widgets';

type useFetchUniqueLocationProps = {
  currentSettings: number;
  settings: MapWidget;
};
export const useFetchUniqueLocation = ({
  currentSettings,
  settings,
}: useFetchUniqueLocationProps) => {
  const [location, setLocation] = useState<LocationDTO | null>(null);
  const [isUniqueLocation, setIsUniqueLocation] = useState<boolean>(true);
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const [data, setData] = useState<{
    [key: string]: {
      currLocation: LocationDTO | undefined;
      measureData: string;
    }[];
  }>({});
  const [selectedMeasures, setSelectedMeasures] = useState<string[]>([]);
  useEffect(() => {
    const hasValidAssetMeasure = settings.markers[currentSettings].assetMeasures.some(
      (assetMeas) => assetMeas.assetId.trim() !== '' && assetMeas.measureId.trim() !== '',
    );

    if (hasValidAssetMeasure) {
      const titles = settings.markers[currentSettings].assetMeasures
        .flatMap((assetMeas) => assetMeas.measureId)
        .filter((measure) => measure.trim() !== '');

      setSelectedMeasures(titles);
    } else {
      // Optionally, clear selectedTitles if no valid asset measures
      setSelectedMeasures([]);
    }
  }, [settings.markers[currentSettings].assetMeasures]);

  useEffect(() => {
    if (selectedMeasures.length > 0) {
      const fetchData = async () => {
        const res = selectedMeasures.map(async (dbMeasureId) => {
          return {
            measureId: dbMeasureId,
            promise: await dispatch(
              measuresApi.endpoints?.getAssetMeasureByLocation.initiate({
                measurementId: dbMeasureId,
              }),
            ),
          };
        });
        const results = res.map((result) => {
          return result.then((result) => {
            return {
              currLocation: result.promise.data,
              measureData: result.measureId,
            };
          });
        });
        const data = await Promise.all(results);
        const groupedData = data
          .map((location) => {
            if (location.currLocation === undefined) {
              return {
                ...location,
                currLocation: {
                  latitude: 0,
                  longitude: 0,
                },
              };
            } else {
              return location;
            }
          })
          .reduce(
            (
              groups: {
                [key: string]: {
                  currLocation: LocationDTO | undefined;
                  measureData: string;
                }[];
              },
              item: {
                currLocation: LocationDTO | undefined;
                measureData: string;
              },
            ) => {
              const locationKey = JSON.stringify(item.currLocation);
              if (!groups[locationKey]) {
                groups[locationKey] = [];
              }
              groups[locationKey].push(item);
              return groups;
            },
            {},
          );
        setData(groupedData);
      };
      (async () => {
        await fetchData();
      })();
    } else {
      setLocation(null);
      setIsUniqueLocation(true);
    }
  }, [selectedMeasures]);
  return { location, isUniqueLocation, data };
};
