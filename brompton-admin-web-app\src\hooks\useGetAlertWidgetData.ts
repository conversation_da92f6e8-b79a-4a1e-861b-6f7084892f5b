import { ThunkDispatch } from '@reduxjs/toolkit';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { EventData } from '~/measurements/domain/types';
import { useGetAllEventsAlertsQuery } from '~/redux/api/alertApi';
import { assetsApi } from '~/redux/api/assetsApi';
import { useGetMeasuresByCustomersMutation } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getGlobalEndDate, getGlobalStartDate } from '~/redux/selectors/widgetSelectors';
import { RootState } from '~/redux/store';
import { Asset } from '~/types/asset';
import { measurementsMetricsDTO } from '~/types/measures';
import { AlertWidget } from '~/types/widgets';

type AlertWidgetDataProps = {
  id: string;
  state: AlertWidget;
};

const getRandomAlerts = (count: number): EventData[] => {
  const assets = [509, 257, 779];
  const measurementTypes = [19, 20, 21, 22, 23];
  const aggOptions = [
    { id: 1, label: 'MAX', value: 'max' },
    { id: 3, label: 'TWA', value: 'twa' },
  ];
  const periods = [
    { id: 4, label: '15min', value: '_15M' },
    { id: 5, label: '1min', value: '_1M' },
    { id: 6, label: '5min', value: '_5M' },
  ];
  const conditions = [
    { id: 1, condition: 'LT' },
    { id: 2, condition: 'GE' },
  ];

  return Array.from({ length: count }, (_, index) => {
    const assetId = assets[Math.floor(Math.random() * assets.length)];
    const measurementId = Math.floor(Math.random() * 10000) + 23000;

    return {
      id: 100 + index + Math.floor(Math.random() * 900),
      assetPath: `/assets/${assetId}`,
      description: 'Random alert description',
      customerId: Math.floor(Math.random() * 50) + 1,
      state: Math.random() < 0.3 ? 'EXCEEDED' : '', // <-- FIXED: always a string
      createdAt: '2024-01-20T00:00:00Z',
      updatedAt: '2024-01-20T00:00:00Z',
      asset: {
        id: assetId,
        tag: `ASSET_${index + 1}`,
        description: 'Random asset description',
        latitude: Number((Math.random() * 180 - 90).toFixed(6)),
        longitude: Number((Math.random() * 360 - 180).toFixed(6)),
        type_id: Math.floor(Math.random() * 100) + 400,
        customer_id: Math.floor(Math.random() * 50) + 1,
        children_ids: [],
        time_zone: null,
      },
      measurementTag: `MEASUREMENT_${index + 1}`,
      measurement: {
        id: measurementId,
        description: 'Random measurement',
        datasource: null,
        meter_factor: null,
        tag: `MEASUREMENT_${index + 1}`,
        metric: null,
        measurementType: measurementTypes[Math.floor(Math.random() * measurementTypes.length)],
      },
      alert_id: {
        id: 239,
        asset: {
          id: 1147,
          tag: 'GreenField',
          latitude: 17.97338,
          longitude: -76.75866,
          description: 'Green Resolution ',
          time_zone: 'America/Jamaica',
          type_id: 469,
          assetTemplate: null,
          assetTemplateId: null,
          unitsGroup: null,
          unitsGroupId: null,
          customer_id: 8,
          parent_ids: [],
          children_ids: [1152, 1225, 1226, 1259, 1313, 1328],
        },
        measurement: {
          id: 29685,
          description: 'WRB 6 March - Black smith',
          writeback: true,
          datasource: null,
          meter_factor: null,
          deletedAt: null,
          type_id: 88,
          unit_of_measure_id: 167,
          data_type_id: 1,
          value_type_id: 4,
        },
        agg: {
          id: 11,
          label: 'None',
          value: 'none',
        },
        period: {
          id: 6,
          label: '5min',
          value: '_5M',
          sort_order: 2,
        },
        thresholdType: 1,
        condition: {
          id: 1,
          condition: 'LT',
        },
        thresholdValue: 28,
        resetDeadband: 1,
        description: 'alert 13 0011',
        customerId: 8,
        enabled: true,
        state: 'EXCEEDED',
        createdAt: '2025-03-13T10:42:08.361Z',
        updatedAt: '2025-03-26T04:39:28.340Z',
        notificationType: null,
        anomalyModel: null,
        anomalyParameter: null,
      },
      asset_id: {
        id: 1147,
        tag: 'GreenField',
        latitude: 17.97338,
        longitude: -76.75866,
        description: 'Green Resolution ',
        time_zone: 'America/Jamaica',
        type_id: 469,
        assetTemplate: null,
        assetTemplateId: null,
        unitsGroup: null,
        unitsGroupId: null,
        customer_id: 8,
        parent_ids: [],
        children_ids: [1152, 1225, 1226, 1259, 1313, 1328],
      },
      measurement_id: {
        id: 29685,
        description: 'WRB 6 March - Black smith',
        writeback: true,
        datasource: null,
        meter_factor: null,
        deletedAt: null,
        type_id: 88,
        unit_of_measure_id: 167,
        data_type_id: 1,
        value_type_id: 4,
      },
      deadband: 1,
      input_value: '26.210333333333335',
      limit: '120',
      timestamp: '2025-05-06T14:30:14.676Z',
      comparator: {
        id: 1,
        condition: 'LT',
      },
      aggregate: {
        id: 11,
        label: 'None',
        value: 'none',
      },
      period: {
        id: 6,
        label: '5min',
        value: '_5M',
        sort_order: 2,
      },
      enabled: Math.random() < 0.5,
      agg: aggOptions[Math.floor(Math.random() * aggOptions.length)],
      thresholdType: { id: 2, threshold: 'ANOMALY' },
      condition: conditions[Math.floor(Math.random() * conditions.length)],
      thresholdValue: Math.floor(Math.random() * 20) + 5,
      resetDeadband: Math.floor(Math.random() * 6) + 5,
      alertUsers:
        Math.random() < 0.5
          ? [
              {
                id: Math.floor(Math.random() * 500),
                user: Math.floor(Math.random() * 100),
                notificationtype: Math.floor(Math.random() * 4),
              },
            ]
          : [],
    };
  });
};

const useGetAlertWidgetData = ({ id, state }: AlertWidgetDataProps) => {
  const activeCustomer = useSelector(getActiveCustomer);
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const [allFetchedAssetIds, setAllFetchedAssetIds] = useState<string[]>([]);
  const [isFetchingAssets, setIsFetchingAssets] = useState(false);
  const [isFetchingMeasures, setIsFetchingMeasures] = useState(false);
  const [data, setData] = useState<{
    isLoading: boolean;
    isError: boolean;
    error: string | null;
    data: EventData[];
  }>({
    isLoading: true,
    error: null,
    isError: false,
    data: [],
  });
  const globalStartDate = useSelector(getGlobalStartDate);
  const globalEndDate = useSelector(getGlobalEndDate);
  const [getMeasuresByCustomer] = useGetMeasuresByCustomersMutation();

  // Memoizing selected assets, measures, asset types, and measurement types to avoid unnecessary renders
  const { selectedAssets, selectedMeasures, selectedAssetTypes, selectedMeasuresTypes } =
    useMemo(() => {
      if (state.mode !== 'dashboard') {
        return {
          selectedAssets: [],
          selectedMeasures: [],
          selectedAssetTypes: [],
          selectedMeasuresTypes: [],
        };
      }

      // Extract Asset & Measurement Data
      const validAssetMeasures = state.assetMeasure.filter(
        (assetMeas) => assetMeas.assetId.trim() !== '',
      );
      const assetIds = validAssetMeasures.map((assetMeas) => assetMeas.assetId);
      const measureTitles = validAssetMeasures.flatMap((assetMeas) => assetMeas.measureId);

      // Extract Asset Type Data
      const validAssetTypeMetrics = state.assetTypes.filter((assetType) => assetType.trim() !== '');
      const assetTypeIds = validAssetTypeMetrics.map((assetType) => assetType);
      return {
        selectedAssets: assetIds.filter((id) => id.trim() !== ''),
        selectedMeasures: measureTitles.filter((m) => m.trim() !== ''),
        selectedAssetTypes: assetTypeIds.filter((id) => id.trim() !== ''),
        selectedMeasuresTypes: state.measurementTypes.filter((id) => id.trim() !== ''),
      };
    }, [
      state.assetMeasure,
      state.assetTypeMetrics,
      state.mode,
      state.measurementTypes,
      state.assetTypes,
    ]);

  const {
    isFetching,
    data: alertsData,
    isError,
  } = useGetAllEventsAlertsQuery(
    {
      start: state.overrideGlobalSettings ? state.startDate : globalStartDate,
      end: state.overrideGlobalSettings ? state.endDate : globalEndDate,
    },
    {
      refetchOnMountOrArgChange: true,
      skip:
        selectedAssets.length === 0 &&
        selectedAssetTypes.length === 0 &&
        selectedMeasuresTypes.length === 0,
    },
  );
  useEffect(() => {
    const fetchAssets = async (parentIds: string[]): Promise<Asset[]> => {
      try {
        if (!parentIds.length) return [];
        setIsFetchingAssets(true); // ✅ Set fetching state
        const { data: assetsData, isError } = await dispatch(
          assetsApi.endpoints.getMultiAsset.initiate(
            {
              customerId: activeCustomer?.id ?? 0,
              ids: parentIds.map(Number),
            },
            {
              forceRefetch: true,
            },
          ),
        );
        setIsFetchingAssets(false); // ✅ Set fetching state
        if (isError || !assetsData) {
          return [];
        }
        const missingChildIds = assetsData
          .filter((asset: Asset) => asset.childrenIds && asset.childrenIds.length > 0)
          .flatMap((asset: Asset) => asset.childrenIds!.map(String))
          .filter((childId) => !parentIds.includes(childId)); // Avoid duplicate fetch

        // If there are children, fetch them recursively
        let nestedChildAssets: Asset[] = [];
        if (missingChildIds.length > 0) {
          nestedChildAssets = await fetchAssets(missingChildIds);
        }
        return [...assetsData, ...nestedChildAssets];
      } catch (error) {
        return [];
      }
    };

    // Function to fetch all assets including children
    const fetchAllAssets = async (): Promise<void> => {
      if ((selectedAssets.length === 0 || !activeCustomer) && selectedMeasures.length > 0) return;
      try {
        const allAssets = await fetchAssets(selectedAssets.map(String));
        setAllFetchedAssetIds(Array.from(new Set(allAssets.map((asset) => asset.id.toString()))));
      } catch (error) {
        setIsFetchingAssets(false); // ✅ Set fetching state
      }
    };
    fetchAllAssets();
  }, [selectedAssets, activeCustomer, dispatch, selectedMeasures]);

  const fetchData = useCallback(async () => {
    if (isFetching || isFetchingAssets) {
      setData((prevState) => ({ ...prevState, isLoading: true }));
      return;
    }

    if (!alertsData) {
      setData((prevState) => ({ ...prevState, isLoading: false }));
      return;
    }

    try {
      let measureResponse: measurementsMetricsDTO | undefined = undefined;
      if (selectedMeasures.length > 0) {
        setIsFetchingMeasures(true);
        measureResponse = await getMeasuresByCustomer({
          customerId: activeCustomer?.id ?? 0,
          measurements: selectedMeasures.map(Number),
        }).unwrap();
        setIsFetchingMeasures(false);
      }
      const measuresData = measureResponse?.items || [];
      const filteredAlerts = alertsData?.items || [];
      let finalFilteredAlerts: EventData[] = [];
      if (selectedMeasuresTypes.length > 0) {
        finalFilteredAlerts = filteredAlerts.filter((alert: EventData) => {
          if (!alert.measurement_id || !alert.measurement_id.type_id) {
            return false; // Ensure alerts without measurementType are excluded
          }
          const alertMeasureTypeId = alert.measurement_id.type_id.toString();
          return selectedMeasuresTypes.includes(alertMeasureTypeId);
        });
      }
      if (selectedAssetTypes.length > 0) {
        const assetTypeAlerts = filteredAlerts.filter((alert: EventData) => {
          if (!alert.asset_id || !alert.asset_id.type_id) {
            return false; // Ensure alert has a valid asset type before filtering
          }
          const alertAssetTypeId = alert.asset_id.type_id.toString();
          return selectedAssetTypes.includes(alertAssetTypeId); // Return boolean for filtering
        });
        finalFilteredAlerts = [...finalFilteredAlerts, ...assetTypeAlerts];
      }
      if (state.assetMeasure.length > 0 && selectedAssets.length > 0) {
        // Filter alerts based on asset ID and its children
        let assetFilteredAlerts = filteredAlerts.filter((alert: EventData) => {
          if (!alert.asset.id) {
            return false; // Ensure alert has a valid asset before filtering
          }
          const alertAssetId = alert.asset.id.toString();

          return state.assetMeasure.some((entry) => entry.assetId === alertAssetId);
        });
        let assetFromChildAlerts: EventData[] = [];
        if (selectedMeasures.length === 0) {
          assetFromChildAlerts = filteredAlerts.filter((alert: EventData) => {
            if (!alert.asset || !alert.asset.id) {
              return false; // Ensure alert has a valid asset before filtering
            }
            const alertAssetId = alert.asset.id.toString();
            return allFetchedAssetIds.includes(alertAssetId);
          });
        }

        if (selectedMeasures.length > 0) {
          assetFilteredAlerts = assetFilteredAlerts.filter((alert: EventData) => {
            if (!alert.measurement || !alert.measurement.id) {
              return false; // Ensure alert has a valid measurement before filtering
            }
            const alertMeasurementId = alert.measurement.id.toString();
            const measureIdList = measuresData.map((measure) => measure.measurement_id.toString());
            return measureIdList.length > 0 ? measureIdList.includes(alertMeasurementId) : true;
          });
        }

        finalFilteredAlerts = Array.from(
          new Map(
            [...finalFilteredAlerts, ...assetFromChildAlerts, ...assetFilteredAlerts].map(
              (alert) => [alert.id, alert],
            ),
          ).values(),
        );
      }

      setData({
        isLoading: false,
        isError: false,
        error: null,
        // data: filteredAlerts,
        // data: finalFilteredAlerts,
        data: [...finalFilteredAlerts].sort((a, b) => {
          const timeA = new Date(a.timestamp).getTime() || 0;
          const timeB = new Date(b.timestamp).getTime() || 0;
          return timeB - timeA;
        }),
      });
    } catch (error) {
      setIsFetchingMeasures(false);
      setData({
        isLoading: false,
        isError: true,
        error: 'Error fetching measurement data',
        data: [],
      });
    }
  }, [
    alertsData,
    isFetching,
    selectedAssets,
    selectedMeasures,
    selectedAssetTypes,
    selectedMeasuresTypes,
    state.assetMeasure,
    allFetchedAssetIds,
    state.assetTypes,
    // state.mode,
  ]);

  // Run fetchData when required
  useEffect(() => {
    if (state.mode === 'dashboard') {
      fetchData();
    }
    if (state.mode === 'template') {
      setData({
        ...data,
        isLoading: false,
        error: null,
        isError: false,
        data: getRandomAlerts(20),
      });
    }
  }, [fetchData]);

  return {
    data: { ...data, isLoading: data.isLoading || isFetchingMeasures || isFetchingAssets },
  };
};

export default useGetAlertWidgetData;
