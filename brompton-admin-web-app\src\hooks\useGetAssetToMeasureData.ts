import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useGetAllBackOfficeAssetTypesQuery, useGetAssetByIdQuery } from '~/redux/api/assetsApi';
import { useGetAllMeasurementsQuery } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getCurrentSelectedNodeId } from '~/redux/selectors/treeSelectors';
import { assetTypePathMapper } from '~/utils/mappers/asset-type-mapper';

const useGetAssetToMeasureData = () => {
  const activeCustomer = useSelector(getActiveCustomer);
  const assetId = useSelector(getCurrentSelectedNodeId);
  const { data: asset, isLoading: isAssetDataLoading } = useGetAssetByIdQuery(
    { customerId: activeCustomer?.id ?? 0, assetId: assetId },
    {
      skip: !activeCustomer?.id, // In RTK Query, the option is `skip` instead of `enabled`
    },
  );

  const { isFetching: fetchingMeasurements, data: measurements } = useGetAllMeasurementsQuery(
    {
      customerId: activeCustomer?.id ?? 0,
      assetId: Number(assetId),
    },
    {
      skip: !activeCustomer?.id,
    },
  );
  const { data: assetTypeListData, isLoading: isAssetLoading } =
    useGetAllBackOfficeAssetTypesQuery();
  const assetTypesList = useMemo(() => {
    return assetTypePathMapper(assetTypeListData ?? []);
  }, [assetTypeListData]);
  return {
    asset,
    isAssetDataLoading,
    assetTypeListData,
    assetTypesList,
    isAssetLoading,
    fetchingMeasurements,
    measurements,
  };
};

export default useGetAssetToMeasureData;
