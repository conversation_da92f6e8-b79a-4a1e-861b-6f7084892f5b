import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AnyAction } from 'redux';
import { ThunkDispatch } from 'redux-thunk';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { measuresApi } from '~/redux/api/measuresApi';
import { timeseriesApi } from '~/redux/api/timeseriesApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import {
  getAssetTz,
  getGlobalSamplePeriod,
  getGlobalTimeRangeType,
} from '~/redux/selectors/topPanleSelectors';
import { getDbMeasureIdToAssetIdMap } from '~/redux/selectors/treeSelectors';
import { getGlobalEndDate, getGlobalStartDate } from '~/redux/selectors/widgetSelectors';
import { RootState } from '~/redux/store';
import { AggByOptions, SamplePeriodOptions } from '~/types/dashboard';
import { AssetMeasurementDetails, measurementsUnitsDTO } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchAndSucess,
  SingleHeatMapTimeSeriesData,
} from '~/types/timeseries';
import { AssetMeasureOptions, HeatmapChartWidget } from '~/types/widgets';

type MeasuresData = {
  isLoading: boolean;
  isError: boolean;
  error: string;
  lastFetchTime?: number;
  tsData: SingleHeatMapTimeSeriesData;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
};

type Params = {
  selectedTitles: string[];
  dataFetchSettings: HeatmapChartWidget;
  assetMeasure: AssetMeasureOptions[];
};

type ApiExtraArg = {
  measuresApi: typeof measuresApi;
  timeseriesApi: typeof timeseriesApi;
};

export function useGetHeatMapMeasuresTsData({
  selectedTitles,
  dataFetchSettings,
  assetMeasure,
}: Params) {
  const [successAndFailedMeasurements, setSuccessAndFailedMeasurements] = useState<
    AssetMeasurementDetailsWithLastFetchAndSucess[]
  >([]);
  const dispatch = useDispatch<ThunkDispatch<RootState, ApiExtraArg, AnyAction>>();
  const customerId = useSelector(getCustomerId);
  const router = useRouter();
  const dbMeasureIdToAssetIdMap = useSelector(getDbMeasureIdToAssetIdMap);
  const [state, setState] = useState<{
    data: undefined | MeasuresData[];
    isLoading: boolean;
    isError: boolean;
  }>({
    data: undefined,
    isLoading: true,
    isError: false,
  });

  const globalStartDate = useSelector(getGlobalStartDate);
  const globalEndDate = useSelector(getGlobalEndDate);
  const globalTimeRange = useSelector(getGlobalTimeRangeType);
  const globalSamplePeriod = useSelector(getGlobalSamplePeriod);
  const assetTz = useSelector(getAssetTz);

  const {
    startDate: selectedStartDate,
    endDate: selectedEndDate,
    aggBy: selectedAggBy,
    samplePeriod: selectedSamplePeriod,
    timeRange: selectedTimeRange,
    isRelativeToGlboalEndTime,
    globalSamplePeriod: isGlobalSamplePeriodOverridden,
    overrideGlobalSettings: isGlobalTimeRangeSettingsOverridden,
    overrideAssetTz: isOverrideAssetTz,
    overrideAssetTzValue,
    groupX,
    groupY,
  } = dataFetchSettings;

  const [startDate, setStartDate] = useState(globalStartDate);
  const [endDate, setEndDate] = useState(globalEndDate);
  const [aggBy, setAggBy] = useState(selectedAggBy);
  const [samplePeriod, setSamplePeriod] = useState(globalSamplePeriod);
  const [timeRange, setTimeRange] = useState(globalTimeRange);
  const [assetTzOverride, setAssetTzOverride] = useState(isOverrideAssetTz);
  const [assetTzOverrideValue, setAssetTzOverrideValue] = useState(overrideAssetTzValue);

  useEffect(() => {
    setAggBy(selectedAggBy);

    if (isGlobalTimeRangeSettingsOverridden) {
      setStartDate(selectedStartDate);
      setEndDate(selectedEndDate);
      setTimeRange(selectedTimeRange);
    } else {
      setStartDate(globalStartDate);
      setEndDate(globalEndDate);
      setTimeRange(globalTimeRange);
    }

    if (isGlobalSamplePeriodOverridden) {
      setSamplePeriod(selectedSamplePeriod);
    } else {
      setSamplePeriod(globalSamplePeriod);
    }
    if (isOverrideAssetTz) {
      setAssetTzOverride(true);
      setAssetTzOverrideValue(overrideAssetTzValue);
    } else {
      setAssetTzOverride(false);
    }
  }, [
    globalEndDate,
    globalSamplePeriod,
    globalStartDate,
    globalTimeRange,
    isGlobalSamplePeriodOverridden,
    isGlobalTimeRangeSettingsOverridden,
    isRelativeToGlboalEndTime,
    selectedAggBy,
    selectedEndDate,
    selectedSamplePeriod,
    selectedStartDate,
    selectedTimeRange,
    isOverrideAssetTz,
    overrideAssetTzValue,
  ]);

  const fetchMeasureData = useCallback(
    async (measureId: string) => {
      if (!dbMeasureIdToAssetIdMap[measureId]) {
        throw new Error(`No assetId found for ${measureId}`);
      }
      if (!measureId) {
        throw new Error(`Error invalid measureId: ${measureId}`);
      }
      const { data: measureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate({
          customerId,
          assetId: dbMeasureIdToAssetIdMap[measureId],
          measId: measureId,
        }),
      );

      if (!isMeasureDataSuccess || !measureData) {
        throw new Error(`Error fetching measure data for measureId: ${measureId}`);
      }

      return measureData;
    },
    [customerId, dbMeasureIdToAssetIdMap, dispatch],
  );

  const fetchMeasureDataByAsset = useCallback(
    async (assetId: string, measureId: string) => {
      if (assetId === '' || !assetId) {
        throw new Error(`No assetId found for ${measureId}`);
      }

      if (!measureId) {
        throw new Error(`Error invalid measureId: ${measureId}`);
      }

      const { data: measureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate({
          customerId,
          assetId: assetId,
          measId: measureId,
        }),
      );

      if (!isMeasureDataSuccess || !measureData) {
        throw new Error(`Error fetching measure data for measureId: ${measureId}`);
      }

      return measureData;
    },
    [customerId, dbMeasureIdToAssetIdMap, dispatch],
  );
  const fetchTimeseriesData = useCallback(
    async (
      tsDbMeasureIds: number[],
    ): Promise<{ error: boolean; tsData: Record<number, SingleHeatMapTimeSeriesData> }> => {
      const { data: tsData, isSuccess: isTsSuccess } = await dispatch(
        timeseriesApi.endpoints.getHeampMeasurementSeries.initiate({
          customerId,
          measId: tsDbMeasureIds.filter(Boolean).sort().join(','),
          start: startDate,
          end: endDate,
          agg: AggByOptions[aggBy].serverValue,
          agg_period: SamplePeriodOptions[samplePeriod].serverValue,
          timeRangeType: timeRange,
          groupX,
          groupY,
          assetTz: isOverrideAssetTz ? assetTzOverrideValue : assetTz,
        }),
      );
      if (!isTsSuccess || !tsData) {
        return {
          error: true,
          tsData: {},
        };
      }
      return { error: false, tsData };
    },
    [
      aggBy,
      customerId,
      dispatch,
      endDate,
      samplePeriod,
      startDate,
      timeRange,
      assetTz,
      groupX,
      groupY,
      isOverrideAssetTz,
      assetTzOverrideValue,
    ],
  );

  const fetchUnitOfMeasure = useCallback(
    async (assetMeasurementTypeId: number) => {
      const { data: unitOfMeasures, isSuccess: isUnitOfMeasureSuccess } = await dispatch(
        measuresApi.endpoints?.getUnitsOfMeasure.initiate({
          measurementTypeId: assetMeasurementTypeId,
        }),
      );

      if (!isUnitOfMeasureSuccess || !unitOfMeasures) {
        throw new Error('Error fetching unit of measure data');
      }

      return unitOfMeasures;
    },
    [dispatch],
  ); // include assetMeasurementTypeId in the dependency array
  const generateRandomData = (
    numPoints: number,
    startTime: number,
    interval: number,
    minValue: number,
    maxValue: number,
  ) => {
    const days = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
    const data: [string, string, number][] = [];
    let currentTime = startTime;

    for (let i = 0; i < numPoints; i++) {
      const hour = String(currentTime % 24).padStart(2, '0');
      const day = days[Math.floor(currentTime / 24) % days.length];
      const value = Math.random() * (maxValue - minValue) + minValue;
      data.push([hour, day, value]);
      currentTime += interval;
    }
    return data;
  };
  const groupAssetMeasures = useCallback(
    (assetMeasures: AssetMeasureOptions[]) => {
      return assetMeasures.reduce<Record<string, number[]>>((acc, cur) => {
        const asset = Number(cur.assetId);
        const measures = Array.isArray(cur.measureId) ? cur.measureId : [cur.measureId]; // ensure array

        if (!acc[asset]) {
          acc[asset] = [];
        }

        // Push all measure IDs converted to numbers
        measures.forEach((m) => {
          acc[asset].push(Number(m));
        });

        return acc;
      }, {});
    },
    [
      assetMeasure, // Ensure assetMeasure is included in the dependencies
      customerId, // Ensure customerId is included in the dependencies
    ],
  );
  const fetchAllAssetMeasures = useCallback(
    async (
      customerId: number,
      assetsWithMeasures: {
        asset_id: number;
        measurement_ids: number[];
      }[],
    ): Promise<measurementsUnitsDTO> => {
      if (!assetsWithMeasures || assetsWithMeasures.length === 0) {
        return {
          items: [],
          total: 0,
        };
      }

      const result = await dispatch(
        measuresApi.endpoints.getMeasuresWithAssetMeasures.initiate({
          customerId,
          data: assetsWithMeasures,
        }),
      );
      const { error, isError, data } = result;
      if (isError || error || !data) {
        console.error('Error fetching measures with asset measures:', error);
        return {
          items: [],
          total: 0,
        };
      }
      return data;
    },
    [customerId, dispatch],
  );
  useEffect(() => {
    const fetchMeasuresData = async () => {
      setState({ data: undefined, isLoading: true, isError: false });
      const filteredAssetMeasures = assetMeasure.filter(
        (assetMeasurement) =>
          assetMeasurement.assetId.trim() !== '' &&
          assetMeasurement.measureId.some((measure) => measure && measure.trim() !== ''),
      );
      // const allPromises: Promise<MeasuresData>[] = filteredAssetMeasures.flatMap(
      //   (assetMeasurement) =>
      //     assetMeasurement.measureId.map(async (measureId) => {
      //       if (!measureId.trim()) {
      //         return {
      //           isLoading: false,
      //           isError: true,
      //           error: 'Invalid measureId provided.',
      //         } as MeasuresData;
      //       }

      //       try {
      //         // Fetch measure data
      //         const measureData = await fetchMeasureDataByAsset(
      //           assetMeasurement.assetId,
      //           measureId,
      //         );
      //         // Fetch unit of measures based on measureData.typeId
      //         const unitOfMeasures = await fetchUnitOfMeasure(measureData.typeId);
      //         // Return successful MeasuresData object
      //         return {
      //           isLoading: false,
      //           isError: false,
      //           error: '',
      //           measureData,
      //           unitOfMeasures,
      //         } as MeasuresData;
      //       } catch (error: any) {
      //         // Log the error for debugging purposes
      //         console.error(
      //           `Error fetching data for assetId: ${assetMeasurement.assetId}, measureId: ${measureId}:`,
      //           error,
      //         );
      //         return {
      //           isLoading: false,
      //           isError: true,
      //           error: error,
      //         } as MeasuresData;
      //       }
      //     }),
      // );
      const grouped = groupAssetMeasures(filteredAssetMeasures);
      const allMeasures = await fetchAllAssetMeasures(
        customerId,
        Object.entries(grouped).map(([asset_id, measurement_ids]) => {
          return {
            asset_id: Number(asset_id),
            measurement_ids,
          };
        }),
      );
      const allPromisesData: MeasuresData[] = await Promise.all(
        allMeasures.items.map(async (measureData) => {
          return {
            error: '',
            isLoading: true,
            isError: false,
            lastFetchTime: Date.now(),
            tsData: {
              tag: measureData.measurementId,
              'gb0,gb1,val': [],
            },
            measureData: {
              ...measureData,
              measurementId: measureData.measurement_id,
            },
            unitOfMeasures: measureData.unitOfMeasure ? [measureData.unitOfMeasure] : [],
          } as MeasuresData;
        }),
      );
      const results = allPromisesData; // await Promise.all(allPromises);
      const tsMeasureIds = results
        .map((result) => result.measureData?.measurementId)
        .filter(Boolean);
      const { error, tsData } = await fetchTimeseriesData(tsMeasureIds);
      if (error) {
        setState({ data: undefined, isLoading: false, isError: true });
        return;
      }
      results.forEach((result) => {
        const seriesData = tsData[result.measureData.measurementId];
        if (seriesData?.error || !seriesData['gb0,gb1,val']) {
          result.isLoading = false;
          result.isError = true;
          result.error = seriesData.error || 'No data available for this measure';
        } else {
          result.isLoading = false;
          result.isError = false;
          result.error = '';
          result.lastFetchTime = Date.now();
          result.tsData = seriesData;
        }
      });
      setState({ data: results, isLoading: false, isError: false });
      const updated: AssetMeasurementDetailsWithLastFetchAndSucess[] = [];
      results.forEach((res) => {
        if (!res.isError && res.tsData) {
          updated.push({
            ...res.measureData,
            lastFetchTime: res.lastFetchTime,
            isSuccess: !res.isError,
          });
        } else {
          const existing = successAndFailedMeasurements.find(
            (r) => r.measurementId === res?.measureData?.measurementId,
          );

          if (existing) {
            updated.push({
              ...existing,
              lastFetchTime: existing.lastFetchTime,
              isSuccess: !res.isError,
            });
          } else {
            updated.push({
              ...res.measureData,
              lastFetchTime: res.lastFetchTime,
              isSuccess: !res.isError,
            }); // fallback to current failed result
          }
        }
      });
      const seenIds = new Set();
      const finalVals: AssetMeasurementDetailsWithLastFetchAndSucess[] = [];
      updated.forEach((update) => {
        if (!seenIds.has(update.measurementId)) {
          seenIds.add(update.measurementId);
          finalVals.push(update);
        }
      });
      setSuccessAndFailedMeasurements([...finalVals]);
    };
    if (router.pathname === '/dashboard-template' && selectedTitles.length > 0) {
      setState({
        data: [
          {
            isLoading: false,
            isError: false,
            error: '',
            measureData: {
              id: 11284,
              measurementId: 18578,
              tag: 'Brenes\\MAINPANEL\\PhaseBVoltage',
              description: 'PHASEB:VOLTAGE',
              meterFactor: null,
              typeId: 23,
              dataTypeId: 2,
              valueTypeId: 1,
              unitOfMeasureId: 70,
              locationId: null,
              datasourceId: null,
            },
            unitOfMeasures: [
              {
                id: 115,
                name: '%',
              },
              {
                id: 69,
                name: 'mV',
              },
              {
                id: 70,
                name: 'volts',
              },
            ],
            tsData: {
              tag: 18578,
              'gb0,gb1,val': generateRandomData(24, 1727626648247, 60000, 20, 100),
            },
          },
        ],
        isLoading: false,
        isError: false,
      });
    } else if (selectedTitles.length > 0) fetchMeasuresData();
  }, [
    aggBy,
    customerId,
    dbMeasureIdToAssetIdMap,
    dispatch,
    endDate,
    fetchMeasureData,
    fetchTimeseriesData,
    fetchUnitOfMeasure,
    samplePeriod,
    selectedTitles,
    startDate,
    timeRange,
    assetTz,
    isOverrideAssetTz,
    assetTzOverrideValue,
  ]);
  return { ...state, successAndFailedMeasurements };
}
