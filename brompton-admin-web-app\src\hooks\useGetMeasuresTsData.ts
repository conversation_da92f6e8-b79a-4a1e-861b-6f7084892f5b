import { useRouter } from 'next/router';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AnyAction } from 'redux';
import { ThunkDispatch } from 'redux-thunk';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { UnitOfMeasure } from '~/measurements/domain/types';
import { measuresApi } from '~/redux/api/measuresApi';
import { timeseriesApi } from '~/redux/api/timeseriesApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { getMetricsIdToName } from '~/redux/selectors/dashboardSelectors';
import {
  getAssetTz,
  getGlobalSamplePeriod,
  getGlobalTimeRangeType,
  getRefreshInterval,
} from '~/redux/selectors/topPanleSelectors';
import { getDbMeasureIdToAssetIdMap } from '~/redux/selectors/treeSelectors';
import { getGlobalEndDate, getGlobalStartDate } from '~/redux/selectors/widgetSelectors';
import { RootState } from '~/redux/store';
import { AggByOptions, SamplePeriodOptions } from '~/types/dashboard';
import { AssetMeasurementDetails, measurementsUnitsDTO } from '~/types/measures';
import {
  AssetMeasurementDetailsWithLastFetchAndSucess,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { AssetMeasureOptions, DataWidget, RealtimeSettings } from '~/types/widgets';

type MeasuresData = {
  isLoading: boolean;
  isError: boolean;
  error: string;
  tsData: SingleScatterTimeSeriesData;
  lastFetchTime: number;
  measureData: AssetMeasurementDetails;
  unitOfMeasures: UnitOfMeasure[];
  // partial_full_error?: 'partial' | 'full';
};

type Params = {
  selectedTitles: string[];
  dataFetchSettings: DataWidget & RealtimeSettings;
  assetMeasure: AssetMeasureOptions[];
};

type ApiExtraArg = {
  measuresApi: typeof measuresApi;
  timeseriesApi: typeof timeseriesApi;
};

export function useGetMeasuresTsData({ selectedTitles, dataFetchSettings, assetMeasure }: Params) {
  const dispatch = useDispatch<ThunkDispatch<RootState, ApiExtraArg, AnyAction>>();
  const metricToName = useSelector(getMetricsIdToName);
  const customerId = useSelector(getCustomerId);
  const timeRefreshInterval = useSelector(getRefreshInterval);
  const dbMeasureIdToAssetIdMap = useSelector(getDbMeasureIdToAssetIdMap);
  const [state, setState] = useState<{
    data: undefined | MeasuresData[];
    isLoading: boolean;
    isError: boolean;
    errorMessage?: string;
    forcastedData: undefined | MeasuresData[];
  }>({
    data: undefined,
    isLoading: true,
    isError: false,
    errorMessage: undefined,
    forcastedData: undefined,
  });

  const globalStartDate = useSelector(getGlobalStartDate);
  const globalEndDate = useSelector(getGlobalEndDate);
  const globalTimeRange = useSelector(getGlobalTimeRangeType);
  const globalSamplePeriod = useSelector(getGlobalSamplePeriod);
  const assetTz = useSelector(getAssetTz);
  const router = useRouter();
  const {
    startDate: selectedStartDate,
    endDate: selectedEndDate,
    aggBy: selectedAggBy,
    samplePeriod: selectedSamplePeriod,
    timeRange: selectedTimeRange,
    globalSamplePeriod: isGlobalSamplePeriodOverridden,
    overrideGlobalSettings: isGlobalTimeRangeSettingsOverridden,
    overrideAssetTz: isOverrideAssetTz,
    isRelativeToGlboalEndTime,
    overrideAssetTzValue,
    showForecast,
    period,
    isRealTime,
    refreshInterval,
    retainPeriod,
  } = dataFetchSettings;

  const [startDate, setStartDate] = useState(globalStartDate);
  const [endDate, setEndDate] = useState(globalEndDate);
  const [aggBy, setAggBy] = useState(selectedAggBy);
  const [samplePeriod, setSamplePeriod] = useState(globalSamplePeriod);
  const [timeRange, setTimeRange] = useState(globalTimeRange);
  const [assetTzOverride, setAssetTzOverride] = useState(isOverrideAssetTz);
  const [assetTzOverrideValue, setAssetTzOverrideValue] = useState(overrideAssetTzValue);
  const retainPeriodRef = useRef(retainPeriod);
  const [successAndFailedMeasurements, setSuccessAndFailedMeasurements] = useState<
    AssetMeasurementDetailsWithLastFetchAndSucess[]
  >([]);
  const applyRetention = (
    tsData: Record<number, SingleScatterTimeSeriesData>,
    retentionMinutes: number,
  ): Record<number, SingleScatterTimeSeriesData> => {
    // const retentionTime = Date.now() - retentionMinutes * 60000; // Calculate retention boundary
    const browserOffsetMinutes = new Date().getTimezoneOffset(); // Get browser's timezone offset in minutes
    const browserOffsetMs = browserOffsetMinutes * 60000; // Convert to milliseconds
    const retentionTime = Date.now() - retentionMinutes * 60000 - browserOffsetMs; // Adjusted retention boundary
    return Object.fromEntries(
      Object.entries(tsData)
        .filter(([, series]) => series.error === undefined) // Filter out errored series
        .map(([key, series]) => {
          const filteredPoints = series['ts,val'].filter(
            ([timestamp]) => timestamp >= retentionTime,
          );
          return [
            key,
            {
              ...series,
              'ts,val': filteredPoints,
            },
          ];
        }),
    );
  };

  useEffect(() => {
    retainPeriodRef.current = retainPeriod;
  }, [retainPeriod]);

  useEffect(() => {
    setAggBy(selectedAggBy);

    if (isGlobalTimeRangeSettingsOverridden) {
      setStartDate(selectedStartDate);
      setEndDate(selectedEndDate);
      setTimeRange(selectedTimeRange);
    } else {
      setStartDate(globalStartDate);
      setEndDate(globalEndDate);
      setTimeRange(globalTimeRange);
    }

    if (isGlobalSamplePeriodOverridden) {
      setSamplePeriod(selectedSamplePeriod);
    } else {
      setSamplePeriod(globalSamplePeriod);
    }
    if (isOverrideAssetTz) {
      setAssetTzOverride(true);
      setAssetTzOverrideValue(overrideAssetTzValue);
    } else {
      setAssetTzOverride(false);
    }
  }, [
    globalEndDate,
    globalSamplePeriod,
    globalStartDate,
    globalTimeRange,
    isGlobalSamplePeriodOverridden,
    isGlobalTimeRangeSettingsOverridden,
    isRelativeToGlboalEndTime,
    selectedAggBy,
    selectedEndDate,
    selectedSamplePeriod,
    selectedStartDate,
    selectedTimeRange,
    isOverrideAssetTz,
    overrideAssetTzValue,
  ]);

  const fetchTimeseriesData = useCallback(
    async (
      tsDbMeasureIds: number[],
    ): Promise<{
      error: boolean;
      errorMessage?: string;
      tsData: Record<number, SingleScatterTimeSeriesData>;
    }> => {
      if (tsDbMeasureIds.length === 0) {
        return { error: true, tsData: {} };
      }

      const {
        data: tsData,
        isSuccess: isTsSuccess,
        isError,
        error,
      } = await dispatch(
        timeseriesApi.endpoints.getMultiMeasurementSeries.initiate({
          customerId,
          measId: tsDbMeasureIds.filter(Boolean).sort().join(','),
          start: isGlobalTimeRangeSettingsOverridden ? selectedStartDate : globalStartDate,
          end: isGlobalTimeRangeSettingsOverridden ? selectedEndDate : endDate,
          agg: AggByOptions[aggBy].serverValue ?? 1,
          agg_period: isGlobalSamplePeriodOverridden
            ? SamplePeriodOptions[selectedSamplePeriod].serverValue
            : SamplePeriodOptions[globalSamplePeriod].serverValue,
          timeRangeType: timeRange,
          assetTz: isOverrideAssetTz ? assetTzOverrideValue : assetTz,
        }),
      );
      if (!isTsSuccess || !tsData || isError) {
        const err = error as CustomError;
        return {
          error: true,
          errorMessage: err?.data?.message ?? 'Error fetching timeseries data',
          tsData: {},
        };
      }

      return { error: false, tsData };
    },
    [
      dispatch,
      customerId,
      startDate,
      endDate,
      aggBy,
      samplePeriod,
      timeRange,
      isOverrideAssetTz,
      assetTzOverrideValue,
      assetTz,
      retainPeriod, // Add this as a dependency
    ],
  );

  useEffect(() => {
    if (isRealTime) {
      const intervalId = setInterval(() => {
        // Fetch and apply retention logic
        const fetchRealTimeData = async () => {
          const tsMeasureIds = state.data?.map((d) => d.measureData.measurementId) || [];
          const { error, tsData } = await fetchTimeseriesData(tsMeasureIds);
          if (!error) {
            setState((prevState) => ({
              ...prevState,
              data: prevState.data?.map((d) => ({
                ...d,
                tsData: tsData[d.measureData.measurementId],
                lastFetchTime: Date.now(),
              })),
            }));
          }
        };

        fetchRealTimeData();
      }, refreshInterval * 1000);

      return () => clearInterval(intervalId);
    }
  }, [isRealTime, refreshInterval, fetchTimeseriesData, state.data]);

  const fetchMeasureData = useCallback(
    async (measureId: string) => {
      // if (!dbMeasureIdToAssetIdMap[measureId]) {
      //   throw new Error(`No assetId found for ${measureId}`);
      // }

      if (!measureId) {
        throw new Error(`Error invalid measureId: ${measureId}`);
      }

      const { data: measureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate({
          customerId,
          assetId: dbMeasureIdToAssetIdMap[measureId],
          measId: measureId,
        }),
      );

      if (!isMeasureDataSuccess || !measureData) {
        throw new Error(`Error fetching measure data for measureId: ${measureId}`);
      }

      return measureData;
    },
    [customerId, dbMeasureIdToAssetIdMap, dispatch],
  );
  const fetchMeasureDataByAsset = useCallback(
    async (assetId: string, measureId: string) => {
      if (assetId === '' || !assetId) {
        throw new Error(`No assetId found for ${measureId}`);
      }

      if (!measureId) {
        throw new Error(`Error invalid measureId: ${measureId}`);
      }

      const { data: measureData, isSuccess: isMeasureDataSuccess } = await dispatch(
        measuresApi.endpoints?.getMeasurementById.initiate({
          customerId,
          assetId: assetId,
          measId: measureId,
        }),
      );

      if (!isMeasureDataSuccess || !measureData) {
        throw new Error(`Error fetching measure data for measureId: ${measureId}`);
      }

      return measureData;
    },
    [customerId, dbMeasureIdToAssetIdMap, dispatch],
  );

  const fetchAllAssetMeasures = useCallback(
    async (
      customerId: number,
      assetsWithMeasures: {
        asset_id: number;
        measurement_ids: number[];
      }[],
    ): Promise<measurementsUnitsDTO> => {
      if (!assetsWithMeasures || assetsWithMeasures.length === 0) {
        return {
          items: [],
          total: 0,
        };
      }

      const result = await dispatch(
        measuresApi.endpoints.getMeasuresWithAssetMeasures.initiate({
          customerId,
          data: assetsWithMeasures,
        }),
      );
      const { error, isError, data } = result;
      if (isError || error || !data) {
        console.error('Error fetching measures with asset measures:', error);
        return {
          items: [],
          total: 0,
        };
      }
      return data;
    },
    [customerId, dispatch],
  );

  const fetchForecastData = useCallback(
    async (
      forecastMeasureId: string,
      period: string,
    ): Promise<{
      error: boolean;
      tsData: Record<number, SingleScatterTimeSeriesData>;
    }> => {
      if (showForecast) {
        const { data: tsData, isSuccess: isTsSuccess } = await dispatch(
          timeseriesApi.endpoints.getForecastMeasurementSeries.initiate(
            {
              customerId,
              measId: forecastMeasureId,
              assetTz: isOverrideAssetTz ? assetTzOverrideValue : assetTz,
              forecast: period,
              agg: AggByOptions[aggBy].serverValue,
            },
            {
              forceRefetch: timeRefreshInterval !== -1 ? true : undefined,
            },
          ),
        );
        if (!isTsSuccess || !tsData) {
          return { error: false, tsData: {} };
        }
        return {
          error: false,
          tsData,
        };
      } else {
        return {
          error: false,
          tsData: {},
        };
      }
    },
    [dispatch, customerId, assetTz, showForecast, assetTzOverrideValue, aggBy, timeRefreshInterval],
  );
  const fetchUnitOfMeasure = useCallback(
    async (assetMeasurementTypeId: number) => {
      const { data: unitOfMeasures, isSuccess: isUnitOfMeasureSuccess } = await dispatch(
        measuresApi.endpoints?.getUnitsOfMeasure.initiate({
          measurementTypeId: assetMeasurementTypeId,
        }),
      );

      if (!isUnitOfMeasureSuccess || !unitOfMeasures) {
        throw new Error('Error fetching unit of measure data');
      }

      return unitOfMeasures;
    },
    [dispatch],
  ); // include assetMeasurementTypeId in the dependency array

  const generateRandomData = (
    numPoints: number,
    startTime: number,
    interval: number,
    minValue: number,
    maxValue: number,
  ): [number, number][] => {
    const data: [number, number][] = [];
    for (let i = 0; i < numPoints; i++) {
      const timestamp = startTime + i * interval;
      const value = Math.random() * (maxValue - minValue) + minValue;
      data.push([timestamp, value]);
    }
    return data;
  };
  const groupAssetMeasures = useCallback(
    (assetMeasures: AssetMeasureOptions[]) => {
      return assetMeasures.reduce<Record<string, number[]>>((acc, cur) => {
        const asset = Number(cur.assetId);
        const measures = Array.isArray(cur.measureId) ? cur.measureId : [cur.measureId]; // ensure array

        if (!acc[asset]) {
          acc[asset] = [];
        }

        // Push all measure IDs converted to numbers
        measures.forEach((m) => {
          acc[asset].push(Number(m));
        });

        return acc;
      }, {});
    },
    [
      assetMeasure, // Ensure assetMeasure is included in the dependencies
      customerId, // Ensure customerId is included in the dependencies
    ],
  );
  const fetchMeasuresData = useCallback(async () => {
    const filteredAssetMeasures = assetMeasure.filter(
      (assetMeasurement) =>
        assetMeasurement?.assetId.trim() !== '' &&
        assetMeasurement?.measureId.some((measure) => measure && measure.trim() !== ''),
    );
    // const allPromises: Promise<MeasuresData>[] = filteredAssetMeasures.flatMap((assetMeasurement) =>
    //   assetMeasurement.measureId.map(async (measureId) => {
    //     if (!measureId.trim()) {
    //       return {
    //         isLoading: false,
    //         isError: true,
    //         error: 'Invalid measureId provided.',
    //       } as MeasuresData;
    //     }

    //     try {
    //       // Fetch measure data
    //       const measureData = await fetchMeasureDataByAsset(assetMeasurement.assetId, measureId);
    //       // Fetch unit of measures based on measureData.typeId
    //       const unitOfMeasures = await fetchUnitOfMeasure(measureData.typeId);
    //       // Return successful MeasuresData object
    //       return {
    //         isLoading: false,
    //         isError: false,
    //         error: '',
    //         measureData,
    //         unitOfMeasures,
    //       } as MeasuresData;
    //     } catch (error: any) {
    //       // Log the error for debugging purposes
    //       console.error(
    //         `Error fetching data for assetId: ${assetMeasurement.assetId}, measureId: ${measureId}:`,
    //         error,
    //       );
    //       return {
    //         isLoading: false,
    //         isError: true,
    //         lastFetchTime: Date.now(),
    //         error: error,
    //       } as MeasuresData;
    //     }
    //   }),
    // );
    const grouped = groupAssetMeasures(filteredAssetMeasures);
    const allMeasures = await fetchAllAssetMeasures(
      customerId,
      Object.entries(grouped).map(([asset_id, measurement_ids]) => {
        return {
          asset_id: Number(asset_id),
          measurement_ids,
        };
      }),
    );
    const allPromisesData: MeasuresData[] = await Promise.all(
      allMeasures.items.map(async (measureData) => {
        return {
          isLoading: false,
          isError: false,
          error: '',
          lastFetchTime: Date.now(),
          measureData: {
            ...measureData,
            id: measureData.id,
            measurementId: measureData.measurement_id, // FIXED: use correct property name
            metricId: measureData.metricId ?? null, // FIXED: use correct property name
            tag: measureData.tag ?? '', // FIXED: use correct property name
          },
          unitOfMeasures: measureData.unitOfMeasure ? [measureData.unitOfMeasure] : [],
          tsData: {
            tag: measureData.id,
            period: '',
            'ts,val': [],
            tag_meta: { uom: measureData.unitOfMeasure?.name ?? '' },
          },
        } as MeasuresData;
      }),
    );
    const results = allPromisesData; // await Promise.all(allPromises);
    const tsMeasureIds = results.map((result) => result.measureData?.measurementId);
    const { error, tsData, errorMessage } = await fetchTimeseriesData(tsMeasureIds.filter(Boolean));
    let forcastRes: MeasuresData[] | undefined = undefined;
    if (error) {
      setState({
        data: undefined,
        isLoading: false,
        isError: true,
        errorMessage,
        forcastedData: undefined,
      });
      return;
    }

    if (showForecast && period) {
      const { error: forecastError, tsData: forecastData } = await fetchForecastData(
        tsMeasureIds[0].toString(),
        period,
      );
      if (forecastError) {
        setState({ data: undefined, isLoading: false, isError: true, forcastedData: undefined });
        return;
      }
      const res = results.filter((res) => res.measureData.measurementId === tsMeasureIds[0]);
      const result = results.map((result) => {
        const seriesData = forecastData[result.measureData.measurementId];
        if (seriesData?.error) {
          return {
            ...result,
            isLoading: false,
            isError: true,
            lastFetchTime: Date.now(),
            error: seriesData.error,
          };
        } else {
          return {
            ...result,
            lastFetchTime: Date.now(),
            isLoading: false,
            tsData: seriesData,
          };
        }
      });
      forcastRes = result;
    }

    results.forEach((result) => {
      const seriesData = tsData[result.measureData?.measurementId];
      if (seriesData?.error || seriesData?.['ts,val'] === undefined) {
        result.isLoading = false;
        result.isError = true;
        // result.lastFetchTime = Date.now();
        result.error = seriesData?.error || 'No data available';
      } else {
        result.isLoading = false;
        result.isError = false;
        result.lastFetchTime = Date.now();
        result.tsData = seriesData;
      }
    });

    if (results.length > 0) {
      const updated: AssetMeasurementDetailsWithLastFetchAndSucess[] = [];

      results.forEach((res) => {
        if (!res.isError && res.tsData) {
          updated.push({
            ...res.measureData,
            lastFetchTime: res.lastFetchTime,
            isSuccess: !res.isError,
          });
        } else {
          const existing = successAndFailedMeasurements.find(
            (r) => r.measurementId === res?.measureData?.measurementId,
          );

          if (existing) {
            updated.push({
              ...existing,
              lastFetchTime: existing.lastFetchTime,
              isSuccess: !res.isError,
            });
          } else {
            updated.push({
              ...res.measureData,
              lastFetchTime: res.lastFetchTime,
              isSuccess: !res.isError,
            }); // fallback to current failed result
          }
        }
      });
      const seenIds = new Set();
      const finalVals: AssetMeasurementDetailsWithLastFetchAndSucess[] = [];
      updated.forEach((update) => {
        if (!seenIds.has(update.measurementId)) {
          seenIds.add(update.measurementId);
          finalVals.push(update);
        }
      });
      setSuccessAndFailedMeasurements([...finalVals]);
      setState({
        data: results,
        isLoading: false,
        isError: false,
        forcastedData: forcastRes,
      });
    }
  }, [
    assetMeasure,
    showForecast,
    period,
    setState,
    fetchMeasureData,
    fetchMeasureDataByAsset,
    fetchAllAssetMeasures,
    fetchUnitOfMeasure,
    fetchTimeseriesData,
    fetchForecastData,
    isRealTime,
  ]);
  useEffect(() => {
    if (router.pathname === '/dashboard-template') {
      const now = Date.now();
      const interval = 60000; // 1 minute

      const statesData = selectedTitles.map((title) => ({
        isLoading: false,
        isError: false,
        error: '',
        lastFetchTime: now,
        measureData: {
          id: Number(title),
          tag: metricToName[title] ?? '',
          dataTypeId: 2,
          measurementId: 18578,
          typeId: 23,
          description: 'PHASEB:VOLTAGE',
          locationId: null,
          unitOfMeasureId: 70,
          meterFactor: null,
          datasourceId: null,
          valueTypeId: 1,
        },
        unitOfMeasures: [
          { id: 115, name: '%' },
          { id: 69, name: 'mV' },
          { id: 70, name: 'volts' },
        ],
        tsData: {
          tag: 18578,
          period: '1min',
          'ts,val': generateRandomData(24, now, interval, 20, 100),
          error: '',
          tag_meta: {
            uom: 'volts',
          },
        },
      }));

      let forecastedData;
      if (showForecast) {
        forecastedData = statesData.map((dataItem) => {
          const realData = dataItem.tsData['ts,val'];
          const lastTimestamp = realData[realData.length - 1][0];
          const forecastStartTime = lastTimestamp + interval;
          return {
            ...dataItem,
            tsData: {
              ...dataItem.tsData,
              'ts,val': generateRandomData(24, forecastStartTime, interval, 30, 120),
            },
          };
        });
      }

      setState({
        data: statesData,
        forcastedData: forecastedData,
        isLoading: false,
        isError: false,
      });
    } else if (assetMeasure && assetMeasure.length > 0 && isRealTime === false) {
      fetchMeasuresData();
    }
  }, [
    dataFetchSettings,
    aggBy,
    customerId,
    dbMeasureIdToAssetIdMap,
    dispatch,
    endDate,
    fetchMeasureData,
    fetchTimeseriesData,
    fetchUnitOfMeasure,
    samplePeriod,
    selectedTitles,
    startDate,
    timeRange,
    assetTz,
    assetTzOverride,
    assetTzOverrideValue,
    showForecast,
    period,
    isRealTime,
  ]);

  const fetchHistoryTimeseriesData = useCallback(
    async (
      tsDbMeasureIds: number[],
    ): Promise<{ error: boolean; tsData: Record<number, SingleScatterTimeSeriesData> }> => {
      if (tsDbMeasureIds.length === 0) {
        return { error: true, tsData: {} };
      }
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - retainPeriodRef.current * 60000);

      const { data: tsData, isSuccess: isTsSuccess } = await dispatch(
        timeseriesApi.endpoints.getMultipleHistoryMeasurementSeries.initiate({
          customerId,
          measId: tsDbMeasureIds.filter(Boolean).sort().join(','),
          start: startDate.getTime(),
          end: endDate.getTime(),
          assetTz: false,
        }),
      );

      if (!isTsSuccess || !tsData) {
        return { error: true, tsData: {} };
      }

      // Apply retention logic
      const filteredTsData = applyRetention(tsData, retainPeriodRef.current);

      return { error: false, tsData: filteredTsData };
    },
    [dispatch, customerId],
  );

  useEffect(() => {
    if (!isRealTime) {
      return;
    }
    const intervalId = setInterval(() => {
      const fetcHistoryMeasuresData = async () => {
        setState({ data: undefined, isLoading: false, isError: false, forcastedData: undefined });
        const filteredAssetMeasures = assetMeasure.filter(
          (assetMeasurement) =>
            assetMeasurement?.assetId.trim() !== '' &&
            assetMeasurement?.measureId.some((measure) => measure && measure.trim() !== ''),
        );
        const grouped = groupAssetMeasures(filteredAssetMeasures);
        const allMeasures = await fetchAllAssetMeasures(
          customerId,
          Object.entries(grouped).map(([asset_id, measurement_ids]) => {
            return {
              asset_id: Number(asset_id),
              measurement_ids,
            };
          }),
        );
        const allPromisesData: MeasuresData[] = await Promise.all(
          allMeasures.items.map(async (measureData) => {
            return {
              isLoading: false,
              isError: false,
              error: '',
              lastFetchTime: Date.now(),
              measureData: {
                ...measureData,
                id: measureData.id,
                measurementId: measureData.measurement_id, // FIXED: use correct property name
                metricId: measureData.metricId ?? null, // FIXED: use correct property name
                tag: measureData.tag ?? '', // FIXED: use correct property name
              },
              unitOfMeasures: measureData.unitOfMeasure ? [measureData.unitOfMeasure] : [],
              tsData: {
                tag: measureData.id,
                period: '',
                'ts,val': [],
                tag_meta: { uom: measureData.unitOfMeasure?.name ?? '' },
              },
            } as MeasuresData;
          }),
        );
        const results = await Promise.all(allPromisesData);
        const tsMeasureIds = results.map((result) => result.measureData?.measurementId);
        const { error, tsData } = await fetchHistoryTimeseriesData(tsMeasureIds.filter(Boolean));
        if (error) {
          setState({ data: undefined, isLoading: false, isError: true, forcastedData: undefined });
          return;
        }
        results.forEach((result) => {
          const seriesData = tsData[result.measureData?.measurementId];
          if (seriesData?.error || seriesData?.['ts,val'] === undefined) {
            result.isLoading = false;
            result.isError = true;
            result.error = seriesData?.error ?? 'No data available';
          } else {
            result.isLoading = false;
            result.isError = false;
            result.tsData = seriesData;
            result.lastFetchTime = Date.now();
          }
        });
        const updated: AssetMeasurementDetailsWithLastFetchAndSucess[] = [];
        results.forEach((res) => {
          if (!res.isError && res.tsData) {
            updated.push({
              ...res.measureData,
              lastFetchTime: res.lastFetchTime,
              isSuccess: !res.isError,
            });
          } else {
            const existing = successAndFailedMeasurements.find(
              (r) => r.measurementId === res?.measureData?.measurementId,
            );

            if (existing) {
              updated.push({
                ...existing,
                lastFetchTime: existing.lastFetchTime,
                isSuccess: !res.isError,
              });
            } else {
              updated.push({
                ...res.measureData,
                lastFetchTime: res.lastFetchTime,
                isSuccess: !res.isError,
              }); // fallback to current failed result
            }
          }
        });
        const seenIds = new Set();
        const finalVals: AssetMeasurementDetailsWithLastFetchAndSucess[] = [];
        updated.forEach((update) => {
          if (!seenIds.has(update.measurementId)) {
            seenIds.add(update.measurementId);
            finalVals.push(update);
          }
        });
        setSuccessAndFailedMeasurements([...finalVals]);
        setState({
          ...state,
          data: results,
          isLoading: false,
          isError: false,
          forcastedData: undefined,
        });
      };
      if (selectedTitles && selectedTitles.length > 0) fetcHistoryMeasuresData();
    }, refreshInterval * 1000);

    return () => clearInterval(intervalId);
  }, [dispatch, isRealTime, refreshInterval, customerId, selectedTitles]);
  return { ...state, successAndFailedMeasurements };
}
