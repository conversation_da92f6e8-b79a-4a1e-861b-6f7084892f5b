import { Layout } from 'plotly.js';
import { MultiPlotWidget } from '~/types/widgets';
import { formatMetricLabel } from '~/utils/utils';
import { SubplotTrace } from './useFetchMultiPlotData';

export const useMultiPlotWidgetLogic = (settings: MultiPlotWidget, subplotData: SubplotTrace[]) => {
  const numPlots = settings.subplots.length;

  let rows, columns;
  const showRangeSlider = settings.subplots.some((subplot) => subplot.showRangeSlider);

  if (showRangeSlider) {
    rows = 1;
    columns = numPlots;
  } else if (settings.layoutType === 'vertical') {
    rows = numPlots;
    columns = 1;
  } else if (settings.layoutType === 'horizontal') {
    rows = 1;
    columns = numPlots;
  } else {
    columns = settings.plotsPerRow;
    rows = Math.ceil(numPlots / settings.plotsPerRow);
  }

  let legendY: number;

  if (typeof settings.legend?.y === 'number') {
    // ✅ Use manual override including 0
    legendY = settings.legend.y;
  } else if (
    settings.legend?.y === null ||
    settings.legend?.y === 0 ||
    settings.legend?.y === undefined
  ) {
    // ✅ Explicitly set to null/0/undefined → treat as default (but NOT auto-align)
    legendY = -0.25;
  } else {
    // ✅ Auto-align based on total trace count
    const totalTraces = subplotData.reduce((acc, sp) => acc + sp.traces.length, 0);

    if (totalTraces <= 2) legendY = -0.35;
    else if (totalTraces <= 4) legendY = -0.45;
    else if (totalTraces <= 6) legendY = -0.55;
    else if (totalTraces <= 8) legendY = -0.65;
    else if (totalTraces <= 10) legendY = -0.85;
    else legendY = -1.05;
  }

  const layoutConfig: Partial<Layout> & { [key: string]: any } = {
    grid: { rows, columns, pattern: 'independent' },
    showlegend: true,
    legend: {
      orientation: 'h',
      y: legendY,
      x: -0.01,
    },
    title: settings.title.isVisible ? settings.title.value : undefined,
    // subplotData
    //     .map((subplot) =>
    //       subplot.traces
    //         .filter((trace) => !trace.name?.includes(' - Forecast')) // ❌ remove forecast traces from title
    //         .map((trace) => formatMetricLabel(trace.name ?? '') + ' Vs. ')
    //         .join('')
    //         .replace(/ Vs. $/, ''),
    //     )
    //     .join(' | '),
    titlefont: settings.title.isVisible
      ? {
          color: settings.title.color,
          size: settings.title.fontSize,
        }
      : undefined,
    shapes: [],
    annotations: [],
  };

  subplotData.forEach(({ subplotId, traces }, index) => {
    const subplotSettings = settings.subplots[index];
    const showSlider = subplotSettings?.showRangeSlider ?? false;
    const allTrend = subplotSettings?.assetMeasures.every((am) => am.chartType === 'trend');
    const isSparkline = allTrend && subplotSettings?.showSparkline === true;

    const xaxisKey = `xaxis${index + 1}`;
    const yaxisKey = `yaxis${index + 1}`;
    const xref = `x${index + 1}`;
    const yref = `y${index + 1}`;

    if (isSparkline) {
      layoutConfig[xaxisKey] = {
        visible: false,
        showticklabels: false,
        showgrid: false,
        zeroline: false,
      };
      layoutConfig[yaxisKey] = {
        visible: false,
        showticklabels: false,
        showgrid: false,
        zeroline: false,
      };
      layoutConfig.plot_bgcolor = 'transparent';
      layoutConfig.paper_bgcolor = 'transparent';
    } else {
      layoutConfig[xaxisKey] = {
        title: 'Time',
        type: 'date',
        rangeslider: { visible: showSlider },
      };
      layoutConfig[yaxisKey] = {
        title: 'Value',
      };
    }

    // Per assetMeasure stats
    traces.forEach((trace, traceIndex) => {
      const y = (trace as any)?.y as number[];
      const x = (trace as any)?.x as string[];
      if (!subplotSettings?.assetMeasures) return;
      const assetMeasure = subplotSettings.assetMeasures[traceIndex];
      const totalMeasures = subplotSettings.assetMeasures.length;

      if (!x?.length || !y?.length || !assetMeasure) return;

      const minY = Math.min(...y);
      const maxY = Math.max(...y);
      const avgY = y.reduce((a, b) => a + b, 0) / y.length;
      const annotationYOffset = (maxY - minY) * 0.06;
      const xMid = x[Math.floor(x.length / 2)];

      const colorPalette = [
        '#1f77b4',
        '#ff7f0e',
        '#2ca02c',
        '#d62728',
        '#9467bd',
        '#8c564b',
        '#e377c2',
        '#7f7f7f',
        '#bcbd22',
        '#17becf',
      ];
      const statLineColor =
        totalMeasures === 1
          ? '#D3D3D3'
          : traceIndex === 0
          ? '#D3D3D3'
          : colorPalette[traceIndex % colorPalette.length];

      const label = formatMetricLabel(
        settings.dbMeasureIdToName?.[assetMeasure.measureId[0]] ?? 'Measure',
      );

      if (assetMeasure.showMinLine) {
        layoutConfig.shapes!.push({
          type: 'line',
          xref: xref as any,
          yref: yref as any,
          x0: x[0],
          x1: x[x.length - 1],
          y0: minY,
          y1: minY,
          line: { color: statLineColor, width: 2, dash: 'dot' },
        });
        layoutConfig.annotations!.push({
          xref: xref as any,
          yref: yref as any,
          x: xMid,
          y: minY - annotationYOffset,
          text: `${label} Min: ${minY.toFixed(2)}`,
          showarrow: false,
          font: { color: 'black', size: 11 },
          xanchor: 'center',
        });
      }

      if (assetMeasure.showMaxLine) {
        layoutConfig.shapes!.push({
          type: 'line',
          xref: xref as any,
          yref: yref as any,
          x0: x[0],
          x1: x[x.length - 1],
          y0: maxY,
          y1: maxY,
          line: { color: statLineColor, width: 2, dash: 'dash' },
        });
        layoutConfig.annotations!.push({
          xref: xref as any,
          yref: yref as any,
          x: xMid,
          y: maxY - annotationYOffset,
          text: `${label} Max: ${maxY.toFixed(2)}`,
          showarrow: false,
          font: { color: 'black', size: 11 },
          xanchor: 'center',
        });
      }

      if (assetMeasure.showAvgLine) {
        layoutConfig.shapes!.push({
          type: 'line',
          xref: xref as any,
          yref: yref as any,
          x0: x[0],
          x1: x[x.length - 1],
          y0: avgY,
          y1: avgY,
          line: { color: statLineColor, width: 2, dash: 'dashdot' },
        });
        layoutConfig.annotations!.push({
          xref: xref as any,
          yref: yref as any,
          x: xMid,
          y: avgY - annotationYOffset,
          text: `${label} Avg: ${avgY.toFixed(2)}`,
          showarrow: false,
          font: { color: 'black', size: 11 },
          xanchor: 'center',
        });
      }

      // ✅ Threshold Line
      if (
        assetMeasure.showThresholdLine &&
        assetMeasure.thresholdValue !== undefined &&
        assetMeasure.thresholdValue !== null
      ) {
        const thresholdColor = assetMeasure.thresholdColor || '#DC143C';
        const thresholdStyle = assetMeasure.thresholdStyle || 'solid';
        const thresholdName = assetMeasure.thresholdName || 'Threshold';

        layoutConfig.shapes!.push({
          type: 'line',
          xref: xref as any,
          yref: yref as any,
          x0: x[0],
          x1: x[x.length - 1],
          y0: assetMeasure.thresholdValue,
          y1: assetMeasure.thresholdValue,
          line: {
            color: thresholdColor,
            width: 2,
            dash: thresholdStyle,
          },
        });

        layoutConfig.annotations!.push({
          xref: xref as any,
          yref: yref as any,
          x: xMid,
          y: assetMeasure.thresholdValue + annotationYOffset,
          text: `${thresholdName}: ${assetMeasure.thresholdValue.toFixed(2)}`,
          showarrow: false,
          font: { color: thresholdColor, size: 11 },
          xanchor: 'center',
        });
      }
    });
  });

  const allTraces = subplotData.flatMap(({ traces, subplotId }, idx) =>
    traces.map((trace) => ({
      ...trace,
      exportId: [subplotId?.toString()],
      xaxis: `x${idx + 1}`,
      yaxis: `y${idx + 1}`,
    })),
  );

  return { layoutConfig, allTraces };
};
