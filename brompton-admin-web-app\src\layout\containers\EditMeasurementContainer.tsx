import { CircularProgress, Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import EditMeasurementForm from '~/components/dashboard/EditMeasurementForm';
import { CustomError } from '~/errors/CustomerErrorResponse';
import useFetchAssetPath from '~/hooks/useFetchAssetPath';
import { EditAssetMeasurementForm } from '~/measurements/domain/types';
import { assetsApi } from '~/redux/api/assetsApi';
import {
  measuresApi,
  useEditMeasureMutation,
  useGetAllDatasourcesQuery,
  useGetAllDataTypesQuery,
  useGetAllLocationsQuery,
  useGetAllMeasureTypesQuery,
  useGetAllUnitsOfMeasureQuery,
  useGetAllValueTypesQuery,
  useGetAssetMeasurementByIdQuery,
} from '~/redux/api/measuresApi';
import { getMainPanel } from '~/redux/selectors/dashboardSelectors';
import { getSelectedViewMeasureId } from '~/redux/selectors/treeSelectors';
import { AlertMessage } from '~/shared/forms/types';
import { Asset } from '~/types/asset';
import { Customer } from '~/types/customers';

const EditMeasurementContainer = ({
  customer,
  parentAsset,
  hideOptions,
}: {
  customer: Customer;
  parentAsset: Asset;
  hideOptions?: boolean;
}): JSX.Element => {
  const dispatch = useDispatch();
  const mainPanel = useSelector(getMainPanel);
  const SelectedViewMeasureId = useSelector(getSelectedViewMeasureId);

  const measurementId = SelectedViewMeasureId.split(':');

  const { data: measurement, isLoading: isAssetDataLoading } = useGetAssetMeasurementByIdQuery(
    {
      customerId: customer?.id ?? 0,
      assetId: Number(measurementId[1]),
      assetMeasurementId: Number(measurementId[2]),
    },
    {
      //   skip: !customer?.id || mainPanel === 'newMeasure', // In RTK Query, the option is `skip` instead of `enabled`
    },
  );
  const { data: measurementTypeList } = useGetAllMeasureTypesQuery();
  const { data: dataTypeList } = useGetAllDataTypesQuery();
  const { data: valueTypeList } = useGetAllValueTypesQuery();
  const { data: locationList } = useGetAllLocationsQuery();
  const { data: datasourceList } = useGetAllDatasourcesQuery({});
  const [calcEngine, setCalcEngine] = useState<boolean>(false);
  const [factor, setFactor] = useState<boolean>(false);
  const [selectedMeasurementTypeId, setSelectedMeasurementTypeId] = useState<number>(NaN);

  const { data: unitsOfMeasure, isSuccess: success } = useGetAllUnitsOfMeasureQuery(
    {
      measurementTypeId: selectedMeasurementTypeId,
    },
    {
      skip: isNaN(selectedMeasurementTypeId),
    },
  );

  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const assetPath = useFetchAssetPath(parentAsset);
  const [
    editMeasurement,
    { data: editAssetMeasurement, isSuccess: successFull, error: errorData, isError: haveError },
  ] = useEditMeasureMutation();
  useEffect(() => {
    if (successFull) {
      setAlertMessage({
        message: `Measure updated successfully!`,
        severity: 'success',
      });

      dispatch(assetsApi.util.invalidateTags([{ type: 'Asset', id: parentAsset.id }]));
      dispatch(assetsApi.util.invalidateTags([{ type: 'Asset' }]));
      dispatch(measuresApi.util.invalidateTags(['Measure']));
    }
    if (haveError && errorData) {
      const err = errorData as CustomError;

      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [parentAsset.id, successFull, haveError, errorData, editAssetMeasurement]);
  return (
    <>
      {hideOptions === undefined ||
        (hideOptions === false && <Typography variant="h4">Edit measurement</Typography>)}
      {isAssetDataLoading ? (
        <CircularProgress />
      ) : (
        <>
          {parentAsset && !calcEngine && !factor && (
            <Typography variant="subtitle1">
              Parent: {assetPath === '' ? parentAsset.tag : assetPath}
            </Typography>
          )}
          <EditMeasurementForm
            calcEngine={calcEngine}
            setCalcEngine={setCalcEngine}
            factor={factor}
            setFactor={setFactor}
            parentAsset={parentAsset}
            measurementId={measurementId}
            loading={isAssetDataLoading}
            assetPath={assetPath}
            defaultValues={
              isAssetDataLoading ? undefined : (measurement as EditAssetMeasurementForm)
            }
            onValidSubmit={async (assetMeasurement: EditAssetMeasurementForm) => {
              const assetId = Number(measurementId[1]);
              assetMeasurement.tag =
                (assetPath === '' ? parentAsset.tag : assetPath) + '\\' + assetMeasurement.tag;
              await editMeasurement({
                customerId: customer.id,
                assetId: assetId.toString(),
                measId: measurementId[2],
                editAssetMeasurement: assetMeasurement as EditAssetMeasurementForm,
              });
            }}
            measurementTypeList={measurementTypeList ?? []}
            dataTypeList={dataTypeList ?? []}
            valueTypeList={valueTypeList ?? []}
            unitOfMeasureList={unitsOfMeasure ?? []}
            locationList={locationList?.items ?? []}
            datasourceList={datasourceList?.items ?? []}
            alertMessage={alertMessage}
            selectedMeasurementTypeId={selectedMeasurementTypeId}
            writeback={measurement?.writeback ?? false}
            onMeasurementTypeIdChange={(measurementTypeId) =>
              setSelectedMeasurementTypeId(measurementTypeId)
            }
          />
        </>
      )}
    </>
  );
};

export default EditMeasurementContainer;
