import { Box } from '@mui/material';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import { Data, PlotData, PlotMouseEvent } from 'plotly.js';
import { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import MapWidgetDialog from '~/components/MapWidget/MapWidgetDialog';
import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import Loader from '~/components/common/Loader';
import NoMeasureSelected from '~/components/common/NoMeasureSelected';
import { useFetchMapData } from '~/hooks/useFetchMapData';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getZoomEnabled } from '~/redux/selectors/dashboardSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { MapWidget } from '~/types/widgets';
const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });

type MapWidgetContainerProps = {
  id: string;
  settings: MapWidget;
};
const hasNoMeasureSelected = (settings: MapWidget) => {
  if (
    settings.mode === 'template' &&
    (!settings.markers?.length ||
      settings.markers.every((marker) => marker.selectedTitles.length === 0))
  ) {
    return true;
  }
  return (
    settings.mode === 'dashboard' &&
    (!settings.markers?.length ||
      settings.markers.every((marker) => marker.assetMeasures.length === 0))
  );
};
const MapWidgetContainer = ({ id, settings }: MapWidgetContainerProps) => {
  const containerRef = useRef(null);
  const router = useRouter();
  const dispatch = useDispatch();
  const activeCustomer = useSelector(getActiveCustomer);
  const enabledZoom = useSelector(getZoomEnabled);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const { data, layout, isLoading, successAndFailedMeasurements } = useFetchMapData(settings);
  const [mapData, setMapData] = useState<PlotData | null>(null);
  useEffect(() => {
    const showMapData = data.find((item: Data) => 'value' in item && item.value === mapData?.value);
    if (showMapData) {
      setMapData(showMapData as PlotData);
    }
  }, [data]);
  useEffect(() => {
    if (containerRef.current) {
      const updateDimensions = () => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        const newWidth = containerRef.current?.offsetWidth;
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        const newHeight = containerRef.current?.offsetHeight;

        // Only update state if dimensions have changed
        if (dimensions.width !== newWidth || dimensions.height !== newHeight) {
          setDimensions({
            width: newWidth,
            height: newHeight,
          });
        }
      };

      const resizeObserver = new ResizeObserver(() => updateDimensions());
      resizeObserver.observe(containerRef.current);

      // Initial update
      updateDimensions();

      return () => {
        if (containerRef.current) {
          resizeObserver.unobserve(containerRef.current);
        }
      };
    }
  }, []);
  const noMeasuresSelected = hasNoMeasureSelected(settings);
  return (
    <CommonWidgetContainer
      id={id}
      settings={settings}
      successAndFailedMeasurements={successAndFailedMeasurements}
      widgetName="Map"
      widgetContent={
        <Box
          sx={{
            height: '100%',
            width: '100%',
          }}
          ref={containerRef}
        >
          {noMeasuresSelected && <NoMeasureSelected />}
          {isLoading ? <Loader /> : <></>}
          {settings.markers?.length > 0 && (
            <Box
              sx={{
                display: 'flex',
                height: '100%',
              }}
            >
              <Box sx={{ height: '100%', width: '100%' }}>
                <Plot
                  className="map-widget-container"
                  data={data}
                  useResizeHandler={true}
                  onClick={(event: Readonly<PlotMouseEvent>) => {
                    const point = event.points[0];
                    setMapData(point.data);
                    const dashboard = settings.markers[event.points[0].data.value].dashboard;
                    if (dashboard !== null) {
                      dispatch(
                        dashboardSlice.actions.setDashboardCrumb({
                          dashboardId: dashboard.id,
                          title: dashboard.title,
                        }),
                      );
                      dispatch(dashboardSlice.actions.setCurrentDashboardId(dashboard.id));
                      dispatch(dashboardSlice.actions.setCurrentDashboardTitle(dashboard.title));
                      router.push(`/customer/${activeCustomer?.id}/dashboard/${dashboard.id}`);
                    }
                  }}
                  style={{ width: '100%', height: '100%', padding: 0 }} // Remove padding
                  layout={{
                    autosize: true,
                    hovermode: 'closest',
                    mapbox: {
                      style: 'open-street-map',
                      center: {
                        lat: -0.257195,
                        lon: -51.581861,
                      },
                      zoom: 10,
                    },
                    ...layout,
                  }}
                  config={{
                    responsive: true,
                    showTips: true,
                    displaylogo: false,
                    displayModeBar: false, // This will hide the entire mode bar
                    modeBarButtonsToRemove: ['pan2d', 'lasso2d'],
                    scrollZoom: !enabledZoom,
                  }}
                />
              </Box>
            </Box>
          )}
        </Box>
      }
      settingsDialog={MapWidgetDialog}
      widgetType="map"
    />
  );
};
export default MapWidgetContainer;
