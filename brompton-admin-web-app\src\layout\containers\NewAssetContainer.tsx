import { Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import NewAssetForm from '~/components/dashboard/NewAssetForm';
import { CustomError } from '~/errors/CustomerErrorResponse';
import {
  useCreateAssetMutation,
  useGetAllBackOfficeAssetTypesQuery,
  useGetAllTimeZonesQuery,
} from '~/redux/api/assetsApi';
import { AlertMessage } from '~/shared/forms/types';
import { Asset, AssetTypeOption } from '~/types/asset';
import { Customer } from '~/types/customers';
import {
  assetTypePathMapper,
  assetTypePathMapperFilterTemplates,
} from '~/utils/mappers/asset-type-mapper';

export default function NewAssetContainer({
  customer,
  parentAsset,
}: {
  customer: Customer;
  parentAsset?: Asset;
}): JSX.Element {
  const {
    data: assetTypeListData,
    status,
    isSuccess: isSuccessfullBackOffieAssetTypes,
  } = useGetAllBackOfficeAssetTypesQuery();
  const [assetTypesWithPath, setAssetTypesWithPath] = useState<AssetTypeOption[]>([]);
  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);

  const { data: timeZoneListData } = useGetAllTimeZonesQuery();
  // on asset type list loaded
  useEffect(() => {
    if (isSuccessfullBackOffieAssetTypes && assetTypeListData) {
      setAssetTypesWithPath(
        assetTypePathMapper(
          assetTypeListData.map((item) => ({
            ...item,
            name: item.name,
            id: item.id,
          })),
        ),
      );
    }
  }, [assetTypeListData, isSuccessfullBackOffieAssetTypes, status]);

  const [createAsset, { data: asset, isSuccess: isCreateAssetSuccessFull, isError, error }] =
    useCreateAssetMutation();

  useEffect(() => {
    if (isCreateAssetSuccessFull && asset) {
      setAlertMessage({
        message: `Asset "${asset.id}" created successfully!`,
        severity: 'success',
      });
    }
    if (isError && error) {
      const err = error as CustomError;
      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [asset, error, isError, isCreateAssetSuccessFull]);

  return (
    <>
      <Typography variant="h4">Create new asset</Typography>
      {parentAsset && <Typography variant="subtitle1">Parent: {parentAsset.tag}</Typography>}
      <NewAssetForm
        assetTypesWithPath={assetTypesWithPath}
        timeZoneList={timeZoneListData ?? []}
        loading={false}
        alertMessage={alertMessage}
        onValidSubmit={async (asset) => await createAsset({ customerId: customer.id, asset })}
        parentId={parentAsset ? parentAsset.id : undefined}
        parentAsset={parentAsset}
      />
    </>
  );
}
