import { Typography } from '@mui/material';
import { ThunkDispatch } from '@reduxjs/toolkit';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import NewMeasurementForm from '~/components/dashboard/NewMeasurementForm';
import { CustomError } from '~/errors/CustomerErrorResponse';
import useFetchAssetPath from '~/hooks/useFetchAssetPath';
import { EditAssetMeasurementForm } from '~/measurements/domain/types';
import { assetsApi } from '~/redux/api/assetsApi';
import {
  measuresApi,
  useCreateMeasurementMutation,
  useEditMeasureMutation,
  useGetAllDataTypesQuery,
  useGetAllDatasourcesQuery,
  useGetAllLocationsQuery,
  useGetAllMeasureTypesQuery,
  useGetAllUnitsOfMeasureQuery,
  useGetAllValueTypesQuery,
} from '~/redux/api/measuresApi';
import { getCustomerId } from '~/redux/selectors/customerSelectors';
import { getMainPanel } from '~/redux/selectors/dashboardSelectors';
import { getSelectedViewMeasureId } from '~/redux/selectors/treeSelectors';
import { RootState } from '~/redux/store';
import { AlertMessage } from '~/shared/forms/types';
import { Asset } from '~/types/asset';
import { Customer } from '~/types/customers';

export default function NewMeasurementContainer({
  customer,
  parentAsset,
}: {
  customer: Customer;
  parentAsset: Asset;
}): JSX.Element {
  const customerId = useSelector(getCustomerId);
  const dispatch = useDispatch<ThunkDispatch<RootState, any, any>>();
  const mainPanel = useSelector(getMainPanel);
  const SelectedViewMeasureId = useSelector(getSelectedViewMeasureId);
  const measurementId = SelectedViewMeasureId.split(':');
  const { data: measurementTypeList } = useGetAllMeasureTypesQuery();
  const { data: dataTypeList } = useGetAllDataTypesQuery();
  const { data: valueTypeList } = useGetAllValueTypesQuery();
  const { data: locationList } = useGetAllLocationsQuery();
  const { data: datasourceList } = useGetAllDatasourcesQuery({});
  const [selectedMeasurementTypeId, setSelectedMeasurementTypeId] = useState<number>(NaN);
  const [calcEngine, setCalcEngine] = useState<boolean>(false);
  const [factor, setFactor] = useState<boolean>(false);
  const { data: unitsOfMeasure } = useGetAllUnitsOfMeasureQuery(
    {
      measurementTypeId: selectedMeasurementTypeId,
    },
    {
      skip: isNaN(selectedMeasurementTypeId),
    },
  );

  const [alertMessage, setAlertMessage] = useState<AlertMessage | undefined>(undefined);
  const [createMeasurement, { data: assetMeasurement, isSuccess, error, isError, isLoading }] =
    useCreateMeasurementMutation();
  const [editMeasurement] = useEditMeasureMutation();

  useEffect(() => {
    if (isSuccess && assetMeasurement) {
      setAlertMessage({
        message: `Asset measurement "${assetMeasurement.id}" with measurement "${assetMeasurement.measurementId}" created successfully!`,
        severity: 'success',
      });

      dispatch(assetsApi.util.invalidateTags([{ type: 'Asset', id: parentAsset.id }]));
      dispatch(assetsApi.util.invalidateTags([{ type: 'Asset' }]));
      dispatch(measuresApi.util.invalidateTags(['Measure']));
    }

    if (isError && error) {
      const err = error as CustomError;
      setAlertMessage({ message: err.data.message ?? 'Server error', severity: 'error' });
    }
  }, [assetMeasurement, error, isError, isSuccess, parentAsset.id]);
  const assetPath = useFetchAssetPath(parentAsset);

  return (
    <>
      {!calcEngine ? (
        <>
          {mainPanel === 'editMeasure' ? (
            <Typography variant="h4">Edit measurement</Typography>
          ) : (
            <Typography variant="h4">Create new measurement</Typography>
          )}
        </>
      ) : null}
      {!calcEngine && !factor && (
        <Typography variant="subtitle1">
          Parent: {assetPath === '' ? parentAsset.tag : assetPath}
        </Typography>
      )}

      <NewMeasurementForm
        loading={isLoading}
        measurementTypeList={measurementTypeList ?? []}
        dataTypeList={dataTypeList ?? []}
        valueTypeList={valueTypeList ?? []}
        unitOfMeasureList={unitsOfMeasure ?? []}
        locationList={locationList?.items ?? []}
        datasourceList={datasourceList?.items ?? []}
        alertMessage={alertMessage}
        calcEngine={calcEngine}
        setCalcEngine={setCalcEngine}
        factor={factor}
        setFactor={setFactor}
        parentAsset={parentAsset}
        assetPath={assetPath}
        onMeasurementTypeIdChange={(measurementTypeId) =>
          setSelectedMeasurementTypeId(measurementTypeId)
        }
        onValidSubmit={async (assetMeasurement) => {
          // if (assetMeasurement.tag && assetMeasurement.tag.includes(':')) {
          //   assetMeasurement.tag = `${assetMeasurement.tag.split(':')[1]}`;
          // }
          assetMeasurement.tag =
            (assetPath === '' ? parentAsset.tag : assetPath) + '\\' + assetMeasurement.tag;
          if (mainPanel === 'editMeasure') {
            await editMeasurement({
              customerId: customer.id,
              assetId: parentAsset.id.toString(),
              measId: measurementId[2],
              editAssetMeasurement: assetMeasurement as unknown as EditAssetMeasurementForm,
            });
          } else {
            await createMeasurement({
              customerId: customer.id,
              assetId: parentAsset.id,
              assetMeasurement,
            });
          }
        }}
      />
    </>
  );
}
