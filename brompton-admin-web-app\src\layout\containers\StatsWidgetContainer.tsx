import { Box, Card, CardContent, Typography, useMediaQuery } from '@mui/material';
import { useSelector } from 'react-redux';
import { StatsWidgetSettingDialog } from '~/components/ChartSettings/StatsWidgetSettingDialog';
import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import Loader from '~/components/common/Loader';
import { NoDataFound } from '~/components/common/NoDataFound';
import NoMeasureSelected from '~/components/common/NoMeasureSelected';
import { useFetchStatsData } from '~/hooks/useFetchStatsData';
import { getThousandSeparator } from '~/redux/selectors/userPreferences';
import { StatsWidget } from '~/types/widgets';
import { formatMetricLabel, formatNumber, hasNoMeasureSelected, roundNumber } from '~/utils/utils';

type StatsWidgetContainerProps = {
  id: string;
  settings: StatsWidget;
};

export function StatsWidgetContainer({ id, settings }: StatsWidgetContainerProps) {
  const { isLoading, stats, isError, successAndFailedMeasurements } = useFetchStatsData(
    id,
    settings,
  );
  const { showMin, showMax, showAvg, showDelta, title, showCurrent, showSum } = settings;
  const defaultTitle = formatMetricLabel(settings.title.value);
  const mobile = useMediaQuery('@media (max-width: 600px)');
  const thousandSeparator = useSelector(getThousandSeparator);
  return (
    <CommonWidgetContainer
      id={id}
      successAndFailedMeasurements={successAndFailedMeasurements}
      widgetType="stats"
      widgetName="Stats"
      removedResults={stats?.data !== undefined ? [stats.data] : undefined}
      settings={settings}
      settingsDialog={StatsWidgetSettingDialog}
      widgetContent={
        <>
          {hasNoMeasureSelected(settings) ? (
            <NoMeasureSelected />
          ) : (
            <>
              {isLoading && !stats && <Loader />}

              {/* {isError && !stats && <Error />} */}

              {stats && (
                <Box
                  id={'stats-widget'}
                  sx={{
                    display: 'flex',
                    height: '100%',
                    flexDirection: 'column',
                    alignItems: 'center',
                  }}
                >
                  {title.isVisible && (
                    <Box
                      sx={{
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'center',
                        marginY: '10px',
                        mt: 3,
                      }}
                    >
                      <Typography
                        variant="h5"
                        sx={{
                          fontSize: title.isVisible ? title.fontSize + 'px' : undefined,
                          fontWeight: title.isVisible ? title.fontWeight : undefined,
                          color: title.isVisible ? title.color : undefined,
                        }}
                        component="div"
                      >
                        {title.isVisible ? title.value : defaultTitle}
                        {stats?.unit?.name && ` (${stats.unit.name})`}
                      </Typography>
                    </Box>
                  )}

                  <Box
                    sx={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      justifyContent: 'center',
                      alignItems: 'center',
                      height: '100%',
                      width: '100%',
                      paddingX: mobile ? 1 : 3,
                      overflow: 'hidden',
                    }}
                  >
                    {showMin && (
                      <Card
                        variant="outlined"
                        sx={{
                          marginRight: '10px',
                          padding: 0,
                          border: !settings.minBorder ? 'unset' : undefined,
                          minWidth: 60,
                        }}
                      >
                        <CardContent
                          sx={{
                            padding: '8px',
                            paddingBottom: '8px !important',
                            color: settings.min.color,
                          }}
                        >
                          <Typography
                            variant="h5"
                            component="div"
                            sx={{
                              color: settings.min.color,
                              fontSize: settings.min.fontSize + 'px',
                              fontWeight: settings.min.fontWeight,
                              textAlign: 'center',
                            }}
                          >
                            {thousandSeparator
                              ? formatNumber(Number(stats.min))
                              : roundNumber(Number(stats.min))}
                          </Typography>
                          <Typography
                            sx={{
                              fontSize: 14,
                              textAlign: 'center',
                              display: settings.showMinLable ? 'block' : 'none',
                              color: settings.min.color,
                            }}
                            color="text.secondary"
                            gutterBottom
                          >
                            {settings.min.label ?? 'Min'}
                          </Typography>
                        </CardContent>
                      </Card>
                    )}
                    {showMax && (
                      <Card
                        variant="outlined"
                        sx={{
                          marginRight: '10px',
                          padding: 0,
                          border: !settings.maxBorder ? 'unset' : undefined,
                          minWidth: 60,
                        }}
                      >
                        <CardContent
                          sx={{
                            padding: '8px',
                            paddingBottom: '8px !important',
                            color: settings.max.color,
                          }}
                        >
                          <Typography
                            variant="h5"
                            component="div"
                            sx={{
                              color: settings.max.color,
                              fontSize: settings.max.fontSize + 'px',
                              fontWeight: settings.max.fontWeight,
                              textAlign: 'center',
                            }}
                          >
                            {thousandSeparator
                              ? formatNumber(Number(stats.max))
                              : roundNumber(Number(stats.max))}
                          </Typography>
                          <Typography
                            sx={{
                              fontSize: 14,
                              textAlign: 'center',
                              display: settings.showMaxLabel ? 'block' : 'none',
                              color: settings.max.color,
                            }}
                            color="text.secondary"
                            gutterBottom
                          >
                            {settings.max.label ?? 'Max'}
                          </Typography>
                        </CardContent>
                      </Card>
                    )}
                    {showAvg && (
                      <Card
                        variant="outlined"
                        sx={{
                          marginRight: '10px',
                          padding: 0,
                          border: !settings.avgBorder ? 'unset' : undefined,
                          minWidth: 60,
                        }}
                      >
                        <CardContent
                          sx={{
                            padding: '8px',
                            paddingBottom: '8px !important',
                            color: settings.avg.color,
                          }}
                        >
                          <Typography
                            variant="h5"
                            component="div"
                            sx={{
                              color: settings.avg.color,
                              fontSize: settings.avg.fontSize + 'px',
                              fontWeight: settings.avg.fontWeight,
                              textAlign: 'center',
                            }}
                          >
                            {thousandSeparator
                              ? formatNumber(Number(stats.avg))
                              : roundNumber(Number(stats.avg))}
                          </Typography>
                          <Typography
                            sx={{
                              fontSize: 14,
                              textAlign: 'center',
                              display: settings.showAvgLabel ? 'block' : 'none',
                              color: settings.avg.color,
                            }}
                            color="text.secondary"
                            gutterBottom
                          >
                            {settings.avg.label ?? 'Avg'}
                          </Typography>
                        </CardContent>
                      </Card>
                    )}
                    {showDelta && (
                      <Card
                        variant="outlined"
                        sx={{
                          marginRight: '10px',
                          padding: 0,
                          border: !settings.deltaBorder ? 'unset' : undefined,
                          minWidth: 60,
                        }}
                      >
                        <CardContent
                          sx={{
                            padding: '8px',
                            paddingBottom: '8px !important',
                            color: settings.delta.color,
                          }}
                        >
                          <Typography
                            variant="h5"
                            component="div"
                            sx={{
                              color: settings.delta.color,
                              fontSize: settings.delta.fontSize + 'px',
                              fontWeight: settings.delta.fontWeight,
                              textAlign: 'center',
                            }}
                          >
                            {thousandSeparator
                              ? formatNumber(Number(stats.max) - Number(stats.min))
                              : roundNumber(Number(stats.max) - Number(stats.min))}
                          </Typography>
                          <Typography
                            sx={{
                              fontSize: 14,
                              textAlign: 'center',
                              display: settings.showDeltaLabel ? 'block' : 'none',
                              color: settings.delta.color,
                            }}
                            color="text.secondary"
                            gutterBottom
                          >
                            {settings.delta.label ?? 'Delta'}
                          </Typography>
                        </CardContent>
                      </Card>
                    )}
                    {showSum && (
                      <Card
                        variant="outlined"
                        sx={{
                          marginRight: '10px',
                          padding: 0,
                          border: !settings.sumBorder ? 'unset' : undefined,
                          minWidth: 60,
                          textAlign: 'center',
                        }}
                      >
                        <CardContent
                          sx={{
                            padding: '8px',
                            paddingBottom: '8px !important',
                            color: settings.sum.color,
                          }}
                        >
                          <Typography
                            variant="h5"
                            component="div"
                            sx={{
                              color: settings.sum.color,
                              fontSize: settings.sum.fontSize + 'px',
                              fontWeight: settings.sum.fontWeight,
                              textAlign: 'center',
                            }}
                          >
                            {thousandSeparator
                              ? formatNumber(Number(stats.sum))
                              : roundNumber(Number(stats.sum))}
                          </Typography>
                          <Typography
                            sx={{
                              fontSize: 14,
                              textAlign: 'center',
                              display: settings.showSumLabel ? 'block' : 'none',
                              color: settings.sum.color,
                            }}
                            color="text.secondary"
                            gutterBottom
                          >
                            {settings.sum.label ?? 'Sum'}
                          </Typography>
                        </CardContent>
                      </Card>
                    )}
                    {showCurrent && (
                      <Card
                        variant="outlined"
                        sx={{
                          marginRight: '10px',
                          padding: 0,
                          border: !settings.showCurrentBorder ? 'unset' : undefined,
                          minWidth: 60,
                        }}
                      >
                        <CardContent
                          sx={{
                            padding: '8px',
                            paddingBottom: '8px !important',
                            color: settings.current.color,
                          }}
                        >
                          <Typography
                            variant="h5"
                            component="div"
                            sx={{
                              color: settings.current.color,
                              fontSize: settings.current.fontSize + 'px',
                              fontWeight: settings.current.fontWeight,
                              textAlign: 'center',
                            }}
                          >
                            {thousandSeparator
                              ? formatNumber(Number(stats.current))
                              : roundNumber(Number(stats.current))}
                          </Typography>
                          <Typography
                            sx={{
                              fontSize: 14,
                              textAlign: 'center',
                              display: settings.showCurrentLabel ? 'block' : 'none',
                              color: settings.current.color,
                            }}
                            color="text.secondary"
                            gutterBottom
                          >
                            {settings.current.label ?? 'Last Value'}
                          </Typography>
                        </CardContent>
                      </Card>
                    )}
                  </Box>
                </Box>
              )}
              {!isLoading && !stats && !isError && (
                <Box
                  sx={{
                    position: 'absolute',
                    height: '100%',
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <NoDataFound />
                </Box>
              )}
            </>
          )}
        </>
      }
    />
  );
}
