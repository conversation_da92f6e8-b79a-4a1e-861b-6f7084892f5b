import CalculateOutlinedIcon from '@mui/icons-material/CalculateOutlined';
import MonitorHeartOutlinedIcon from '@mui/icons-material/MonitorHeartOutlined';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import AssetsTree from '~/components/AssetsTree';
import {
  AnalyticsIcon,
  BuildingIcon,
  TechoMeterIcon,
  UserCircleIcon,
} from '~/components/AssetsTree/RemoteSvgIcon';
import { Tree, TreeNode } from '~/components/AssetsTree/tree';
import { useFetchAllMeasures } from '~/hooks/useFetchAllMeasures';
import { Role, useRolePermission } from '~/hooks/useRolePermission';
import { AssetIdWithMeasurements, AssetMeasurement } from '~/measurements/domain/types';
import { useGetAllAssetQuery } from '~/redux/api/assetsApi';
import { useGetAllDatasourcesQuery, useGetAllMeasureTypesQuery } from '~/redux/api/measuresApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import {
  getExpandedAssetIds,
  getExpandedNodeIds,
  getSelectedNodeIds,
} from '~/redux/selectors/treeSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { normalizeAsset } from '~/utils/mappers/asset-normalizer';
import { AssetWithMeasurements, mapAssetTree } from '~/utils/mappers/asset-tree-mapper';

export type TreeNodeType = 'customer' | 'asset' | 'measurement' | 'metric';
export type SelectType = 'select' | 'unselect';
export type SelectEventProps = {
  eventType: SelectType;
  currentNode: TreeNode;
  selectedNodeIds: string[];
  type: TreeNodeType;
};

type TreePanelContainerProps = {
  onNewRootAsset: (nodeId: string) => unknown;
  onNewChildAsset: (nodeId: string) => unknown;
  onNewAssetTemplate: (nodeId: string) => unknown;
  onAssetDetails: (nodeId: string) => unknown;
  onAssetEdit: (nodeId: string) => unknown;
  onNewMeasurement: (nodeId: string) => unknown;
  onMeasurementEdit: (nodeId: string) => unknown;
  onMeasurementView: (nodeId: string) => unknown;
  onDeleteMeasurement: (parentAssetId: number, assetMeasurementId: number) => unknown;
  onViewMeasurement: (nodeId: string) => unknown;
  onEditMeasurement: (nodeId: string) => unknown;
  onDeleteAsset: (assetId: number) => unknown;
  onAssetSelection: (even: SelectEventProps) => unknown;
  onMetricSelection: (even: SelectEventProps) => unknown;
  onMetricAlertCreate: (nodeId: string) => unknown;
  onViewAlerts: (nodeId: string) => unknown;
  onDashboardTemplate: (nodeId: string) => unknown;
  onNewTemplateFromAsset: (nodeId: string) => unknown;
};

const TreePanelContainer = ({
  onNewRootAsset,
  onNewChildAsset,
  onNewAssetTemplate,
  onAssetDetails,
  onAssetEdit,
  onNewMeasurement,
  onMeasurementEdit,
  onMeasurementView,
  onDeleteMeasurement,
  onViewMeasurement,
  onEditMeasurement,
  onDeleteAsset,
  onAssetSelection,
  onMetricSelection,
  onMetricAlertCreate,
  onViewAlerts,
  onDashboardTemplate,
  onNewTemplateFromAsset,
}: TreePanelContainerProps): JSX.Element => {
  const dispatch = useDispatch();
  const { hasDashboardPermission, hasPermission } = useRolePermission();
  const customer = useSelector(getActiveCustomer);
  const customerId = customer?.id ?? 0;
  const expandedNodeIds = useSelector(getExpandedNodeIds);
  const selectedNodeIds = useSelector(getSelectedNodeIds);
  const selectedAssetIds = useSelector(getExpandedAssetIds);
  const { data: datasourceList } = useGetAllDatasourcesQuery({});

  const { data: assetData } = useGetAllAssetQuery(
    { customerId, parentIds: selectedAssetIds },
    {
      skip: !customerId,
      refetchOnMountOrArgChange: true,
    },
  );

  const measurementRequests = useFetchAllMeasures(customerId, selectedAssetIds);

  const { data: measurementTypes } = useGetAllMeasureTypesQuery();

  const measurementTypesMap = useMemo(() => {
    return new Map(measurementTypes ? measurementTypes.map((type) => [type.id, type.name]) : []);
  }, [measurementTypes]);

  const assetMeasurementsMap = useMemo(
    () =>
      new Map(
        measurementRequests
          .filter(
            (measurementData): measurementData is AssetIdWithMeasurements =>
              measurementData !== undefined,
          )
          .map(
            (measurement) =>
              [measurement.assetId, measurement.measurements] as [number, AssetMeasurement[]],
          ),
      ),
    [measurementRequests],
  );

  const [cachedData, setCachedData] = useState<AssetWithMeasurements[]>([]);

  useEffect(() => {
    if (assetData && assetData.length > 0) {
      setCachedData(
        assetData.map((asset) =>
          normalizeAsset(measurementTypesMap, asset, assetMeasurementsMap.get(asset.id) ?? []),
        ),
      );
    } else {
      setCachedData([]);
    }
  }, [assetData, assetMeasurementsMap, measurementTypesMap]);

  if (!customer) return <></>;

  const treeNode = mapAssetTree(customer, cachedData);

  const findMetricById = (data: Tree, metricId: string): Tree | null => {
    if (data.id === metricId) {
      return data;
    }

    if (data.children && data.children.length > 0) {
      for (const child of data.children) {
        const result = findMetricById(child, metricId);
        if (result) {
          return result;
        }
      }
    }

    return null;
  };

  return (
    <>
      <AssetsTree
        selectedNodeIds={selectedNodeIds}
        tree={treeNode}
        onNodeToggle={(nodeIds: string[]) => {
          dispatch(dashboardSlice.actions.setExpandedNodeIds(nodeIds));
          dispatch(dashboardSlice.actions.setIsDirty(true));
        }}
        expandedNodeIds={expandedNodeIds}
        onSelect={(evenType, currentNode, selectedNodeIds) => {
          let nodeType: TreeNodeType;
          if (currentNode.type === 'company') {
            nodeType = 'customer';
          } else if (currentNode.type === 'activo') {
            nodeType = 'asset';
          } else if (currentNode.type === 'medicion') {
            nodeType = 'measurement';
          } else {
            nodeType = 'metric';
          }

          const event: SelectEventProps = {
            eventType: evenType as SelectType,
            currentNode,
            selectedNodeIds,
            type: nodeType,
          };

          if (nodeType === 'metric') {
            onMetricSelection(event);
          } else {
            onAssetSelection(event);
          }
        }}
        nodeTypeConfigs={{
          customer: {
            icon: () => <UserCircleIcon />,
            contextMenuActions: [
              ...(hasDashboardPermission('asset.create', Role.POWER_USER)
                ? [{ label: 'New root asset', onClick: (nodeId: string) => onNewRootAsset(nodeId) }]
                : []),
            ],
            selectable: false,
            expandable: true,
            showCheckbox: false,
          },
          asset: {
            icon: () => <BuildingIcon />,
            contextMenuActions: [
              ...(hasDashboardPermission('asset.update', Role.POWER_USER)
                ? [
                    {
                      label: 'Edit',
                      onClick: (nodeId: string) => onAssetEdit(nodeId),
                    },
                  ]
                : []),
              { label: 'Details', onClick: (nodeId) => onAssetDetails(nodeId) },
              ...(hasDashboardPermission('asset.create', Role.POWER_USER)
                ? [
                    {
                      label: 'New child asset',
                      onClick: (nodeId: string) => onNewChildAsset(nodeId),
                    },
                  ]
                : []),
              ...(hasDashboardPermission('measurement.create', Role.POWER_USER)
                ? [
                    {
                      label: 'New dashboard from template',
                      onClick: (nodeId: string) => onDashboardTemplate(nodeId),
                    },
                  ]
                : []),
              ...(hasDashboardPermission('measurement.create', Role.POWER_USER)
                ? [
                    {
                      label: 'New measurement',
                      onClick: (nodeId: string) => onNewMeasurement(nodeId),
                    },
                  ]
                : []),
              {
                label: 'New Instance From Template',
                onClick: (nodeId) => onNewAssetTemplate(nodeId),
              },
              {
                label: 'New template from asset',
                onClick: (nodeId) => onNewTemplateFromAsset(nodeId),
              },
              ...(hasDashboardPermission('asset.delete', Role.POWER_USER)
                ? [
                    {
                      label: 'Delete',
                      onClick: (nodeId: string) => onDeleteAsset(Number(nodeId)),
                    },
                  ]
                : []),
            ],
            selectable: true,
            expandable: true,
            showCheckbox: false,
          },
          measurement: {
            icon: () => <TechoMeterIcon />,
            contextMenuActions: [],
            selectable: false,
            expandable: true,
            showCheckbox: false,
          },
          metric: {
            icon: (node) => {
              const metric = findMetricById(treeNode, node);
              if (metric) {
                if (
                  metric.datasourceId !== -1 &&
                  datasourceList &&
                  Array.isArray(datasourceList.items) &&
                  datasourceList.items.length >= 1
                ) {
                  const findDataSource = datasourceList.items.find(
                    (item) => item.id === metric.datasourceId,
                  );
                  if (findDataSource && findDataSource.name === 'Calculation') {
                    return <CalculateOutlinedIcon />;
                  }
                  if (findDataSource && findDataSource.name === 'TimeVaryingFactor') {
                    return <MonitorHeartOutlinedIcon sx={{ mr: 0.5 }} />;
                  }
                }
              }
              return <AnalyticsIcon />;
            },
            contextMenuActions: [
              {
                label: 'Details',
                onClick: (nodeId) => {
                  const measurementId = Number(nodeId.split(':')[2]);
                  const entry = Array.from(assetMeasurementsMap.entries()).find(
                    ([, measurements]) =>
                      measurements?.find((measurement) => measurement.id === measurementId),
                  );
                  if (entry) {
                    onViewMeasurement(nodeId);
                  }
                },
              },
              ...(hasDashboardPermission('measurement.delete', Role.POWER_USER)
                ? [
                    {
                      label: 'Delete',
                      onClick: (nodeId: string) => {
                        const measurementId = Number(nodeId.split(':')[2]);
                        const entry = Array.from(assetMeasurementsMap.entries()).find(
                          ([, measurements]) =>
                            measurements?.find((measurement) => measurement.id === measurementId),
                        );

                        if (entry) {
                          onDeleteMeasurement(entry[0], measurementId);
                        }
                      },
                    },
                  ]
                : []),

              ...(hasDashboardPermission('measurement.update', Role.POWER_USER)
                ? [
                    {
                      label: 'Edit',
                      onClick: (nodeId: string) => {
                        onEditMeasurement(nodeId);
                      },
                    },
                  ]
                : []),
              // ...(hasPermission('alert.create') && (globalAdmin || admin)
              //   ? [
              //       {
              //         label: 'Create Alert',
              //         onClick: (nodeId: string) => {
              //           const measurementId = Number(nodeId.split(':')[2]);
              //           const entry = Array.from(assetMeasurementsMap.entries()).find(
              //             ([, measurements]) =>
              //               measurements?.find((measurement) => measurement.id === measurementId),
              //           );
              //           onMetricAlertCreate(nodeId);
              //         },
              //       },
              //     ]
              //   : []),
              // {
              //   label: 'View Alerts',
              //   onClick: (nodeId) => {
              //     onViewAlerts(nodeId);
              //   },
              // },
            ],
            selectable: false, // hasDashboardPermission('measurement.selection', Role.POWER_USER),
            expandable: false,
            showCheckbox: false,
          },
        }}
      />
    </>
  );
};

export default TreePanelContainer;
