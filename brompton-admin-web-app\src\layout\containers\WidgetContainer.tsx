import { Box, Card, Typography } from '@mui/material';
import { ReactNode, useEffect, useState } from 'react';
import { DragOverEvent, Layout, Responsive, WidthProvider } from 'react-grid-layout';

import dynamic from 'next/dynamic';
import Loader from '~/components/common/Loader';
import AlertWidgetContainer from '~/components/dashboard/AlertWidget';
import KPIBarChartContainer from '~/components/dashboard/KPIBarChart/KPIBarChartContainer';
import KPIColorBoxContainer from '~/components/dashboard/KPIColorBox/KPIColorBoxContainer';
import KPICurrentWidgetContainer from '~/components/dashboard/KPICurrent/KPICurrentWidgetContainer';
import KPIPercentageContainer from '~/components/dashboard/KPIPercentage/KPIPercentageContainer';
import KPISparkLine from '~/components/dashboard/KPISparkLine/KPISparkLine';
import KPITableContainer from '~/components/dashboard/KPITable/KPITableContainer';
import KPIValueIndicatorContainer from '~/components/dashboard/KPIValueIndicator/KPIValueIndicatorContainer';
import NewWeatherContainer from '~/components/dashboard/Weather/NewWeatherContainer';
import ErrorBoundary from '~/errors/ErrorBoundry';
import { useDashboardWidgetContainer } from '~/hooks/useDashboardWidgetContainer';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useHasPowerUserAccess } from '~/hooks/useHasPowerUserAccess';
import ImageWidgetContainer from '~/layout/containers/ImageWidgetContainer';
import MapWidgetContainer from '~/layout/containers/MapWidgetContainer';
import { StatsWidgetContainer } from '~/layout/containers/StatsWidgetContainer';
import { ChartWidgetContainer } from '~/layout/containers/chart/ChartWidgetContainer';
import { Widget, WidgetType } from '~/types/widgets';
import { TitleWidgetContainer } from './TitleWidgetContainer';
import { RealTimeWidgetContainer } from './chart/RealTimeWidgetContainer';
import { TableWidgetContainer } from './chart/TableWidgetContainer';
import DashboardWidgetContainer from '~/components/dashboard/DashboardWidget';
import MultiPlotWidgetContainer from '~/components/dashboard/MultiPlotWidget';
const DiagramWidgetPanel = dynamic(() => import('~/components/JointJSWidget/JointJSWidget'), {
  ssr: false,
  loading() {
    return <Loader />;
  },
});
const ResponsiveGridLayout = WidthProvider(Responsive);
const WidgetCards = ({ children, type }: { children: ReactNode; type: WidgetType }) => {
  return (
    <Card
      variant="outlined"
      sx={{
        height: '100%',
        width: '100%',
        borderRadius: 2,
        marginRight: '10px',
        padding: 0,
        overflowY: type === 'dashboard-widget' ? 'scroll' : undefined,
        border: (theme) => `2px solid ${theme.palette.divider}`,
      }}
    >
      {children}
    </Card>
  );
};

type WidgetContainerProps = {
  isDashboardDetailsSuccess: boolean;
  dashboardId: number;
  widgets: Widget[];
  widgetLayout: Layout[];
  isLoading: boolean;
  isError: boolean;
};

export function WidgetContainer({
  dashboardId,
  isDashboardDetailsSuccess,
  widgets,
  widgetLayout,
  isLoading,
  isError,
}: WidgetContainerProps) {
  const { mounted, checkIsLayoutEditable, compactType, onDrop, onLayoutChange, resizeHandler } =
    useDashboardWidgetContainer({ widgetLayout });
  const [isChildWidget, setIsChildWidget] = useState<boolean>(false);

  useEffect(() => {
    const isChildWidget =
      Array.isArray(widgets) &&
      widgets.some((widget) => 'isChildWidget' in widget.settings && widget.settings.isChildWidget);
    setIsChildWidget(isChildWidget);
  }, [widgets]);

  const { admin, globalAdmin } = useHasAdminAccess();
  const powerUser = useHasPowerUserAccess();
  if (isLoading) return <Loader style={{ height: '100%' }} />;
  if (isError) return <Box>Error loading dashboard</Box>;
  // console.log(widgets);
  return (
    <>
      {isDashboardDetailsSuccess || dashboardId <= 0 ? (
        <>
          {widgets.length === 0 && (admin || globalAdmin || powerUser) && (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                height: 'inherit',
                alignItems: 'center',
                position: 'fixed',
                width: '-webkit-fill-available',
              }}
            >
              Please drop a widget here to get started.
            </Box>
          )}
          {widgets.length === 0 && !admin && !globalAdmin && !powerUser && (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                height: 'inherit',
                alignItems: 'center',
                position: 'fixed',
                width: '-webkit-fill-available',
              }}
            >
              We do not have any widgets added on dashboard.
            </Box>
          )}
          <ResponsiveGridLayout
            className="layout"
            style={{ height: '100%', marginTop: '40px' }}
            layouts={{ lg: widgetLayout }}
            breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
            cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
            rowHeight={40}
            isDraggable={true}
            isResizable={true}
            // isBounded={true}
            onDragStop={resizeHandler}
            onDrop={onDrop}
            draggableHandle=".drag-handle"
            droppingItem={{ i: 'a', w: 2, h: 2 }}
            resizeHandles={isChildWidget ? [] : ['s', 'w', 'e', 'n', 'sw', 'nw', 'se', 'ne']}
            onLayoutChange={onLayoutChange}
            isDroppable={true}
            // onResize={resizeHandler}
            onResizeStop={resizeHandler}
            onDropDragOver={(e: DragOverEvent) => {
              return {
                w: 6,
                h: 5,
              };
            }}
            useCSSTransforms={false}
            compactType={compactType}
            // This turns off rearrangement so items will not be pushed around.
            preventCollision={!compactType}
            measureBeforeMount={false}
          >
            {widgets.map(({ id, type, settings }) => {
              switch (type) {
                case 'alert-widget':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <AlertWidgetContainer id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'dashboard-widget':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <DashboardWidgetContainer id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'stats':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <StatsWidgetContainer id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'chart':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <ChartWidgetContainer id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );

                case 'table':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <TableWidgetContainer id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'title':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <TitleWidgetContainer id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'image':
                  return (
                    <div
                      key={id}
                      // className={`widget ${settings.isEditable ? '' : 'disabled-resize'}`}
                      className={checkIsLayoutEditable(id)}
                    >
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <ImageWidgetContainer id={id} settings={settings} imageType="image" />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'map':
                  return (
                    <div
                      key={id}
                      // className={`widget ${settings.isEditable ? '' : 'disabled-resize'}`}
                      className={checkIsLayoutEditable(id)}
                    >
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <MapWidgetContainer id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'kpi-sparkline':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <KPISparkLine id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'kpi-value-indicator':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <KPIValueIndicatorContainer id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'kpi-color-box':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <KPIColorBoxContainer id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'kpi-percentage':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <KPIPercentageContainer id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'kpi-table':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <KPITableContainer id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'Weather':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <NewWeatherContainer id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'image-stats':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <KPICurrentWidgetContainer id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'kpi-bar-chart':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <KPIBarChartContainer id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'real-time':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <RealTimeWidgetContainer
                            widgetId={id}
                            settings={settings}
                            widgets={widgets}
                          />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                case 'Diagram':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <DiagramWidgetPanel id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );

                case 'multi-plot':
                  return (
                    <div key={id} className={checkIsLayoutEditable(id)}>
                      <WidgetCards type={type}>
                        <ErrorBoundary>
                          <MultiPlotWidgetContainer id={id} settings={settings} />
                        </ErrorBoundary>
                      </WidgetCards>
                    </div>
                  );
                default:
                  return null;
              }
            })}
          </ResponsiveGridLayout>
        </>
      ) : (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            height: '100%',
            alignItems: 'center',
          }}
        >
          <Typography>No data to preview. Please select any measure from left panel.</Typography>
        </Box>
      )}
    </>
  );
}
