import Chart from '~/components/Chart';
import { useFetchBulletChartData } from '~/hooks/useFetchBulletChartData';

import { BulletChartWidget } from '~/types/widgets';

type BulletChartContainerProps = {
  id: string;
  settings: BulletChartWidget;
};
export function BulletChartContainer({ id, settings }: BulletChartContainerProps): JSX.Element {
  const { chartData, isLoading, layoutData, tsData, removedResults, successAndFailedMeasurements } =
    useFetchBulletChartData(id, settings);
  return (
    <Chart
      id={id}
      successAndFailedMeasurements={successAndFailedMeasurements}
      removedResults={removedResults ? [removedResults] : undefined}
      chartType="bullet"
      settings={settings}
      data={chartData}
      layout={layoutData}
      isLoading={isLoading}
      tsData={tsData}
      showSettings={true}
    />
  );
}
