import Chart from '~/components/Chart';
import { useFetchGaugeData } from '~/hooks/useFetchGaugeData';
import { GaugeChartWidget } from '~/types/widgets';

type BarChartContainerProps = {
  id: string;
  settings: GaugeChartWidget;
};
export function GaugeChartContainer({ id, settings }: BarChartContainerProps): JSX.Element {
  const { chartData, isLoading, layoutData, tsData, removedResults, successAndFailedMeasurements } =
    useFetchGaugeData(id, settings);

  return (
    <>
      <Chart
        removedResults={removedResults}
        id={id}
        successAndFailedMeasurements={successAndFailedMeasurements}
        chartType="indicator"
        settings={settings}
        data={chartData}
        layout={layoutData}
        isLoading={isLoading}
        tsData={tsData}
        showSettings={true}
      />
    </>
  );
}
