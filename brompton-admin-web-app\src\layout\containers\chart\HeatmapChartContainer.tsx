import Chart from '~/components/Chart';
import { useFetchHeatmapData } from '~/hooks/useFetchHeatMapData';
import { HeatmapChartWidget } from '~/types/widgets';

export type HeatmapChartContainerProps = {
  id: string;
  settings: HeatmapChartWidget;
};

export function HeatmapChartContainer({ id, settings }: HeatmapChartContainerProps): JSX.Element {
  const { isLoading, chartData, layoutData, successAndFailedMeasurements } = useFetchHeatmapData(
    id,
    settings,
  );
  return (
    <>
      <Chart
        id={id}
        successAndFailedMeasurements={successAndFailedMeasurements}
        chartType="heatmap"
        settings={settings}
        data={chartData}
        layout={layoutData}
        isLoading={isLoading}
        showSettings={true}
      />
    </>
  );
}
