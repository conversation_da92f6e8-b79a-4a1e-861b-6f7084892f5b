import dynamic from 'next/dynamic';
import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import Loader from '~/components/common/Loader';
import NoMeasureSelected from '~/components/common/NoMeasureSelected';
import RealTimeChartSettingsDialog from '~/components/RealTimeChartSettings/RealTimeChartSettingsDialog';
import { useRealTimeChartHook } from '~/hooks/useRealTimeChartHook';
import { RealTimeChart, Widget } from '~/types/widgets';

const Plot = dynamic(() => import('react-plotly.js'), { ssr: false });

type RealTimeWidgetContainerProps = {
  widgetId: string;
  settings: RealTimeChart;
  widgets: Widget[];
};

export const RealTimeWidgetContainer = ({
  widgetId,
  settings,
  widgets,
}: RealTimeWidgetContainerProps) => {
  const { data, revision, isLoading, realTimeChartName } = useRealTimeChartHook(
    settings.assetMeasure?.measureId[0],
    widgets,
    settings.retention,
    settings,
  );

  const sortedData: {
    x: number[];
    y: number[];
  } = {
    x: [],
    y: [],
  };

  if (data && data.x && data.y) {
    const combinedData = data.x.map((xValue, index) => ({
      x: xValue,
      y: data.y[index],
    }));
    combinedData.sort((a, b) => a.x - b.x); // Sort by x value
    sortedData.x = combinedData.map((item) => item.x);
    sortedData.y = combinedData.map((item) => item.y);
  }

  return (
    <>
      <CommonWidgetContainer
        id={widgetId}
        widgetType="real-time"
        widgetName="real-time"
        settings={settings}
        widgetContent={
          <>
            {settings.assetMeasure?.measureId.some((measure) => measure === '') ? (
              <NoMeasureSelected />
            ) : (
              <>
                {isLoading ? (
                  <Loader style={{ height: 'inherit' }} />
                ) : (
                  <Plot
                    data={[
                      {
                        x: sortedData.x,
                        y: sortedData.y,
                        type: 'scatter',
                        mode: 'lines+markers',
                        marker: { color: 'blue' },
                      },
                    ]}
                    layout={{
                      title: settings.title.isVisible ? settings.title.value : realTimeChartName,
                      autosize: true,
                      titlefont: {
                        color: settings.title.color,
                        size: settings.fontSize,
                      },
                      xaxis: { title: 'Time', type: 'date' },
                      yaxis: { title: 'Values' },
                    }}
                    style={{ width: '100%', height: '100%' }}
                    config={{
                      displayModeBar: false,
                    }}
                    useResizeHandler
                    revision={revision}
                  />
                )}
              </>
            )}
          </>
        }
        settingsDialog={RealTimeChartSettingsDialog}
      />
    </>
  );
};
