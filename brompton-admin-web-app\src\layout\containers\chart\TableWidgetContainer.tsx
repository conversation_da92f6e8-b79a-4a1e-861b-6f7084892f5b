import ArticleIcon from '@mui/icons-material/Article';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import { Box, Typography, useMediaQuery, useTheme } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import dayjs from 'dayjs';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { useSelector } from 'react-redux';
import * as XLSX from 'xlsx';
import CommonWidgetContainer from '~/components/common/CommonWidgetContainer';
import Error from '~/components/common/Error/Error';
import Loader from '~/components/common/Loader';
import NoMeasureSelected from '~/components/common/NoMeasureSelected';
import CustomNoRowsOverlay from '~/components/common/NoRowsOverlay';
import TableSettingDialog from '~/components/TableSettings/TableSettingsDialog';
import { useFetchTableData } from '~/hooks/useFetchTableData';
import { getDateTimeFormat } from '~/redux/selectors/userPreferences';
import { AssetMeasureOptions, TableWidget } from '~/types/widgets';
import { fortmatUTCDate } from '~/utils/utils';

type TableWidgetContainerProps = {
  id: string;
  settings: TableWidget;
};

function hasNoMeasuresSelected(
  assetMeasure: AssetMeasureOptions[],
  mode: 'dashboard' | 'template',
  selectedTitles: string[],
): boolean {
  if (mode === 'template') {
    if (selectedTitles.length === 0) {
      return true;
    }
    return false;
  }
  // If there's no assetMeasure array or it is empty, consider "no measure"
  if (!assetMeasure?.length) return true;

  // Return `true` if every measureId array is empty OR every measure within it is empty
  const atLeastOneSelected = assetMeasure.some(
    (am) => Array.isArray(am.measureId) && am.measureId.some((mId) => mId.trim() !== ''),
  );
  // If we can't find any non-empty measureId, then "no measure"
  return !atLeastOneSelected;
}
export const TableWidgetContainer = ({ id, settings }: TableWidgetContainerProps) => {
  const dateFormats = useSelector(getDateTimeFormat);
  const { rowHeader, rows, isLoading, isError, removedResults, successAndFailedMeasurements } =
    useFetchTableData(id, settings);
  const noMeasureSelected = hasNoMeasuresSelected(
    settings.assetMeasure,
    settings.mode,
    settings.selectedTitles,
  );
  const theme = useTheme();
  const isLargeScreen = useMediaQuery(theme.breakpoints.up('md'));

  const exportToPDF = async () => {
    const doc = new jsPDF();
    const tableColumn = rowHeader.map((item) => {
      return item.field;
    });
    const tableHeaders = rowHeader.map((item) => {
      return item.headerName;
    }) as string[];
    const tableRows = rows.map((item) => {
      const { timeData, ...rest } = item;
      delete rest?.id;
      return {
        timeData,
        ...rest,
      };
    });
    autoTable(doc, {
      head: [tableHeaders],
      body: [
        ...tableRows.map((item) => {
          const row: string[] = [];
          tableColumn.map((col) => {
            row.push(item[col]);
          });
          return row;
        }),
      ],
    });
    doc.save('table.pdf');
  };

  const createXLSXFile = async () => {
    const data = rows.map((item) => {
      const { timeData: Time, ...rest } = item;
      delete rest?.id;
      const rowData: { [key: string]: any } = {};
      Object.keys(rest).forEach((key) => {
        const header = rowHeader.find((headerItem) => headerItem.field === key)?.headerName;
        if (header) {
          rowData[header] = rest[key];
        }
      });
      const utcDateStr = fortmatUTCDate(dayjs(Time, 'DD-MM-YYYY hh:mm:ss A').toDate(), dateFormats);
      return {
        Time: utcDateStr,
        ...rowData,
      };
    });
    const workbook = XLSX.utils.book_new();
    const sheet = XLSX.utils.json_to_sheet(data);
    // Move the 'timeData' column to the beginning
    const columns = ['Time', ...Object.keys(data[0]).filter((key) => key !== 'Time')];

    XLSX.utils.sheet_add_json(sheet, data, {
      header: columns as string[],
      skipHeader: false,
      origin: 'A1',
    });
    XLSX.utils.book_append_sheet(workbook, sheet, 'Sheet1'); // Assign the 'sheet' variable here
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const excelData = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const excelUrl = URL.createObjectURL(excelData);
    const link = document.createElement('a');
    link.href = excelUrl;
    const timestamp = new Date().toLocaleString().replace(/[/\\?%*:|"<>]/g, '-');
    link.download = `table ${timestamp}.xlsx`;
    link.click();
  };
  return (
    <CommonWidgetContainer
      id={id}
      widgetType="table"
      widgetName="Table"
      successAndFailedMeasurements={successAndFailedMeasurements}
      removedResults={removedResults}
      settings={settings}
      widgetContent={
        <Box sx={{ width: '100%', height: '100%' }}>
          {noMeasureSelected && <NoMeasureSelected />}
          {isLoading ? (
            <Loader style={{ height: 'inherit' }} />
          ) : (
            <>
              {rows && (
                <DataGrid
                  rows={rows ?? []}
                  columns={
                    isLargeScreen
                      ? rowHeader.map((col) => ({ ...col, flex: 1, width: undefined }))
                      : rowHeader
                  }
                  loading={isLoading}
                  autoHeight
                  slots={{
                    noRowsOverlay: CustomNoRowsOverlay,
                    toolbar: () => (
                      <>
                        {settings.title.isVisible && (
                          <Typography
                            variant="h4"
                            component="div"
                            sx={{ flexGrow: 1, mb: 3, mt: 3 }}
                            style={{
                              textAlign: 'center',
                              fontSize: settings.fontSize + 'px',
                              fontWeight: settings.fontWeight,
                              color: settings.title.color,
                            }}
                          >
                            {settings.title.value}
                          </Typography>
                        )}
                      </>
                    ),
                  }}
                  initialState={{
                    pagination: {
                      paginationModel: { page: 0, pageSize: 10 },
                    },
                  }}
                  sx={{
                    width: '100%',
                    textAlign: 'left',
                    '& .MuiDataGrid-root': {
                      overflow: 'visible',
                    },
                    '& .MuiDataGrid-main': {
                      overflow: 'auto',
                      width: 'auto',
                    },
                    '& .MuiDataGrid-columnHeader': {
                      whiteSpace: 'normal',
                      lineHeight: '1.2',
                      padding: '8px',
                      width: isLargeScreen ? 'auto' : 'auto !important',
                    },
                    '& .MuiDataGrid-columnHeaderTitle': {
                      overflow: 'visible',
                      lineHeight: '1.2',
                      whiteSpace: 'normal',
                      fontWeight: 'bold',
                    },
                    '& .MuiDataGrid-cell': {
                      whiteSpace: 'normal',
                      padding: '8px',
                      overflow: 'visible',
                      width: isLargeScreen ? 'auto' : 'auto !important',
                    },
                    '& .MuiDataGrid-virtualScroller': {
                      overflow: 'auto',
                      minWidth: '100%',
                    },
                    '& .MuiDataGrid-footerContainer': {
                      minWidth: '100%',
                    },
                    '& .MuiDataGrid-columnHeaders': {
                      minWidth: '100%',
                    },
                    '--DataGrid-overlayHeight': '269px',
                  }}
                  pageSizeOptions={[5, 10, 25]}
                  disableColumnMenu={false}
                  disableColumnSelector={false}
                  disableDensitySelector={false}
                  columnVisibilityModel={{}}
                />
              )}
            </>
          )}
        </Box>
      }
      settingsDialog={TableSettingDialog}
      options={[
        {
          method: exportToPDF,
          content: (
            <>
              <PictureAsPdfIcon style={{ marginRight: '5px' }} />
              Export To PDF
            </>
          ),
        },
        {
          method: createXLSXFile,
          content: (
            <>
              <ArticleIcon style={{ marginRight: '5px' }} />
              Export To Excel
            </>
          ),
        },
      ]}
    />
  );
};
