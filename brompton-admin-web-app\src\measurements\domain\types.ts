import { Annotations } from 'plotly.js';
import * as yup from 'yup';
import { CollectionDto } from '~/infra/dto-types';
import { AssetDo } from '~/types/asset';
import { CalcMetricDTO } from '~/types/measures';

export const AssetMeasurementSchema = yup.object({
  tag: yup
    .string()
    .nullable()
    .matches(
      /^[a-zA-Z][a-zA-Z0-9\s]*$/,
      'Tag can only start with a letter and contain letters, digits, and spaces',
    )
    .required('Please enter a tag'),
  description: yup.string().nullable().max(100).optional(),
  typeId: yup.number().nullable().integer().required(),
  dataTypeId: yup.number().nullable().integer().required(),
  valueTypeId: yup.number().nullable().integer().required(),
  unitOfMeasureId: yup.number().nullable().integer().optional(),
  locationId: yup.number().nullable().integer().optional(),
  datasourceId: yup.number().nullable().integer().optional(),
  meterFactor: yup
    .number()
    .nullable()
    .transform((value) => (isNaN(value) ? undefined : value))
    .optional(),
  writeback: yup.boolean().nullable().optional(),
});
export const AssetMeasurementSchemaEdit = yup.object({
  tag: yup
    .string()
    .nullable()
    .matches(
      /^[a-zA-Z][a-zA-Z0-9\s]*$/,
      'Tag can only start with a letter and contain letters, digits, and spaces',
    )
    .required('Please enter a tag'),
  description: yup.string().nullable().max(100).optional(),
  type_id: yup.number().nullable().required(),
  data_type_id: yup.number().nullable().integer().required(),
  value_type_id: yup.number().nullable().integer().required(),
  unit_of_measure_id: yup.number().nullable().integer().optional(),
  location_id: yup.number().nullable().integer().optional(),
  datasource_id: yup.number().nullable().integer().optional(),
  meter_factor: yup
    .number()
    .nullable()
    .transform((value) => (isNaN(value) ? undefined : value))
    .optional(),
  writeback: yup.boolean().nullable().optional(),
});

export const createAssetTemplateFirstStep = yup.object({
  save_as_global_asset_template: yup.boolean().default(false),
  assetTypeId: yup.number().required('Please select asset type for asset template.'),
  manufacturer: yup.string().nullable().required('Please enter manufacturer.'),
  model_number: yup.string().nullable().required('Please enter model number.'),
});
export type CreateAssetTemplateFirstStep = yup.InferType<typeof createAssetTemplateFirstStep>;
export type createAssetTemplateFirstStepData = Omit<CreateAssetTemplateFirstStep, 'id'>;

export const MeasurementSchema = yup
  .object({
    id: yup.number().optional(), // Optional id field
    type_id: yup
      .number()
      .required('Type id is required.')
      .nullable()
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
    data_type_id: yup
      .number()
      .required('Data Type is required.')
      .nullable()
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
    value_type_id: yup
      .number()
      .required('Value Type is required.')
      .nullable()
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
    // metric_id: yup
    //   .number()
    //   .required('Metric is required.')
    //   .nullable()
    //   .transform((value, originalValue) => (originalValue === '' ? null : value)),
    metric_id: yup.lazy((value) => {
      if (typeof value === 'number') {
        return yup
          .number()
          .required('Metric is required.')
          .nullable()
          .transform((value, originalValue) => (originalValue === '' ? null : value));
        // .typeError('Metric must be a number.');
      } else if (typeof value === 'string') {
        return yup.string().required('Metric is requiredss.').min(1, 'Metric cannot be empty.');
      }
      return yup.mixed().required('Metric is required.');
    }),
    description: yup.string().nullable(),
    location_id: yup
      .number()
      .nullable()
      .transform((value, originalValue) => (originalValue === '' ? null : value))
      .nullable(),
    datasource_id: yup
      .number()
      .nullable()
      .transform((value, originalValue) => (originalValue === '' ? null : value))
      .nullable(),
    meter_factor: yup
      .number()
      .nullable()
      .transform((value, originalValue) => (originalValue === '' ? null : value))
      .nullable(),
  })
  .defined();

export type measurementListSchema = yup.InferType<typeof MeasurementSchema>;
export type measurementSchemaData = Omit<measurementListSchema, 'id'>;
export type measurementSchemaEdit = measurementListSchema;
export const EditMeasurementSchema = yup
  .object({
    type_id: yup
      .number()
      .required('Type id is required.')
      .nullable()
      .default(undefined) // Default to null if no value is provided
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
    data_type_id: yup
      .number()
      .required('Data Type is required.')
      .nullable()
      .default(undefined) // Default to null if no value is provided
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
    value_type_id: yup
      .number()
      .required('Value Type is required.')
      .nullable()
      .default(null) // Default to null if no value is provided
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
    metric_id: yup.lazy((value) => {
      if (typeof value === 'number') {
        return yup
          .number()
          .required('Metric is required.')
          .nullable()
          .default(undefined) // Default to null if no value is provided
          .transform((value, originalValue) => (originalValue === '' ? null : value));
      } else if (typeof value === 'string') {
        return yup.string().required('Metric is required').min(1, 'Metric cannot be empty.');
      }
      return yup.mixed().required('Metric is required.');
    }),
    description: yup.string().nullable().default(undefined), // Default to null if no value is provided
    location_id: yup
      .number()
      .nullable()
      .default(undefined) // Default to null if no value is provided
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
    datasource_id: yup
      .number()
      .nullable()
      .default(undefined) // Default to null if no value is provided
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
    meter_factor: yup
      .number()
      .nullable()
      .default(undefined) // Default to null if no value is provided
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
  })
  .defined(); // This ensures the schema is defined and all fields can be nullable or required

export type EditmeasurementListSchema = yup.InferType<typeof EditMeasurementSchema>;
export type EditmeasurementSchemaData = Omit<EditmeasurementListSchema, 'id'>;
export const createAssetTemplateSecondStep = yup.object({
  measurements: yup
    .array()
    .of(MeasurementSchema) // Validate each measurement object using the sub-schema
    .required('At least one measurement is required') // Ensure that "measurements" array is present
    .min(1, 'At least one measurement is required') // Enforce at least one measurement
    .test('unique-metric_id', 'Metric must be unique', (measurements = []) => {
      const metricIdSet = new Set(); // Using Set for efficient duplicate checking
      for (const measurement of measurements) {
        if (metricIdSet.has(measurement.metric_id)) {
          return false; // Found a duplicate metric_id
        }
        metricIdSet.add(measurement.metric_id);
      }
      return true;
    }),
});
export type createAssetTemplateSecondStep = yup.InferType<typeof createAssetTemplateSecondStep>;
export type createAssetTemplateSecondStepData = Omit<createAssetTemplateSecondStep, 'id'>;

export const CreateAssetTemplateSchema = yup.object({
  assetTypeId: yup.number().required('Please select asset type for asset template.'),
  manufacturer: yup.string().nullable().required('Please enter manufacturer.'),
  model_number: yup.string().nullable().required('Please enter model number.'),
  measurements: yup
    .array()
    .of(
      yup
        .object({
          type_id: yup
            .number()
            .required('Type id is required.')
            .transform((value) => (isNaN(value) ? undefined : value))
            .nullable(),
          data_type_id: yup
            .number()
            .required('Data Type is required.')
            .transform((value) => (isNaN(value) ? undefined : value))
            .nullable(),
          value_type_id: yup
            .number()
            .required('Value Type is required.')
            .transform((value) => (isNaN(value) ? undefined : value))
            .nullable(),
          metric_id: yup
            .number()
            .required('Metric is required.')
            .transform((value) => (isNaN(value) ? undefined : value))
            .nullable(),
          description: yup.string(),
          location_id: yup
            .number()
            .transform((value) => (isNaN(value) ? undefined : value))
            .nullable(),
          datasource_id: yup
            .number()
            .transform((value) => (isNaN(value) ? undefined : value))
            .nullable(),
          meter_factor: yup
            .number()
            .transform((value) => (isNaN(value) ? undefined : value))
            .nullable(),
        })
        .required(),
    )
    .min(1, 'At least one measurement is required') // Enforce at least one measurement
    .test('unique-metric_id', 'Metric must be unique', (measurements) => {
      if (!measurements) return true; // Assuming the array itself is optional
      const metricIdMap = new Map();
      for (const measurement of measurements) {
        if (metricIdMap.has(measurement.metric_id)) {
          return false; // Found a duplicate metric_id
        }
        metricIdMap.set(measurement.metric_id, true);
      }
      return true;
    }),
});
export type createAssetTemplate = yup.InferType<typeof CreateAssetTemplateSchema>;
export type createAssetTemplateData = Omit<createAssetTemplate, 'id'>;
export type createAssetTemplateDataWithId = createAssetTemplateData & {
  id?: number;
};
export type createAssetTemplateDataWIthMeasureId = createAssetTemplateData & {
  measurements: (createAssetTemplateData['measurements'] & { name?: string; id?: number })[];
};
export type createAssetTemplateDataWithMetricName = createAssetTemplateData & {
  measurements: (createAssetTemplate['measurements'] & { name?: string })[];
};

export const EditAssetTemplateSchema = yup.object({
  save_as_global_asset_template: yup.boolean().default(false),
  asset_type_id: yup.number().required('Please select asset type for asset template.'),
  manufacturer: yup.string().nullable().required('Please enter manufacturer.'),
  model_number: yup.string().nullable().required('Please enter model number.'),
  measurements: yup
    .array()
    .of(
      yup
        .object({
          id: yup.number().optional(),
          type_id: yup
            .number()
            .nullable(undefined) // Allow null
            .transform((value) => (value === undefined ? undefined : value)) // Transform undefined to null
            .required('Type id is required.'),

          data_type_id: yup
            .number()
            .nullable() // Allow null
            .transform((value) => (value === undefined ? undefined : value)) // Transform undefined to null
            .required('Data Type is required.'),

          value_type_id: yup
            .number()
            .nullable() // Allow null
            .transform((value) => (value === undefined ? null : value)) // Transform undefined to null
            .required('Value Type is required.'),
          metric_id: yup.lazy((value) => {
            if (typeof value === 'number') {
              return yup
                .number()
                .required('Metric is required.')
                .nullable()
                .default(undefined) // Default to null if no value is provided
                .transform((value, originalValue) => (originalValue === '' ? null : value));
            } else if (typeof value === 'string') {
              return yup.string().required('Metric is required').min(1, 'Metric cannot be empty.');
            }
            return yup.mixed().required('Metric is required.');
          }),

          description: yup.string().nullable().default(null), // Allow null or string

          location_id: yup
            .number()
            .nullable() // Allow null
            .transform((value) => (value === undefined ? null : value)) // Transform undefined to null
            .default(null),

          datasource_id: yup
            .number()
            .nullable() // Allow null
            .transform((value) => (value === undefined ? null : value)) // Transform undefined to null
            .default(null),

          meter_factor: yup
            .number()
            .nullable() // Allow null
            .transform((value) => (value === undefined ? null : value)) // Transform undefined to null
            .default(null),
        })
        .required(),
    )
    .min(1, 'At least one measurement is required')
    .test('unique-metric_id', 'Metric must be unique', (measurements) => {
      if (!measurements) return true;
      const metricIdMap = new Map();
      for (const measurement of measurements) {
        if (metricIdMap.has(measurement.metric_id)) {
          return false; // Found a duplicate metric_id
        }
        metricIdMap.set(measurement.metric_id, true);
      }
      return true;
    }),
});

export type EditAssetTemplate = yup.InferType<typeof EditAssetTemplateSchema>;
export type EditAssetTemplateData = Omit<EditAssetTemplate, 'id'>;

export const createTemplateFromAssetSchema = yup.object({
  save_as_global_asset_template: yup.boolean().default(false),
  asset_type_id: yup.number().required('Please select asset type for asset template.'),
  manufacturer: yup.string().nullable().required('Please enter manufacturer.'),
  model_number: yup.string().nullable().required('Please enter model number.'),
  measurements: yup
    .array()
    .of(
      yup
        .object({
          type_id: yup
            .number()
            .nullable(undefined) // Allow null
            .transform((value) => (value === undefined ? undefined : value)) // Transform undefined to null
            .required('Type id is required.'),

          data_type_id: yup
            .number()
            .nullable() // Allow null
            .transform((value) => (value === undefined ? undefined : value)) // Transform undefined to null
            .required('Data Type is required.'),

          value_type_id: yup
            .number()
            .nullable() // Allow null
            .transform((value) => (value === undefined ? null : value)) // Transform undefined to null
            .required('Value Type is required.'),
          metric_id: yup.lazy((value) => {
            if (typeof value === 'number') {
              return yup
                .number()
                .required('Metric is required.')
                .nullable()
                .default(undefined) // Default to null if no value is provided
                .transform((value, originalValue) => (originalValue === '' ? null : value));
            } else if (typeof value === 'string') {
              return yup.string().required('Metric is required').min(1, 'Metric cannot be empty.');
            }
            return yup.mixed().required('Metric is required.');
          }),

          description: yup.string().nullable().default(null), // Allow null or string

          location_id: yup
            .number()
            .nullable() // Allow null
            .transform((value) => (value === undefined ? null : value)) // Transform undefined to null
            .default(null),

          datasource_id: yup
            .number()
            .nullable() // Allow null
            .transform((value) => (value === undefined ? null : value)) // Transform undefined to null
            .default(null),

          meter_factor: yup
            .number()
            .nullable() // Allow null
            .transform((value) => (value === undefined ? null : value)) // Transform undefined to null
            .default(null),
        })
        .required(),
    )
    .min(1, 'At least one measurement is required')
    .test('unique-metric_id', 'Metric must be unique', (measurements) => {
      if (!measurements) return true;
      const metricIdMap = new Map();
      for (const measurement of measurements) {
        if (metricIdMap.has(measurement.metric_id)) {
          return false; // Found a duplicate metric_id
        }
        metricIdMap.set(measurement.metric_id, true);
      }
      return true;
    }),
  link_asset_to_template: yup.boolean().optional(),
  override_existing_asset_template: yup.boolean().optional(),
});

export type createAssetTemplateFromAsset = yup.InferType<typeof createTemplateFromAssetSchema>;
export type createAssetTemplateFromAssetData = Omit<createAssetTemplateFromAsset, 'id'>;

export const TemplateFromAsssetMeasurementSchema = yup
  .object({
    id: yup.number().optional(), // Optional id field
    type_id: yup
      .number()
      .required('Type id is required.')
      .nullable()
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
    data_type_id: yup
      .number()
      .required('Data Type is required.')
      .nullable()
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
    value_type_id: yup
      .number()
      .required('Value Type is required.')
      .nullable()
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
    // metric_id: yup
    //   .number()
    //   .required('Metric is required.')
    //   .nullable()
    //   .transform((value, originalValue) => (originalValue === '' ? null : value)),
    metric_id: yup.lazy((value) => {
      if (typeof value === 'number') {
        return yup
          .number()
          .required('Metric is required.')
          .nullable()
          .transform((value, originalValue) => (originalValue === '' ? null : value));
        // .typeError('Metric must be a number.');
      } else if (typeof value === 'string') {
        return yup.string().required('Metric is requiredss.').min(1, 'Metric cannot be empty.');
      }
      return yup.mixed().required('Metric is required.');
    }),
    description: yup.string().nullable(),
    location_id: yup
      .number()
      .nullable()
      .transform((value, originalValue) => (originalValue === '' ? null : value))
      .nullable(),
    datasource_id: yup
      .number()
      .nullable()
      .transform((value, originalValue) => (originalValue === '' ? null : value))
      .nullable(),
    meter_factor: yup
      .number()
      .nullable()
      .transform((value, originalValue) => (originalValue === '' ? null : value))
      .nullable(),
  })
  .defined();

export type templatefromAssetmeasurementListSchema = yup.InferType<
  typeof TemplateFromAsssetMeasurementSchema
>;
export type templatefromAssetmeasurementListData = Omit<
  templatefromAssetmeasurementListSchema,
  'id'
>;
export type templateFromAssetmeasurementSchema = templatefromAssetmeasurementListData;
export type templateFromAssetmeasurementSchemaFrom = {
  measurements?:
    | {
        type_id: number;
        data_type_id: number;
        value_type_id: number;
        metric_id: string | number | any | null;
        description: string | null;
        location_id: number | null;
        datasource_id: number | null;
        meter_factor: number | null;
      }[]
    | undefined;
  save_as_global_asset_template: boolean;
  asset_type_id: number;
  manufacturer: string;
  model_number: string;
};
export type getAssetTemplateDataWithMetricName = EditAssetTemplateData & {
  measurements: (createAssetTemplate['measurements'] & {
    name?: string;
    id: number | null;
  })[];
  asset_type_id: number;
  model_number: string;
  asset?: AssetDo[];
  save_as_global_asset_template: boolean;
  customer: number;
  calculatedMetrics: CalculationMetricInstance[];
  calculatedMetricsInputs: CalculationMetricInput[];
};

export type CalculationMetricInstance = {
  id: number;
  assetTemplate: number;
  calculation: number;
  outputMetric: {
    id: number;
    name: string;
  };
  ispersisted: boolean;
  pollPeriod: number;
  created: string;
  updated: string | null;
  createdby: number;
  updatedby: number | null;
};

export type CalculationMetricInput = {
  id: number;
  inputLabel: string;
  calculationMetricInstance: number;
  metric: {
    id: number;
    name: string;
  } | null;
  constantNumber: number | null;
  constantString: string | null;
  comment: string;
  created: string;
  updated: string | null;
  createdby: number;
  updatedby: number | null;
};

export type UpdateAssetTemplateRequest = {
  asset_type_id: number; // ID of the asset type
  manufacturer: string; // Manufacturer name
  model_number: string; // Model number of the asset
  customerId?: number; // set customer id then its customer specific template else its global template
  measurements: EditMeasurement[]; // Array of measurements associated with the asset template
};

export type UpdateAssetTemplateRequestDTO = {
  metrics: string[]; // Array of metric names
  updateAssetTemplate: {
    asset_type_id: number; // ID of the asset type
    manufacturer: string; // Manufacturer name
    model_number: string; // Model number of the asset
    customerId?: number; // set customer id then its customer specific template else its global template
    measurements: EditMeasurement[];
  }; // Object containing asset template details
  expressionInstance?: CalcMetricDTO[];
  // Array of measurements associated with the asset template
};
export type EditMeasurement = {
  type_id: number; // ID of the measurement type
  data_type_id: number; // ID of the data type
  value_type_id: number; // ID of the value type
  metric_id?: number | string; // ID of the metric
  description: string; // Description of the measurement
  location_id: number | null; // ID of the location (nullable)
  datasource_id: number | null; // ID of the datasource (nullable)
  meter_factor: number | null; // Meter factor (nullable)
  id: number | null | undefined; // ID of the measurement (nullable)
};

export type editAssetTemplateForm = {
  measurements?:
    | {
        id?: number | undefined;
        type_id: number;
        data_type_id: number;
        value_type_id: number;
        metric_id: string | number | any | null;
        description: string | null;
        location_id: number | null;
        datasource_id: number | null;
        meter_factor: number | null;
      }[]
    | undefined;
  save_as_global_asset_template: boolean;
  asset_type_id: number;
  manufacturer: string;
  model_number: string;
};
export const CreateAssetTemplateInstanceSchema = yup.object().shape({
  units_group_id: yup
    .number()
    .required('Units group ID is required.')
    .positive('Units group ID must be required.')
    .integer('Units group ID must be required.'),
  prefix: yup.string().optional(),
  // .required('Prefix is required.')
  // .nullable()
  // .matches(/^[A-Za-z0-9]+$/, 'Prefix must contain only alphabets and numbers'),
  asset: yup.object().shape({
    tag: yup.string().required('Tag is required.').trim(),
    type_id: yup
      .number()
      .required('Type ID is required.')
      .positive('Type ID must be a positive number.')
      .integer('Type ID must be an integer.'),
    parent_ids: yup
      .array()
      .of(yup.string().required('Parent ID is required.'))
      .required('Parent IDs are required.'),
    time_zone: yup.string().nullable().trim(),
    latitude: yup
      .number()
      .nullable()
      .min(-90, 'Latitude must be between -90 and 90.')
      .max(90, 'Latitude must be between -90 and 90.'),
    longitude: yup
      .number()
      .nullable()
      .min(-180, 'Longitude must be between -180 and 180.')
      .max(180, 'Longitude must be between -180 and 180.'),
    description: yup.string().nullable().trim(),
    customer_id: yup
      .number()
      .required('Customer is required.')
      .positive('Customer must be a positive number.')
      .integer('Customer must be an integer.'),
  }),
});

export type createAssetTemplateInstance = yup.InferType<typeof CreateAssetTemplateInstanceSchema>;

export const MeasurementSchemaForInstance = yup
  .object({
    tag: yup
      .string()
      .required('Tag is required.')
      .matches(/^[A-Za-z0-9]+$/, 'Tag must contain only alphabets and numbers'),
    type_id: yup
      .number()
      .required('Type id is required.')
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
    data_type_id: yup
      .number()
      .required('Data Type is required.')
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
    value_type_id: yup
      .number()
      .required('Value Type is required.')
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
    metric_id: yup
      .number()
      .required('Metric is required.')
      .transform((value, originalValue) => (originalValue === '' ? null : value)),
    description: yup.string().nullable(),
    location_id: yup
      .number()
      .transform((value, originalValue) => (originalValue === '' ? null : value))
      .nullable(),
    datasource_id: yup
      .number()
      .transform((value, originalValue) => (originalValue === '' ? null : value))
      .nullable(),
    meter_factor: yup
      .number()
      .transform((value, originalValue) => (originalValue === '' ? null : value))
      .nullable(),
    unit_of_measure_id: yup
      .number()
      .transform((value, originalValue) => (originalValue === '' ? null : value))
      .nullable(),
  })
  .defined();

export type measurementListInstanceSchema = yup.InferType<typeof MeasurementSchemaForInstance>;
export type measurementSchemaDataDTO = Omit<measurementListInstanceSchema, 'id'>;
export type measurementSchemaDataDTOWithInstance = measurementSchemaDataDTO & {
  instance?: number;
};

export type createAssetTemplateDTO = {
  assetTypeId: number;
  manufacturer: string;
  model_number: string;
  measurements: measurementSchemaData[];
  id?: string | number;
  save_as_global_asset_template: boolean;
};
export type createAssetTemplateDataRequest = {
  metrics: string[];
  newTemplate: createAssetTemplateDTO;
  expressionInstance?: CalcMetricDTO[];
};
export type assetTemplatesDTO = {
  items: createAssetTemplateDataWithMetricName[];
  total: number;
};

export type createAssetTemplateInstanceDTO = {
  assetTemplateInstance: createAssetTemplateInstance;
  measurement: measurementSchemaDataDTO[];
};
export type AssetMeasurement = yup.InferType<typeof AssetMeasurementSchema> & {
  id: number;
  measurementId: number;
  asset_id: number;
  metric_id: number | null;
};

export type AssetMeasurementEdit = yup.InferType<typeof AssetMeasurementSchemaEdit> & {
  id: number;
  measurementId: number;
};
export type NewAssetMeasurement = Omit<AssetMeasurement, 'id' | 'measurementId'>;

export type EditAssetMeasurement = Omit<AssetMeasurement, 'id' | 'measurementId'>;

export type EditAssetMeasurementForm = {
  tag: string;
  description: string;
  type_id: number;
  data_type_id: number;
  value_type_id: number;
  unit_of_measure_id: number;
  location_id: number;
  datasource_id: number;
  meter_factor: number;
  id: number;
  writeback: boolean;
};

export type EditAssetMeasurementData = Omit<
  AssetMeasurementEdit,
  'id' | 'measurementId' | 'asset_id'
>;

export type MeasurementType = {
  id: number;
  name: string;
};

export type DataType = {
  id: number;
  name: string;
};

export type ValueType = {
  id: number;
  name: string;
};

export type UnitOfMeasure = {
  id: number;
  name: string;
};

export type MeasurementLocation = {
  id: number;
  name: string;
};

export type UnitOfGroups = {
  id: number;
  name: string;
};

export type UnitOfGroupsMeasure = {
  id: number;
  name: string;
  measurement_type_id: number;
  is_measurement_type_default: boolean;
};

export type UnitOfGroupsMeasureDTO = {
  items: UnitOfGroupsMeasure[];
  total: number;
};
export type Datasource = {
  id: number;
  name: string;
  uri: string;
};

export type AssetIdWithMeasurements = {
  assetId: number;
  measurements: AssetMeasurement[];
};

export type AssetMeasurementDto = Omit<
  AssetMeasurement,
  | 'typeId'
  | 'unitOfMeasureId'
  | 'locationId'
  | 'meterFactor'
  | 'assetId'
  | 'measurementId'
  | 'dataTypeId'
  | 'valueTypeId'
  | 'datasourceId'
> & {
  type_id: number;
  unit_of_measure_id?: number;
  location_id?: number;
  meter_factor?: number;
  measurement_id: number;
  data_type_id: number;
  value_type_id: number;
  datasource_id?: number;
};

export type NewAssetMeasurementDto = Omit<AssetMeasurementDto, 'id' | 'measurement_id'>;
export type AssetMeasurementCollection = CollectionDto<AssetMeasurementDto>;

export type LocationDTO = {
  latitude: number;
  longitude: number;
};
export const measureAlertsValidationSchema = yup.object().shape({
  asset: yup.string().required('Asset is required.'),
  measurementId: yup.string().required('Measurement is required.'),
  customerId: yup.string().required('Customer is required.'),
  // aggregate: yup.string().required('Aggregate is required.'),
  aggregate: yup
    .object()
    .shape({
      label: yup.string().required('Aggregate is required.'),
      value: yup.string().required('Aggregate is required.'),
    })
    .required('Aggregate is required.'),
  aggregatePeriod: yup.string().when('aggregate.label', (label, schema) => {
    return label.includes('None')
      ? schema.optional()
      : schema.required('Aggregate period is required.');
  }),
  thresholdType: yup.string().required('Threshold type is required.'),
  learningPeriod: yup.string().when('thresholdType', {
    is: '2',
    then: (schema) => schema.required('Learning Days are required.'),
    otherwise: (schema) => schema.notRequired(),
  }),
  includeVelocity: yup.boolean().default(false),
  includeMomentum: yup.boolean().default(false),
  comparison: yup.number().when('thresholdType', {
    is: (value: string | undefined) => !value || value === '1' || value === '3' || value === '4',
    then: (schema) => schema.required('Comparison is required.'),
    otherwise: (schema) => schema.notRequired(),
  }),
  thresholdValue: yup
    .number()
    .nullable()
    .positive('Value must be a positive number')
    .transform((value) => (isNaN(value) ? undefined : value))
    .when('thresholdType', {
      is: (value: string | undefined) => !value || value === '1' || value === '3' || value === '4',
      then: (schema) => schema.required('Threshold value is required.'),
      otherwise: (schema) => schema.notRequired(),
    }),
  resetDeadband: yup
    .number()
    .nullable()
    .positive('Reset deadband must be a positive number.')
    .transform((value) => (isNaN(value) ? undefined : Math.abs(value)))
    .when('thresholdType', {
      is: (value: string | undefined) => !value || value === '1',
      then: (schema) => schema.required('Reset deadband is required.'),
      otherwise: (schema) => schema.notRequired(),
    }),
  description: yup.string().required('Description is required'),
  contactNumbers: yup
    .array()
    .of(
      yup.object().shape({
        user: yup.string().required('User is required.'),
        notificationType: yup
          .string()
          .oneOf(['sms', 'email', 'both'])
          .required('Notification type is required.'),
      }),
    )
    .min(1, 'At least one contact is required.')
    .required('Contacts are required.'),
});

export type MeasureAlertsValidationData = yup.InferType<typeof measureAlertsValidationSchema>;

export type AlertAggregate = {
  id: number;
  label: string;
  value: string;
};

export type AlertAggregatesDTO = {
  items: AlertAggregate[];
  total: number;
};

export type AlertThresholdType = {
  id: number;
  threshold: string;
};
export type AlertThresholdTypeDTO = {
  items: AlertThresholdType[];
  total: number;
};

export type AlertCondition = {
  id: number;
  condition: string;
};

export type AlertConditionDTO = {
  items: AlertCondition[];
  total: number;
};

export type createAlertDTO = {
  customerId: string;
  measurement: string;
  agg: string;
  period?: string;
  thresholdType: string;
  learningPeriod?: string;
  includeVelocity?: boolean;
  includeMomentum?: boolean;
  condition: number;
  thresholdValue: number;
  resetDeadband: number;
  asset: string;
  description: string;
  id?: number;
  users: {
    id: number;
    notificationType: number;
  }[];
};

export type Period = {
  id: number;
  label: string;
  value: string;
};
export type PeriodDTO = {
  items: Period[];
  total: number;
};
export type alertMeasurement = {
  id: number;
  assetPath: string;
  asset: number;
  measurementTag: string;
  measurement: number;
  agg: number;
  period: number;
  thresholdType: number;
  condition: number;
  thresholdValue: number;
  resetDeadband: number;
};

export type AlertMeasurementDTO = {
  items: alertMeasurement[];
  total: number;
};

export type Alerts = {
  id: number;
  assetPath: string | null;
  description: string;
  customerId: number;
  state: string | null;
  asset: {
    id: number;
    tag: string;
    latitude: number;
    longitude: number;
    description: string;
    type_id: number;
    customer_id: number;
    time_zone: string | null;
    children_ids: number[] | null;
  };
  measurementTag: string;
  measurement: {
    id: number;
    description: string;
    datasource: number | null;
    meter_factor: number | null;
    tag: string;
    metric: number | null;
    measurementType: number | null;
  };
  enabled: boolean;
  agg: {
    id: number;
    label: string;
    value: string;
  };
  period: {
    id: number;
    label: string;
    value: string;
  };
  thresholdType: {
    id: number;
    threshold: string;
  };
  condition: {
    id: number;
    condition: string;
  };
  thresholdValue: number;
  resetDeadband: number;
  notificationType?: number;
  count?: number;
  users?: number[];
  createdAt: string | null;
  updatedAt: string | null;
  alertUsers?: {
    id: number;
    user: number;
    notificationtype: number;
  }[];
};

export type AlertsDTO = {
  items: Alerts[];
  total: number;
};

export type AlertStats = {
  id: number;
  alert_id: number;
  start_time: string; // ISO 8601 formatted string
  end_time: string; // ISO 8601 formatted string
  duration: string; // String representation of duration (e.g., "1 hour")
  max_value: number;
  min_value: number;
  avg_value: number;
  formattedTimeDuration: string;
};

export type Excursion = {
  id: number;
  alert: {
    id: number;
    asset: number;
    measurement: {
      id: number;
      description: string;
      datasource: number | null;
      meter_factor: number | null;
      tag: string;
    };
    agg: number;
    period: number;
    thresholdType: number;
    condition: number;
    thresholdValue: number;
    resetDeadband: number;
    description: string;
    customerId: number;
    enabled: boolean;
    state: string; // e.g., "NORMAL"
    notificationType: string | null; // nullable field
  };
  measureTag: string;
  start_time: string; // ISO 8601 formatted string
  end_time: string; // ISO 8601 formatted string
  time_duration: {
    days: number;
    hours: number;
    milliseconds: number;
    minutes: number;
    seconds: number;
  }; // Optional string representation of duration (e.g., "1 hour")
  max_value: number;
  min_value: number;
  avg_value: number;
  formattedTimeDuration: string;
};

export type AlertAnalytics = {
  alert_id: number;
  avg_value: number;
  excursion_count: string;
  max_value: number;
  min_value: number;
  period_start: string;
  total_duration_seconds: string;
  threshold_type: number;
  agg: number;
  period: number;
  state: string; // Assuming 'NORMAL', 'EXCEEDED', etc.
  enabled: boolean;
  a_type: number; // Asset type
  m_type: number; // Measurement type
};

export type AlertStatsDTO = {
  total: number;
  items: AlertStats[];
};

export type ExcursionStatsDTO = {
  total: number;
  items: Excursion[];
};

export type AlertAnalyticsDTO = {
  total: number;
  items: AlertAnalytics[];
};

export const editMeasureAlertsValidationSchema = yup.object().shape({
  measurementId: yup.string().required('Measurement ID is required.'),
  customerId: yup.string().required('Customer ID is required.'),
  aggregate: yup.string().required('Aggregate is required.'),
  aggregatePeriod: yup.string().required('Aggregate period is required.'),
  thresholdType: yup.string().required('Threshold type is required.'),
  learningPeriod: yup.string().when('thresholdType', {
    is: '2',
    then: (schema) => schema.required('Learning Days are required.'),
    otherwise: (schema) => schema.notRequired(),
  }),
  includeVelocity: yup.boolean().default(false),
  includeMomentum: yup.boolean().default(false),
  comparison: yup.number().notRequired(),
  thresholdValue: yup
    .number()
    .nullable()
    .positive('Value must be a positive number')
    .transform((value) => (isNaN(value) ? undefined : value))
    .notRequired(),
  resetDeadband: yup
    .number()
    .nullable()
    // .positive('Reset deadband must be a positive number.')
    .transform((value) => (isNaN(value) ? undefined : Math.abs(value)))
    .notRequired(),
  description: yup.string().required('Description is required'),
  contactNumbers: yup
    .array()
    .of(
      yup.object().shape({
        user: yup.string().required('User is required.'),
        notificationType: yup
          .string()
          .oneOf(['sms', 'email', 'both'])
          .required('Notification type is required.'),
      }),
    )
    .min(1, 'At least one contact is required.')
    .required('Contacts are required.'),
});
export type EditMeasureAlertsValidationData = yup.InferType<
  typeof editMeasureAlertsValidationSchema
>;

export type AlertDetails = {
  measurement: {
    id: number;
    tag: string;
    description: string;
    deletedAt: string;
    datasource: any | null;
    meter_factor: number | null;
    type_id: number;
    unit_of_measure_id: number;
    data_type_id: number;
    value_type_id: number;
  };
  customerId: string;
  agg: string;
  period: string;
  thresholdType: string;
  learningPeriod?: {
    days: string;
  };
  includeVelocity?: boolean;
  includeMomentum?: boolean;
  condition: number;
  thresholdValue: number;
  resetDeadband: number;
  asset: string;
  id?: number;
  notificationType: number;
  alertUsers?: {
    id: number;
    user: number;
    notificationtype: number;
  }[];
  description: string;
};

export type Comparator = {
  id: number;
  condition: string;
};

export type Aggregate = {
  id: number;
  label: string;
  value: string;
};

export type EventPeriod = {
  id: number;
  label: string;
  value: string;
  sort_order: number;
};

export type Asset = {
  id: number;
  tag: string;
  latitude: number;
  longitude: number;
  description: string;
  type_id: number;
  customer_id: number;
  time_zone: string | null;
  parent_ids: number[];
  children_ids: number[];
};

export type Measurement = {
  id: number;
  description: string;
  datasource: string | null;
  meter_factor: string | null;
  deletedAt: string | null;
  type_id: number;
  unit_of_measure_id: number;
  data_type_id: number;
  value_type_id: number;
};

export type Event = {
  id: number;
  timestamp: string;
  deadband: number;
  state: string;
  comparator: Comparator;
  limit: number;
  aggregate: Aggregate;
  period: EventPeriod;
  asset_id: Asset;
  measurement_id: Measurement;
  input_value: number;
  alert_id: {
    id: number;
    customerId: number;
  };
  asset_measurement: {
    id: number;
    asset_id: number;
    measurement_id: number;
    tag: string;
    metric_id: number | null;
    description: string;
    meter_factor: string | null;
    type_id: number;
    data_type_id: number;
    value_type_id: number;
    unit_of_measure_id: number;
    location_id: number | null;
    datasource_id: number | null;
  };
};

export type EventDTO = {
  total: number;
  items: {
    id: number;
    timestamp: string;
    deadband: number;
    state: string;
    limit: string;
    input_value: string;
    comparator: Comparator;
    aggregate: AlertAggregate;
    period: EventPeriod;
    asset_id: number;
    measurement_id: number;
    alert_id: number;
  }[];
};

export type UnitOfMeasureData = {
  id: number;
  name: string;
  measurement_type_id: {
    id: number;
    name: string;
  };
};

export type UnitsGroup = {
  id: number;
  name: string;
  isBase: boolean;
};

export type MeasurementUnits = {
  id: number;
  unitOfMeasure: UnitOfMeasureData;
  unitsGroup: UnitsGroup;
  is_default: boolean;
  createdAt: string | null;
  updatedAt: string | null;
  createdBy: string | null;
  updatedBy: string | null;
}[];

export type createAnnotationRes = {
  id: number;
  dashboard: number;
  measurement_id: number;
  description: string;
  widget_id: number;
  time_of_annotation: number; // Representing timestamp as string
  value: number; // Representing value as string, could also be a number depending on the case
  settings: string; // Assuming settings are stored as a JSON string
};
export type Annotation = {
  id?: number;
  dashboard: number;
  measurement_id: number;
  description: string;
  widget_id: number;
  time_of_annotation: number; // Representing timestamp as string
  value: number; // Representing value as string, could also be a number depending on the case
  settings: string; // Assuming settings are stored as a JSON string
};

export type AnnotationDTO = {
  total: number;
  items: Annotation[];
};

export interface CustomAnnotation extends Annotations {
  id: number;
  measurement_id: number; // Add measurement_id property
}

export type EventData = {
  id: number;
  timestamp: string; // ISO date string
  deadband: number;
  state: 'NORMAL' | 'ALERT' | string;
  limit: string;
  input_value: string;
  comparator: {
    id: number;
    condition: 'LT' | 'GT' | 'EQ' | string;
  };
  aggregate: {
    id: number;
    label: string;
    value: string;
  };
  period: {
    id: number;
    label: string;
    value: string;
    sort_order: number;
  };
  asset_id: Asset;
  measurement_id: Measurement;
  alert_id: Alert;
  measurement: {
    id: number;
    tag: string;
  };
  asset: {
    id: number;
    tag: string;
  };
};

export type EventDataDTO = {
  total: number;
  items: EventData[];
};

export type AssetData = {
  id: number;
  tag: string;
  latitude: number;
  longitude: number;
  description: string;
  time_zone: string;
  type_id: number;
  assetTemplate: any | null;
  assetTemplateId: number | null;
  unitsGroup: any | null;
  unitsGroupId: number | null;
  customer_id: number;
  parent_ids: number[];
  children_ids: number[];
};

export type MeasurementData = {
  id: number;
  description: string;
  writeback: boolean;
  datasource: any | null;
  meter_factor: any | null;
  deletedAt: string | null;
  type_id: number;
  unit_of_measure_id: number;
  data_type_id: number;
  value_type_id: number;
};

export type Alert = {
  id: number;
  asset: Asset;
  measurement: Measurement;
  agg: {
    id: number;
    label: string;
    value: string;
  };
  period: {
    id: number;
    label: string;
    value: string;
    sort_order: number;
  };
  thresholdType: number;
  condition: {
    id: number;
    condition: 'LT' | 'GT' | 'EQ' | string;
  };
  thresholdValue: number;
  resetDeadband: number;
  description: string;
  customerId: number;
  enabled: boolean;
  state: 'NORMAL' | 'ALERT' | string;
  createdAt: string;
  updatedAt: string;
  notificationType: any | null;
  anomalyModel: any | null;
  anomalyParameter: any | null;
};
