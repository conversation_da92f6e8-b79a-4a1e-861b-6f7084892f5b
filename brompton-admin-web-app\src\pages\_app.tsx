import '@fontsource/roboto/300.css';
import '@fontsource/roboto/400.css';
import '@fontsource/roboto/500.css';
import '@fontsource/roboto/700.css';
import { createTheme, CssBaseline, GlobalStyles, ThemeProvider } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { openobserveLogs } from '@openobserve/browser-logs';
import { openobserveRum } from '@openobserve/browser-rum';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import Script from 'next/script';
import { useEffect } from 'react';
import 'react-grid-layout/css/styles.css';
import { Provider } from 'react-redux';
import 'react-resizable/css/styles.css';
import { PersistGate } from 'redux-persist/integration/react';
import { persistor, store } from '~/redux/store';
import { AuthGuard } from '~/security/components/AuthGuard';
import UIWrapper from '~/security/components/UIWrapper';
import { Release } from '~/types/release';
import releasesInfo from './release/release.json';
import SessionHandler from '~/security/components/SessionHandler';
export const theme = createTheme({
  palette: {
    primary: {
      main: '#00A76F',
      contrastText: '#f5f5f5',
    },
    secondary: {
      main: '#46a1d9',
      contrastText: '#f5f5f5',
      dark: '#747474',
    },
    success: {
      main: '#58ba54',
      '500': '#00A76F',
    },
    warning: {
      main: '#F2C94C',
    },
    error: {
      main: '#D02C2F',
    },
    text: {
      primary: '#344054',
      secondary: '#667085',
    },
    grey: {
      '100': '#f5f5f5',
      '200': '#EBF8F4',
      '300': '#EBEBEB',
      '400': '#BDBDBD',
    },
    divider: '#EAECF0',
  },
  spacing: 8,
  typography: {
    fontFamily: `'Inter', sans-serif`,
  },
  components: {
    MuiDialog: {
      styleOverrides: {
        paper: {
          borderRadius: '10px',
        },
      },
    },
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          borderRadius: 8,
          padding: '8px 12px',
          backgroundColor: '#101828',
        },
      },
    },
    MuiCssBaseline: {
      styleOverrides: {
        '::-webkit-scrollbar': {
          width: '8px',
          backgroundColor: '#F5F5F5',
          scrollbarWidth: 'thin',
        },
        '::-webkit-scrollbar-thumb': {
          backgroundColor: '#888',
          borderRadius: '4px',
        },
        '::-webkit-scrollbar-thumb:hover': {
          backgroundColor: '#555',
        },
      },
    },
  },
});

const openObserveOptions = {
  clientToken: process.env.NEXT_PUBLIC_OPENOBSERVE_CLIENT_TOKEN || '',
  applicationId: process.env.NEXT_PUBLIC_OPENOBSERVE_APPLICATION_ID || '',
  site: process.env.NEXT_PUBLIC_OPENOBSERVE_SITE || '',
  service: process.env.NEXT_PUBLIC_OPENOBSERVE_SERVICE || '',
  env: process.env.NEXT_PUBLIC_OPENOBSERVE_ENV || '',
  version: process.env.NEXT_PUBLIC_OPENOBSERVE_VERSION || '',
  organizationIdentifier: process.env.NEXT_PUBLIC_OPENOBSERVE_ORGANIZATION_IDENTIFIER || '',
  insecureHTTP: process.env.NEXT_PUBLIC_OPENOBSERVE_INSECURE_HTTP === 'TRUE',
  apiVersion: process.env.NEXT_PUBLIC_OPENOBSERVE_API_VERSION || 'v1',
};

export default function App({ Component, pageProps }: AppProps) {
  useEffect(() => {
    if (
      typeof window !== 'undefined' &&
      process.env.NEXT_PUBLIC_ENVIRONMENT &&
      process.env.NEXT_PUBLIC_ENVIRONMENT !== 'development'
    ) {
      // Initialize OpenObserve RUM
      const releases: Release[] = releasesInfo.map((release) => ({
        ...release,
        changes: Array.isArray(release.changes)
          ? release.changes.map((change) =>
              typeof change === 'string' ? { description: [change] } : change,
            )
          : [],
      }));
      openobserveRum.init({
        applicationId: openObserveOptions.applicationId,
        clientToken: openObserveOptions.clientToken,
        site: openObserveOptions.site,
        organizationIdentifier: openObserveOptions.organizationIdentifier,
        service: openObserveOptions.service,
        env: openObserveOptions.env,
        version: releases.at(0)?.version ?? '0.0.1',
        trackResources: true,
        trackLongTasks: true,
        trackUserInteractions: true,
        apiVersion: openObserveOptions.apiVersion,
        insecureHTTP: openObserveOptions.insecureHTTP,
        defaultPrivacyLevel: 'allow', // 'allow', 'mask-user-input', or 'mask'
      });

      // Initialize OpenObserve Logs
      openobserveLogs.init({
        clientToken: openObserveOptions.clientToken,
        site: openObserveOptions.site,
        organizationIdentifier: openObserveOptions.organizationIdentifier,
        service: openObserveOptions.service,
        env: openObserveOptions.env,
        version: releases.at(0)?.version ?? '0.0.1',
        forwardErrorsToLogs: true,
        insecureHTTP: openObserveOptions.insecureHTTP,
        apiVersion: openObserveOptions.apiVersion,
      });
    }
  }, []);

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <ThemeProvider theme={theme}>
          <GlobalStyles
            styles={{
              '.react-grid-item .react-resizable-handle': {
                display: 'none',
              },
              '.react-grid-item:hover .react-resizable-handle': {
                display: 'block',
              },
              '.react-grid-item.disabled-resize:hover .react-resizable-handle': {
                display: 'none',
              },
              '.rdrDefinedRangesWrapper .rdrStaticRanges': {
                maxHeight: '300px',
                overflowY: 'auto',
              },
              '.rdrStaticRange': {
                fontFamily: 'Inter',
              },
              '.rdrDateDisplay span': {
                display: 'flex',
              },
              '.rdrStartEdge': {
                backgroundColor: '#00A76F',
              },
              '.rdrEndEdge': {
                backgroundColor: '#00A76F',
                fontFamily: 'Inter',
              },
              '.rdrInRange': {
                color: '#00A76F',
                background: '#F6FFFC',
              },
              '.rdrDay:not(.rdrDayPassive) .rdrInRange ~ .rdrDayNumber span': {
                color: '#00A76F',
              },
              '.rdrDay': {
                fontSize: '14px',
              },
              '.rdrStaticRangeLabel': {
                fontSize: '14px',
              },
              '.rdrInputRange': {
                fontSize: '14px',
              },
              '.rdrDateDisplayItem input': {
                fontSize: '14px',
                fontFamily: 'Inter',
              },
              '.rdrMonth .rdrMonthName': {
                fontSize: '14px',
              },
              '.rdrMonthAndYearPickers span': {
                fontSize: '14px',
              },
              '.rdrDayToday>span.rdrDayNumber>span:after': {
                background: '#747474',
              },
            }}
          />
          <Head>
            <title>pivotol.ai</title>
            <link href="/pivotol.png" rel="icon" />
            <link rel="apple-touch-icon" href="/pivotol.png" />
            <meta
              name="viewport"
              content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
            />
            <meta name="mobile-web-app-capable" content="yes" />
            <meta name="apple-mobile-web-app-capable" content="yes" />
            <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
            <link rel="manifest" href="/manifest.json" />
            <link rel="preconnect" href="https://fonts.googleapis.com" />
            <link rel="preconnect" href="https://fonts.gstatic.com" />
            <link
              href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap"
              rel="stylesheet"
            ></link>
          </Head>
          <Script src="https://openweathermap.org/themes/openweathermap/assets/vendor/owm/js/d3.min.js"></Script>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <AuthGuard>
              <>
                <SessionHandler />
                <CssBaseline />
                <UIWrapper>
                  <Component {...pageProps} />
                </UIWrapper>
              </>
            </AuthGuard>
          </LocalizationProvider>
        </ThemeProvider>
      </PersistGate>
    </Provider>
  );
}
