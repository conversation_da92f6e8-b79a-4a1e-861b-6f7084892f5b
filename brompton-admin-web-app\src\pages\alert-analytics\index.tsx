import { Box } from '@mui/material';
import dynamic from 'next/dynamic';
import React from 'react';
import Loader from '~/components/common/Loader';

const index = () => {
  const AlertAnalyticsPage = dynamic(() => import('~/components/AlertAnalytics/AlertAnalytics'), {
    ssr: false,
    loading: (loadingProps) => {
      return (
        <Box sx={{ mt: '20%' }}>
          <Loader />
        </Box>
      );
    },
  });
  return <AlertAnalyticsPage />;
};

export default index;
