import { Box, Container } from '@mui/material';
import { useRouter } from 'next/router';
import EditAlert from '~/components/Alerts/edit/EditAlert';
import HomeButton from '~/components/common/Home/HomeButton';
import PageName from '~/components/common/PageName/PageName';

const EditAlertPage = () => {
  const router = useRouter();
  const { alertId } = router.query;
  return (
    <Container sx={{ my: 2 }} maxWidth="xl">
      <PageName name="Edit Alert" />

      <EditAlert alertId={alertId ? parseInt(alertId as string) : undefined} />
    </Container>
  );
};
export default EditAlertPage;
