import { Box, Button, Typography } from '@mui/material';
import Link from 'next/link';
import { useRouter } from 'next/router';
import EditAssetTemplateContainer from '~/components/AssetTemplate/EditAssetTemplateContainer';
import Loader from '~/components/common/Loader';
import { useGetAssetTemplatedByAssetTypeQuery } from '~/redux/api/assetsApi';

const EditAssetTemplate = () => {
  const router = useRouter();
  const { assetTemplate, assetType } = router.query;
  // Call the query without any conditional logic
  const { data, error, isFetching } = useGetAssetTemplatedByAssetTypeQuery(
    {
      assetTypeId: assetType as string,
      templateId: assetTemplate as string,
      assetAffected: true,
    },
    {
      skip: !assetType || !assetTemplate,
    },
  );
  // Show loading state if router is not ready
  if (!router.isReady) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'column',
          minHeight: '90vh',
        }}
      >
        <Loader />
      </Box>
    );
  }

  // If parameters are missing, show 404 page
  if (!assetTemplate || !assetType) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'column',
          minHeight: '90vh',
        }}
      >
        <Typography variant="h1">404</Typography>
        <Typography variant="h6">The page you’re looking for doesn’t exist.</Typography>
        <Button variant="contained" sx={{ mt: 3 }} href="/" LinkComponent={Link}>
          Back Home
        </Button>
      </Box>
    );
  }

  // Render the actual component with the fetched data
  return (
    <EditAssetTemplateContainer
      error={error}
      data={data}
      isFetching={isFetching}
      assetTemplate={assetTemplate as string}
    />
  );
};

export default EditAssetTemplate;
