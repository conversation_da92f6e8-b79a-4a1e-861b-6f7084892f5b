import { Box } from '@mui/material';
import { useRouter } from 'next/router';
import CalcEngineBuilder from '~/components/CalcEngine/CalcEngineBuilder';
import HomeButton from '~/components/common/Home/HomeButton';
import PageName from '~/components/common/PageName/PageName';

const CreateExpressionTemplate: React.FC = () => {
  const router = useRouter();
  const { templateId } = router.query;
  return (
    <Box p={3}>
      <Box p={2} pb={0} pt={0} sx={{ display: 'flex' }}>
        <Box
          sx={{
            flexGrow: 1,
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <PageName name="Expression Templates" />
        </Box>
        <HomeButton />
      </Box>
      <CalcEngineBuilder templateId={templateId ? parseInt(templateId as string) : undefined} />
    </Box>
  );
};

export default CreateExpressionTemplate;
