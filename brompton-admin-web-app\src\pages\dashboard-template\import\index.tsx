import FileUploadIcon from '@mui/icons-material/FileUpload';
import {
  <PERSON><PERSON>,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  IconButton,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Paper,
} from '@mui/material';
import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useCreateDashboardTemplateMutation } from '~/redux/api/dashboardTemplate';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';

const ImportTemplate = () => {
  const { globalAdmin } = useHasAdminAccess();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [title, setTitle] = useState<string>('');
  const [openDialog, setOpenDialog] = useState(false);
  const [isFileValid, setIsFileValid] = useState(false);
  const [saveAsGlobal, setSaveAsGlobal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const dispatch = useDispatch();
  const [metrics, setMetrics] = useState<{ id: number; name: string; description: string }[]>([]);

  const [templateData, setTemplateData] = useState<{
    asset_template?: number;
    manufacturer?: string;
    modelNumber?: string;
    assetType?: string;
    customer?: any;
    data?: string;
  } | null>(null);

  // RTK Mutation Hook
  const [createDashboardTemplate, { isLoading }] = useCreateDashboardTemplateMutation();

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];

      // Validate file type (must be JSON)
      if (!file.name.endsWith('.json')) {
        setErrorMessage('Invalid file type. Please upload a JSON file.');
        return;
      }

      try {
        const fileContent = await file.text();
        const parsedData = JSON.parse(fileContent);

        // Validate required fields in JSON
        if (
          !parsedData.asset_template ||
          !parsedData.data ||
          !parsedData.asset_template.measurements
        ) {
          throw new Error('Invalid file structure: Missing required fields.');
        }

        setTemplateData({
          asset_template: parsedData.asset_template.id,
          manufacturer: parsedData.asset_template.manufacturer,
          modelNumber: parsedData.asset_template.modelNumber,
          assetType: parsedData.asset_template.assetType?.name,
          customer: parsedData.customer,
          data: parsedData.data, // Save raw data for API
        });

        // Extract metrics from JSON file
        const extractedMetrics = parsedData.asset_template.measurements.map((measurement: any) => ({
          id: measurement.metric.id,
          name: measurement.metric.name,
          description: measurement.description || 'N/A',
        }));
        setMetrics(extractedMetrics);

        if (globalAdmin) {
          // Set checkbox state based on customer field
          setSaveAsGlobal(parsedData.customer === null);
        }

        setSelectedFile(file);
        setIsFileValid(true);
        setErrorMessage(null); // Clear any previous errors
      } catch (error) {
        setErrorMessage('Invalid JSON format. Please upload a valid file.');
        console.error(error);
        setSelectedFile(null);
        setIsFileValid(false);
        setTemplateData(null);
        setMetrics([]);
      }
    }
  };

  const handleImport = async () => {
    if (!selectedFile || !title.trim() || !templateData) return;

    try {
      // Call API and extract response data
      const template = await createDashboardTemplate({
        asset_template: templateData.asset_template!,
        title,
        data: templateData.data!,
        is_global: saveAsGlobal, // Map checkbox state to API field
      }).unwrap(); // Unwrap ensures we directly get the response

      dispatch(dashboardSlice.actions.setTemplateId(template.id));
      dispatch(dashboardSlice.actions.setTemplateName(template.title));

      // Reset states after successful import
      setSelectedFile(null);
      setTitle('');
      setIsFileValid(false);
      setSaveAsGlobal(false);
      setTemplateData(null);
      setMetrics([]);
      setOpenDialog(false);
    } catch (error) {
      setErrorMessage('Failed to import template. Please try again.');
      console.error(error);
    }
  };

  return (
    <div>
      {/* Import Button */}
      <Tooltip title="Import Dashboard Template">
        <IconButton component="span" color="primary" onClick={() => setOpenDialog(true)}>
          <FileUploadIcon />
        </IconButton>
      </Tooltip>

      {/* Dialog for Upload & Title Input */}
      <Dialog
        open={openDialog}
        onClose={() => {
          setOpenDialog(false);
          setIsFileValid(false);
          setSelectedFile(null);
          setSaveAsGlobal(false);
          setTemplateData(null);
          setMetrics([]);
          setTitle('');
          setErrorMessage(null);
        }}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Import Dashboard Template</DialogTitle>
        <DialogContent>
          {/* File Input */}
          <input type="file" accept=".json" onChange={handleFileChange} id="upload-template" />

          {/* Show error message */}
          {errorMessage && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {errorMessage}
            </Alert>
          )}

          {/* Show Read-Only Fields if a valid file is uploaded */}
          {isFileValid && templateData && (
            <>
              <TextField
                fullWidth
                label="Asset Template"
                value={`${templateData.manufacturer} - ${templateData.modelNumber || ''}`}
                variant="outlined"
                margin="normal"
                disabled
                InputProps={{ readOnly: true }}
              />

              <TextField
                fullWidth
                disabled
                sx={{ mt: 1 }}
                label="Asset Type"
                value={templateData.assetType}
                variant="outlined"
                margin="normal"
                InputProps={{ readOnly: true }}
              />
            </>
          )}

          {/* Title Input Field (User Editable) */}
          {isFileValid && (
            <TextField
              fullWidth
              label="Template Title"
              sx={{ mt: 1 }}
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              variant="outlined"
              margin="normal"
            />
          )}

          {/* Save as Global Checkbox */}
          {isFileValid && (
            <FormControlLabel
              control={
                <Checkbox
                  disabled={!globalAdmin}
                  checked={saveAsGlobal}
                  onChange={(e) => setSaveAsGlobal(e.target.checked)}
                />
              }
              label="Save as Global Dashboard Template"
              sx={{ mt: 1 }}
            />
          )}

          {/* Metrics Table */}
          {metrics.length > 0 && (
            <TableContainer component={Paper} sx={{ mt: 2, maxHeight: 300, overflow: 'auto' }}>
              <Table stickyHeader>
                <TableHead sx={{ position: 'sticky', top: 0, zIndex: 1, backgroundColor: 'white' }}>
                  <TableRow>
                    <TableCell>
                      <strong>Metric ID</strong>
                    </TableCell>
                    <TableCell>
                      <strong>Name</strong>
                    </TableCell>
                    <TableCell>
                      <strong>Description</strong>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {metrics.map((metric) => (
                    <TableRow key={metric.id}>
                      <TableCell>{metric.id}</TableCell>
                      <TableCell>{metric.name}</TableCell>
                      <TableCell>{metric.description}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </DialogContent>

        {/* Dialog Actions */}
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)} color="primary" variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleImport}
            color="primary"
            variant="contained"
            disabled={isLoading || !title.trim()}
          >
            {isLoading ? 'Importing...' : 'Import'}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default ImportTemplate;
