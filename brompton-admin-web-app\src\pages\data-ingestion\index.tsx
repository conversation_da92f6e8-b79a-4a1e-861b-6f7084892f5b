import { Box } from '@mui/material';
import dynamic from 'next/dynamic';
import React from 'react';
import Loader from '~/components/common/Loader';

const DataIngestion = () => {
  const DataIngestionPage = dynamic(() => import('~/components/DataIngestion/DataIngestion'), {
    ssr: false,
    loading: (loadingProps) => {
      return (
        <Box sx={{ mt: '20%' }}>
          <Loader />
        </Box>
      );
    },
  });

  return <DataIngestionPage />;
};

export default DataIngestion;
