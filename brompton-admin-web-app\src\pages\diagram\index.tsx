import { Box } from '@mui/material';
import dynamic from 'next/dynamic';
import React from 'react';
import Loader from '~/components/common/Loader';

const Diagram = () => {
  const DiagramPage = dynamic(() => import('~/components/JointJs/JointJsPage'), {
    ssr: false,
    loading: (loadingProps) => {
      return (
        <Box sx={{ mt: '20%' }}>
          <Loader />
        </Box>
      );
    },
  });

  return <DiagramPage />;
};

export default Diagram;
