import HistoryIcon from '@mui/icons-material/History';
import { Box, Typography } from '@mui/material';
import { openobserveRum } from '@openobserve/browser-rum';
import Cookies from 'js-cookie';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useLogoutUserMutation } from '~/redux/api/authApi';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';

export default function LogoutPage() {
  const dispatch = useDispatch();
  const router = useRouter();
  const [logoutUser, { isLoading, isSuccess }] = useLogoutUserMutation();
  const url = router.query.redirect as string;
  const start = router.query.s as string;
  const end = router.query.e as string;
  const sample_period = router.query.sample_period as string;
  const queryParams = new URLSearchParams();
  if (start) {
    queryParams.set('s', start);
  }
  if (end) {
    queryParams.set('e', end);
  }
  if (sample_period) {
    queryParams.set('sample_period', sample_period);
  }
  const newUrl = `${url}${queryParams.size > 0 ? `&${queryParams.toString()}` : ''}`;
  const handleLogout = () => {
    logoutUser();
    dispatch(dashboardSlice.actions.logout());
    openobserveRum.clearUser();
    openobserveRum.stopSessionReplayRecording();
    localStorage.removeItem('sessionPopupDismissed');
    // dispatch(authApi.util.resetApiState());
  };

  useEffect(() => {
    if (isSuccess) {
      const allCookies = Cookies.get();
      Object.keys(allCookies).forEach((cookieName) => {
        Cookies.remove(cookieName);
      });
      localStorage.removeItem('sessionPopupDismissed');
    }
  }, [isLoading, isSuccess]);

  return (
    <Box
      sx={{
        height: '90vh',
      }}
    >
      <Box
        sx={{
          height: '100%',
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Box
          sx={{
            display: 'block',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Box width={'100%'} textAlign={'center'}>
            <HistoryIcon
              sx={{
                fontSize: 100,
              }}
            />
          </Box>
          <Box width={'100%'} textAlign={'center'}>
            <Typography>Oops Your Session has expired.</Typography>
            <Link
              href={newUrl && url ? `/login?redirect=${encodeURIComponent(newUrl)}` : '/login'}
              onClick={handleLogout}
            >
              Login Again
            </Link>{' '}
          </Box>
        </Box>
      </Box>
    </Box>
  );
}
