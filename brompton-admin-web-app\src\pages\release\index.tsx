import { Box, Container, Divider, List, ListItem, Typography } from '@mui/material';
import { Release } from '~/types/release';
import releasesInfo from './release.json';

const ReleasesPage = () => {
  const releases: Release[] = releasesInfo.map((release) => ({
    ...release,
    changes: Array.isArray(release.changes)
      ? release.changes.map((change) =>
          typeof change === 'string' ? { description: [change] } : change,
        )
      : [],
  }));

  return (
    <Container maxWidth="xl">
      <Box p={3} pt={2}>
        <Typography mt={1} variant="h4">
          Release Details
        </Typography>
        {releases.map((release) => (
          <div key={release.version}>
            <Typography variant="h6">Version {release.version}</Typography>
            {release.updated_at && (
              <Typography variant="caption">Released on : {release.updated_at}</Typography>
            )}
            <List>
              {release.changes.map((change) => {
                return change.description.map((description, index) => (
                  <ListItem key={index}>{description}</ListItem>
                ));
              })}
            </List>

            <Divider />
          </div>
        ))}
      </Box>
    </Container>
  );
};

export default ReleasesPage;
