[{"version": "2.0.225", "updated_at": "3-June-2025", "changes": [{"description": ["Bug - (1128) Fixed assetTZ dynamic value based on topbar in Forecast API", "Bug - (1595) Session Expiry Not Notified to User on Mobile Application Login Page"]}]}, {"version": "2.0.224", "updated_at": "2-June-2025", "changes": [{"description": ["Bugfix - (1632) Widget Link Navigation Bypasses Dashboard Save Prompt", "Refactor - (1606) Mobile topbar/side conditionally render on Portrait and landscape mode", "Bug - (1418) Sometimes: Dashboard Redirection Shows `Access to this dashboard is restricted` Even for Global Admin Users"]}]}, {"version": "2.0.223", "updated_at": "30-May-2025", "changes": [{"description": ["Refactor - (1606) Table widget Cropping fix", "Refactor - (1606) Alert widget Cropping fix", "Bugfix - (1630) Table header undefined fix"]}]}, {"version": "2.0.222", "updated_at": "29-May-2025", "changes": [{"description": ["Refactor - (1606) UI refactor for Topbar, Add Widget <PERSON>, Stats widget, Title/Value widget", "Refactor - (1606) UI refactor for Chart Spacing", "Refactor - (1621) KPI-Bar widget loader in center", "Refactor - (1606) Side bar hidden for Mobile Vertical and Horizontal view", "Refactor - (1606) KPI-Bar widget cropping issue fix", "Feature - (1397) Create Template from Asset", "Refactor - (1606) KPI-Bar widget Scrollable tabs"]}]}, {"version": "2.0.223", "updated_at": "28-May-2025", "changes": [{"description": ["Bug - (1610) Remove Heatmap Realtime Setting from UI", "Feature - (1614) Asset details page should contain the name of the asset template from which it was created", "Bug - (1470) Show UTC timestamp on Asset Details Page", "Bug - (1609) Bar/Scatter Chart Y-axis Position is not working", "Bug - (1613) Annotation is not working", "Refactor - (1607) Diagram widget measurement API's"]}]}, {"version": "2.0.222", "updated_at": "27-May-2025", "changes": [{"description": ["Feature - (1603) Safari Browser warning", "Feature - (1600) Optimize Code for api calls", "Bug - (1559) Show proper error message for Data Ingestion if redis key does not exist, from FE and TS"]}]}, {"version": "2.0.221", "updated_at": "26-May-2025", "changes": [{"description": ["Bug - (1527) Revert Parent type validation in the Create New Asset Type flow", "Bug - (1520) Persisted flag Calculation Measurement is giving error while updating", "Bug - (1462) Clicking on linked dashboard/template in breadcrumb does not load or highlight the selected dashboard", "Bug - (1594) Measurement Trend Chart Not Fully Visible on Mobile View After Opening from <PERSON><PERSON>t", "Feature - (1441) Sankey Status Indicator", "Bug - (1578/1587) Diagram widget variable values fix with persistence.", "Bug - (1589) Stats widget Title config fix", "Bug - (1598) Resolved Conflicting Value/Title widget text color", "Bug - (1596) Dashboard Template Dropdown Not Populating After Asset Type Selection in Dashboard Tile Widget on dashboard template page.", "Bug - (1601) Diagram Widget Rendering Without Waiting For Api Response To Show Variable Values"]}]}, {"version": "2.0.220", "updated_at": "23-May-2025", "changes": [{"description": ["Bug - (1581) Fix missing/default timestamp in payload for data ingestion", "Bug - (1584) Fix data ingection for duplicate columns in (TS)", "Bug - (1588) the X and Y axis labels become heavily cluttered and overlap with each other and with the legend on trend/bar chart for above 90 days' time ranges"]}]}, {"version": "2.0.219", "updated_at": "22-May-2025", "changes": [{"description": ["Bug - (1574) On Prod : <PERSON><PERSON> giving invalid date", "Bug - (1527) Parent Type validation while adding New Asset Type", "Bug - (1551) Resolve stats widget title, it was conflicting with the widget indicator", "Bug - (1419) Diagram Widget measures value Not appearing on retain After Redirecting to Linked Dashboard Template", "Bug - (1399) Image Widget Shows Redundant and Unclear Link Options", "Bug - (1559) Show proper error message for Data Ingestion", "Bug - (1576) Diagram Selection in Widget Settings Dropdown Does Not Reflect on Dashboard Template Page"]}]}, {"version": "2.0.218", "updated_at": "21-May-2025", "changes": [{"description": ["Refactor - (1563) 500 Internal Server Error When Attempting to Save Edited Asset Template", "Bug - (1566) sometimes: Validation Error `Each Variable Must Have a Valid Metric or Constant Value` Appears Despite Supplying Valid Inputs on edit asset template.", "Bug - (1558) 500 Internal Server Error when creating/updating Asset Template Instance", "Feature - (1486) Measurement type page with create/edit flow", "Bug - (1552) After click on <PERSON> But<PERSON> Not redirecting to previous page on Edit Page of Calculation and Time-Varying Factor Measures.", "Bug - (1557) Asset Detail page show Last Recorded timestamp (UTC) as per user preference", "Bug - (1545) Dashboard tile select without asset due to cache", "Bug - (1473) In the Dashboard-widget Settings modal, users are allowed to select a Dashboard Template and proceed with override measure mapping even when no Asset has been selected", "Bug - (1540) User Name should be Case Insensitive", "Feature - (1531) Value/title widget support in dashboard template mode", "Bug - (1448) Show full path for assetType name in Dashboard Template List", "Feature - (1535) When there is no data from api we will show dash (-) instead of O"]}]}, {"version": "2.0.218", "updated_at": "20-May-2025", "changes": [{"description": ["Refactor - (1538) Refactored Data Ingestion Flow", "Bug - (1542) Fixed Heatmap reversed values issue", "Feature - (1533) Asset timezone in mobile view should always be false", "Bug - (1419) Diagram Widget measures value Not appearing on retain After Redirecting to Linked Dashboard Template"]}]}, {"version": "2.0.217", "updated_at": "19-May-2025", "changes": [{"description": ["Bug - (1546) User redirected via Alert SMS link sees client error if not logged in; does not land on Alert Trend page after login", "Feature - (1536) On realtime indicator we will show blinking state effect", "Feature - (1532) Edit Asset Template with calculated matrics."]}]}, {"version": "2.0.216", "updated_at": "16-May-2025", "changes": [{"description": ["Task - (1538) Rename from TSDB to Data injection", "Bug - (1514) Dashbaord tile select without the asset then link  clcik on not work"]}]}, {"version": "2.0.215", "updated_at": "15-May-2025", "changes": [{"description": ["Bugfix - (1422) Disable update button when value widget mode is measurement", "Feature - (1539) <PERSON><PERSON> show alerts events data along with trend", "Bugfix - (1423) Seperate Title formatting options for Fixed Value Type", "Task - (1534) Heatmap widget label"]}]}, {"version": "2.0.214", "updated_at": "14-May-2025", "changes": [{"description": ["Feature - (1518) Add export Feature Dynamic Charts"]}]}, {"version": "2.0.213", "updated_at": "13-May-2025", "changes": [{"description": ["Feature - (1499) BE -Asset template with calculated metrics", "Feature - (1500)  FE - Asset template with calculated metrics", "Bug - (1514) Time Range should show not show date time"]}]}, {"version": "2.0.212", "updated_at": "08-May-2025", "changes": [{"description": ["Rename - (1488) Override measure mapping -> Measure Mapping", "Bug - (1489) Widget Error State Shows Empty Instead of Previous Value", "Bug - (1453) Autoupdate not updating Forecast", "Bug - (1466) <PERSON><PERSON> Template's metric name is constrained"]}]}, {"version": "2.0.211", "updated_at": "07-May-2025", "changes": [{"description": ["Refactor - (1476/1477) Alert module changes", "Feature - (1442) Diagram Widget Status Indicator", "Feature - (1452) Widget status indicator with realtime and Non-realtime settings"]}]}, {"version": "2.0.210", "updated_at": "06-May-2025", "changes": [{"description": ["Refactor - (1476/1477) Alert module changes", "Feature -(1432) KPI Trend Chart Status Indicator", "Feature - (1433) Gauge Chart Status Indicator", "Feature - (1435) KPI Table Status Indicator", "Feature - (1436) Map Widget status indicator", "Feature - (1437) KPI Sparkline status indicator", "Feature - (1438) KPI percentage status indicator", "Feature - (1439) KPI Trend status indicator"]}]}, {"version": "2.0.209", "updated_at": "05-May-2025", "changes": [{"description": ["Feature - (1426) Stats Widget Status Indicator", "Feature - (1427) Bar And trend Chart Status Indicator", "Feature - (1428) Table Widget Status Indicator", "Feature - (1429) Bullet Chart Status Indicator", "Feature - (1431) KPI Table Chart Status Indicator"]}]}, {"version": "2.0.208", "updated_at": "30-Apr-2025", "changes": [{"description": ["Bug - (1443) Remove home button from edit expression template page"]}]}, {"version": "2.0.207", "updated_at": "29-Apr-2025", "changes": [{"description": ["Bug - (1412) Sankey Chart Virtual Node Auto-Appending 'sss' to Label When Selecting Destination on dashboard template page.", "Bug - (1417) Multiplot inside dashboard tile showing proper data", "Bug - (1420) Sankey widget is not showing the proper  chart on Dashboard  template as well as  on Dashbaord  also", "Bug - (1411) All widget redirect to extended dashboard template page fix"]}]}, {"version": "2.0.206", "updated_at": "28-Apr-2025", "changes": [{"description": ["Feature - (1406) Multiplot Widget for dashboard template", "Bug - (1411) All widget's Global link option working", "Bug - (1410/1411) <PERSON><PERSON>, Image widget's per-measure link redirection working"]}]}, {"version": "2.0.205", "updated_at": "25-Apr-2025", "changes": [{"description": ["Refactor - (1396) Remove Home Button", "Refactor - (1396/1405) Remove Home Button, Text Change", "Refactor - (1396/1405) Sankey Widget Settings UI Refactor", "Feature - (1402) Layout changes remove the Home Button from the all the places in the product", "Feature - (1403) Layout changes for remove white spaces 4K screen ration", "Feature - (1235) Sankey Link Configurations for Dashboard Template Page."]}]}, {"version": "2.0.204", "updated_at": "24-Apr-2025", "changes": [{"description": ["Feature - (1377) Layout changes - Asset Templates Page", "Bug - (1260/1270) Alert form validation", "Bug - (1376/1381) Alert and Excursion Layout.", "Bug - (1379/1380) Dashboard Layout.", "Feature - (1383) Layout changes - Measurement Browser page", "Feature - (1381) Excursion MinMaxAvg format", "Bug - (1398) Regular Users Cannot See Widget Menu", "BUg - (1395) For all widget template link is not visible for setting panel", "BUg - (1379) Dashboard table meta data", "BUg - (1379) Widget Settings Options for User"]}]}, {"version": "2.0.203", "updated_at": "23-Apr-2025", "changes": [{"description": ["Feature - (1394) Image widget needs common link  tab , remove all the links from existing  widget  from 3 places", "Bug - (1389) Linked Dashboard Template Does Not Display KPI Bar Chart Measurement Name — Shows 'Unknown Measure'", "Bug - (1392) KPI Percentage Widget 'Update' <PERSON><PERSON> Remains Disabled Despite Valid Measurement Configuration on Dashboard template page.", "Feauture - (1384) Layout changes - Users page", "Bug - (1396) Image widget is showing only link dashboard template on settings"]}]}, {"version": "2.0.202", "updated_at": "22-Apr-2025", "changes": [{"description": ["Bug - (1368) Getting browser_tz UTC in forecast api call even if brower tz in Asia/Calcutta", "Feature - (1355) New Image Widget Settings Layout Configuration", "Bug - (1369) Image Widget Retains Visuals but Fails to Display Measurement Name, Value, and Unit After Linking to Dashboard/Template", "Bug - (1373/1233) Sankey Dashboard Template Redirect.", "Bug - (1370) Linked Template Not Retained or Displayed in Settings When Selected via Dashboard Tile", "Refactor - (1375) Alert Analytics Page UI Refactor", "Feature - (1353) New Diagram Widget Settings Layout Configuration", "Refactor - (1378) Asset Tree UI Refactor", "Refactor - (1376) <PERSON><PERSON> Page UI Refactor", "Refactor - (1384) Users Page UI Refactor", "Refactor - (1381) Excursions Page UI Refactor", "Task - (1393) Dashboard Tile in Dashboard Template Flow", "Bug - (1270) Create Alert Form validation when threshold type is STALE | DEAD", "Bug - (1391) Dashbaord Tile option is not visible in Template"]}]}, {"version": "2.0.201", "updated_at": "21-Apr-2025", "changes": [{"description": ["Feature - (1355) New Image Widget Settings Layout Configuration", "Feature -(1365) Dashboard Tile - Show redirection link per widget if link is present.", "Feature - (1356) New Dashboard Tile Widget Settings Layout Configuration", "Feature - (1357) New KPI Bar Widget Settings Layout Configuration", "Feature - (1260) Alert DEAD Measurement", "Feature - (1268) Alert STALE Measurement", "Bug - (1233) Sankey all node elements are clickable.", "Bug - (1362) Update button enabled without selecting asset or measurement – missing validation"]}]}, {"version": "2.0.200", "updated_at": "18-Apr-2025", "changes": [{"description": ["Bug -(1352) After enable the  Realtime setting on widget it was Flickering to KPI Percentage Settings & KPI Sparkline Settings", "Feature - (1233) Support Per Label/Measure link redirection in Sankey Chart.", "Bug - (1351) Linked Dashboard Template Not Rendering After Widget Link Click", "Bug - (1361) Measurement mapping is not retained or passed correctly during the linking process.(map widget) – 'Please select a measure from widget settings' message displayed", "Bug - (1358) Table widget on enable the Realtime  it is flickring", "Bug - (1342) Internal Server Error (500) when adding annotation – 'Measurement not found' on trend/bar chart", "Bug - (1343) Notification Font Size Mismatch After Updating Calculation Measure", "Feature - (1350) Realtime support for diagram widget"]}]}, {"version": "2.0.199", "updated_at": "17-Apr-2025", "changes": [{"description": ["Bug -(1336) Dashboard template link not showing measurements", "Feature - (1237) Create Common Widget Settings Configuration KPI percentage, KPI Table, KPI Sparkline", "Bug -(1344) Diagram Add Label/variable issue fix."]}]}, {"version": "2.0.198", "updated_at": "16-Apr-2025", "changes": [{"description": ["Bug -(1336) Refactor overlapping legend on Measurement Trend <PERSON>", "Feature - (1237) Create Common Widget Settings Configuration heatmap, gauge, KPI Trend, Map Widget, KPI Current", "Bug - (1345) Add Widget is replacing existing widgets", "Bug - (1347) Dashboard Clone setting is not working properly for all widget"]}]}, {"version": "2.0.197", "updated_at": "15-Apr-2025", "changes": [{"description": ["Bug -(1332) Render image, without selecting measure", "Feature - (1128/1323) Multiplot Forecast support", "Feature - (1234) Per Label/Measure AggBy support for sankey", "Feature - (1237) Create Common Widget Settings Configuration Scatter,bar,Stats, Table"]}]}, {"version": "2.0.196", "updated_at": "12-Apr-2025", "changes": [{"description": ["Bug -(1337) Sankey split should have equal values"]}]}, {"version": "2.0.195", "updated_at": "11-Apr-2025", "changes": [{"description": ["Bug -(1128) Auto legend position"]}]}, {"version": "2.0.194", "updated_at": "10-Apr-2025", "changes": [{"description": ["Blocker - (1329) Restored the deleted .developement env file, as it was causing the issue while starting the project.", "Bug - (1319) On Diagram widget Global Time Range is set to more than 6 hours, then should have the pop message on UI.", "Bug - (1321) Forecast Period dropdown label fix", "Bug -(1128) When forecast is on, handle the chart title and legend.", "Bug -(1128) Auto legend position"]}]}, {"version": "2.0.193", "updated_at": "09-Apr-2025", "changes": [{"description": ["Bug - (1311) On diagram Widget Arggragte & Override time range setting  not working as expected.", "Task - (1312) Chart's legends auto position", "Feature - (1317) Sankey chart does not render when source is split in 2 virtual nodes", "Bug - (1319) On Diagram widget Global Time Range is set to more than 6 hours, then should have the pop message on UI.", "Feature - (1128) Show forecast feature, for multiplot widget"]}]}, {"version": "2.0.192", "updated_at": "08-Apr-2025", "changes": [{"description": ["Bug - (1207) Image widget preview image ratio and size fix", "Bug - (1298) Alert Events Table Displays State Values as Numbers (1/0) Instead of Text Labels (NORMAL/EXCEEDED)", "Feature - (1255) Dynamic ports for Diagram Builder", "Feature - (1306) Pre-populated asset type and template in dropdown"]}]}, {"version": "2.0.191", "updated_at": "07-Apr-2025", "changes": [{"description": ["Bug - (1289) Dashboard Layout Overlap Issue – Time Range Obstruction", "Bug - (1286) Trend should open as dynamic chart", "Feature - (1287) Dynamic Chart should have range slider option", "Bug - (1231) Appropriate error message for customer creation failure", "Task - (1283) Always enable dashbard save button", "Task - (1258) Show warning message when user tries to save global dashboard template", "Bug - (1289) Dashboard Layout Overlap Issue – Time Range Obstruction"]}]}, {"version": "2.0.190", "updated_at": "03-Apr-2025", "changes": [{"description": ["Feature - (1130) Widget Settings Realtime Setting - Trend and Bar Chart.", "Bug - (1126) Fix broken bar chart render in multiplot widget", "Bug - (1230) Fix broken excel sheet tab name for multiplot widget"]}]}, {"version": "2.0.190", "updated_at": "03-Apr-2025", "changes": [{"description": ["Feature - (1223) Override multiplot chart color.", "Bug - (1196) Duplicate Override Title Displayed", "Bug - (1207) Prevent Image Stretching in Image Widget by Maintaining Aspect Ratio", "Feature - (1217) Add Toggle Functionality to Show/Hide Left-Side Panel in Dashboard Template", "Bug - (1216) New Widgets Overlap Existing Widgets in ElectricMeter_Row Dashboard Template", "Feature - (1147) User error message."]}]}, {"version": "2.0.189", "updated_at": "03-Apr-2025", "changes": [{"description": ["Feature - (1207) Prevent Image Stretching in Image Widget by Maintaining Aspect Ratio"]}]}, {"version": "2.0.189", "updated_at": "02-Apr-2025", "changes": [{"description": ["Bug - (1167) Show Warning When 'None' Aggregation Is Selected for Time Ranges Over 6 Hours", "Task - (1210) Sort Unit Group table by Measurement Type, then Unit Group, rename 'Unit of Group' to 'Unit Group'", "Bug - (1140) T<PERSON>d Widget not showing Sum when more than one measurement", "Bug - (1176) Consistant Filter Chips with Clar all button for all the mentioned modules", "Bug - (1207) Image widget preview image ratio and size fix", "Feature - (1133) Widget Settings Export(Excel & PDF) Setting", "Feature - (1214) Alert Creation Validation"]}]}, {"version": "2.0.188", "updated_at": "01-Apr-2025", "changes": [{"description": ["Task - (1200) Sort Alert Details -> Events table in DESC order based on Timestamp", "Task - (1200) Sort Alert Details -> Alert Stats Data table in DESC order based on EndTime", "Feature - (1201) Clicking on 'Trend' link in Measurement Browser page throws client-side exception", "Bug - (1188) Autofilled Password Not Cleared on Username Change - Password field retains old value when username is changed", "Bug - (1193) User Filter also not have the clear button", "Bug - (1185) Restrict annotation creation if dashboard is not created", "Bug - (1189) Show Full Asset Path in Filters -Display full asset path in filter dropdowns", "Bug - (1204) Resolved flickering issue on stats widget when Real-Time is enabled", "Bug - (1192) Temporary removing the X-Axis Label input for the charts", "Bug - (1147) API Exception messages shown for creating diagram, expressionTemplate and asset"]}]}, {"version": "2.0.187", "updated_at": "31-March-2025", "changes": [{"description": ["Bug - (1186) Remove trend chart default dots on the trend line, to avoid annotation conflict", "Bug - (1194) Multiplot chart hovered tooltip maintain consistency", "Bug - (1175) Show assetMeasure Spcific MIN/MAX/AVG lines for multiplot widget", "Feature - (1129) Show assetMeasure Spcific Threshold Line for multiplot widget", "Feature - (1125) Show Legend and legend position input for multiplot widget", "Task - (1190) Rename Background Transparency to Background Opacity in Diagram Builder", "Feature - (1180) Dynamically render dashboard template when linked in widgets"]}]}, {"version": "2.0.186", "updated_at": "28-March-2025", "changes": [{"description": ["Bug - (1166) <PERSON><PERSON> to Trend in Dynamic Charts Page", "Bug - (1044) <PERSON><PERSON> Widget Update button disable issue fix for dashboard template"]}]}, {"version": "2.0.185", "updated_at": "27-March-2025", "changes": [{"description": ["Task - (1155) Rename Create Diagram to Diagram Builder", "Feature - (1134) Override Asset Time zone, Override Global Sample Period, Override Global Time Range for MultiPlot Widget", "Bug - (1165) Fix Incorrect Override Sample Period Selection in the Widget Settings", "Feature - (1126) Subplot Specific show sparkline(trend) for Multi-Plot widget", "Feature - (1085) Add search bar for Template Management screen", "Bug - (1171) Add <PERSON> to <PERSON><PERSON> in Dashboard Template List", "Bug - (1169) Show separate min max and avg lines if charts are aligned in one row in Multi-Plot widget", "Feature - (1132) Link Dashboard integration in the MultiPlot Widget"]}]}, {"version": "2.0.184", "updated_at": "26-March-2025", "changes": [{"description": ["Feature - (1069) Multi-Plot widget creation", "Feature - (1115) API integration for Multi-Plot widget charts dynamic data", "Feature - (1124) Subplot specific range slider for Multi-Plot widget", "Bug - (1121) Diagram: Labels don't keep the value entered", "Bug - (1142) Dashboard template instance is giving Conflict error", "Bug - (1138) Sankey widget settings delete label and connection implemented, also refactored the UI/UX", "Bug - (1119) Enabled element Label/variable border options", "Bug - (1145) Fix delete asset measure on multiplot widget", "Bug - (1146) Multiplot charts overlapping when range slider added", "Bug - (1148) Show hovered tooltip details for specific chart measure in multiplot widget", "Bug - (1134) Subplot specific Show Area for Multi-Plot widget", "Feature - (1108) Support Aggregation, Time Range and Sample Period per Variable in Diagram Widget", "Bug - (1149) Sankey chart - Only virtual nodes can be destinations", "Feature - (1131) Subplot specific show min, max, avg for Multi-Plot widget", "Feature - (1150) Expression Template Spelling mistake"]}]}, {"version": "2.0.183", "updated_at": "25-March-2025", "changes": [{"description": ["Bug - (1062) Missing Forecast Data in Trend Chart on Dashboard Template", "Bug - (1103) Creating a Duplicate User Shows Generic 'Bad Request' Error", "Bug - (1083) Adding Diagram into Dashboard Template - Referencing Asset"]}]}, {"version": "2.0.182", "updated_at": "24-March-2025", "changes": [{"description": ["Feature - (1113) Asset Template Disconnection", "Bug - (1103) Creating a Duplicate User Shows Generic 'Bad Request' Error", "Bug - (1084) Widget settings end date can not be less then start date", "Bug - (1057) Reference Column Not Visible Due to Scroll Bar in Alert History Events Data Page", "Bug - (1063) Missing Legend Option in Trend Chart on Dashboard Template", "Bug - (1107) Diagram: Label background color doesn't respond", "Bug - (1109) Enable formatting for values in diagram variables", "Bug - (1011) Parameters do not apply to Anomaly (should hide)"]}]}, {"version": "2.0.181", "updated_at": "21-March-2025", "changes": [{"description": ["Feature - (1068) Add new page for measurement browser with paginated recorded datas", "Bug - (1099) Missing Data Source in Measurement Browser", "Bug - (1088) Duplicate Measure Entries in Dynamic Chart Dropdown", "Feature - (1072) Filtering option for measurements table", "Bug - (1100) Application Error in Measurement Browser Page", "Bug - (1102) Sample period is not updating on change of time range"]}]}, {"version": "2.0.180", "updated_at": "20-March-2025", "changes": [{"description": ["Feature - (1071) Dynamic Chart on asset details page", "Feature - (1070) Create Dynamic Charts Page", "Feature - (1068) Add new page for measurement browser", "Feature - (1073) Trend Chart Integration"]}]}, {"version": "2.0.180", "updated_at": "18-March-2025", "changes": [{"description": ["Feature - (501) Make <PERSON><PERSON> Summary Bar Clickable"]}]}, {"version": "2.0.180", "updated_at": "18-March-2025", "changes": [{"description": ["Bug - (1056) Chart Fails to Render When Changing Time Zone in Real-time Settings"]}]}, {"version": "2.0.179", "updated_at": "17-March-2025", "changes": [{"description": ["Refactor - (1029) Fix layout in the product for all the pages"]}]}, {"version": "2.0.179", "updated_at": "13-March-2025", "changes": [{"description": ["Bug - (1034) On Asset Detail page Value should be round up formula", "Bug - (1036) When we select relative to global end time then it should update on global end time changes", "Bug - (979) Aggregate none needs to be limited in the UI to a maximum of 6 hours.", "Bug - (1043) Fix Warning Message Display for Sample Period Selection", "Bug - (1033) Fix alert creation bug", "Bug - (652) Fix Pump Fan resizing", "Task - (1046) Disabled ANOMALY type for alert threshold", "Task - (650) Fix ControlValve element's resizing"]}]}, {"version": "2.0.178", "updated_at": "12-March-2025", "changes": [{"description": ["Bug - (916) Link animation validation", "Bug - (735) Fix domain element's palette preview", "Bug - (886) Dashboard Tile Bug Fix, when user re-opens the widget settings", "Bug - (886) Dashboard Tile Bug Fix, when user re-opens the widget settings", "Feature - (1031) Update widgets aggregate by behaviour for STD.P and Monthly combination", "Bug - (652) Fixed Pump element's resizing.", "Bug - (651) Fixed HandValve element's resizing.", "Bug - (1023 Fixed Asset Details page showing Missing data", "Feature - (1027) Last N time range option should be relative to Global time range", "Feature - (916) Link animation validation updated", "Task - (1038) Restrict dashboard template when tile widget is present at dashboard", "Bug - (1037) Font size relative global and global time range settings"]}]}, {"version": "2.0.176", "updated_at": "11-March-2025", "changes": [{"description": ["Bug - (894) Show more data on Asset Details page with measurements with  show measurements which all are associated with current selected asset only. if there is child will not include those measurements", "Bug - (898) <PERSON><PERSON> Widget: Missing Validation and Highlighting for Asset Selection", "Bug - (924) Only show relavent input fields when alert threshold type is selected as NOMONAL or ANOMALY", "Bug - (1022) Asset template Selection Does Not Navigate to Next Page", "Bug - (1012) Alerts should also allow for Aggregate NONE", "Bug - (1024) Export to Excel Not Working for Heatmap Widgets", "Bug - (991) All widgets, and the nested widgets inside dashboard widget are looking smooth and crisp.", "Bug - (1025) 'Oops! Something went wrong.; appears after selecting asset type in widget settings on the Alert widget."]}]}, {"version": "2.0.176", "updated_at": "10-March-2025", "changes": [{"description": ["Bug - (1010) Dashboard template global option is disabled for Global admin", "Bug - (1002) Make the import button a green one in line with 'ADD ASSET TEMPLATE' and 'ADD ASSET TEMPLATE INSTANCE'", "Bug - (992) Multiple Asset Selection Should Not Be Allowed in Alert Creation", "Bug - (899) Asset Type Filter Not Displaying Alerts in Alert List", "Task - (1001) Hide dashboard tile widget if we're on dashboard-template page", "Task - (1019) Show trend chart in a popup modal"]}]}, {"version": "2.0.175", "updated_at": "7-March-2025", "changes": [{"description": ["Feature - (990)Show dashboard template based on active customer", "Feature - (902)Global Dashboard Template Can be accessible for Super admin", "Feature - (1003)Sync Dashboard Widget based on parent dashboard template", "Task - (764)Removed Excursion table state column", "Feature - (988) Handle Export & Import for dashboard template", "Feature - (1007) Save Dashboard as Template", "Bug -(1009) Make import / export button primary"]}]}, {"version": "2.0.174", "updated_at": "6-March-2025", "changes": [{"description": ["Task - Show N/A if threshold type is ANOMALY in alert details page", "Feature - Hide all widget options when widget is inside the dashboard widget", "Feature - Hide widget resize handlers when widget is inside the dashboard widget", "Bug - Get specific dashboard templates based on selected asset type.", "Bug - Dashboard creation from instance have issue while creating it due to wrong sync calls", "Feature - On retrival of dashboard will do metric to measurement mapping for dahsboard", "Feature - Disconnect a dashboard from the template", "Feature - Glboal/Customer specific dashboard templates"]}]}, {"version": "2.0.175", "updated_at": "5-March-2025", "changes": [{"description": ["Bug - Error 'Oops! Something Went Wrong' Appears for KPI Current widget", "Bug - Save Dashboard as Dashboard template not working for bar/scatter", "Bug - Dashboard Template Instance - Trend Chart, Bar Chart, Map Widget, and Sankey Chart Not Rendering on Retain", "Feauture  - API changes for save/update dashboard template instance", "Feature - warn user on update of dashboard template instance"]}]}, {"version": "2.0.174", "updated_at": "4-March-2025", "changes": [{"description": ["Feature - Convert asset templates cards selectable (Radio Button)", "Bug - Time Range Selection Causes Loop Issue on Test and Production", "Bug - Error 'Oops! Something Went Wrong' Appears When Selecting Time Range of 7 Days or More for dashboards"]}]}, {"version": "2.0.173", "updated_at": "3-March-2025", "changes": [{"description": ["Feature - Asset template import/export", "Bug - units of measure for newly created measurements", "Bug - Save as Global Asset Template Option is Available for Customer Admin & Power Users After Importing Asset Template", "Feature - while editing asset template flow show warning in collapsed manner"]}]}, {"version": "2.0.172", "updated_at": "28-Feb-2025", "changes": [{"description": ["Bug - Submit <PERSON><PERSON> Disabled After Adding Measurement But Updates Successfully", "Bug - Data Source Column Not Displaying in Create Asset Template Page"]}]}, {"version": "2.0.171", "updated_at": "27-Feb-2025", "changes": [{"description": ["Bug - Filtered Dashboard-templates based on selected asset type", "Feautre - asset template add measurement to add measurement to assets on dashboard", "Feature - asset template clone form for confirm model number and Manufacturer"]}]}, {"version": "2.0.170", "updated_at": "26-Feb-2025", "changes": [{"description": ["Feature - Global asset template", "Feature - Customer Specific asset template", "Feature - Show only  Customer specific and global asset templates only", "Feauture - clone global asset template to customer specific asset template", "Feature - Glboal asset template RBAC to admins", "Feature - Glboal asset template can be clone by customer specific admins/global admins", "Bug - customer specific admin can create global asset templates", "Bug - show measurement metrics on measurement details page"]}]}, {"version": "2.0.169", "updated_at": "25-Feb-2025", "changes": [{"description": ["Feature - Change label for measurements on asset details page"]}]}, {"version": "2.0.168", "updated_at": "24-Feb-2025", "changes": [{"description": ["Feature - Increased visible period for realtime to 10,20,30 mins", "Task - Disable Link Animation"]}]}, {"version": "2.0.168", "updated_at": "24-Feb-2025", "changes": [{"description": ["Refactor - Alert details page UI refactor", "Feature - Show more data on Asset Details page", "Bug - show trend on dashboard template without data as well", "Bug - Show no data text on alert details UI"]}]}, {"version": "2.0.167", "updated_at": "21-Feb-2025", "changes": [{"description": ["Bug - <PERSON><PERSON> is not working with filter for some specific assets", "Bug - General elements title", "Bug - Visible period updation in real-time chart", "Bug - KPI Bar chart data issue", "Bug - KPI Bar chart unwanted api calls on previous values", "Bug - Alerts events and stats page show UTC label"]}]}, {"version": "2.0.166", "updated_at": "20-Feb-2025", "changes": [{"description": ["Bug - Alert meta data created at/updated at fix", "Bug - timestamp issue fix and dateformating"]}]}, {"version": "2.0.165", "updated_at": "18-Feb-2025", "changes": [{"description": ["Feature - Alert Widget filter with asset to its childs", "Bug - Image element border color and width fix", "Bug - Link animation after selecting image in create diagram and diagram widget", "Bug - Circle element resizing issue", "Bug - Conditional Rule- Border, Title, Color Not Working for Progress Bar on Dashboard.", "Bug - Progress Bar Fill Style 'Solid' Is Not Working", "Bug - asset type filter not working on alert widget"]}]}, {"version": "2.0.164", "updated_at": "17-Feb-2025", "changes": [{"description": ["Bug - <PERSON><PERSON>d page null checks", "Bug - Border rule UI improvements, and fixes"]}]}, {"version": "2.0.163", "updated_at": "14-Feb-2025", "changes": [{"description": ["Feature - Round of all alert values", "Bug - Pump element ports are visible", "Bug - Favourite Dashboard showing correct message"]}]}, {"version": "2.0.162", "updated_at": "14-Feb-2025", "changes": [{"description": ["Bug - Normal users can see Export/Import dashboard Options.", "Bug - Measurement Name Truncated in Measurement Tag Table Column of <PERSON><PERSON> Widget.", "Feature - Update 'CalcEngine' Icon to a More Mathematical Symbol", "Bug - Image widget existing images are not supporting"]}]}, {"version": "2.0.162", "updated_at": "13-Feb-2025", "changes": [{"description": ["Feature - Mention resize aspect ration when element is an image", "Bug - image widget issue fix", "Task - Disable Select Threshold Type input in all cases for edit alert flow", "Feature - Dashboard template alert widget", "Bug - Alert widget/table widget default pagination", "Bug - root asset was not showing on asset measurements drop down"]}]}, {"version": "2.0.161", "updated_at": "12-Feb-2025", "changes": [{"description": ["Feature - Create Anomaly", "Feature - Edit Anomaly", "Bug - alert widget measurement filter", "Bug - edit dashboard title tab on mobile screen", "Feature - added asset type and metric for alert widget", "Bug - image widget max file upload validation", "Bug - image widget asset type and measurement types", "Bug - Handled self connections from diagram elements"]}]}, {"version": "2.0.160", "updated_at": "11-Feb-2025", "changes": [{"description": ["Feature - alerts widget", "Feauture - image widget custom images"]}]}, {"version": "2.0.159", "updated_at": "10-Feb-2025", "changes": [{"description": ["Bug - Modify port removal code, also removed custom pipe animation code", "Bug - Image element conditional rules working", "Feature - Default bordered image element", "Feature - Partially removed image element background and border", "Feature - import dashboard", "Bug - import dashboard duplicate title issue", "Feature - pump element's rule for rotation"]}]}, {"version": "2.0.158", "updated_at": "8-Feb-2025", "changes": [{"description": ["Bug - Agg dropdown options can select options properly", "Bug - Chart working fine even after selecting Agg delta-max", "Bug - Validations for alert analytics charts", "Bug - Reset Password text fix in user preferences", "Bug - Smaller ports at diagram widget level"]}]}, {"version": "2.0.157", "updated_at": "7-Feb-2025", "changes": [{"description": ["Feature - Create a link for Title and image widget", "Bug - after creation of dashboad template from dashboard it will redirect to dashboard template page", "Bug - Label Element Not Rendering on Canvas", "Bug - For Battery, Liquid Tank, Conical Tank & Progress Bar Maximum capacity value should be in limitation.", "Feature - Dashboard export"]}]}, {"version": "2.0.156", "updated_at": "5-Feb-2025", "changes": [{"description": ["Bug - For image elements added dropshadow when selected, multiple elements will also have dropshadow", "Bug - User preference show customers logo on dropdown", "Bug - fix label on navs", "Bug - Dashboard template Trend Chart is crashing when we open settings", "Bug - When we are on mobile screen and disable zoom", "Bug - <PERSON><PERSON><PERSON> Widget Expand Button Not Opening Full-Screen Canvas.", "Bug - General elements title name shows unnamed cell on widget setting after selecting diagram.", "Bug - When expand diagram widget it was showing a lot of space below the canvas", "Bug - On mobile view map widget zooming after making even zoom lock.", "Bug - On mobile view diagram widget zooming after zoom lock", "Bug - On Dashboard Template - for Trend chart forecast option are not available.", "Feature - map widget link to marker"]}]}, {"version": "2.0.155", "updated_at": "4-Feb-2025", "changes": [{"description": ["Feature - Domain element's in the palette, also upgraded the image pop-over position when it hit's to bottom viewport", "Feature - Link animations and link snaping like magnet effect", "Bug - reset password with user preferences", "Bug - Normal user can not see the default customer option on user prefernces", "Bug - power user unable to delete dashboard", "Bug - Power user create/update dashboard template permission", "Bug - Power User calc engine permission", "Bug - Normal user tree visible", "Bug - Make the Value with Round Up Formula given by <PERSON> use that on for all measurements"]}]}, {"version": "2.0.154", "updated_at": "3-Feb-2025", "changes": [{"description": ["Bug - For alert analytics drilldown, Pie chart Title shows overwritten and text cutting shape..", "Feature - Progress element in palette", "Feature - Hamburger menu for mobile view", "Bug - On mobile, even after disabling the zoom lock on charts, the KPI trend, KPI bar, KPI percentage, and KPI sparkline get zoomed in.", "Bug - For Image Widget setting asset measurement written outside the field"]}]}, {"version": "2.0.153", "updated_at": "31-Jan-2025", "changes": [{"description": ["Feature - Basic element's visual elements UI", "Feature - Added Ports to elements and changed the link conection flow", "Feature - Image Placeholder When Dropped", "Bug - Label element working", "Feature - Link end style"]}]}, {"version": "2.0.152", "updated_at": "30-Jan-2025", "changes": [{"description": ["feature - <PERSON><PERSON> Drilldown.", "Feature - disabled zoom on widget for mobile screen", "Bug - Customer RBAC", "Feature - Open dashboard in dashboardTitle tab", "Feature - Added diagram SVG library"]}]}, {"version": "2.0.151", "updated_at": "29-Jan-2025", "changes": [{"description": ["Bug - Custom element's refactor.", "feature - <PERSON><PERSON> Drilldown."]}]}, {"version": "2.0.151", "updated_at": "28-Jan-2025", "changes": [{"description": ["Feature - Stats widget unit of measure label", "Feature - Gauge Widget unit of measure label", "Feature - Heatmap Widget unit of measure label", "Feature - Bullet Widget unit of measure label", "Feature - Table Widget unit of measure label", "Feature - KPI table Widget unit of measure label", "Feautre - added RBAC for alert analytics and listing page", "Bug - hide reset zoom button on disabled zoom", "Feature - zoom/pan retain on dashboard save in diagram widget and diagram page", "Bug - Normal user unable to share dashboard"]}]}, {"version": "2.0.150", "updated_at": "27-Jan-2025", "changes": [{"description": ["Feature - Added range slider configurable on Scatter/Bar chart", "Feature - <PERSON><PERSON><PERSON> Widget - Zoom Lock", "Bug - Selected element border color and width values in sync.", "Bug - Border style based on conditional rules, also removed some border styles.", "Bug - For KPI percentage in place of value shows time and in place of time shows value.", "Bug - Heatmap showing wrong value for z index where not values there", "Bug - Increased default label element size", "Bug - Apply border styles for all over the element", "Feature - <PERSON><PERSON><PERSON> Widget - Add Dummy Units", "Bug - The user's session expires while working on the diagram page, leading to a loss of unsaved progress."]}]}, {"version": "2.0.149", "updated_at": "24-Jan-2025", "changes": [{"description": ["Bug - Restored grouping elements feature", "Bug - Elements delete variables in sync", "Feature - Diagram UOM ", "Feature - KPI Sparkline UOM", "Feature - Map widget dynamic measure unit label", "Bug - Basic element's border color fix", "Feature - Image widget UOM", "Bug - Restore element's connection", "Bug - Cylinder Element Border Fix"]}]}, {"version": "2.0.148", "updated_at": "23-Jan-2025", "changes": [{"description": ["Bug - On Annotation Add & Update Page multiple times annotation is written", "Bug - After saving Time-varying Factor measurement effective date format getting changed", "Bug - On Test dashboard, after clear asset/measurement field still shows data on chart.", "Bug - In Power User- Dashboard create/update permission", "Add - Added Toku images in the create-diagram", "Add - Added info for user understanding in pipe's Liquid dashed array", "Bug - Fixed battery element on diagram page", "Bug - Validation for the element values in common settings"]}]}, {"version": "2.0.147", "updated_at": "22-Jan-2025", "changes": [{"description": ["Bug - On Test Dashboard - Measurement are showing with asset path also", "Bug - Diagram label font size and title characters validation", "Bug - On Trend and Bar Chart dont  show the  MIn,, Max & Avg after enable the options", "Bug - Selected element's shadow fix on create diagram page", "Bug - session time out popup issue", "Bug - add annotation is not working", "Bug - Removed default orange color from elements on diagram widget", "Bug - Smart routing added in create diagram's connections", "Bug - For basic elements added the border color and weight config", "Bug - After adding annotation and save dashboard was showing variant on data point of annotation", "Bug - Annotation setting page once you change the setting, then changes are not saved", "Bug - Unable to Save Annotations After Repeated <PERSON><PERSON><PERSON> on the Same Point", "Bug - Annotation point made are changing the position of it on Dashboard saving.", "Bug - After adding the annotation we save the dashboard then we cannot see the annotation chart", "Bug - Disable diagram interactions at widget level", "Bug - Test Dashboard - after deselecting the asset measure are show in dropdown", "Bug - Added Annotation value and time"]}]}, {"version": "2.0.146", "updated_at": "21-Jan-2025", "changes": [{"description": ["Feature - Diagram label opacity slider", "Bug - Diagram widget edit button permission", "Fix - Moved edit diagram button in widget context menu", "Bug - Widget titles set as per the widget", "Bug - Create measurement UI fix", "Feature - Diagram update sync on Diagrm Widget", "Bug - Diagram variables are showing the exact values", "Bug - Diagram variables not connecting, also element's are only connecting once with each other", "Bug - In the Dashboard Template when we plot the Diagram Widget on board than while selecting the metric it don't show from the dashboard template"]}]}, {"version": "2.0.145", "updated_at": "20-Jan-2025", "changes": [{"description": ["Bug - While creating/updating time varying factor measurement back button not working as well as effective date also getting changed after saved.", "Bug - Asset type validation on UI and backend", "Feature - Diagram widget canvas PAN feature", "Bug - Customer logo validations", "Bug - Diagram double values on rectangle issue fixed", "Bug - Edit diagram flow on diagram widget"]}]}, {"version": "2.0.144", "updated_at": "17-Jan-2025", "changes": [{"description": ["Bug - Alert Analytics chart X-axis in sorted manner.", "Change - Hidden Realtime widget from add widget section.", "bug - While creating/updating time varying factor measurement back button not working as well as effective date also getting changed after saved.", "Feature - Add Customization for Label Color and Transparency", "Feature - Create Diagram link anchor is implemented"]}]}, {"version": "2.0.143", "updated_at": "16-Jan-2025", "changes": [{"description": ["Feature - Chart Annotations", "Feature - Chart Annotations update flow", "Bug - <PERSON><PERSON><PERSON> subplot issue", "Bug - Removed Total option from alert agg period", "Bug - Validation in excursion date filter", "Bug - For excursion start and end time format based on user preference", "Bug - Asset type 0th assets are showing.", "Bug - Added openobserve prod configs.", "Bug - On edit asset template page, after deleting measures and after adding measures Textboxes alignment not showing properly.", "Bug - On trend Chart after clicking on checkbox of show as sparkline showing select metric option text box but measures dropdown not available."]}]}, {"version": "2.0.142", "updated_at": "15-Jan-2025", "changes": [{"description": ["feature - <PERSON><PERSON>"]}]}, {"version": "2.0.141", "updated_at": "14-Jan-2025", "changes": [{"description": ["feature - Excursion List.", "feature - Filters in excursion list."]}]}, {"version": "2.0.140", "updated_at": "13-Jan-2025", "changes": [{"description": ["Bug - effective date is not able to select previous dates", "feature - alert stats and analytics page", "Bug - Map Widget override default location for marker"]}]}, {"version": "2.0.139", "updated_at": "10-Jan-2025", "changes": [{"description": ["Bug - Rename diagram widget", "Bug - Forecast Value Not Showing on Trend Chart", "Bug - Removed 'Total' from Aggregate By dropdown", "Feature - Integrate RUM on Pivotol SPA.", "Bug - Asset type dropdown hide 0 values."]}]}, {"version": "2.0.137", "updated_at": "08-Jan-2025", "changes": [{"description": ["Bug - On Dashboard Template Still which are zero asset should remove from dropdown.", "Bug - Active Customer Change not working properly", "Bug - Realtime chart data sorting", "Bug - Removed KPI Circle Widget", "Bug - On KPI percentage even after selecting metrics data not shows.", "Bug - Dashboard creating from template KPI Bar title not working", "Bug - Remove from Inter link between all Chart widget ( like Heatmap, Gauge, Bullet, Sankey.) only Trend and Bar should be there interlink", "Bug - For Normal User - Alert List is only view mode- but can see the edit button also"]}]}, {"version": "2.0.136", "updated_at": "07-Jan-2025", "changes": [{"description": ["Bug - Alert clear filter issue fix"]}]}, {"version": "2.0.135", "updated_at": "06-Jan-2025", "changes": [{"description": ["Bug - Link Shared not working", "Bug - Dashboard template override bar color fix", "Feature - Remove Heatmap / Gauge from charts"]}]}, {"version": "2.0.134", "updated_at": "03-Jan-2025", "changes": [{"description": ["Bug - Sankey Dashboard template issue", "Bug - Subplot issue on dashboard template", "Bug - On Bar Chart , don't show  the Min, Avg & Max checkmark is tick.", "Feature - Add Link on dashboard linkage settings", "Feature - <PERSON><PERSON> Set<PERSON>s", "Feature - Add Expression templates option on navs", "Bug - Without selecting the metrics for Heatmap chart showing by default phasebvoltage measure data on chart.", "Bug - Without selecting metrics from widget setting for Guage chart showing data on chart.", "Bug - Without selecting metric on widget setting of bullet chart data getting default on chart after dragging widget.", "Bug - On KPI Bar chart measures name shows unknown measure and data shows only in month format.", "Bug - KPI sparkline shows data without selecting measures from widget setting.", "Bug - On KPI circle, after plotting the widget circle auto rendering on chart and showing unknown measure even after selecting or not selecting measure.", "Bug - On KPI Bar chart measures name shows unknown measure and data shows only in month format.", "Bug - On KPI circle, after plotting the widget circle auto rendering on chart and showing unknown measure even after selecting or not selecting measure.", "Bug - KPI sparkline shows data without selecting measures from widget setting.", "Bug - Map widget shows measurement option twice and asset dropdown also on dashboard template.", "Feature - Show on on asset types which have templates on dashboard template", "Feature - added new menu "]}]}, {"version": "2.0.133", "updated_at": "02-Jan-2025", "changes": [{"description": ["Feature - Alert page UI changes.", "Bug - Map measure label fix.", "Bug - Add alert user permission fix.", "Feature - User can edit any/all dashboard titles.", "bug - Validation while editing the dashboard titles."]}]}, {"version": "2.0.132", "updated_at": "31-Dec-2024", "changes": [{"description": ["Feature - Removed view settings on new UI.", "Feature - add new customer", "Bug - Create and update customer permissions fix.", "Feature - User can edit inline default dashboard's title ", "Feature - Widget title based on the widget.", "Bug - KPI Bar, KPI Percentage, image widget, charts, stats widget fix on dashboard tFemplate"]}]}, {"version": "2.0.131", "updated_at": "30-Dec-2024", "changes": [{"description": ["Feature - active customer with edit options and images", "Bug - Asset Parent tag issue fixed.", "Bug - Disable Datatype Field in Update Measurement Form"]}]}, {"version": "2.0.130", "updated_at": "27-Dec-2024", "changes": [{"description": ["Bug - User permissions refactor.", "Bug - Real time chart dummy data.", "Bug - Removed 2d widget", "Bug - Able to save Empty diagram on create diagram page", "Bug - While creating asset template instance getting error Invalid tag name and save and finish button also not visible.", "Bug - Dashboard nav fix"]}]}, {"version": "2.0.129", "updated_at": "26-Dec-2024", "changes": [{"description": ["Bug - There should be proper name for Unit of measure  for text box", "Bug - When try to plot Map Widget measurements dropdown is not visible.", "Bug - When we Drag and Drop the widget  on board  then widget still rendering.", "Bug - For Sanky chart asset dropdown not available", "Bug - Fixed selection issue"]}]}, {"version": "2.0.128", "updated_at": "24-Dec-2024", "changes": [{"description": ["Feature - added Unit of measure with Measurement type.", "Feature - Asset template metric drop down allow custom values", "Bug - Real time chart data.", "Bug - As Show Sub Plot is not show the drop down on Widget setting", "Bug - After putting wrong current password and new password & confirm password true in reset password field, getting error 400 bad request instead of invalid current password"]}]}, {"version": "2.0.127", "updated_at": "23-Dec-2024", "changes": [{"description": ["Bug - Prevent error 500 from create-alert modal.", "Bug - Combined alert filters.", "Feature - Unit of Group add/edit option", "Bug - While default dashboard deselected getting 400 error for Normal user.", "Bug - When Mouse hover the the chart the on top corner right side we can see the Widget setting option on Test Dashboard", "Bug - In Bar chart also missing some position related field from test ev.", "Bug - In Trend Chart setting option there are measurement position is missing", "Bug - For Circle Widget the Measurement name is not Visible.", "Bug - For KPI Bar Chart data is not visible on test Env."]}]}, {"version": "2.0.126", "updated_at": "20-Dec-2024", "changes": [{"description": ["Bug - Alert creation.", "Bug - Fresh user login, and user-pref flow."]}]}, {"version": "2.0.125", "updated_at": "19-Dec-2024", "changes": [{"description": ["Bug- User login re-direction issue.", "Bug - On select /Manage page you can see the  twice the same name", "Bug - For Title widget there Font size should have some limit", "Bug - For Stats widget it is showing <PERSON><PERSON><PERSON> while fetching data after plot on board and Font size issue", "Bug - While default dashboard deselected getting 400 error for Normal user.", "Bug - on KPI Bar chart asset label and measurement label not fully visible.", "Feature - removed full path from measurement", "Bug - Dashboard logo bug resolved."]}]}, {"version": "2.0.124", "updated_at": "18-Dec-2024", "changes": [{"description": ["Feature - <PERSON>owrd hide/show on login", "Bug - Normal user is able to select/unselect measure", "Bug - Normal user able to resize widgets", "Bug - For normal user asking for dashboard save confirmation", "Bug - Expand/Collapse tree issue fixed", "Bug- Able to navigate to customer and users.", "Bug - Rename user-pref to User Profile as per PPT.", "Bug - Test Dashboard customer disabled, it will show by default selected customer.", "Bug - Customer logo based on current selected customer.", "Feature - Removed tree and integrated timeseries with new UI", "Bug - Removed Active Customer Disabled Textfields From Dashboard"]}]}, {"version": "2.0.123", "updated_at": "16-Dec-2024", "changes": [{"description": ["Bug- Creating Asset Templates user shall be able to name Metrics anything", "Bug - Renaming root assets causes them to create new entry in asset hier"]}]}, {"version": "2.0.122", "updated_at": "13-Dec-2024", "changes": [{"description": ["Feature - Added Resize and Rotate Handlers for create-elements.", "Feature - Removed grouping from create-elements as it groups element by default.", "Feature - Rename save diagram -> save element, Also clear state for diagram name."]}]}, {"version": "2.0.121", "updated_at": "12-Dec-2024", "changes": [{"description": ["Bug - Fixed Permissions For Power User", "Bug - Fixed selection issue from Create-Diagram while saving the diagram"]}]}, {"version": "2.0.120", "updated_at": "11-Dec-2024", "changes": [{"description": ["Bug - Rename Reference Dashboard --> Trend", "Feature - Added alerts color based on state", "Remove User Preferences --> select currency", "Feature - Add dynamic elements section on create elements page.", "Feature - Grouped elements can connect from outside elements as well"]}]}, {"version": "2.0.119", "updated_at": "10-Dec-2024", "changes": [{"description": ["Bug - Forbidden access issue fixed", "Bug - User permissions changed and verified"]}]}, {"version": "2.0.118", "updated_at": "09-Dec-2024", "changes": [{"description": ["Bug - fixed UI fixes for variables/conditional rules", "Bug - Deleting an Element Does Not Remove Associated Labels", "Bug - Liquid tank element showing values in 3 places", "Bug - Resolved the pipe icons", "Bug - Added delete buttons for labels and variables", "Bug - Grouped elements was showing links between elements which are inside group", "Bug - When added an element from create-element, reset canvas and diagram name.", "Bug - Link between elements inside grouped elements.", "Feature - Allow user to change position inside grouped element and restrict to not move outside grouped element"]}]}, {"version": "2.0.117", "updated_at": "06-Dec-2024", "changes": [{"description": ["Feature - Pre-defined images for image element to select.", "Bug - Grouping element's showing link to grouped element", "Bug - Removed base64 string from the upload image."]}]}, {"version": "2.0.116", "updated_at": "05-Dec-2024", "changes": [{"description": ["Bug - fixed progress element.", "Bug - adedd max capacity on battery", "Feature - Added Link configuration", "Feature - Added edit icon for opening link editing modal", "Bugfix - Restrict width input's from link editing modal"]}]}, {"version": "2.0.114", "updated_at": "03-Dec-2024", "changes": [{"description": ["Refactor - Remove Options for element settings.", "Feature - Removed Add conditional rules on diagram page", "Feature - Eliminated upload json flow.", "Feature - Removed settings icon on elements", "Feature - added conditional rules on create element page", "Feature - Added new elements on create element page", "Feature - Group elements on diagram page", "Feature - <PERSON><PERSON> on shift+click+drag."]}]}, {"version": "2.0.113", "updated_at": "02-Dec-2024", "changes": [{"description": ["Feature - New Label element in Basic element.", "Feature -  Fix widget not clickable on dashboard page on diagram widget", "Feature - Pre-Filled variables and labels in element settings modal.", "Bug - Element specified var's and label's are rendered in the modal.", "Feature - Front/back option for elements.", "Bug -  <PERSON><PERSON> cancel was not working expected.", "Bug -  Label input fixed for basic and domain elements.", "Feature -  User can drop multiple/duplicate elements on the canvas.", "Bug - Diagram showing wrong on grouped elements on dashboard.", "Feature - Element settings enhanced and selection issue fixed.", "Bug - For the liquid tank Element variable option not showing on the element setting."]}]}, {"version": "2.0.112", "updated_at": "29-Nov-2024", "changes": [{"description": ["Bug - Diagram widget not able to load.", "Feature - Renamed title to label for all diagram.", "Refactor - Resolved the add variable and added in common settings.", "Feature - Add conditional rules "]}]}, {"version": "2.0.111", "updated_at": "28-Nov-2024", "changes": [{"description": ["Feature - Able to name the diagram when saving JSON.", "Feature - Able to name the diagram when saving JSON.", "Feature - When Adding diagram JSON to canvas, canvas doesn't reset.", "Feature - When adding variables to the elements from the diagram page, it adds that draggable label on the canvas."]}]}, {"version": "2.0.110", "updated_at": "27-Nov-2024", "changes": [{"description": ["Feature - Realtime api endpoint change.", "Feature - add conditional configs for elements"]}]}, {"version": "2.0.109", "updated_at": "25-Nov-2024", "changes": [{"description": ["Feature - added color configs on diagram when rendering on dashboard"]}]}, {"version": "2.0.108", "updated_at": "22-Nov-2024", "changes": [{"description": ["Feature - On Sankey show measurement details option.", "Feature - added animations on elements", "Feature - Created the battery element with it's configurations.", "Feature - Added the battery element into the diagram's add element section."]}]}, {"version": "2.0.107", "updated_at": "19-Nov-2024", "changes": [{"description": ["Feature - User cannot add same variables in the diagram elements.", "Bug -  <PERSON><PERSON><PERSON> Widget issues"]}]}, {"version": "2.0.106", "updated_at": "18-Nov-2024", "changes": [{"description": ["Feature - Create diagram make Liquid Tanks colors configurable settings."]}]}, {"version": "2.0.105", "updated_at": "15-Nov-2024", "changes": [{"description": ["Feature - on Create diagram make Liquid Tanks colors configurable."]}]}, {"version": "2.0.104", "updated_at": "7-Nov-2024", "changes": [{"description": ["Bug - Asset template instance creation measurement tag issue fix."]}]}, {"version": "2.0.103", "updated_at": "6-Nov-2024", "changes": [{"description": ["Feature - Show assets which only have templates when creating instance.", "Feature - Rename to Asset tag instead of tag", "Feature - Remove prefix from asset instance creation flow.", "Feature - Remove metric, description from measurements screen"]}]}, {"version": "2.0.102", "updated_at": "5-Nov-2024", "changes": [{"description": ["Feature - Use diagram templates from Diagram widget.", "Bug - fix calculated measure writeback issue."]}]}, {"version": "2.0.102", "updated_at": "30-Oct-2024", "changes": [{"description": ["Feature - Create Diagrams renamed widget.", "Feature - Save as and create diagram."]}]}, {"version": "2.0.102", "updated_at": "30-Oct-2024", "changes": [{"description": ["Feature - Create Diagrams api integration."]}]}, {"version": "2.0.101", "updated_at": "29-Oct-2024", "changes": [{"description": ["Feature - Create Custom Diagrams."]}]}, {"version": "2.0.100", "updated_at": "28-Oct-2024", "changes": [{"description": ["Bug - KPI Table issue on dashboard template", "Feature - Realtime integration to timeseries.", "Bug - Asset Types fix backdrop issue."]}]}, {"version": "2.0.99", "updated_at": "25-Oct-2024", "changes": [{"description": ["Bug - Asset type parent type issue fix", "Bug - Realtime configs"]}]}, {"version": "2.0.98", "updated_at": "24-Oct-2024", "changes": [{"description": ["Bug - Asset Type edit flow issue fix", "Feature - Realtime api integrated"]}]}, {"version": "2.0.97", "updated_at": "23-Oct-2024", "changes": [{"description": ["Bug - Dashboard template redirect confirmation issue", "Bug - KPI Table issue on dashboard template", "Feature - Asse types page"]}]}, {"version": "2.0.97", "updated_at": "22-Oct-2024", "changes": [{"description": ["Feature - Forgot password"]}]}, {"version": "2.0.96", "updated_at": "21-Oct-2024", "changes": [{"description": ["Bug - Enabled user fix"]}]}, {"version": "2.0.95", "updated_at": "16-Oct-2024", "changes": [{"description": ["Bug - Fix Dashboard template KPI Table issue fix"]}]}, {"version": "2.0.95", "updated_at": "16-Oct-2024", "changes": [{"description": ["Feature  -   Added Writback Checkbox Create Measurement (Null Datasource and Calculated Persisted)", "Feature  -   Added Writeback in Measurement Deatils", "Feature  -   Added Writeback Checkbox in Edit Measurement (Null Datasource and Calculated Persisted)", "Bug - The retention dropdown working.", "Bug - Multiple graph's can render/update properly when the measureId or retention period is selected.", "Bug - Real-time chart's height fixed.", "Bug - The fontWeight is removed from the override title as this is the graph's limitation.", "Feature - Added the loader for real-time chart.", "Feature - Added realtime with widgets settings"]}]}, {"version": "2.0.94", "updated_at": "15-Oct-2024", "changes": [{"description": ["Bug - Asset template instace UOM issue.", "Bug - asset template instance loader fix", "Bug - Asset template instace tag issue fix", "Bug  - Measurement path fix"]}]}, {"version": "2.0.93", "updated_at": "14-Oct-2024", "changes": [{"description": ["Bug - Do not close popup when clicked outside it.", "Bug - Timezone should be inherited when creating an Child Asset, it should not be mandatory.", "Bug - prefix should allow numbers, etc(as like tag)", "Bug - Measurement pre selection is not working in asset instance creation flow.", "Bug - Measuement tag issue fix on asset template instance creation flow", "Bug - While dragging the real-time widget's, it is hitting unwanted api calls.", "Feature - Upgraded the real-time-chart widget."]}]}, {"version": "2.0.92", "updated_at": "11-Oct-2024", "changes": [{"description": ["Bug - Dashboard template was not showing new dashboad as expected", "Bug -  Dashboard tempalate is showing blank information message popup", "Bug - Fixed heatmap issue on dashboard template"]}]}, {"version": "2.0.91", "updated_at": "09-Oct-2024", "changes": [{"description": ["Bug - Dashboard template showing blank values.", "Feature - Dashboard Template Top Panel"]}]}, {"version": "2.0.90", "updated_at": "08-Oct-2024", "changes": [{"description": ["Bug - While creating dashboard from dashboard template its not redirecting to newly created dashboard.", "Bug - Fix dashboard template related issues"]}]}, {"version": "2.0.89", "updated_at": "07-Oct-2024", "changes": [{"description": ["Bug - when unauthorised user access the alert dashboard link, 'unauthorised message' should get visible.", "Bug - on the details page of the calculation measurement with a persistent, poll period of persistent is not visible.", "Bug - TimeVaring Factor details day showing wrong.", "Bug - While triggering asset template instance flow from assets page"]}]}, {"version": "2.0.88", "updated_at": "04-Oct-2024", "changes": [{"description": ["Feature - Dashboard Template."]}]}, {"version": "2.0.87", "updated_at": "27-Sept-2024", "changes": [{"description": ["Bug - Issue of time varying factor measurement."]}]}, {"version": "2.0.86", "updated_at": "26-Sept-2024", "changes": [{"description": ["Bug - Issue of time varying factor measurement.", "Bug - Table Widget date format not matching with user preference date format (DD/MM/YY)", "Bug - If measure is deleted from left hand tree, still it is visible in widgets settings select measure dropdown."]}]}, {"version": "2.0.85", "updated_at": "25-Sept-2024", "changes": [{"description": ["Bug - Data is not matching for mountain view location, whether asset Time zone is even false or true.", "Bug - getting data difference when shifting asset timezone on and off."]}]}, {"version": "2.0.84", "updated_at": "24-Sept-2024", "changes": [{"description": ["Bug - when unauthorised user access the alert dashboard link, 'unauthorised message' should get visible.", "Bug - Normal admin not able to create asset template instance.", "Bug - Edit asset/asset template shows error 'fail to apply update asset event", "Bug - Edit time varing factor showing parents twice"]}]}, {"version": "2.0.83", "updated_at": "23-Sept-2024", "changes": [{"description": ["Feature - Unit of measure in the asset template instance creation flow", "Feature  - Units of Unit default value"]}]}, {"version": "2.0.82", "updated_at": "19-Sept-2024", "changes": [{"description": ["Feature - Events table added reference dashboard link", "Feature - events link added -4hr on start time and end time +1hr of the event timestamp"]}]}, {"version": "2.0.81", "updated_at": "18-Sept-2024", "changes": [{"description": ["Feature - Alerts events logs page", "Bug - Events datetime format issue fix", "Bug - Table date format fix"]}]}, {"version": "2.0.80", "updated_at": "06-Sept-2024", "changes": [{"description": ["Bug - when unauthorised user access the alert dashboard link, 'unauthorised message' should get visible.", "Bug - alert dashboard get changed after saving it."]}]}, {"version": "2.0.79", "updated_at": "05-Sept-2024", "changes": [{"description": ["Bug - Created alert shows wrong measure name.", "Feature - <PERSON><PERSON>"]}]}, {"version": "2.0.78", "updated_at": "04-Sept-2024", "changes": [{"description": ["Feature - Alert link to dashboard."]}]}, {"version": "2.0.77", "updated_at": "03-Sept-2024", "changes": [{"description": ["Bug - <PERSON><PERSON><PERSON><PERSON> of Measure getting mesaure api call."]}]}, {"version": "2.0.76", "updated_at": "30-Aug-2024", "changes": [{"description": ["Bug - Pop up not get hidden on the filters."]}]}, {"version": "2.0.75", "updated_at": "28-Aug-2024", "changes": [{"description": ["Feature - Add customer in alert table. Also add it to the filters.", "Bug - <PERSON><PERSON><PERSON> not working."]}]}, {"version": "2.0.74", "updated_at": "27-Aug-2024", "changes": [{"description": ["Bug - Login screen is getting infinite url", "Feature - Remove end date time restricton from custom date"]}]}, {"version": "2.0.73", "updated_at": "26-Aug-2024", "changes": [{"description": ["Filter- Make Alerts page Searchable", "Bug - Searchable alert issue fix", "Bug - On Alerts page, Apply filters button not working."]}]}, {"version": "2.0.72", "updated_at": "23-Aug-2024", "changes": [{"description": ["Bug - threshold and deadband text fields are not in proper position.", "Bug - Alert creation shows GATWAY TIMEOUT.", "Bug - Decimal values get converted to 0, in the alerts."]}]}, {"version": "2.0.71", "updated_at": "22-Aug-2024", "changes": [{"description": ["Bug - Url shows customer id as undefined and dashboard id as -1.", "Refactor -Email/SMS should show actual message instead of sample message", "Bug - Disable/enable alert filter must be considered", "Bug - Create Alert option not visible to power user."]}]}, {"version": "2.0.70", "updated_at": "21-Aug-2024", "changes": [{"description": ["Bug - Sparkline not visible on trend chart.", "Bug - Filter button should match with the search button.", "Refactor - Move alerts to left menu from User context menu."]}]}, {"version": "2.0.69", "updated_at": "20-Aug-2024", "changes": [{"description": ["Bug - Search keyword not showing records as per the search word", "Bug - Not able to type entire word in the search fields.", "Bug - Allowed values in alert", "Bug - Normal admin not able to create asset template instance.", "Bug - Sparkline not visible on trend chart.", "Bug - Normal admin not able to create alert.", "Bug- On asset edit page, remove count from parent."]}]}, {"version": "2.0.68", "updated_at": "16-Aug-2024", "changes": [{"description": ["Bug - alert check box issue fix", "Bug - On asset creation and asset details page, remove count from parent.", "Bug - On trend chart, Mean not visible.", "Bug - Issues related to Customer UI", "Bug - Issues related to user UI.", "Bug - Stats widget responsive UI fix"]}]}, {"version": "2.0.67", "updated_at": "14-Aug-2024", "changes": [{"description": ["Feature - Add provision to disable alert"]}]}, {"version": "2.0.66", "updated_at": "13-Aug-2024", "changes": [{"description": ["Feature - Customers New UI", "Feature - Users New UI", "Feature - Role based Access on UI"]}]}, {"version": "2.0.65", "updated_at": "12-Aug-2024", "changes": [{"description": ["Refactor - calculation measure edit/create flow api level changes"]}]}, {"version": "2.0.64", "updated_at": "9-Aug-2024", "changes": [{"description": ["Bug - Asset template issue fix.", "Bug - On edit of asset template, remove datasource time varying factor and calculation.", "Bug - Showing error on create asset using template page.", "Bug - Variation between asset template instance creation flow.", "Bug - Issue related to calculation measure", "Bug - On update of dashboard, selected measures get erased/removed.", "Bug - Show min, show avg, show max not visible when we select forecast and sub plot for trend chart.", "Bug - Showing 'something went wrong' on sanky chart."]}]}, {"version": "2.0.63", "updated_at": "8-Aug-2024", "changes": [{"description": ["Bug - Exit fullscreen goes to fullscreen mode fixed.", "Bug - Edit alert bug fixes", "Bug - Asset template instance asset type drop down fixed", "Bug - Alerts contacts/users drop down fixed", "Bug - Scatter chart min,max and avg not visible fixed"]}]}, {"version": "2.0.62", "updated_at": "7-Aug-2024", "changes": [{"description": ["Bug - Show min, show avg, show max not visible when we select forecast and sub plot for trend chart.", "Bug - On asset time zone true, we should be able to select one day in future.", "Bug - Issues on alerts creation.", "Bug - Shows something went wrong on the trend chart.", "Bug - User phone number and country code issue fix."]}]}, {"version": "2.0.61", "updated_at": "6-Aug-2024", "changes": [{"description": ["Feature - Phone number/country code on user creation/edit/listing flow"]}]}, {"version": "2.0.60", "updated_at": "5-Aug-2024", "changes": [{"description": ["Feature - Test Asset template. Units/Unit groups not visible for measurements"]}]}, {"version": "2.0.59", "updated_at": "1-Aug-2024", "changes": [{"description": ["Feature - Alerts contacts/users drop down to select multiple users", "Feature - Lock down all the features except for tabs in mobile view.", "Bug - Change global settings to time range"]}]}, {"version": "2.0.58", "updated_at": "31-July-2024", "changes": [{"description": ["Feature - Alerts contacts/users drop down", "Refactor - Edit/Create alerts api level changes"]}]}, {"version": "2.0.58", "updated_at": "30-July-2024", "changes": [{"description": ["Bug - edit Calculated/TIme Varing Measure disabled submit on save fix", "Bug - Edit measure parent path showing wrong path", "Bug - Clear button not working users list page", "Feature - Alerts contacts and notification type"]}]}, {"version": "2.0.57", "updated_at": "29-July-2024", "changes": [{"description": ["Feature - Heatmap pastel colors", "Bug - Long user name and long email gets break in the user context menu.", "Bug - Scatter and Bar chart UI improvement", "Bug - Edit User Role issue fix", "Bug - edit Calculated Measure issue fix", "Bug - Edit of time varying factor measure, create copy of the measure", "Bug - Edit User roles issue fix"]}]}, {"version": "2.0.56", "updated_at": "28-July-2024", "changes": [{"description": ["Feature - Hide alerts"]}]}, {"version": "2.0.55", "updated_at": "26-July-2024", "changes": [{"description": ["Bug - Asset template removed data source time varying factor and calculationn from data source", "Bug - Asset time zone bug fix", "Bug - Removed persistance option from create/edit measure with data source calculation", "Bug - Removed persistance option from Measure details page where data source is calculation", "Bug - Table Time get change, on change of top panels asset timezone.", "Bug - Edit/Create calculated measure with data source calculation issue fix"]}]}, {"version": "2.0.56", "updated_at": "25-July-2024", "changes": [{"description": ["Bug - Dashboard list must be sorted.", "Bug - Data not visible for the newly created normal user.", "Bug - Long user name gets break in the user context menu.", "Bug - When we drag and drop the sanky chart, API is failing with 500 internal server error", "Feature - Normal User can add favourite dashboard", "Feature - Normal User can set default dashboard", "Feature - added Time varing factor icon for measure in left hand tree", "Bug - Normal user is able to edit expression template fix", "Bug - On click of logo not able to select the dashboard", "Feature - Asset path on measurement creation page", "Feature-  Asset path on Edit measurement page", "Bug fix - Asset tag, Measure tag name overflown issue fix", "Refactor - Change Legend Position Value on Widget", "Bug fix - Issues related to the calc measure edit"]}]}, {"version": "2.0.55", "updated_at": "24-July-2024", "changes": [{"description": ["Bug - Edit Measurement not working if there is no data source", "Bug - API failing, when clicked on the add Asset template button", "Bug - On deselection of measure from left hand tree, confirmation does not get visible for sankey chart", "Bug - calculation measure edit flow", "Bug - Removed Intellegence from the measurement creation flow"]}]}, {"version": "2.0.54", "updated_at": "23-July-2024", "changes": [{"description": ["Bug - Asset template Api issue, Giving 403 error", "Bug - Asset template instance creation flow issue", "Bug - Stepline chart for datasource TimeVaring Factor in scatter chart"]}]}, {"version": "2.0.53", "updated_at": "22-July-2024", "changes": [{"description": ["Alert Comparision Message on create/edit flow", "Bug - Asset tag field removed from Edit/Create Measurement flow", "Feature - Create measurement intelligence for parent selection", "Fix - Roll back on error of measuement creation"]}]}, {"version": "2.0.52", "updated_at": "19-July-2024", "changes": [{"description": ["Bug - Underscored not get accepted  in the asset tag.", "Bug -Issues related to alignment of icon and detail message of the alerts", "Bug - Measure Creation reset form issue fix", "Bug - Subplot get visible, even sparkline option is selected.", "Bug - No proper error message on expression template.", "Bug - While editing alerts, value get overlapped with the place holder.", "Bug - while editing Asset, value get overlapped with the place holder.", "Bug - TextField value get overlapped with the place holder.", "Bug - Issues in mobile view.", "Bug - User account issues. on asset template creation and calculation instance creation", "Bug - Datatype shows N/A on calc measure details", "Bug - Show min, show avg, show max not visible when we select forecast and sub plot for trend chart.", "Bug - On deselection of measure from left hand tree, confirmation does not get visible."]}]}, {"version": "2.0.51", "updated_at": "18-July-2024", "changes": [{"description": ["Feature - Reset passowrd", "Bug - Asset tags validation issue fix on asset edit flow"]}]}, {"version": "2.0.50", "updated_at": "17-July-2024", "changes": [{"description": ["Bug - Asset tags validation rules", "Bug - Measurement tags validation rules"]}]}, {"version": "2.0.49", "updated_at": "16-July-2024", "changes": [{"description": ["Bug - Issues related to the Alerts", "Bug - Add Description in alerts", "Bug - Alerts list spelling mistake fix in the alerts list page"]}]}, {"version": "2.0.48", "updated_at": "15-July-2024", "changes": [{"description": ["Feature - List , edit and delete alerts"]}]}, {"version": "2.0.47", "updated_at": "11-July-2024", "changes": [{"description": ["Refactor - Time varing factor api level change."]}]}, {"version": "2.0.46", "updated_at": "11-July-2024", "changes": [{"description": ["Bug - Last weekday not visible on the measurement details page.", "Bug - disabled the previous date while creating time varing factor creation", "Bug - factor type label misplaced in time varing factor creation", "Bug - Measurement gets created without adding effective date.", "Bug - poll period and data type added tooltip in measurement details page in calcucation details section."]}]}, {"version": "2.0.45", "updated_at": "10-July-2024", "changes": [{"description": ["Feature - Added Measure details calculation details section in measure details page.", "Feauter - Added Measure details Time Varing Factor details section in measure details page.", "Feature - Added Time Varing factore remove effective date and remove added date option", "Feautre - Fix success message on Time varying factor creation"]}]}, {"version": "2.0.44", "updated_at": "09-July-2024", "changes": [{"description": ["Bug - Search user app error issue resolved."]}]}, {"version": "2.0.43", "updated_at": "08-July-2024", "changes": [{"description": ["Bug - Trend Chart Sub plot  Min, max , Avg Should not visible  in settings.", "Bug - When Normal User role loggs in Then no option for create dashboard and add widget", "Bug - KPI current widget getting re-loaded on change of top panel's sample period."]}]}, {"version": "2.0.42", "updated_at": "05-July-2024", "changes": [{"description": ["Feature - Time Varing Factor creation from measurement creation"]}]}, {"version": "2.0.41", "updated_at": "03-July-2024", "changes": [{"description": ["Bug - after login showing 404 error with home back button on test environment.", "Bug - responsive logo and icon fix", "Bug - responsive drawer should close on navigation"]}]}, {"version": "2.0.40", "updated_at": "02-July-2024", "changes": [{"description": ["Bug - widget setting dashboards api call fix", "Bug - Bar chart unrequired api fixed", "Bug - KPI Bar chart showing date formats in x-axis for Week and months", "Bug - KPI Bar chart showing wrong dates on 6M and yearly for previous", "Bug - KPI Bar Date format must be same on kpi side by side bar chart.", "Feature - Make UI Responsive"]}]}, {"version": "2.0.39", "updated_at": "01-July-2024", "changes": [{"description": ["Bug - KPI Bar chart shows previous dates on hover on bars.", "Bug fix - User role can create dashboard.", "Bug fix - User role has access of  edit user flow", "Bug fix - User role can asset asset template flow"]}]}, {"version": "2.0.38", "updated_at": "28-June-2024", "changes": [{"description": ["Feature - KPI Bar chart make bars color configurable.", "Bug fix - Expression invalid issue fix", "Feature - KPI Bar chart side by side data compare"]}]}, {"version": "2.0.37", "updated_at": "27-June-2024", "changes": [{"description": ["Feaute - Added output option on expression creation with sample input.", "Bug fix - Edit Expression is showing blank value on edit for data types.", "Bug fix - Shared dashboard showing invalid date in the time range.", "Bug fix - On edit of expression template, data type not get prepopulated.", "Bug fix - Expression not support minus sign if added at 1st.", "Bug fix - Not able to save expression with time datatype."]}]}, {"version": "2.0.37", "updated_at": "26-June-2024", "changes": [{"description": ["Feature - Removed all apis from the apis", "Bug fix - On drag and drop of sanky chart, empty chart get visible. "]}]}, {"version": "2.0.36", "updated_at": "25-June-2024", "changes": [{"description": ["Bug fix - Fixed top space in calc instace creation", "Feature - measure creation on success of calculation instance creation", "Feature - Speed dialer automation with library"]}]}, {"version": "2.0.35", "updated_at": "24-June-2024", "changes": [{"description": ["Bug fix - Removed Calculation instance creation flow from Calculation engine flow.", "Feature - Calculation Engine added sample input."]}]}, {"version": "2.0.34", "updated_at": "21-June-2024", "changes": [{"description": ["Bug fix - Calculation Engine UI fix.", "Bug fix - Fix constant and measurement selection issue"]}]}, {"version": "2.0.33", "updated_at": "20-June-2024", "changes": [{"description": ["Feature - Expression Template builder", "Feature - Expression Template list page"]}]}, {"version": "2.0.32", "updated_at": "17-June-2024", "changes": [{"description": ["Bug fix - KPI Bar chart Avg Change calculation fixed", "Bug fix - Dashboard top panel styling issue fixed", "Bug fix - Fullscreen styling issue fixed", "Bug fix - fix KPI bar chart legend title issue"]}]}, {"version": "2.0.31", "updated_at": "14-June-2024", "changes": [{"description": ["Bug fix - KPI Bar chart Avg issue fixed", "Bug fix - KPI Bar chart global sample period reload issue fixed", "Bug fix - KPI Bar chart measure issue fix when we remove form tree"]}]}, {"version": "2.0.30", "updated_at": "13-June-2024", "changes": [{"description": ["Feature - Sample period 30 days to monthly", "Bug - KPI Bar chart sample period issue fixed", "Bug - Edit user role issue fixed", "Bug fix - Widget settings dialog styling fix"]}]}, {"version": "2.0.29", "updated_at": "12-June-2024", "changes": ["Feature - KPI Bar chart."]}, {"version": "2.0.28", "updated_at": "11-June-2024", "changes": [{"description": ["Feature - removed None from Sample period.", "Bug fix - de-select measure from left hand tree, subplot get removed but if we re-select it then subplot will get appeared.", "Bug fix -  Measure creation Calculation Engine issue fixed.", "Bug fix - Change order of the user create."]}]}, {"version": "2.0.28", "updated_at": "11-June-2024", "changes": [{"description": ["Feature - removed None from Sample period.", "Bug fix - de-select measure from left hand tree, subplot get removed but if we re-select it then subplot will get appeared."]}]}, {"version": "2.0.27", "updated_at": "10-June-2024", "changes": [{"description": ["Bug fix - Scoped Role flow fixes.", "Bug fix - Empty input box get appeared."]}]}, {"version": "2.0.26", "updated_at": "7-June-2024", "changes": ["Feature - Test dashboard make assets and measures searchable."]}, {"version": "2.0.26", "updated_at": "7-June-2024", "changes": [{"description": ["Bug fix - On customers page, the height of logo must issue fix.", "Bug fix - Searchable dropdown in Calc Engine", "Bug fix - Login and default dashboar should not have any extra redirect url"]}]}, {"version": "2.0.25", "updated_at": "6-June-2024", "changes": [{"description": ["Bug fix - Measurement blank request fix.", "Bug fix - start date  is more then end date in agg api", "Bug fix - Dashboard tabs switching delay issue fixed"]}]}, {"version": "2.0.24", "updated_at": "5-June-2024", "changes": [{"description": ["Bug fix - Previous customer's dashboard get visible.", "Bug fix - customer change dashboard loading issue fixed"]}]}, {"version": "2.0.23", "updated_at": "3-June-2024", "changes": [{"description": ["Bug fix - Calculation Engine UI fix.", "Bug fix - Fix constant and measurement selection issue", "Bug fix - show loader on UI while fetching data on UI", "Bug fix - Change submit button to next button while creating measurement on creation flow of measurement when Data Source is calculated ", "Bug fix - On KPI trend label/stats get misplaced after saving dashboard.", "Bug fix - Share dashboard modification.", "Bug fix - Restrict measurement id sending blank to api's"]}]}, {"version": "2.0.22", "updated_at": "31-May-2024", "changes": [{"description": ["Bug fix - Date range not get set to custom.", "Bug fix - Top panel - Verify sample period default selection depending on the time range from the excel sheet.", "Bug fix - Date get reset.", "Bug fix - Date range not get set to custom.", "Bug fix - On sanky chart, if we remove measure from left hand tree still its node visible on chart with value."]}]}, {"version": "2.0.21", "updated_at": "31-May-2024", "changes": ["Feature - Left panel measurement deselection on show popup."]}, {"version": "2.0.21", "updated_at": "30-May-2024", "changes": [{"description": ["Bug fix - Subplot checkbox already checked.", "Bug fix - Save button not get enabled.", "Bug fix  - Trend chart shows loader only on deselecting selected stacked.", "Bug fix - Measure get selected automatically on kpi current.", "Bug fix - On sanky chart, measure's label name should be mandatory.", "Bug fix - same customer gets selected automatically for user role.", "Bug fix - while creating measurement on shree temple shows 'tag contains invalid character'.", "Bug fix - Weather widget not matching the value on the Tempest Web Page. Does this refresh at least once every 5 to 30 mins?", "Feature - the forecast api now requires to pass the egg_by (same one selected for the trend)."]}]}, {"version": "2.0.20", "updated_at": "29-May-2024", "changes": [{"description": ["Feature - On Changing of charts common thing will be retains", "Bug fix - Legend not remove from scatter chart issue fix.", "Feature - the calc engine should be now initiated from new measurement", "Bug fix - success message not visible on the edit of asset", "Bug fix - Match new calendar font to product font", "Bug fix - Edit measurement data source is disable and data type is now updateable on edit", "Bug fix - On user list page with role show customers", "Bug fix - On customer page now logo will display", "Feature - Customer edit page modification done"]}]}, {"version": "2.0.19", "updated_at": "28-May-2024", "changes": [{"description": ["Feature - Date Time Picker for Dashboard", "Feature - Sankey connection colors", "Bug fix - On sanky chart, on click of cancel data is retaining.", "Bug fix - Date Time Picker data retain issue fix", "Bug fix - On sanky chart, measure's label name should be mandatory."]}]}, {"version": "2.0.18", "updated_at": "27-May-2024", "changes": [{"description": ["Bug fix - Update measure is not working", "Bug fix - Save button not working fine"]}]}, {"version": "2.0.17", "updated_at": "24-May-2024", "changes": [{"description": ["Bug fix - Tag validation and measurement api error handle", "Bug fix - Remove shared buttons from dashboard"]}]}, {"version": "2.0.16", "updated_at": "23-May-2024", "changes": [{"description": ["Bug fix - Ovr badge need to positioned proper for low resolution devices", "Feature - Stacked Bar chart", "Bug fix - create asset template creation fixed", "Bug fix - Create dashboard fix", "Bug fix - Chart widget modification", "Feature - KPI Table should support all options as stats widget", "Feature - UX changes for favourite tabs", "Bug fix - Share button UX improvements.", "Bug fix - Bar chart showing wrong time due to utc"]}]}, {"version": "2.0.15", "updated_at": "22-May-2024", "changes": [{"description": ["Bug fix - Add snackbar when default customer response get.", "Bug fix - Change delete icon on dashboard and customer listing page", "Feature - KPI Table Titles configurable in table for min , max and mean ", "Feature - Change on dashboard widget icon hover color", "Feature - Dashboard and customer listing action padding set", "Feature - On tree change outline calculation"]}]}, {"version": "2.0.14", "updated_at": "22-May-2024", "changes": [{"description": ["Bug fix - Add snackbar when default customer response get.", "Bug fix - Change delete icon on dashboard and customer listing page", "Bug fix - On assets tree change calculation metric icon", "Bug fix - On Details page or edit page don't show Top panel and dashboards tab on UI", "Bug fix - Stats widget should support sum", "Bug fix - Widgets Speed Dialer should match UX of figma"]}]}, {"version": "2.0.13", "updated_at": "21-May-2024", "changes": [{"description": ["Bug fix - Change padding of widget container.", "Bug fix - Change left of left sidebar menu."]}]}, {"version": "2.0.12", "updated_at": "21-May-2024", "changes": [{"description": ["Feautre  - Sum & Delta for Bar, Trend chart", "Bug fix - KPI Current widget UX enhancements.", "Bug fix - Overriden setting refresh issue", "Bug fix - Dashboard UX improvement", "Bug fix - Change tooltip UX", "Bug fix - Stats Widget wrong data fixed", "Bug fix - clone widget in widget settings", "Bug fix - KPI Current widget data not showing"]}]}, {"version": "2.0.11", "updated_at": "20-May-2024", "changes": [{"description": ["Bug fix - Show total fix.", "Feature - Show mean on forecast chart and make configurable mean and forecast line.", "Bug fix - Favourite Tabs change confirm pop up issue fix.", "Bug fix - If a measure selected in upper selector but selected in lower selector then it does not plot data", "Bug fix - Show Total to Show sum label will change."]}]}, {"version": "2.0.10", "updated_at": "17-May-2024", "changes": [{"description": ["Bug fix - Nav bar icon not visible.", "Bug fix  - On click of create user navigated to create customer.", "Bug fix -  Label get overlapped.", "Bug fix - on test env, confirmation pop up visible even if we didn't change anything on dashboard.", "Bug fix - Select dashboard label not properly visible.", "Bug fix  - Asset details page remains.", "Bug fix- On selection of the menu item, background gets blur.", "Bug fix -Heatmap not showing data as per selected time range.", "Bug fix - Search dashboard field not get cleared.", "Bug fix - forecast leable in legends on chart", "Bug fix - Removed blur effects ", "Bug fix - forecast not get removed on multi measure selection", "Bug fix - KPI Current widget layout fix", "Bug fix - Widget drop fix"]}]}, {"version": "2.0.9", "updated_at": "16-May-2024", "changes": ["Feature - Forecast Chart in Bar,scatter and KPI Trend"]}, {"version": "2.0.8", "updated_at": "15-May-2024", "changes": [{"description": ["Feature - UI changes", "Bug fix - Edit User Roles are not visible by default", "Bug fix - Expression Template instance error when output measure not selected."]}]}, {"version": "2.0.7", "updated_at": "14-May-2024", "changes": ["Feature - Calculation Engine Page"]}, {"version": "2.0.6", "updated_at": "13-May-2024", "changes": ["Bug fix - Error handling in Widget APIs"]}, {"version": "2.0.5", "updated_at": "13-May-2024", "changes": ["Feature - UI changes in Dashboard"]}, {"version": "2.0.4", "changes": [{"description": ["Bug fix - Menu navs fixes", "Bug fix - Page name fixes", "Bug fix - Widget Names to Widge<PERSON><PERSON>", "Bug fix - Fix date format in Test Dashboard page"]}]}, {"version": "2.0.3", "changes": [{"description": ["Bug fix - Stacked Chart in Spark Line Chart", "Bug fix - Dashboard UI", "Add Notification and Report Page"]}]}, {"version": "2.0.2", "changes": [{"description": ["Feature - Stacked Chart in Spark Line Chart"]}]}, {"version": "2.0.1", "changes": [{"description": ["Bug fix - Fix create dashboard"]}]}, {"version": "2.0.0", "changes": [{"description": ["Feature - New UI design", "Feature - New Dashboard layout"]}]}, {"version": "1.117.0", "changes": [{"description": ["Feature - KPI Trend with sparkline chart"]}]}, {"version": "1.116.0", "changes": [{"description": ["Bug - Default dashboard not visible, when we logged in first time/or select customer."]}]}, {"version": "1.115.0", "changes": [{"description": ["Feature - Sample period change on global time range change"]}]}, {"version": "1.114.0", "changes": [{"description": ["Bug fix - <PERSON><PERSON><PERSON> <PERSON><PERSON> in favourite dashboards tabs", "Bug fix - create dashboard visible for normal user", "Bug fix - top panel refresh interval time 5 min to 1 hours", "Bug fix - Dashboard update button disable if there is duplicate name", "Bug fix - Error message does not get cleared on click of cancel.", "Bug fix - Weather station id retains on cancel.", "Bug fix - Export to excel should be removed from Sankey chart.", "Bug fix - on trend chart data not visible a per selected time range.", "Feature - User filter in user list page"]}]}, {"version": "1.113.0", "changes": [{"description": ["Bug fix - Added unit in KPI Current Widget", "Bug fix - Sankey chart settings data retains on cancel issue fix", "Bug fix - KPI Current Widget issue fix on no sample period selected", "Bug fix - KPI Current Widget on select/unselect reloading fix on widget", "Bug fix - KPI Current shows error on selection of specific measures.", "Bug fix - KPI Current widget get loaded on refresh time interval.", "Bug fix - on dashboard update not showing error message for already exist name.", "Feature -  Add image placement and size options to KPI Current Widget"]}]}, {"version": "1.112.0", "changes": [{"description": ["Bug fix - Dashboard title issue fix"]}]}, {"version": "1.111.0", "changes": [{"description": ["Feature  - New Image Widges images", "Feature -  Image widget and KPI Current Widget has now title configuration"]}]}, {"version": "1.110.0", "changes": [{"description": ["Feature  -  KPI Current Widget realTime api integration"]}]}, {"version": "1.109.0", "changes": [{"description": ["Bug fix - Update button is disabled.", "Bug fix - On sanky chart, measure not visible, if label not given.", "Bug fix  - Loader not visible on image stats widget( KPI Current Widget)."]}]}, {"version": "1.108.0", "changes": [{"description": ["Bug fix - KPI Trend Delta values", "Bug fix - KPI Current Widget", "Feature - KPI Current Widget added font configurations", "Feature - Speed dialer onclick event added to expand the dialer"]}]}, {"version": "1.107.0", "changes": [{"description": ["Bug fix - Dashboard navigation issue fixed."]}]}, {"version": "1.106.0", "changes": [{"description": ["Bug fix - At widget level user can not set one day in future in end date.", "Bug fix - Asset time zone get selected automatically.", "Bug fix - Dashboard title retains.", "Bug fix - default title not visible when we drag and drop the title widget.", "Bug fix - While making dashboard unfavourite/removing favourite showing wrong message."]}]}, {"version": "1.105.0", "changes": [{"description": ["Bug fix - Heatmap fetch data"]}]}, {"version": "1.104.0", "changes": [{"description": ["Bug fix - Sankey chart label fix."]}]}, {"version": "1.103.0", "changes": [{"description": ["Bug fix - Tree tag issue fix.", "Bug fix - Navbar icons are not visible until dashboard is selected"]}]}, {"version": "1.102.0", "changes": [{"description": ["Bug fix - Map widget showing oops something went wrong."]}]}, {"version": "1.101.0", "changes": [{"description": ["Feature - Image Stats Widget"]}]}, {"version": "1.100.0", "changes": [{"description": ["Feature - Favorites dashboard", "Feature - Favourite dashboard "]}]}, {"version": "1.99.0", "changes": [{"description": ["Bug fix - Share dashboard", "Bug fix - Dashboard navigation"]}]}, {"version": "1.98.0", "changes": [{"description": ["Feature - Sankey chart in chart widget"]}]}, {"version": "1.97.0", "changes": [{"description": ["Bug fix - top panel start date showing wrong date"]}]}, {"version": "1.96.0", "changes": [{"description": ["Feature - new date time format in user preferences"]}]}, {"version": "1.95.0", "changes": [{"description": ["Bug fix - On map widget, even if measures are selected from different latitude and longitude, it shows error for it but still it gets added to widget"]}]}, {"version": "1.94.0", "changes": [{"description": ["Feature - Added new Weather Widget UI"]}]}, {"version": "1.93.0", "changes": [{"description": ["Feature - Map widget should pick location from details"]}]}, {"version": "1.92.0", "changes": [{"description": ["Bug fix - Change KPI Trend Triangle to Up and Down arrow"]}]}, {"version": "1.91.0", "changes": [{"description": ["Bug fix - Updated dashboard name not visible, instead it shows previous name", "Bug fix - Asset Type should be selected and disabled in Unit Group & Asset selection on form", "Bug fix - Confirmation pop up visible on edit measurement and edit asset page"]}]}, {"version": "1.90.0", "changes": [{"description": ["Bug fix - 2d widget does not have dashboard link option"]}]}, {"version": "1.89.0", "changes": [{"description": ["Feature - Home button on all non dashboard pages", "Feature - Asset/measures creation and edit page have back buttons"]}]}, {"version": "1.88.0", "changes": [{"description": ["Bug fix -  Asset get saved many times on asset template instance", "Bug fix - On Asset Creation of asset template instance asset type field not showing hierarchy."]}]}, {"version": "1.87.0", "changes": [{"description": ["Feature - Added new Weather Widget"]}]}, {"version": "1.86.0", "changes": [{"description": ["Optimisation - Login/Logout flow"]}]}, {"version": "1.85.0", "changes": [{"description": ["Bug fix- Asset type dropdown should show hiearchy"]}]}, {"version": "1.84.0", "changes": [{"description": ["Feature -  Navigation to linked dashboard via click on icon on top right", "Feature - Increase the space between the widget icons"]}]}, {"version": "1.83.0", "changes": [{"description": ["Feature -  Dashboards BreadCrumb added"]}]}, {"version": "1.82.0", "changes": [{"description": ["Feature -  UI can display the count of asset type templates"]}]}, {"version": "1.81.0", "changes": [{"description": ["Feature -  User Preferences API Integration", "Bug fix - asset template data retains issue fix"]}]}, {"version": "1.80.0", "changes": [{"description": ["Bug fix -Data do not get cleared on click on cancel on asset template flow", "Bug fix -select metric not showing any option in asset template instance flow", "bug fix- Same dashboard name get accepted.", "bug fix - 2D image widget shows no data found initially", "bug fix - Data not visible as per the selected overridden sample period on chart widget", "bug fix - Start date showing wrong.", "bug fix - On logout confirmation pop up not visible.", "bug fix - Title visible bold on KPI color box widget"]}]}, {"version": "1.79.0", "changes": [{"description": ["Feature - Share dashboard via URL"]}]}, {"version": "1.78.0", "changes": [{"description": ["Feature -Default dashboard selection on login", "Feature -Default dashboard and search dashboard in dashboards list page", "bug fix- confirmation pop up not visible on selection of other dashboard "]}]}, {"version": "1.77.0", "changes": [{"description": ["Feature -User Preferences Time Context Menu", "bug fix- confirmation pop up not visible on selection of other dashboard or customer", "bug fix - Selected dashboard not visible from asset details page"]}]}, {"version": "1.76.0", "changes": [{"description": ["Feature -Blank Image Widget", "bug fix- dashboard navigational issue fix"]}]}, {"version": "1.75.0", "changes": [{"description": ["Feature - Link image widget to a dashboard"]}]}, {"version": "1.74.0", "changes": [{"description": ["Bug fix - Dashboard widget pin feature fixed.", "Bug fix - Select label inconsistency in KPI widgets", "Bug fix - confirmation pop up visible, even if customer and dashboard not selected."]}]}, {"version": "1.73.0", "changes": [{"description": ["Bug fix - User not visible as per selected customer."]}]}, {"version": "1.72.0", "changes": [{"description": ["Bug fix - Sparkline and Map widget showing errors.", "Bug fix - Title Placement Inconsistent on Bullet Chart.", "Bug fix - While editing create asset template instance, Description name not changing.", "Bug fix -  Delete and clone widget confirmation fixed"]}]}, {"version": "1.71.0", "changes": [{"description": ["Bug fix - Select measure/metric dropdown consistency.", "Bug fix - On cancel on chart settings data was retaining on chart settings without saving it.", "Bug fix - Bar and Trend charts was showing wrong data on change in X-axis label or removal of it.", "Feature - added tooltip for all wiget icons.", "Bug fix -  Delete and clone widget confirmation fixed"]}]}, {"version": "1.70.0", "changes": [{"description": ["Bug fix - After deleting widget, confirmation pop up not visible.", "Bug fix - After pinning widget, confirmation pop up not visible.", "Bug fix - Sample period, Aggegate by drop down is now visible consistently for all widgets."]}]}, {"version": "1.69.0", "changes": [{"description": ["Bug fix - Fixed Gauge and bullet capitalized issue.", "Bug fix - X axis label will visible for bar and scatter chart only and label is fixed and now show in capitalized.", "Bug fix- Sample period minutes to minute for 1 minute.", "Bug fix - Override time Range date capitalized issue fixed.", "Bug fix - Fixed label inconsistency in measure/metric selection for single and multiple selection."]}]}, {"version": "1.68.0", "changes": [{"description": ["Bug fix - while editing create asset template instance. tag name not changing.", "Feature - User Role Change for Customer Improvement."]}]}, {"version": "1.67.0", "changes": [{"description": ["Bug fix - Fixed admin user list page access issue for scoped admin users", "Bug fix - Chart widget flag issue"]}]}, {"version": "1.66.0", "changes": [{"description": ["Feature -  Added Asset time zone on widget level", "Feature - Widgets now show the `OVR` badges if the widget is overridden with what override settings are applied", "Bug fix - Fixed confirmation dialog issue on navigation change from dashboard", "Bug fix - Fixed issue on KPI Value indicators on override asset time zone settings"]}]}, {"version": "1.65.0", "changes": [{"description": ["Bug fix - Fixed issue on search users criteria"]}]}, {"version": "1.64.0", "changes": [{"description": ["Feature - api level changes in dashboards", "Feature - drag widget to dashboard without selecting any measure on tree", "Feature - Added server side erros on forms and added error messages on forms"]}]}, {"version": "1.63.0", "changes": [{"description": ["Feature - Expand widget to full screen", "Feature - Copy widdget or duplicate widget", "Feature - Unsaved state ask for confirmation to save on change of navigation", "Bug fix - Tree not clickable after delete any asset/measure fixed"]}]}, {"version": "1.62.0", "changes": [{"description": ["Feature - User search criteria added in user list page with name and role bases", "Feature - Color box show colors based on trend arrow", "Feature - KPI percentage widget added bar color customization option", "Feature - Asset details from right click on tree", "Bug fix - 2d widget loader issue fixed"]}]}, {"version": "1.61.0", "changes": [{"description": ["Show loaders in Image widgets when the dashboard retrieves first time and on every refresh interval and map widget on loads", "Bug fix - Gauge chart fixed spacing issue", "Bug fix - Scoped admin have access users list page and fixed user page issue"]}]}, {"version": "1.60.0", "changes": [{"description": ["Show loaders in Heatmap widgets when the dashboard retrieves first time and on every refresh interval.", "Bug fix - Stats Widget fixed issue on dashboard fetching data", "Bug fix - KPI widgets fixed issues on overridden settings not auto refreshing on refresh interval"]}]}, {"version": "1.59.0", "changes": ["Show loaders in the widgets when the dashboard retrieves first time."]}, {"version": "1.58.0", "changes": [{"description": ["Bug fix - Map widget measure labels not able clear fields", "Bug fix- Map Widget data retains on right side details section even we removed from tree", "Bug fix - Map Widget data retains on Map Settings dialong section even we removed from tree", "Bug fix - Map Widget if we remove or add new measure in any marker, was not reflecting on right side details section", "Feature - Map Widget can add marker locationm without measure", "Bug fix - For normal admin, select customer option is visible."]}]}, {"version": "1.57.0", "changes": [{"description": ["Bug fix on login screen not showing errors", "Bug fix-Asset time zone not getting save", "Bug fix - create dashboard navigation issue", "Bug fix - Color Box fixed trends arrow issue", "Feature - added help menu in navbar and tooltip for all top icons"]}]}, {"version": "1.56.0", "changes": [{"description": ["Performance improvement with- UI fetch all measure data in one API call", "Create new Dashboard option on user profile", "Added asset time zone in top panel of dashboard"]}]}, {"version": "1.55.0", "changes": [{"description": ["Feature -  Customer's users List Page and edit user page", "Bug fix- KPI percentage widget showing tables fixed", "Map widget having details options added at right end side of the widget"]}]}, {"version": "1.54.0", "changes": ["Feature -  KPI Table widget including charts in table"]}, {"version": "1.53.0", "changes": ["Added the feature of upload json on diagram page"]}, {"version": "1.52.0", "changes": ["Feature - KPI Color Box Widget", "KPI Value indicator Widget"]}, {"version": "1.51.0", "changes": ["Feature - KPI SparkLine Chart Widget"]}, {"version": "1.50.0", "changes": ["Bug fix on overriden widgets refreshing issue"]}, {"version": "1.49.0", "changes": ["Power users can create diagrams for 2D widget"]}, {"version": "1.48.0", "changes": [{"description": ["Map widget settings added with multi measures and markers", "Image widget added margin settings for background image"]}]}, {"version": "1.47.0", "changes": [{"description": ["Add Alternate Colors for TEST Dashboard", "Test Dashboard crashing issue fixed", "Chart threshold line styling feature added", "Multiple 2d-widget issue fixed", "New widget to the dashboard by default no measures should be selected"]}]}, {"version": "1.46.1", "changes": ["Bug fix on 2d Widget"]}, {"version": "1.46.0", "changes": ["Bug fix on left tree on selection of measure"]}, {"version": "1.45.0", "changes": [{"description": ["Added Feature - enabled edit in dashboard title on edit", "Add prefix field to CreateAssetTemplateInstance form"]}]}, {"version": "1.44.0", "changes": ["Bug fix on charts getting crashed."]}, {"version": "1.43.0", "changes": ["Added select measure feature in 2d-widget"]}, {"version": "1.42.0", "changes": ["Added drop down for <PERSON><PERSON><PERSON> selection in top panel"]}, {"version": "1.41.0", "changes": [{"description": ["Bug fix- removed id from Test Dashboard select measure", "Bug-stats widget checkbox side by side issue"]}]}, {"version": "1.40.0", "changes": [{"description": ["added  new Image -leaf in image widget", "removed image from Login section"]}]}, {"version": "1.39.0", "changes": [{"description": ["change 2d widget icon", "consolidate image widgets in a single icon", "min,max, avg for trend and bar color should be light grey", "add below image in image widget", "Asset template table indexing issue fixed"]}]}, {"version": "1.38.0", "changes": [{"description": ["Major refactoring of the codebase for performance improvement", "Fixed infinite api call bug"]}]}, {"version": "1.37.0", "changes": ["Feature - 2D Widget", "Reintroduced UI side cache"]}, {"version": "1.36.0", "changes": ["Feature - added Test Dashboard Page "]}, {"version": "1.35.0", "changes": [{"description": ["Bug fix on top panel's time range for custom dates", "Bug fix on Overrider time range options in widget settings"]}]}, {"version": "1.34.0", "changes": ["Feature - Add Asset Template creation page"]}, {"version": "1.33.0", "changes": [{"description": ["Stats Widget showing updated values on click of cancel button", "Unit of Measure not visible issue fixed on Measure details page", "Map Widget location pin color reverting to default color on adding or remoing new measures from list", "Kiosk mode issue fixed on escape key captured on UI side", "Spelling mistake for <PERSON>,<PERSON><PERSON><PERSON> and Gauge Chart Threshold value", "For Premier Food Customer it was showing blank values for legend position and global sample period in defalt settings", "Features - Total is removed from Stats Widget"]}]}, {"version": "1.32.0", "changes": [{"description": ["Kiosk mode fix", "Bar and Trend chart threshold text overlapping issue fix", "On removel measure from tree showing blank value on table settings"]}]}, {"version": "1.31.0", "changes": [{"description": ["Export to excel feature for bullet chart", "Override global time and sample period for all widgets fixed"]}]}, {"version": "1.30.0", "changes": [{"description": ["Export to excel feature for bullet chart", "Override glogal time and sample period for all widgets fixed"]}]}, {"version": "1.30.0", "changes": [{"description": ["Right side drawer added CO2 and Assignment section", "Font weight and color fix stats widget"]}]}, {"version": "1.29.0", "changes": [{"description": ["Threshold now available for multiple measure selection", "Font weight repeatation fix for all widgets", "Fix Heatmap Y axis label issue and title issue"]}]}, {"version": "1.28.0", "changes": [{"description": ["Map Widget color issue fix", "HeatMap show Y Values", "Chart Widget loading issue fix", "Widget showing unselected measures in settigs issue fix", "Corrected Measure Details page to show Measurement Id"]}]}, {"version": "1.27.0", "changes": ["Bar color issue fix"]}, {"version": "1.26.1", "changes": ["Kiosk mode fix"]}, {"version": "1.26.0", "changes": ["Added Right sidebar for dashboard"]}, {"version": "1.25.0", "changes": [{"description": ["Global sample period was not working for all widgets", "Added map widget"]}]}, {"version": "1.24.0", "changes": [{"description": ["Stats Widget select measure related issue fix", "Sparkline was showing area chart instead of line chart", "Changing of dashboard top panel was showing wrong sample periods"]}]}, {"version": "1.23.0", "changes": [{"description": ["Widgets now auto-correct the settings to remove the deleted measures.", "Image widget now supports selecting a image from the list of supported images."]}]}, {"version": "1.22.0", "changes": [{"description": ["Stats widget now supports last fetched value", "A new way to drag and drop widgets", "Image widget drag and arrange label is very smooth now", "Added support for spark line in Trend chart"]}]}, {"version": "1.21.0", "changes": [{"description": ["Image widget labels font size ,weight and color are now configurable", "Stats widget number rounding fix"]}]}, {"version": "1.20.0", "changes": [{"description": ["Image widget labels realignment issue fixed", "Stats and chart widget measure selection error message on if no measures selected"]}]}, {"version": "1.19.0", "changes": [{"description": ["Message on no measures selected for table widget", "Stats widget coloring for values", "Stats widget loading issue fixed on non-measure values"]}]}, {"version": "1.18.0", "changes": [{"description": ["Major refactoring of the codebase", "Minor UI setting enhancements"]}]}, {"version": "1.17.0", "changes": [{"description": ["Title color, font weight, font size are now configurable for all widgets except chart", "Override global sample period for Chart widget"]}]}, {"version": "1.16.0", "changes": [{"description": ["Added 2 new Voltage Image Widgets", "Image widgets like Gas Meter, Solar Widget, Voltage Meter can now show real data instead of dummy data"]}]}, {"version": "1.15.0", "changes": [{"description": ["Title edit feature added for all widgets if there is Overrider title is set", "Chart bar colors are now configurable if Overrider bar colors is set", "Make dialogs more interactive"]}]}, {"version": "1.14.0", "changes": [{"description": ["Titles are now configurable for all widgets with font size and color", "Stats widgets now show with configurable colors, we can hide labels for stats"]}]}, {"version": "1.13.1", "changes": ["Solar panel widget text font size issue fixed"]}, {"version": "1.13.0", "changes": [{"description": ["For trend/scatter, bar chart user can select colors for each measure", "In-progress: ability to override global time for all widgets"]}]}, {"version": "1.12.0", "changes": ["Dashboard all widgets have been make pinning enabled"]}, {"version": "1.11.1", "changes": ["Measure details page issues fixed"]}, {"version": "1.11.0", "changes": [{"description": ["Dashboard now shows grey background color", "Heatmap hide axis at 0th index", "Bar & Scatter chart now show Min, Max & Avg markers if only one measure is selected", "Redux-persist performance improvement"]}]}, {"version": "1.10.0", "changes": [{"description": ["Date should get selected after clicking on OK.", "Complete measure name not visible on table settings", "table selected measure not properly visible for select metric in settings.", "updating stats, title or other non measure widgets re-fetches data."]}]}, {"version": "1.9.0", "changes": ["Cache fix", "Added network request optimization"]}]