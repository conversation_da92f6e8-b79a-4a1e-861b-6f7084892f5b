import { Box, Typography } from '@mui/material';
import NoEncryptionIcon from '@mui/icons-material/NoEncryption';
import Link from 'next/link';
const UnAuthorized: React.FC = () => {
  return (
    <Box
      sx={{
        height: '90vh',
      }}
    >
      <Box
        sx={{
          height: '100%',
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Box
          sx={{
            display: 'block',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Box width={'100%'} textAlign={'center'}>
            <NoEncryptionIcon sx={{ fontSize: 100 }} />
          </Box>
          <Box width={'100%'} textAlign={'center'}>
            <Typography>Unauthorized access.</Typography>
            <Link href={'/customer'}>Go back</Link>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};
export default UnAuthorized;
