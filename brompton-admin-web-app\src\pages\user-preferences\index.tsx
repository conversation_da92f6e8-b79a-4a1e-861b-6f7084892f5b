import SaveIcon from '@mui/icons-material/Save';
import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Container,
  FormControl,
  FormControlLabel,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
  Typography,
} from '@mui/material';
import dayjs from 'dayjs';
import 'dayjs/locale/de';
import 'dayjs/locale/en-gb';
import 'dayjs/locale/zh-cn';
import { useRouter } from 'next/router';
import { ReactNode, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ResetPasswordContainer from '~/components/ResetPassword';
import { CustomError } from '~/errors/CustomerErrorResponse';
import { useHasAdminAccess } from '~/hooks/useHasAdminAccess';
import { useHasPowerUserAccess } from '~/hooks/useHasPowerUserAccess';
import { useGetCustomersQuery } from '~/redux/api/customersApi';
import { useGetUserPreferencesQuery, useUpdateUserPreferencesMutation } from '~/redux/api/usersApi';
import { getUserPreferences } from '~/redux/selectors/userPreferences';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { AlertSnackbar } from '~/shared/snackbars/components/AlertSnackbar';
import { useSnackbar } from '~/shared/snackbars/snackbar-hooks';
import { Customer } from '~/types/customers';
import { dateFormats } from '~/types/dashboard';

const UserPreferences = () => {
  const [snackbarState, showSuccessAlert, showErrorAlert] = useSnackbar();

  const { data, isLoading } = useGetCustomersQuery({ is_logo: true });
  const { globalAdmin, admin } = useHasAdminAccess();
  const hasPowerUserAccess = useHasPowerUserAccess();
  const currentUserPreferences = useSelector(getUserPreferences);
  const [activeCustomer, setActiveCustomer] = useState<Customer | null>(null);
  const dispatch = useDispatch();
  const [selectedFormat, setSelectedFormat] = useState(currentUserPreferences.DATE_FORMAT);
  const router = useRouter();
  const [thousandSeparator, setThousandSeparator] = useState<'enabled' | 'disabled'>(
    currentUserPreferences.THOUSAND_SEPARATOR ?? 'enabled',
  );

  const {
    data: UserPreferences,
    isLoading: isLoadingPreferences,
    refetch,
  } = useGetUserPreferencesQuery();
  useEffect(() => {
    setActiveCustomer(
      data?.find((customer) => customer.id === Number(currentUserPreferences.DEFAULT_CUSTOMER)) ??
        null,
    );
  }, [data]);
  useEffect(() => {
    if (UserPreferences) {
      const { DATE_FORMAT, DEFAULT_CUSTOMER, THOUSAND_SEPARATOR } = UserPreferences.preferences;
      setSelectedFormat(DATE_FORMAT || currentUserPreferences.DATE_FORMAT);
      setActiveCustomer(data?.find((customer) => customer.id === Number(DEFAULT_CUSTOMER)) ?? null);
      console.log(THOUSAND_SEPARATOR);
      // setThousandSeparator(
      //   Boolean(THOUSAND_SEPARATOR || currentUserPreferences.THOUSAND_SEPARATOR),
      // );
    }
  }, [UserPreferences]);
  const [createUserPreference, { isError, isLoading: usePreferenceLoading, isSuccess, error }] =
    useUpdateUserPreferencesMutation();
  const handleChange = (event: SelectChangeEvent<string>, child: ReactNode) => {
    setSelectedFormat(event.target.value);
  };
  const handleUserPreference = () => {
    createUserPreference({
      preferences: {
        DATE_FORMAT: selectedFormat,
        DEFAULT_CUSTOMER: (activeCustomer?.id ?? 0).toString(),
        THOUSAND_SEPARATOR: thousandSeparator,
      },
    });
    refetch();
  };
  const handleChangeCustomer = (e: React.SyntheticEvent, value: Customer | null) => {
    if (value === null) {
      return;
    }
    setActiveCustomer(value);
  };
  useEffect(() => {
    if (isSuccess) {
      showSuccessAlert('User Preferences updated successfully!');
      dispatch(
        dashboardSlice.actions.setUserPreferences({
          DATE_FORMAT: selectedFormat,
          DEFAULT_CUSTOMER: (activeCustomer?.id ?? 0).toString(),
          THOUSAND_SEPARATOR: thousandSeparator,
        }),
      );
      // router.back();
    }
    if (isError && error) {
      const err = error as CustomError;
      showErrorAlert(err.data.message ?? 'Server error');
    }
  }, [error, isError, isSuccess]);
  return (
    <>
      <AlertSnackbar {...snackbarState} />
      <Container maxWidth="xl" sx={{ mt: 2 }}>
        <Typography variant="h4" mt={2} mb={1}>
          User Preferences
        </Typography>
        <Typography variant="body1" sx={{ mb: 4 }}>
          User Preferences allow you to set your preferred date-time format and default customer.
        </Typography>

        <Autocomplete<Customer>
          id="combo-box-demo"
          options={data ?? []}
          loading={isLoading}
          renderOption={(props, option) => (
            <Box component="li" {...props} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <img
                src={(option.logo as any) || '/placeholder.jpg'}
                alt={option.name}
                width={40}
                height={40}
                style={{ borderRadius: '50%' }}
              />
              {option.name}
            </Box>
          )}
          getOptionLabel={(option) => option.name}
          onChange={handleChangeCustomer}
          value={activeCustomer ?? null}
          renderInput={(params) => <TextField {...params} label="Select Customer" />}
        />
        <FormControl fullWidth sx={{ mt: 2, mb: 2 }}>
          <InputLabel id="datetime-format-label">Select Date-Time Format</InputLabel>
          <Select
            labelId="datetime-format-label"
            id="datetime-format-select"
            value={selectedFormat}
            onChange={handleChange}
            label="Select Date-Time Format"
          >
            {dateFormats.sort().map((format, i) => (
              <MenuItem key={format} value={format}>
                {format}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControlLabel
          control={
            <Checkbox
              checked={thousandSeparator === 'enabled'}
              onChange={(e) => {
                if (e.target.checked) {
                  setThousandSeparator('enabled');
                } else {
                  setThousandSeparator('disabled');
                }
              }}
            />
          }
          label="Enable Thousand Separator (e.g., 1,000 vs 1000)"
        />

        <FormHelperText>
          <Typography>
            Note: The selected format will be used for all date-time fields in the application.
          </Typography>
          <Typography fontWeight={'bold'}>
            Date will be like : {dayjs(new Date().getTime()).format(selectedFormat)}
          </Typography>
        </FormHelperText>
        <Box width={'100%'} display={'flex'} justifyContent={'end'}>
          <Button
            onClick={handleUserPreference}
            variant="contained"
            color="primary"
            disabled={usePreferenceLoading || isLoadingPreferences}
            startIcon={<SaveIcon />}
          >
            Save
          </Button>
        </Box>
      </Container>

      <Container sx={{ my: 2 }} maxWidth="xl">
        <Box p={2} pb={0} pt={0} sx={{ display: 'flex' }}>
          <Box
            sx={{
              flexGrow: 1,
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <Typography variant="h4" fontWeight={'500'}>
              Reset Password
            </Typography>
          </Box>
        </Box>
        <Box sx={{ mt: 2 }}>
          <ResetPasswordContainer />
        </Box>
      </Container>
    </>
  );
};

export default UserPreferences;
