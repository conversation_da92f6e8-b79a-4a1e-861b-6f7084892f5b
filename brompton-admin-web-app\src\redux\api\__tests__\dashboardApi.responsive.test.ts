import { DashboardDetails } from '~/types/dashboard';
import { Layout } from 'react-grid-layout';

// Mock the transformResponse function from dashboardApi
// Since we can't easily test the actual RTK Query endpoint, we'll test the transformation logic
const mockTransformResponse = (response: DashboardDetails): DashboardDetails => {
  response.data = JSON.parse(response.data as unknown as string);
  
  // Apply the same transformation logic as in the actual API
  response.data.topPanel = {
    assetTz: response.data.topPanel?.assetTz ?? true,
    isVisible: response.data.topPanel?.isVisible || true,
    samplePeriod: response.data.topPanel?.samplePeriod || 2,
    timeRangeType: response.data.topPanel?.timeRangeType ?? 6,
    refreshInterval: response.data.topPanel?.refreshInterval || -1,
  };
  response.data.isDirty = false;
  response.data.tree = { ...response.data.tree };
  response.data.chart = { ...response.data.chart };
  response.data.dateFormat = response.data.dateFormat || 0;
  response.data.rightSideBar = response.data.rightSideBar || false;
  response.data.rightSideBarActiveTab = response.data.rightSideBarActiveTab || '/icons/alerts.svg';
  response.data.fullScreen = response.data.fullScreen || false;
  response.data.kisok = response.data.kisok || false;
  response.data.widget.deleteWidgets = response.data.widget.deleteWidgets || [];
  response.data.enableZoom = response.data.enableZoom ?? false;
  
  // Handle responsive layouts
  response.data.desktopMobile = response.data.desktopMobile ?? 0; // Default to desktop
  if (response.data.responsiveLayouts) {
    // If responsive layouts exist, use the appropriate layout based on desktopMobile
    const currentMode = response.data.desktopMobile === 0 ? 'desktop' : 'mobile';
    response.data.widget.widgetLayout = response.data.responsiveLayouts[currentMode]?.widgetLayout || response.data.widget.widgetLayout || [];
  } else {
    // If no responsive layouts, create them from current widgetLayout for backward compatibility
    const currentLayout = response.data.widget.widgetLayout || [];
    response.data.responsiveLayouts = {
      desktop: { widgetLayout: [...currentLayout] },
      mobile: { widgetLayout: [...currentLayout] }
    };
  }
  
  return response;
};

describe('Dashboard API - Responsive Layout Transform', () => {
  const mockDesktopLayout: Layout[] = [
    { i: '1', x: 0, y: 0, w: 6, h: 4 },
    { i: '2', x: 6, y: 0, w: 6, h: 4 },
  ];

  const mockMobileLayout: Layout[] = [
    { i: '1', x: 0, y: 0, w: 12, h: 4 },
    { i: '2', x: 0, y: 4, w: 12, h: 4 },
  ];

  const createMockResponse = (dataOverrides = {}): DashboardDetails => ({
    id: 1,
    title: 'Test Dashboard',
    description: 'Test Description',
    customerId: 1,
    data: JSON.stringify({
      currentDashboardId: 1,
      dashboardTitle: 'Test Dashboard',
      userDetails: null,
      userToken: null,
      customer: null,
      enableZoom: false,
      userPreferences: {
        DATE_FORMAT: 'DD-MM-YYYY HH:mm:ss',
        DEFAULT_CUSTOMER: '',
        THOUSAND_SEPARATOR: 'enabled',
      },
      dashboardCrumb: [],
      mainPanel: 'chart',
      isLeftPanelOpen: true,
      isDirty: true,
      kisok: false,
      fullScreen: false,
      rightSideBar: false,
      dateFormat: 0,
      newMeasureId: 0,
      rightSideBarActiveTab: '/icons/alerts.svg',
      topPanel: {
        isVisible: true,
        timeRangeType: 6,
        refreshInterval: -1,
        samplePeriod: 2,
        assetTz: true,
      },
      tree: {
        currentSelectedNodeId: '-1',
        selectedViewMeasureId: '-1',
        selectedNodeIds: ['-1'],
        expandedNodeIds: ['-1'],
        dbMeasureIdToName: {},
      },
      chart: {
        startDate: Date.now(),
        endDate: Date.now(),
      },
      widget: {
        widgets: [],
        widgetLayout: mockDesktopLayout,
        deleteWidgets: [],
        lastWidgetId: 0,
      },
      template: {
        assetTemplate: 0,
        templateId: 0,
        templateName: '',
        assetType: 0,
        metrics: [],
        idToName: {},
        topPanel: {
          timeRangeType: 6,
          refreshInterval: -1,
          samplePeriod: 2,
          assetTz: true,
        },
        chart: {
          startDate: Date.now(),
          endDate: Date.now(),
        },
      },
      metricMeasurements: {},
      ...dataOverrides,
    }),
  });

  describe('transformResponse with responsive layouts', () => {
    it('should set desktop mode as default when desktopMobile is not provided', () => {
      const response = createMockResponse();
      const transformed = mockTransformResponse(response);

      expect(transformed.data.desktopMobile).toBe(0);
    });

    it('should preserve existing desktopMobile value', () => {
      const response = createMockResponse({ desktopMobile: 1 });
      const transformed = mockTransformResponse(response);

      expect(transformed.data.desktopMobile).toBe(1);
    });

    it('should use desktop layout when desktopMobile is 0 and responsiveLayouts exist', () => {
      const response = createMockResponse({
        desktopMobile: 0,
        responsiveLayouts: {
          desktop: { widgetLayout: mockDesktopLayout },
          mobile: { widgetLayout: mockMobileLayout },
        },
      });

      const transformed = mockTransformResponse(response);

      expect(transformed.data.widget.widgetLayout).toEqual(mockDesktopLayout);
    });

    it('should use mobile layout when desktopMobile is 1 and responsiveLayouts exist', () => {
      const response = createMockResponse({
        desktopMobile: 1,
        responsiveLayouts: {
          desktop: { widgetLayout: mockDesktopLayout },
          mobile: { widgetLayout: mockMobileLayout },
        },
      });

      const transformed = mockTransformResponse(response);

      expect(transformed.data.widget.widgetLayout).toEqual(mockMobileLayout);
    });

    it('should create responsiveLayouts from existing widgetLayout when not provided', () => {
      const response = createMockResponse({
        widget: {
          widgets: [],
          widgetLayout: mockDesktopLayout,
          deleteWidgets: [],
          lastWidgetId: 0,
        },
      });

      const transformed = mockTransformResponse(response);

      expect(transformed.data.responsiveLayouts).toEqual({
        desktop: { widgetLayout: mockDesktopLayout },
        mobile: { widgetLayout: mockDesktopLayout },
      });
    });

    it('should handle empty widgetLayout gracefully', () => {
      const response = createMockResponse({
        widget: {
          widgets: [],
          widgetLayout: [],
          deleteWidgets: [],
          lastWidgetId: 0,
        },
      });

      const transformed = mockTransformResponse(response);

      expect(transformed.data.responsiveLayouts).toEqual({
        desktop: { widgetLayout: [] },
        mobile: { widgetLayout: [] },
      });
      expect(transformed.data.widget.widgetLayout).toEqual([]);
    });

    it('should fallback to empty layout when responsiveLayouts mode is missing', () => {
      const response = createMockResponse({
        desktopMobile: 0,
        responsiveLayouts: {
          mobile: { widgetLayout: mockMobileLayout },
          // desktop layout missing
        },
        widget: {
          widgets: [],
          widgetLayout: mockDesktopLayout,
          deleteWidgets: [],
          lastWidgetId: 0,
        },
      });

      const transformed = mockTransformResponse(response);

      expect(transformed.data.widget.widgetLayout).toEqual(mockDesktopLayout);
    });

    it('should preserve other dashboard properties during transformation', () => {
      const response = createMockResponse({
        desktopMobile: 1,
        responsiveLayouts: {
          desktop: { widgetLayout: mockDesktopLayout },
          mobile: { widgetLayout: mockMobileLayout },
        },
      });

      const transformed = mockTransformResponse(response);

      expect(transformed.data.isDirty).toBe(false);
      expect(transformed.data.topPanel.assetTz).toBe(true);
      expect(transformed.data.enableZoom).toBe(false);
      expect(transformed.data.dateFormat).toBe(0);
    });
  });
});
