import {
  AlertAggregatesDTO,
  AlertAnalytics,
  AlertAnalyticsDTO,
  AlertConditionDTO,
  AlertDetails,
  AlertMeasurementDTO,
  AlertsDTO,
  AlertStatsDTO,
  AlertThresholdTypeDTO,
  createAlertDTO,
  Event,
  EventDataDTO,
  EventDTO,
  ExcursionStatsDTO,
  PeriodDTO,
} from '~/measurements/domain/types';
import { authApi } from './authApi';

export const alertApi = authApi
  .enhanceEndpoints({
    addTagTypes: [
      'alert',
      'alert-aggregates',
      'alert-threshold-types',
      'alert-conditions',
      'alert-periods',
      'alert-measurements',
      'alert-details',
      'alert-stats',
      'excursions',
      'alert-analytics',
    ],
  })
  .injectEndpoints({
    endpoints: (build) => ({
      getAggregationPeriods: build.query<AlertAggregatesDTO, void>({
        query: () => '/v0/alert/aggregates',
        providesTags: ['alert-aggregates'],
      }),
      getThresholdTypes: build.query<AlertThresholdTypeDTO, void>({
        query: () => '/v0/alert/thresholdTypes',
        providesTags: ['alert-threshold-types'],
      }),
      getConditions: build.query<AlertConditionDTO, void>({
        query: () => '/v0/alert/conditions',
        providesTags: ['alert-conditions'],
      }),
      getPeriod: build.query<PeriodDTO, void>({
        query: () => '/v0/alert/periods',
        providesTags: ['alert-periods'],
      }),
      getAlertsByMeasurement: build.query<AlertMeasurementDTO, { measurementId: string }>({
        query: (measurementId) => `/v0/alert/measurements/${measurementId}`,
      }),
      enableAlert: build.mutation<void, { alertId: number }>({
        query: ({ alertId }) => ({
          url: `/v0/alert/enable/${alertId}`,
          method: 'PUT',
        }),
        invalidatesTags: ['alert'],
      }),
      getAllEventsAlerts: build.query<
        EventDataDTO,
        {
          start: number;
          end: number;
        }
      >({
        query: ({ start, end }) => ({
          url: `/v0/alert/events?start=${start}&end=${end}`,
        }),
        providesTags: (result, error, { start, end }) => [
          { type: 'alert', id: `events-${start}-${end}` },
        ],
      }),
      getAllAlerts: build.query<AlertsDTO, void>({
        query: () => '/v0/alert',
        providesTags: ['alert'],
      }),
      getAlertStats: build.query<AlertStatsDTO, { alertId: string }>({
        query: ({ alertId }) => `/v0/alert-stats/${alertId}`,
        providesTags: ['alert-stats'],
      }),
      getExcursions: build.query<ExcursionStatsDTO, void>({
        query: () => `/v0/alert-stats/excursions`,
        providesTags: ['excursions'],
      }),
      getAlertAnalytics: build.query<
        AlertAnalyticsDTO,
        { interval: string; assetId?: number; measureId?: number }
      >({
        query: ({ interval, assetId, measureId }) => {
          const params = new URLSearchParams({ interval });
          if (assetId) params.append('assetId', assetId.toString());
          if (measureId) params.append('measureId', measureId.toString());

          return `/v0/excursions?${params.toString()}`;
        },
        providesTags: ['alert-analytics'],
      }),

      getAlertAnalyticsDrillDownByDate: build.query<
        AlertAnalytics[],
        { interval: string; assetId?: number; measureId?: number; date?: string }
      >({
        query: ({ interval, assetId, measureId, date }) => {
          const params = new URLSearchParams({ interval });
          if (assetId) params.append('assetId', assetId.toString());
          if (measureId) params.append('measureId', measureId.toString());
          if (date) params.append('date', date);
          return `/v0/excursions?${params.toString()}`;
        },
        providesTags: ['alert-analytics'],
      }),
      getAlertById: build.query<AlertDetails, { alertId: number }>({
        query: ({ alertId }) => `/v0/alert/details/${alertId}`,
        providesTags: ['alert-details'],
      }),
      getEvent: build.query<Event, { eventID: string }>({
        query: ({ eventID }) => `/v0/alert/events/${eventID}`,
      }),
      getEventsByAlert: build.query<EventDTO, { alertId: string }>({
        query: ({ alertId }) => `/v0/alert/event/${alertId}`,
      }),
      deleteAlert: build.mutation<void, { alertId: number }>({
        query: ({ alertId }) => ({
          url: `/v0/alert/${alertId}`,
          method: 'DELETE',
        }),
        invalidatesTags: ['alert', 'alert-details'],
      }),
      editAlert: build.mutation<void, createAlertDTO>({
        query: ({
          id,
          agg,
          asset,
          condition,
          measurement,
          customerId,
          period,
          resetDeadband,
          thresholdType,
          learningPeriod,
          includeMomentum,
          includeVelocity,
          thresholdValue,
          description,
          users,
        }) => ({
          url: `/v0/alert/${id}`,
          method: 'PUT',
          body: {
            agg,
            asset,
            condition,
            measurement,
            customerId,
            period,
            resetDeadband,
            thresholdType,
            learningPeriod,
            includeMomentum,
            includeVelocity,
            thresholdValue,
            description,
            users,
          },
        }),
      }),
      createAlert: build.mutation<void, createAlertDTO>({
        query: ({
          agg,
          asset,
          condition,
          measurement,
          customerId,
          period,
          resetDeadband,
          thresholdType,
          thresholdValue,
          description,
          users,
          learningPeriod,
          includeMomentum,
          includeVelocity,
        }) => ({
          url: '/v0/alert',
          method: 'POST',
          body: {
            agg,
            asset,
            condition,
            measurement,
            customerId,
            period,
            resetDeadband,
            thresholdType,
            thresholdValue,
            description,
            users,
            learningPeriod,
            includeMomentum,
            includeVelocity,
          },
        }),
        invalidatesTags: ['alert'],
      }),
    }),
  });

export const {
  useGetAggregationPeriodsQuery,
  useGetConditionsQuery,
  useGetThresholdTypesQuery,
  useCreateAlertMutation,
  useGetPeriodQuery,
  useGetAlertsByMeasurementQuery,
  useGetAllAlertsQuery,
  useGetAlertByIdQuery,
  useEditAlertMutation,
  useDeleteAlertMutation,
  useEnableAlertMutation,
  useGetEventQuery,
  useGetEventsByAlertQuery,
  useGetAlertStatsQuery,
  useGetExcursionsQuery,
  useGetAlertAnalyticsQuery,
  useGetAlertAnalyticsDrillDownByDateQuery,
  useGetAllEventsAlertsQuery,
} = alertApi;
