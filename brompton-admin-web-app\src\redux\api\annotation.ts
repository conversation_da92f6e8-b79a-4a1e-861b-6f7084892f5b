import { Annotation, AnnotationDTO, createAnnotationRes } from '~/measurements/domain/types';
import { authApi } from './authApi';

export const annotationApi = authApi
  .enhanceEndpoints({
    addTagTypes: ['annotations'],
  })
  .injectEndpoints({
    endpoints: (build) => ({
      getAnnotations: build.query<
        AnnotationDTO,
        {
          dashboardId: string;
          widgetId: string;
          measureId: string;
          start: number;
          end: number;
        }
      >({
        query: ({ dashboardId, widgetId, measureId, start, end }) => {
          return `/v0/annotations/${dashboardId}/${widgetId}/${measureId}?startTime=${start}&endTime=${end}`;
        },
        providesTags: (result, error, { dashboardId, end, measureId, start, widgetId }) => [
          {
            type: 'annotations',
            id: `${dashboardId}_${widgetId}_${measureId}_${start}_${end ?? ''}`,
          },
        ],
      }),
      createAnnotation: build.mutation<createAnnotationRes, Annotation>({
        query: (newAnnotation) => ({
          url: '/v0/annotations',
          method: 'POST',
          body: newAnnotation, // Send the new annotation as the request body
        }),
        invalidatesTags: ['annotations'],
      }),
      updateAnnotation: build.mutation<void, Annotation>({
        query: (updateAnnotation) => ({
          url: `/v0/annotations/${updateAnnotation.id}`,
          method: 'PATCH',
          body: updateAnnotation, // Send the new annotation as the request body
        }),
        invalidatesTags: ['annotations'],
      }),
    }),
  });

export const { useGetAnnotationsQuery, useCreateAnnotationMutation, useUpdateAnnotationMutation } =
  annotationApi;
