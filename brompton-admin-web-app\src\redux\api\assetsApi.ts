import { AxiosError } from 'axios';
import { InvalidInputException } from '~/errors/exceptions';
import {
  assetTemplatesDTO,
  createAssetTemplateDataRequest,
  createAssetTemplateDTO,
  createAssetTemplateInstanceDTO,
  getAssetTemplateDataWithMetricName,
  UpdateAssetTemplateRequest,
  UpdateAssetTemplateRequestDTO,
} from '~/measurements/domain/types';
import { authApi } from '~/redux/api/authApi';
import {
  Asset,
  AssetDo,
  AssetTypeCollection,
  AssetTypeDtoCollection,
  AssetTypeMetricsCollection,
  EditAssetDo,
  NewAsset,
  TimeZoneCollection,
} from '~/types/asset';
import {
  AssetDto,
  AssetParams,
  CalcMetricDTORequest,
  CalcMetricDTOResponse,
  editAssetParams,
} from '~/types/measures';

function mapDtoToDomain(assetDto: AssetDto): Asset {
  const {
    type_id: assetTypeId,
    children_ids: childrenIds,
    parent_ids: parentIds,
    time_zone: timeZone,
    ...rest
  } = assetDto;
  const parent_ids = parentIds;
  return {
    assetTypeId,
    childrenIds,
    parentIds,
    timeZone,
    parent_ids,
    time_zone: timeZone,
    ...rest,
  };
}

export const assetsApi = authApi
  .enhanceEndpoints({
    addTagTypes: ['Customer', 'Asset', 'Measure', 'AssetTemplate', 'metric'],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      getAssetById: builder.query<AssetDo | Asset, AssetParams>({
        query: ({ customerId, assetId }) => `/v0/customers/${customerId}/assets/${assetId}`,
        providesTags: (result, error, { customerId, assetId }) => [
          { type: 'Customer', id: customerId },
          { type: 'Asset' },
          { type: 'Customer' },
          { type: 'Asset', id: assetId },
        ],
      }),
      getEditAssetById: builder.query<EditAssetDo, AssetParams>({
        query: ({ customerId, assetId }) => `/v0/customers/${customerId}/assets/${assetId}`,
        providesTags: (result, error, { customerId, assetId }) => [
          { type: 'Customer', id: customerId },
          { type: 'Customer' },
          { type: 'Asset' },
          { type: 'Asset', id: assetId },
        ],
      }),
      getMeasureById: builder.query<any, any>({
        query: ({ customerId, assetId }) => `/v0/customers/${customerId}/assets/${assetId}`,
        providesTags: (result, error, { customerId, assetId }) => [
          { type: 'Customer', id: customerId },
          { type: 'Customer' },
          { type: 'Asset' },
          { type: 'Asset', id: assetId },
        ],
      }),
      getAllTimeZones: builder.query<string[], void>({
        query: () => `/v0/assets-backoffice/time-zones`,
        transformResponse: (response: TimeZoneCollection) => {
          return response.items ?? [];
        },
        providesTags: () => [{ type: 'Asset' }],
      }),
      getAllBackOfficeAssetTypes: builder.query<AssetTypeCollection, void>({
        query: () => `/v0/assets-backoffice/asset-types`,
        providesTags: () => [{ type: 'Asset' }],
        transformResponse: (response: AssetTypeDtoCollection) => {
          return (
            response.items?.map((item) => ({
              ...item,
              parentType: item.parent_type_id,
            })) || []
          );
        },
      }),
      getAllBackOfficeMetrics: builder.query<AssetTypeCollection, void>({
        query: () => `/v0/assets-backoffice/metrics`,
        providesTags: () => [{ type: 'metric' }],
        // transformResponse: (response: AssetTypeDtoCollection) => {
        //   return (
        //     response.items?.map((item) => ({
        //       ...item,
        //       parentType: item.parent_type_id,
        //     })) || []
        //   );
        // },
      }),
      createBackOfficeAssetType: builder.mutation<void, { name: string; parent_type_id: number }>({
        query: ({ ...rest }) => {
          return {
            url: `/v0/assets-backoffice/asset-types`,
            method: 'POST',
            body: {
              ...rest,
            },
          };
        },
        invalidatesTags: ['Asset'],
      }),
      updateBackOfficeAssetType: builder.mutation<
        void,
        { id: number; name: string; parent_type_id: number }
      >({
        query: ({ ...rest }) => {
          return {
            url: `/v0/assets-backoffice/asset-types/${rest.id}`,
            method: 'PATCH',
            body: {
              ...rest,
            },
          };
        },
        invalidatesTags: ['Asset'],
      }),
      getAllBackOfficeAssetTypesMetrics: builder.query<
        AssetTypeMetricsCollection,
        { assetId: string }
      >({
        query: ({ assetId }) => `/v0/assets-backoffice/asset-types/${assetId}/metrics`,
        providesTags: () => ['Asset'],
        transformResponse: (response: AssetTypeMetricsCollection) => {
          return response;
        },
      }),
      createBackOfficeAssetTypeMetric: builder.mutation<void, { assetId: string; name: string }>({
        query: ({ assetId, name }) => {
          return {
            url: `/v0/assets-backoffice/asset-types/${assetId}/metrics`,
            method: 'POST',
            body: {
              name,
            },
          };
        },
        invalidatesTags: ['Asset'],
      }),
      createBackOfficeAssetTypeMetricBulk: builder.mutation<
        {
          id: number;
          name: string;
        }[],
        { assetId: number; name: string[] }
      >({
        query: ({ assetId, name }) => {
          return {
            url: `/v0/assets-backoffice/asset-types/${assetId}/bulk-metrics`,
            method: 'POST',
            body: {
              name,
            },
          };
        },
        invalidatesTags: ['Asset'],
      }),
      getAllAsset: builder.query<
        Asset[],
        {
          customerId: number;
          parentIds: number[];
        }
      >({
        query: ({ customerId, parentIds }) => {
          return {
            url: `/v0/customers/${customerId}/assets?parentIds=${parentIds.join(',')}`,
          };
        },
        transformResponse(response: { items: AssetDto[] }) {
          return response.items.map(mapDtoToDomain);
        },
        providesTags: (result, error, { customerId, parentIds }) => [
          { type: 'Customer', id: customerId },
          { type: 'Asset' },
          { type: 'Asset', id: parentIds[parentIds.length - 1] },
          { type: 'Asset', id: parentIds.join(',') },
        ],
      }),
      getMultiAsset: builder.query<
        Asset[],
        {
          customerId: number;
          ids: number[];
        }
      >({
        query: ({ customerId, ids }) => {
          return {
            url: `/v0/customers/${customerId}/assets?ids=${ids.join(',')}`,
          };
        },
        transformResponse(response: { items: AssetDto[] }) {
          return response.items.map(mapDtoToDomain);
        },
        providesTags: (result, error, { customerId, ids }) => [
          { type: 'Customer', id: customerId },
          { type: 'Asset' },
          { type: 'Asset', id: ids[ids.length - 1] },
          { type: 'Asset', id: ids.join(',') },
        ],
      }),
      createAssetTemplate: builder.mutation<any, createAssetTemplateDTO>({
        query: ({ assetTypeId, ...rest }) => {
          return {
            url: `/v0/assets-backoffice/asset-types/${assetTypeId}/asset-templates`,
            method: 'POST',
            body: {
              ...rest,
            },
          };
        },
        invalidatesTags: ['AssetTemplate'],
      }),
      createAssetTemplateMulti: builder.mutation<any, createAssetTemplateDataRequest>({
        query: ({ newTemplate, ...rest }) => {
          return {
            url: `/v0/assets-backoffice/asset-types/${newTemplate.assetTypeId}/asset-templates/multi`,
            method: 'POST',
            body: {
              ...rest,
              newTemplate,
            },
          };
        },
        invalidatesTags: ['AssetTemplate'],
      }),
      getAllAssetTemplatedByAssetType: builder.query<assetTemplatesDTO, { assetTypeId: string }>({
        query: ({ assetTypeId }) => {
          return {
            url: `/v0/assets-backoffice/asset-types/${assetTypeId}/asset-templates`,
          };
        },
        providesTags: ['AssetTemplate'],
      }),
      getAssetTemplatedByAssetType: builder.query<
        getAssetTemplateDataWithMetricName,
        { assetTypeId: string; templateId: string; assetAffected?: boolean }
      >({
        query: ({ assetTypeId, templateId, assetAffected }) => {
          const params = assetAffected ? '?assetAffected=true' : '';
          return {
            url: `/v0/assets-backoffice/asset-types/${assetTypeId}/asset-templates/${templateId}${params}`,
          };
        },
        providesTags: ['AssetTemplate'],
      }),

      cloneAssetTemplateByAssetType: builder.mutation<
        any,
        { assetTypeId: string; templateId: string; manufacturer: string; model_number: string }
      >({
        query: ({ assetTypeId, templateId, manufacturer, model_number }) => ({
          method: 'PUT',
          url: `/v0/assets-backoffice/asset-types/${assetTypeId}/asset-templates/${templateId}`,
          body: { manufacturer, model_number },
        }),
        invalidatesTags: ['AssetTemplate'], // Invalidate cache after mutation
      }),
      createAssetTemplateInstance: builder.mutation<
        any,
        {
          assetTypeId: string;
          assetTemplateId: string;
          createAssetTemplateData: createAssetTemplateInstanceDTO;
        }
      >({
        query: ({ assetTypeId, assetTemplateId, createAssetTemplateData }) => {
          const { asset, units_group_id } = createAssetTemplateData.assetTemplateInstance;
          const createAssetTemplate = { asset, units_group_id };
          return {
            url: `/v0/assets-backoffice/asset-types/${assetTypeId}/asset-templates/${assetTemplateId}/instances`,
            method: 'POST',
            body: {
              ...createAssetTemplate,
              measurements: createAssetTemplateData.measurement,
            },
          };
        },
        invalidatesTags: ['Asset', 'Measure'],
      }),
      // updateTemplateInstance:builder.mutation<void,void>()
      updateAssetTemplate: builder.mutation<
        any,
        {
          assetTypeId: string;
          assetTemplateId: string;
          updateData: UpdateAssetTemplateRequest;
        }
      >({
        query: ({ assetTypeId, assetTemplateId, updateData }) => {
          return {
            url: `/v0/assets-backoffice/asset-types/${assetTypeId}/asset-templates/${assetTemplateId}`,
            method: 'PATCH',
            body: updateData, // Correctly passing updateData as the request body
          };
        },
      }),
      updateAssetTemplateMulti: builder.mutation<
        any,
        {
          assetTypeId: string;
          assetTemplateId: string;
          updateAssetTemplate: UpdateAssetTemplateRequestDTO;
        }
      >({
        query: ({ assetTypeId, assetTemplateId, updateAssetTemplate }) => {
          return {
            url: `/v0/assets-backoffice/asset-types/${assetTypeId}/asset-templates/${assetTemplateId}/multi`,
            method: 'PATCH',
            body: {
              ...updateAssetTemplate,
            }, // Correctly passing updateData as the request body
          };
        },
      }),
      createAsset: builder.mutation<Asset, { customerId: number; asset: NewAsset }>({
        query: ({ customerId, asset }) => {
          const {
            assetTypeId: type_id,
            parentIds: parent_ids,
            parent_ids: parentId,
            timeZone: time_zone,
            tag,
            description,
            latitude,
            longitude,
          } = asset;
          try {
            return {
              url: `/v0/customers/${customerId}/assets`,
              method: 'POST',
              body: {
                parent_ids,
                type_id,
                time_zone,
                tag,
                description,
                latitude,
                longitude,
              },
            };
          } catch (error) {
            if (error instanceof AxiosError) {
              if (error.response?.status === 400 && error.response.data) {
                throw new InvalidInputException(error.response.data.message);
              }
            }
            throw error;
          }
        },
        transformResponse: (data: AssetDto, error, asset) => {
          return {
            id: data.id,
            childrenIds: data.children_ids,
            ...asset,
          } as unknown as Asset;
        },
        invalidatesTags: (result, error, { asset: { parentIds } }) => [
          { type: 'Asset' },
          { type: 'Asset', id: parentIds[parentIds.length - 1] },
          { type: 'Asset', id: parentIds.join(',') },
        ],
      }),
      deleteAsset: builder.mutation<void, AssetParams>({
        query: ({ customerId, assetId }) => {
          return {
            url: `/v0/customers/${customerId}/assets/${assetId}`,
            method: 'DELETE',
          };
        },
        invalidatesTags: (result, error, { assetId }) => [
          { type: 'Asset' },
          { type: 'Asset', id: assetId },
          { type: 'Measure' },
        ],
      }),
      editAsset: builder.mutation<void, editAssetParams>({
        query: ({ customerId, assetId, editAsset }) => {
          return {
            url: `/v0/customers/${customerId}/assets/${assetId}`,
            method: 'PATCH',
            body: {
              ...editAsset,
            },
          };
        },
        invalidatesTags: (result, error, { assetId }) => [
          { type: 'Asset' },
          { type: 'Asset', id: assetId },
          { type: 'Measure' },
        ],
      }),
      createCalcMetricTemplate: builder.mutation<CalcMetricDTOResponse[], CalcMetricDTORequest>({
        query: ({ data }) => {
          return {
            url: `/v0/calc-metrics-template`,
            method: 'POST',
            body: {
              data,
            },
          };
        },
        invalidatesTags: ['Asset'],
      }),
    }),
  });

export const {
  useGetAssetByIdQuery,
  useGetAllAssetQuery,
  useCreateAssetMutation,
  useDeleteAssetMutation,
  useEditAssetMutation,
  useGetEditAssetByIdQuery,
  useGetAllTimeZonesQuery,
  useGetAllBackOfficeAssetTypesQuery,
  useGetMultiAssetQuery,
  useGetAllBackOfficeAssetTypesMetricsQuery,
  useCreateAssetTemplateMutation,
  useGetAllAssetTemplatedByAssetTypeQuery,
  useCreateAssetTemplateInstanceMutation,
  useCreateBackOfficeAssetTypeMutation,
  useUpdateBackOfficeAssetTypeMutation,
  useCreateBackOfficeAssetTypeMetricMutation,
  useCreateBackOfficeAssetTypeMetricBulkMutation,
  useGetAssetTemplatedByAssetTypeQuery,
  useUpdateAssetTemplateMutation,
  useCloneAssetTemplateByAssetTypeMutation,
  useGetAllBackOfficeMetricsQuery,
  useCreateCalcMetricTemplateMutation,
  useCreateAssetTemplateMultiMutation,
  useUpdateAssetTemplateMultiMutation,
} = assetsApi;
