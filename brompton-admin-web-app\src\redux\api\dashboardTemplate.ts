import { DashboardTemplate, DashboardTemplateDTO } from '~/types/dashboardTemplate';
import { authApi } from './authApi';

export const dashboardTemplateApi = authApi
  .enhanceEndpoints({
    addTagTypes: ['DashboardTemplate'],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      getDashboardTemplates: builder.query<DashboardTemplateDTO, { assetTypeId?: number }>({
        query: (params) => ({
          url: 'v0/dashboard-templates',
          params: params.assetTypeId ? { assetTypeId: params.assetTypeId } : undefined,
        }),
        serializeQueryArgs: ({ queryArgs }) => `dashboardTemplates-${queryArgs.assetTypeId ?? ''}`,
        providesTags: (result, error, queryArg) => [
          { type: 'DashboardTemplate', id: queryArg.assetTypeId ?? '' },
        ],
      }),
      getDashboardTemplateDetails: builder.query<DashboardTemplate, number>({
        query: (id) => `v0/dashboard-templates/${id}`,
      }),
      updateDashboardTemplate: builder.mutation<
        DashboardTemplate,
        {
          id: number;
          title: string;
          data: string;
          asset_template: number;
          is_global?: boolean;
        }
      >({
        query: (body) => ({
          url: `/v0/dashboard-templates/${body.id}`,
          method: 'PATCH',
          body,
        }),
        invalidatesTags: ['DashboardTemplate'],
      }),
      createDashboardTemplate: builder.mutation<
        DashboardTemplate,
        {
          asset_template: number;
          title: string;
          data: string;
          is_global?: boolean;
        }
      >({
        query: (body) => ({
          url: '/v0/dashboard-templates',
          method: 'POST',
          body,
        }),
        invalidatesTags: ['DashboardTemplate'],
      }),
      deleteDashboardTemplate: builder.mutation<void, number>({
        query: (id) => ({
          url: `/v0/dashboard-templates/${id}`,
          method: 'DELETE',
        }),
        invalidatesTags: ['DashboardTemplate'],
      }),
      createDashboardFromTemplate: builder.mutation<
        void,
        {
          asset_template: number;
          title: string;
          data: string;
          id: number;
        }
      >({
        query: (body) => ({
          url: `/v0/dashboard-templates/create-from-template/${body.id}`,
          method: 'POST',
          body,
        }),
      }),
    }),
  });

export const {
  useGetDashboardTemplatesQuery,
  useCreateDashboardTemplateMutation,
  useDeleteDashboardTemplateMutation,
  useCreateDashboardFromTemplateMutation,
  useGetDashboardTemplateDetailsQuery,
  useUpdateDashboardTemplateMutation,
} = dashboardTemplateApi;
