import { authApi } from '~/redux/api/authApi';

export const dataIngestionApi = authApi
  .enhanceEndpoints({
    addTagTypes: ['dataIngestion'],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      uploadCsv: builder.mutation<
        {
          status: string;
          message: string;
          cache_id: string;
          unique_values: string[];
          csv_headers: string[];
          composite_signals?: { fileAsset: string; fileMeasure: string }[];
        },
        FormData
      >({
        query: (formData) => ({
          url: `${process.env.NEXT_PUBLIC_TS_IMPORT_API_ENDPOINT}/import_data/upload`,
          method: 'POST',
          body: formData,
          formData: true,
        }),
      }),

      injectTsData: builder.mutation<
        {
          status: string;
          total_points: number;
          failed_count: number;
          success_count: number;
          signal_summary: Record<
            string,
            {
              redis_key: string;
              total_count: number;
              success_count: number;
              failed_count: number;
            }
          >;
          invalid_signals: string[] | null;
          invalid_composites: string[] | null;
          failed_operations: Record<string, any> | null;
          message: string;
        },
        FormData
      >({
        query: (formData) => ({
          url: `${process.env.NEXT_PUBLIC_TS_IMPORT_API_ENDPOINT}/import_data/inject-data`,
          method: 'POST',
          body: formData,
          formData: true,
        }),
      }),
    }),
  });

export const { useUploadCsvMutation, useInjectTsDataMutation } = dataIngestionApi;
