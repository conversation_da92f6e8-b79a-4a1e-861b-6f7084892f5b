import {
  FactorScheduleDetails,
  factorSchedulesDTO,
  factorScheduleTimesDTO,
  FactorTypeDTO,
  timeVaringVariantDTO,
} from '~/types/factor';
import { authApi } from './authApi';

export const factorApi = authApi
  .enhanceEndpoints({
    addTagTypes: ['schedules'],
  })
  .injectEndpoints({
    endpoints: (build) => ({
      getFactors: build.query<FactorTypeDTO, void>({
        query: () => '/v0/factors/types',
      }),
      getFactorByMeasureId: build.query<FactorScheduleDetails, number>({
        query: (measureId) => `/v0/factors/timeVariangFactor/${measureId}`,
      }),
      getFactorTimeSchedules: build.query<
        factorScheduleTimesDTO,
        {
          factorTypeId: number;
          factor: number;
        }
      >({
        query: ({ factorTypeId, factor }) => {
          return `/v0/factors/schedules/${factorTypeId}/weekTime/${factor}`;
        },
      }),
      getFactorSchdules: build.query<
        factorSchedulesDTO,
        {
          factorTypeId: number;
        }
      >({
        query: ({ factorTypeId }) => {
          return `/v0/factors/schedules/${factorTypeId}`;
        },
      }),
      createTimeVaringFactorSchedule: build.mutation<void, timeVaringVariantDTO>({
        query: ({ factorSchedule, factorTimeOfDayValue, timeVaryingFactor }) => ({
          url: `/v0/factors/create-factor`,
          method: 'POST',
          body: {
            factorSchedule,
            factorTimeOfDayValue,
            timeVaryingFactor,
          },
        }),
      }),
      editTimeVaringFactorSchedule: build.mutation<void, timeVaringVariantDTO>({
        query: ({ factorSchedule, factorTimeOfDayValue, timeVaryingFactor, id }) => ({
          url: `/v0/factors/timeVariangFactor/${id}`,
          method: 'PUT',
          body: {
            factorSchedule,
            factorTimeOfDayValue,
            timeVaryingFactor,
          },
        }),
      }),
    }),
  });

export const {
  useGetFactorsQuery,
  useGetFactorSchdulesQuery,
  useGetFactorTimeSchedulesQuery,
  useCreateTimeVaringFactorScheduleMutation,
  useGetFactorByMeasureIdQuery,
  useEditTimeVaringFactorScheduleMutation,
} = factorApi;
