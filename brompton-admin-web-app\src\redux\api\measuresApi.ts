import { UnitOfMeasureDTO } from '~/components/UnitsOfMeasure/domain';
import {
  AssetMeasurement,
  AssetMeasurementDto,
  AssetMeasurementEdit,
  DataType,
  Datasource,
  LocationDTO,
  MeasurementLocation,
  MeasurementType,
  MeasurementUnits,
  NewAssetMeasurement,
  UnitOfGroups,
  UnitOfGroupsMeasureDTO,
  UnitOfMeasure,
  ValueType,
} from '~/measurements/domain/types';
import { authApi } from '~/redux/api/authApi';
import {
  AssetMeasurementDetails,
  AssetMeasurementDetailsParams,
  DeleteMeasureParams,
  EditMeasureParams,
  extendedMeasurementsDTO,
  measurementsMetricsDTO,
  measurementsUnitsDTO,
} from '~/types/measures';

function toCamelCase(str: string): string {
  return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
}

function mapDtoToDomain(assetMeasurementDto: AssetMeasurementDto): AssetMeasurement {
  return {
    typeId: assetMeasurementDto.type_id,
    unitOfMeasureId: assetMeasurementDto.unit_of_measure_id,
    locationId: assetMeasurementDto.location_id,
    meterFactor: assetMeasurementDto.meter_factor,
    measurementId: assetMeasurementDto.measurement_id,
    dataTypeId: assetMeasurementDto.data_type_id,
    valueTypeId: assetMeasurementDto.value_type_id,
    datasourceId: assetMeasurementDto.datasource_id,
    ...assetMeasurementDto,
  };
}

function mapNewDomainToDto(assetMeasurement: NewAssetMeasurement) {
  const {
    typeId: type_id,
    unitOfMeasureId: unit_of_measure_id,
    locationId: location_id,
    meterFactor: meter_factor,
    dataTypeId: data_type_id,
    valueTypeId: value_type_id,
    datasourceId: datasource_id,
    ...rest
  } = assetMeasurement;
  return {
    type_id,
    unit_of_measure_id,
    data_type_id,
    value_type_id,
    location_id,
    meter_factor,
    datasource_id,
    ...rest,
  };
}

export const measuresApi = authApi
  .enhanceEndpoints({
    addTagTypes: [
      'Customer',
      'Asset',
      'Measure',
      'MeasureTypes',
      'MeasurementDataResources',
      'Locations',
      'UnitsOfMeasure',
      'unitOfGroups',
    ],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      getAllMeasureTypes: builder.query<MeasurementType[], void>({
        query: () => `/v0/measurements-backoffice/measurement-types`,
        providesTags: () => [{ type: 'MeasureTypes' }],
        transformResponse: (response: { items: UnitOfMeasure[] }) => {
          return response.items;
        },
      }),
      getAllDataTypes: builder.query<DataType[], void>({
        query: () => `/v0/measurements-backoffice/data-types`,
        providesTags: () => [{ type: 'MeasureTypes' }],
        transformResponse: (response: { items: UnitOfMeasure[] }) => {
          return response.items;
        },
      }),
      getAllDatasources: builder.query<
        { items: Datasource[] },
        {
          extra?: boolean;
        }
      >({
        query: ({ extra } = {}) => {
          const queryParams = new URLSearchParams();
          if (extra) {
            queryParams.set('extra', 'true');
          }

          return {
            url: `/v0/measurements-backoffice/datasources?${queryParams.toString()}`,
            credentials: 'include',
          };
        },
        providesTags: () => [{ type: 'MeasurementDataResources' }],
        transformResponse: (response: { items: Datasource[] }) => {
          return response;
        },
      }),
      getAllLocations: builder.query<{ total: number; items: MeasurementLocation[] }, void>({
        query: () => `/v0/measurements-backoffice/locations`,
        providesTags: () => [{ type: 'Locations' }],
        transformResponse: (response: { total: number; items: MeasurementLocation[] }) => {
          return response;
        },
      }),
      getAllUnitOfGroups: builder.query<{ total: number; items: UnitOfGroups[] }, void>({
        query: () => `/v0/measurements-backoffice/units-groups`,
        providesTags: () => [{ type: 'unitOfGroups' }],
        transformResponse: (response: { total: number; items: UnitOfGroups[] }) => {
          return response;
        },
      }),
      getUnitOMeasureWithMeasureType: builder.query<
        UnitOfGroupsMeasureDTO,
        { unitOfGroup: number; measurementType: number }
      >({
        query: ({ unitOfGroup, measurementType }) =>
          `/v0/measurements-backoffice/units-groups/${unitOfGroup}/units-of-measure?measurementTypeId=${measurementType}`,
        providesTags: () => [{ type: 'unitOfGroups' }],
      }),
      getAllUnitsOfMeasure: builder.query<UnitOfMeasure[], { measurementTypeId: number }>({
        query: ({ measurementTypeId }) => {
          return {
            url: `/v0/measurements-backoffice/measurement-types/${measurementTypeId}/units-of-measure`,
            credentials: 'include',
          };
        },
        providesTags: () => [{ type: 'UnitsOfMeasure' }],
        transformResponse: (response: { items: UnitOfMeasure[] }) => {
          return response.items;
        },
      }),
      getAllUnitsOfMeasureWithMeasureType: builder.query<UnitOfMeasureDTO, void>({
        query: () => `/v0/measurements-backoffice/units-groups/unit-of-measures`,
      }),
      createUnitOfMeasureByMeasures: builder.mutation<
        void,
        {
          measurementTypeId: number;
          name: string;
        }
      >({
        query: ({ measurementTypeId, name }) => {
          return {
            url: `/v0/measurements-backoffice/units-groups/${measurementTypeId}`,
            method: 'POST',
            body: { name, measurementTypeId },
          };
        },
        invalidatesTags: (result, error, { measurementTypeId }) => [
          { type: 'Measure', id: measurementTypeId },
        ],
      }),
      updateUnitOfMeasureByMeasures: builder.mutation<
        void,
        {
          unitOfMeasureId: number;
          measurementTypeId: number;
          name: string;
        }
      >({
        query: ({ unitOfMeasureId, measurementTypeId, name }) => {
          return {
            url: `/v0/measurements-backoffice/units-groups/${unitOfMeasureId}`,
            method: 'PUT',
            body: { name, measurementTypeId, unitOfMeasureId },
          };
        },
        invalidatesTags: (result, error, { measurementTypeId }) => [
          { type: 'Measure', id: measurementTypeId },
        ],
      }),
      getAllValueTypes: builder.query<ValueType[], void>({
        query: () => `/v0/measurements-backoffice/value-types`,
        providesTags: () => [{ type: 'MeasureTypes' }],
        transformResponse: (response: { items: UnitOfMeasure[] }) => {
          return response.items;
        },
      }),
      createUnitOfMeasure: builder.mutation<
        void,
        {
          measurementTypeId: number;
          unitsGroupId: number;
          unitOfMeasureId: number;
          is_default: boolean;
        }
      >({
        query: (unitOfMeasure) => {
          return {
            url: `/v0/measurements-backoffice/units-groups/units-group-unit/create`,
            method: 'POST',
            body: unitOfMeasure,
          };
        },
      }),
      updateUnitOfMeasure: builder.mutation<
        void,
        {
          unitsofUnitId: number;
          measurementTypeId: number;
          unitsGroupId: number;
          unitOfMeasureId: number;
          is_default: boolean;
        }
      >({
        query: (unitOfMeasure) => {
          return {
            url: `/v0/measurements-backoffice/units-groups/units-group-unit/${unitOfMeasure.unitsofUnitId}`,
            method: 'PUT',
            body: unitOfMeasure,
          };
        },
      }),
      getUnitGroupsUnits: builder.query<MeasurementUnits, void>({
        query: () => `/v0/measurements-backoffice/units-groups/units-groups-units`,
        providesTags: () => [{ type: 'unitOfGroups' }],
      }),
      setDefaultUnitOfMeasure: builder.mutation<
        void,
        {
          unitsofUnitId: number;
          measurementTypeId: number;
          unitOfMeasureId: number;
          unitsGroupId: number;
        }
      >({
        query: ({ unitsofUnitId, measurementTypeId, unitOfMeasureId, unitsGroupId }) => {
          return {
            url: `/v0/measurements-backoffice/units-groups/units-group-unit/${unitsofUnitId}`,
            method: 'PUT',
            body: {
              measurementTypeId,
              unitOfMeasureId,
              unitsGroupId,
            },
          };
        },
      }),
      getUnitsOfMeasure: builder.query<
        UnitOfMeasure[],
        {
          measurementTypeId: number;
        }
      >({
        query: ({ measurementTypeId }) =>
          `/v0/measurements-backoffice/measurement-types/${measurementTypeId}/units-of-measure`,
        providesTags: (result, error, { measurementTypeId }) => [
          { type: 'Measure' },
          { type: 'Measure', id: measurementTypeId },
        ],
        transformResponse: (response: { items: UnitOfMeasure[] }) => {
          return response.items;
        },
      }),
      getMeasurementById: builder.query<AssetMeasurementDetails, AssetMeasurementDetailsParams>({
        query: ({ customerId, assetId, measId }) => {
          return {
            url: `/v0/customers/${customerId}/assets/${assetId}/measurements/${measId}`,
          };
        },
        providesTags: (result, error, { customerId, assetId, measId }) => [
          { type: 'Customer', id: customerId },
          { type: 'Asset', id: assetId },
          { type: 'Measure' },
          { type: 'Measure', id: measId },
        ],
        transformResponse: (response: Record<string, unknown>) => {
          const transformedResponse: Record<string, unknown> = {};

          Object.keys(response).forEach((key) => {
            transformedResponse[toCamelCase(key)] = response[key];
          });

          return transformedResponse as AssetMeasurement;
        },
      }),
      getMeasuresWithAssetMeasures: builder.query<
        measurementsUnitsDTO,
        {
          customerId: number;
          data: {
            asset_id: number;
            measurement_ids: number[];
          }[];
        }
      >({
        query: ({ customerId, data }) => {
          return {
            url: `/v0/customers/${customerId}/measurements`,
            method: 'POST',
            body: data,
          };
        },
        providesTags: (result, error, arg) => [
          { type: 'Customer', id: arg.customerId },
          { type: 'Measure' },
        ],
      }),
      getAllMeasurements: builder.query<
        AssetMeasurement[],
        {
          customerId: number;
          assetId: number;
        }
      >({
        query: ({ customerId, assetId }) =>
          `/v0/customers/${customerId}/assets/${assetId}/measurements`,
        providesTags: (result, error, { customerId, assetId }) => [
          { type: 'Customer', id: customerId },
          { type: 'Asset', id: assetId },
          { type: 'Measure' },
        ],
        transformResponse: (response: { items: AssetMeasurementDto[] }) => {
          return response.items.map(mapDtoToDomain);
        },
      }),
      getAssetMeasurementById: builder.query<
        AssetMeasurement | AssetMeasurementEdit,
        { customerId: number; assetId: number; assetMeasurementId: number }
      >({
        query: ({ customerId, assetId, assetMeasurementId }) => {
          return {
            url: `/v0/customers/${customerId}/assets/${assetId}/measurements/${assetMeasurementId}`,
            credentials: 'include',
          };
        },
        transformResponse: (resposnse: AssetMeasurement | AssetMeasurementEdit) => {
          return resposnse;
        },
        providesTags: (result, error, { customerId, assetId, assetMeasurementId }) => [
          { type: 'Customer', id: customerId },
          { type: 'Asset', id: assetId },
          { type: 'Measure' },
          { type: 'Measure', id: assetMeasurementId },
        ],
      }),
      getAssetMeasureByLocation: builder.query<LocationDTO, { measurementId: string }>({
        query: ({ measurementId }) =>
          `/v0/measurements-backoffice/location-by-measure/${measurementId}`,
        transformResponse: (response: LocationDTO) => {
          return response;
        },
        providesTags: (result, error, { measurementId }) => [
          { type: 'Locations', id: measurementId },
        ],
      }),
      getMeasuresByCustomers: builder.mutation<
        measurementsMetricsDTO,
        { customerId: number; measurements: number[] }
      >({
        query: ({ customerId, measurements }) => {
          return {
            url: `/v0/customers/${customerId}/measurements-metrics`,
            method: 'POST',
            body: measurements,
          };
        },
      }),
      getAllMeasurementsByCustomer: builder.query<
        extendedMeasurementsDTO,
        { customerId: number; lastreadings?: boolean }
      >({
        query: ({ customerId, lastreadings }) => {
          const params = new URLSearchParams();
          if (lastreadings !== undefined) {
            params.append('lastreadings', String(lastreadings));
          }
          return `/v0/measurements-backoffice/customer/${customerId}?${params.toString()}`;
        },
        providesTags: (result, error, { customerId }) => [{ type: 'Customer', id: customerId }],
      }),
      createMeasurement: builder.mutation<
        AssetMeasurement,
        {
          customerId: number;
          assetId: number;
          assetMeasurement: NewAssetMeasurement;
        }
      >({
        query: ({ customerId, assetId, assetMeasurement }) => {
          return {
            url: `/v0/customers/${customerId}/assets/${assetId}/measurements`,
            method: 'POST',
            body: mapNewDomainToDto(assetMeasurement),
          };
        },
        transformResponse: (result: AssetMeasurementDto) => {
          return mapDtoToDomain(result);
        },
        invalidatesTags: (result, error, { assetId }) => {
          return [{ type: 'Asset' }, { type: 'Asset', id: assetId }, { type: 'Measure' }];
        },
      }),
      deleteMeasure: builder.mutation<void, DeleteMeasureParams>({
        query: ({ customerId, assetId, measId }) => {
          return {
            url: `/v0/customers/${customerId}/assets/${assetId}/measurements/${measId}`,
            method: 'DELETE',
          };
        },
        invalidatesTags: (result, error, { assetId, measId }) => [
          { type: 'Asset' },
          { type: 'Measure' },
          { type: 'Asset', id: assetId },
          { type: 'Measure', id: measId },
        ],
      }),
      editMeasure: builder.mutation<void, EditMeasureParams>({
        query: ({ customerId, assetId, measId, editAssetMeasurement }) => {
          return {
            url: `/v0/customers/${customerId}/assets/${assetId}/measurements/${measId}`,
            method: 'PATCH',
            body: {
              ...editAssetMeasurement,
            },
          };
        },
        invalidatesTags: (result, error, { assetId, measId }) => [
          { type: 'Asset', id: assetId },
          { type: 'Asset' },
          { type: 'Measure', id: measId },
          { type: 'Measure' },
        ],
      }),
      createMeasurementType: builder.mutation<
        MeasurementType,
        {
          name: string;
          category?: string | null;
          description?: string | null;
        }
      >({
        query: (measurementType) => ({
          url: `/v0/measurements-backoffice/measurement-types`,
          method: 'POST',
          body: measurementType,
        }),
        invalidatesTags: [{ type: 'MeasureTypes' }],
      }),
      updateMeasurementType: builder.mutation<
        void,
        {
          id: number;
          name: string;
          category?: string | null;
          description?: string | null;
        }
      >({
        query: ({ id, ...measurementType }) => ({
          url: `/v0/measurements-backoffice/measurement-types/${id}`,
          method: 'PUT',
          body: measurementType,
        }),
        invalidatesTags: [{ type: 'MeasureTypes' }],
      }),
      getMeasurementTypeById: builder.mutation<
        {
          id: number;
          name: string;
          category?: string | null;
          description?: string | null;
        },
        number
      >({
        query: (id) => ({
          url: `/v0/measurements-backoffice/measurement-types/${id}`,
          method: 'GET',
        }),
      }),
      deleteMeasurementType: builder.mutation<void, number>({
        query: (id) => ({
          url: `/v0/measurements-backoffice/measurement-types/${id}`,
          method: 'DELETE',
        }),
        invalidatesTags: [{ type: 'MeasureTypes' }],
      }),
    }),
  });

export const {
  useCreateUnitOfMeasureMutation,
  useUpdateUnitOfMeasureMutation,
  useGetAllMeasureTypesQuery,
  useGetAllDataTypesQuery,
  useGetAllValueTypesQuery,
  useGetMeasurementByIdQuery,
  useCreateMeasurementMutation,
  useDeleteMeasureMutation,
  useGetAllDatasourcesQuery,
  useGetAllLocationsQuery,
  useGetAllMeasurementsQuery,
  useGetAllUnitsOfMeasureQuery,
  useGetAssetMeasurementByIdQuery,
  useEditMeasureMutation,
  useGetAllUnitOfGroupsQuery,
  useGetAssetMeasureByLocationQuery,
  useGetAllMeasurementsByCustomerQuery,
  useGetUnitOMeasureWithMeasureTypeQuery,
  useSetDefaultUnitOfMeasureMutation,
  useGetUnitGroupsUnitsQuery,
  useGetAllUnitsOfMeasureWithMeasureTypeQuery,
  useCreateUnitOfMeasureByMeasuresMutation,
  useUpdateUnitOfMeasureByMeasuresMutation,
  useGetMeasuresByCustomersMutation,
  useCreateMeasurementTypeMutation,
  useUpdateMeasurementTypeMutation,
  useDeleteMeasurementTypeMutation,
  useGetMeasurementTypeByIdMutation,
  useGetMeasuresWithAssetMeasuresQuery,
} = measuresApi;
