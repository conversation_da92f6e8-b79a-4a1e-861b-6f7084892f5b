import { authApi } from '~/redux/api/authApi';
import {
  ForecastSeriesDataParams,
  HeatMapSeriesParams,
  ScatterSeriesDataParams,
  SingleHeatMapTimeSeriesData,
  SingleScatterTimeSeriesData,
} from '~/types/timeseries';
import { getBrowserTimezoneName } from '~/utils/utils';

export const timeseriesApi = authApi
  .enhanceEndpoints({
    addTagTypes: ['timeseries', 'Customer', 'Asset', 'Measure'],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      getMeasurementSeries: builder.query<SingleScatterTimeSeriesData, ScatterSeriesDataParams>({
        query: ({ customerId, start, end, measId, agg, agg_period, timeRangeType, assetTz }) => {
          const aggBy = agg ? `&agg=${agg}` : '';
          const samplePeriod = agg_period ? `&agg_period=${agg_period}` : '';
          const queryType = aggBy ? 'agg' : 'history';
          const use_asset_tz = assetTz;
          const startDate = start;
          const endDate = end;
          const endQuery = end ? `&end=${endDate}` : '';
          return {
            url: `${
              process.env.NEXT_PUBLIC_TS_API_ENDPOINT
            }/${queryType}/${customerId}?start=${startDate}&meas_id=${measId}${endQuery}${aggBy}${samplePeriod}&use_asset_tz=${use_asset_tz}&browser_tz=${getBrowserTimezoneName()}`,
          };
        },
        providesTags: (result, error, { customerId, measId, start, end }) => [
          { type: 'Customer' },
          { type: 'Asset' },
          { type: 'Measure' },
          { type: 'Measure', id: measId },
          { type: 'timeseries', id: `${customerId}_${measId}_${start}_${end ?? ''}` },
        ],
        transformResponse(response: SingleScatterTimeSeriesData[]): SingleScatterTimeSeriesData {
          if (response.length === 0) {
            return [] as unknown as SingleScatterTimeSeriesData;
          }
          return response[0];
        },
      }),
      getTestMeasurementSeries: builder.query<SingleScatterTimeSeriesData, ScatterSeriesDataParams>(
        {
          query: ({ customerId, start, end, measId, agg, agg_period, timeRangeType, assetTz }) => {
            const aggBy = agg ? `&agg=${agg}` : '';
            const samplePeriod = agg_period ? `&agg_period=${agg_period}` : '';
            const queryType = aggBy ? 'agg' : 'history';
            const use_asset_tz = assetTz;
            const startDate = start;
            const endDate = end;
            const endQuery = end ? `&end=${endDate}` : '';
            return {
              url: `${
                process.env.NEXT_PUBLIC_TS_API_ENDPOINT
              }/${queryType}/${customerId}?start=${startDate}&meas_id=${measId}${endQuery}${aggBy}${samplePeriod}&use_asset_tz=${use_asset_tz}&browser_tz=${getBrowserTimezoneName()}`,
            };
          },
          providesTags: (result, error, { customerId, measId, start, end }) => [
            { type: 'Customer' },
            { type: 'Asset' },
            { type: 'Measure' },
            { type: 'Measure', id: measId },
            { type: 'timeseries', id: `${customerId}_${measId}_${start}_${end ?? ''}` },
          ],
          transformResponse(response: SingleScatterTimeSeriesData[]): SingleScatterTimeSeriesData {
            if (response.length === 0) {
              return [] as unknown as SingleScatterTimeSeriesData;
            }
            return response[0];
          },
        },
      ),
      getForecastMeasurementSeries: builder.query<
        Record<number, SingleScatterTimeSeriesData>,
        ForecastSeriesDataParams
      >({
        query: ({ customerId, measId, forecast, assetTz, agg }) => {
          return {
            url: `${
              process.env.NEXT_PUBLIC_TS_API_ENDPOINT
            }/forecast/${customerId}?agg=${agg}&meas_id=${measId}&forecast=${forecast}&use_asset_tz=${assetTz}&browser_tz=${getBrowserTimezoneName()}`,
          };
        },
        transformResponse(
          response: SingleScatterTimeSeriesData[],
        ): Record<number, SingleScatterTimeSeriesData> {
          if (response.length === 0) {
            return [] as unknown as SingleScatterTimeSeriesData[];
          }
          const tagToTsData: Record<number, SingleScatterTimeSeriesData> = {};
          response.forEach((res) => {
            tagToTsData[res.tag] = res;
          });

          return tagToTsData;
        },
      }),
      getMultiDummyMeasurementSeries: builder.query<
        Record<number, SingleScatterTimeSeriesData>,
        ScatterSeriesDataParams
      >({
        query: ({ customerId, start, end, measId, agg, agg_period, timeRangeType, assetTz }) => {
          const aggBy = agg ? `&agg=${agg}` : '';
          const samplePeriod = agg_period ? `&agg_period=${agg_period}` : '';
          const queryType = 'dummy';
          const startDate = start;
          const endDate = end;
          const endQuery = end ? `&end=${endDate}` : '';
          return {
            url: `${
              process.env.NEXT_PUBLIC_TS_API_ENDPOINT
            }/${queryType}/${customerId}?start=${startDate}&meas_id=${measId}${endQuery}${aggBy}${samplePeriod}&use_asset_tz=${assetTz}&browser_tz=${getBrowserTimezoneName()}`,
          };
        },
        providesTags: (result, error, { customerId, measId, start, end }) => [
          { type: 'Customer' },
          { type: 'Asset' },
          { type: 'Measure' },
          { type: 'Measure', id: measId },
          { type: 'timeseries', id: `${customerId}_${measId}_${start}_${end ?? ''}` },
        ],
        transformResponse(
          response: SingleScatterTimeSeriesData[],
        ): Record<number, SingleScatterTimeSeriesData> {
          if (response?.length === 0) {
            return [] as unknown as SingleScatterTimeSeriesData[];
          }
          const tagToTsData: Record<number, SingleScatterTimeSeriesData> = {};
          response.forEach((res) => {
            tagToTsData[res.tag] = res;
          });
          return tagToTsData;
        },
      }),
      getMultipleHistoryMeasurementSeries: builder.query<
        Record<number, SingleScatterTimeSeriesData>,
        {
          customerId: number;
          measId: string;
          start: number;
          end: number;
          assetTz: boolean;
        }
      >({
        query: ({ customerId, start, end, measId, assetTz }) => {
          return {
            url: `${
              process.env.NEXT_PUBLIC_TS_API_ENDPOINT_V2
            }/history/${customerId}?start=${start}&end=${end}&meas_id=${measId}&use_asset_tz=${assetTz}&browser_tz=${getBrowserTimezoneName()}`,
          };
        },
        providesTags: (result, error, { customerId, measId, start, end }) => [
          { type: 'Customer' },
          { type: 'Asset' },
          { type: 'Measure' },
          { type: 'Measure', id: measId },
          { type: 'timeseries', id: `${customerId}_${measId}_${start}_${end ?? ''}` },
        ],
        transformResponse(
          response: SingleScatterTimeSeriesData[],
        ): Record<number, SingleScatterTimeSeriesData> {
          if (response?.length === 0) {
            return [] as unknown as SingleScatterTimeSeriesData[];
          }
          const tagToTsData: Record<number, SingleScatterTimeSeriesData> = {};
          response.forEach((res) => {
            tagToTsData[res.tag] = res;
          });
          return tagToTsData;
        },
      }),
      getMultiMeasurementSeries: builder.query<
        Record<number, SingleScatterTimeSeriesData>,
        ScatterSeriesDataParams
      >({
        query: ({ customerId, start, end, measId, agg, agg_period, timeRangeType, assetTz }) => {
          const aggBy = agg ? `&agg=${agg}` : '';
          const samplePeriod = agg_period ? `&agg_period=${agg_period}` : '';
          const queryType = aggBy ? 'agg' : 'history';
          const startDate = start;
          const endDate = end;
          const endQuery = end ? `&end=${endDate}` : '';
          return {
            url: `${
              process.env.NEXT_PUBLIC_TS_API_ENDPOINT_V2
            }/${queryType}/${customerId}?start=${startDate}&meas_id=${measId}${endQuery}${aggBy}${samplePeriod}&use_asset_tz=${assetTz}&browser_tz=${getBrowserTimezoneName()}`,
          };
        },
        providesTags: (result, error, { customerId, measId, start, end }) => [
          { type: 'Customer' },
          { type: 'Asset' },
          { type: 'Measure' },
          { type: 'Measure', id: measId },
          { type: 'timeseries', id: `${customerId}_${measId}_${start}_${end ?? ''}` },
        ],
        transformResponse(
          response: SingleScatterTimeSeriesData[],
        ): Record<number, SingleScatterTimeSeriesData> {
          if (response?.length === 0) {
            return [] as unknown as SingleScatterTimeSeriesData[];
          }
          const tagToTsData: Record<number, SingleScatterTimeSeriesData> = {};
          response.forEach((res) => {
            tagToTsData[res.tag] = res;
          });
          return tagToTsData;
        },
      }),
      getHeampMeasurementSeries: builder.query<
        Record<number, SingleHeatMapTimeSeriesData>,
        HeatMapSeriesParams
      >({
        query: ({ customerId, start, end, measId, agg, agg_period, groupX, groupY, assetTz }) => {
          const aggBy = agg ? `&agg=${agg}` : '';
          const samplePeriod = agg_period ? `&agg_period=${agg_period}` : '';
          const queryType = aggBy ? 'agg' : 'history';
          const groupby = `&groupby=${groupX}&groupby=${groupY}`;
          const startDate = start;
          const endDate = end;
          const endQuery = end ? `&end=${endDate}` : '';
          return {
            url: `${
              process.env.NEXT_PUBLIC_TS_API_ENDPOINT_V2
            }/${queryType}/${customerId}?start=${startDate}&meas_id=${measId}${endQuery}${aggBy}${samplePeriod}${groupby}&use_asset_tz=${assetTz}&browser_tz=${getBrowserTimezoneName()}`,
          };
        },
        providesTags: (result, error, { customerId, measId, start, end }) => [
          { type: 'Customer' },
          { type: 'Asset' },
          { type: 'Measure' },
          { type: 'Measure', id: measId },
          { type: 'timeseries', id: `${customerId}_${measId}_${start}_${end ?? ''}` },
        ],
        transformResponse(
          response: SingleHeatMapTimeSeriesData[],
        ): Record<number, SingleHeatMapTimeSeriesData> {
          if (response.length === 0) {
            return [] as unknown as SingleHeatMapTimeSeriesData[];
          }
          const tagToTsData: Record<number, SingleHeatMapTimeSeriesData> = {};
          response.forEach((res) => {
            tagToTsData[res.tag] = res;
          });

          return tagToTsData;
        },
        // transformResponse(response: SingleHeatMapTimeSeriesData[]): SingleHeatMapTimeSeriesData {
        //   if (response.length === 0) {
        //     return [] as unknown as SingleHeatMapTimeSeriesData;
        //   }
        //   return response[0];
        // },
      }),
      getHeatMapMeasurementSeries: builder.query<SingleHeatMapTimeSeriesData, HeatMapSeriesParams>({
        query: ({
          customerId,
          start,
          end,
          measId,
          agg,
          agg_period,
          groupX,
          groupY,
          timeRangeType,
        }) => {
          const aggBy = agg ? `&agg=${agg}` : '';
          const samplePeriod = agg_period ? `&agg_period=${agg_period}` : '';
          const queryType = aggBy ? 'agg' : 'history';
          const groupby = `&groupby=${groupX}&groupby=${groupY}`;
          const timeRange = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
          const use_asset_tz = !timeRange.includes(timeRangeType);
          const startDate = start;
          const endDate = end;
          const endQuery = end ? `&end=${endDate}` : '';
          return {
            url: `${
              process.env.NEXT_PUBLIC_TS_API_ENDPOINT
            }/${queryType}/${customerId}?start=${startDate}&meas_id=${measId}${endQuery}${aggBy}${samplePeriod}${groupby}&use_asset_tz=${use_asset_tz}&browser_tz=${getBrowserTimezoneName()}`,
          };
        },
        providesTags: (result, error, { customerId, measId, start, end }) => [
          { type: 'Customer' },
          { type: 'Asset' },
          { type: 'Measure' },
          { type: 'Measure', id: measId },
          { type: 'timeseries', id: `${customerId}_${measId}_${start}_${end ?? ''}` },
        ],
        transformResponse(response: SingleHeatMapTimeSeriesData[]): SingleHeatMapTimeSeriesData {
          if (response.length === 0) {
            return [] as unknown as SingleHeatMapTimeSeriesData;
          }
          return response[0];
        },
      }),
      getMultipleLastReadingsMeasurementSeries: builder.query<
        Record<number, SingleScatterTimeSeriesData>,
        {
          customerId: number;
          measId: string;
        }
      >({
        query: ({ customerId, measId }) => {
          return {
            url: `${process.env.NEXT_PUBLIC_TS_API_ENDPOINT}/lastreadings/${customerId}?meas_id=${measId}`,
          };
        },
        providesTags: (result, error, { customerId, measId }) => [
          { type: 'Customer' },
          { type: 'Measure', id: measId },
          { type: 'timeseries', id: `${customerId}_${measId} ` },
        ],
        transformResponse(
          response: SingleScatterTimeSeriesData[],
        ): Record<number, SingleScatterTimeSeriesData> {
          if (response?.length === 0) {
            return [] as unknown as SingleScatterTimeSeriesData[];
          }
          const tagToTsData: Record<number, SingleScatterTimeSeriesData> = {};
          response.forEach((res) => {
            tagToTsData[res.tag] = res;
          });
          return tagToTsData;
        },
      }),
    }),
  });

export const {
  useGetMeasurementSeriesQuery,
  useGetHeatMapMeasurementSeriesQuery,
  useGetMultiMeasurementSeriesQuery,
  useGetMultipleHistoryMeasurementSeriesQuery,
  useGetMultipleLastReadingsMeasurementSeriesQuery,
  useGetTestMeasurementSeriesQuery,
} = timeseriesApi;
