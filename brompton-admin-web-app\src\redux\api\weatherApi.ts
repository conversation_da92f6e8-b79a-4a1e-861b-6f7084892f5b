import { weatherData } from '~/hooks/useFetchWeather';
import { authApi } from './authApi';

export const weatherApi = authApi
  .enhanceEndpoints({
    addTagTypes: ['weather'],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      getWeather: builder.query<
        weatherData,
        { stationId: string; startDate: number; endDate: number }
      >({
        query: ({ stationId }) => {
          return {
            url: `/v0/weather/station/${stationId}`,
          };
        },
      }),
    }),
  });

export const { useGetWeatherQuery } = weatherApi;
