// widgetSelector.test.tsimport { selectWidgetById, selectAllWidgets, selectWidgetsByType } from '../widgetSelector';

describe('widgetSelector', () => {
  const mockState = {
    widgets: {
      byId: {
        '1': { id: '1', type: 'chart', name: 'Widget 1' },
        '2': { id: '2', type: 'table', name: 'Widget 2' },
        '3': { id: '3', type: 'chart', name: 'Widget 3' },
      },
      allIds: ['1', '2', '3'],
    },
  };

  it('should select a widget by id', () => {
    const widget = selectWidgetById(mockState, '1');
    expect(widget).toEqual({ id: '1', type: 'chart', name: 'Widget 1' });
  });

  it('should return undefined for non-existent widget id', () => {
    const widget = selectWidgetById(mockState, '999');
    expect(widget).toBeUndefined();
  });

  it('should select all widgets', () => {
    const widgets = selectAllWidgets(mockState);
    expect(widgets).toHaveLength(3);
    expect(widgets[0].id).toBe('1');
    expect(widgets[1].id).toBe('2');
    expect(widgets[2].id).toBe('3');
  });

  it('should select widgets by type', () => {
    const chartWidgets = selectWidgetsByType(mockState, 'chart');
    expect(chartWidgets).toHaveLength(2);
    expect(chartWidgets[0].type).toBe('chart');
    expect(chartWidgets[1].type).toBe('chart');
  });

  it('should return empty array if no widgets of type', () => {
    const mapWidgets = selectWidgetsByType(mockState, 'map');
    expect(mapWidgets).toEqual([]);
  });
});