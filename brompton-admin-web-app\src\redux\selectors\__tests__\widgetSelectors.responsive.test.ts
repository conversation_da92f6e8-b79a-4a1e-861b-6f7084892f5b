import { RootState } from '~/redux/store';
import {
  getDesktopMobileMode,
  getResponsiveLayouts,
  getCurrentLayoutMode,
} from '../widgetSelectors';
import { Layout } from 'react-grid-layout';

// Mock the store type to avoid circular dependencies in tests
type MockRootState = {
  dashboard: any;
};

describe('Widget Selectors - Responsive Layout', () => {
  const mockDesktopLayout: Layout[] = [
    { i: '1', x: 0, y: 0, w: 6, h: 4 },
    { i: '2', x: 6, y: 0, w: 6, h: 4 },
  ];

  const mockMobileLayout: Layout[] = [
    { i: '1', x: 0, y: 0, w: 12, h: 4 },
    { i: '2', x: 0, y: 4, w: 12, h: 4 },
  ];

  const createMockState = (overrides = {}): MockRootState => ({
    dashboard: {
      currentDashboardId: 1,
      dashboardTitle: 'Test Dashboard',
      userDetails: null,
      userToken: null,
      customer: null,
      enableZoom: false,
      userPreferences: {
        DATE_FORMAT: 'DD-MM-YYYY HH:mm:ss',
        DEFAULT_CUSTOMER: '',
        THOUSAND_SEPARATOR: 'enabled',
      },
      dashboardCrumb: [],
      mainPanel: 'chart',
      isLeftPanelOpen: true,
      isDirty: false,
      kisok: false,
      fullScreen: false,
      rightSideBar: false,
      dateFormat: 0,
      newMeasureId: 0,
      rightSideBarActiveTab: '/icons/alerts.svg',
      topPanel: {
        isVisible: true,
        timeRangeType: 6,
        refreshInterval: -1,
        samplePeriod: 2,
        assetTz: true,
      },
      tree: {
        currentSelectedNodeId: '-1',
        selectedViewMeasureId: '-1',
        selectedNodeIds: ['-1'],
        expandedNodeIds: ['-1'],
        dbMeasureIdToName: {},
      },
      chart: {
        startDate: Date.now(),
        endDate: Date.now(),
      },
      widget: {
        widgets: [],
        widgetLayout: [],
        deleteWidgets: [],
        lastWidgetId: 0,
      },
      template: {
        assetTemplate: 0,
        templateId: 0,
        templateName: '',
        assetType: 0,
        metrics: [],
        idToName: {},
        topPanel: {
          timeRangeType: 6,
          refreshInterval: -1,
          samplePeriod: 2,
          assetTz: true,
        },
        chart: {
          startDate: Date.now(),
          endDate: Date.now(),
        },
      },
      metricMeasurements: {},
      desktopMobile: 0,
      responsiveLayouts: {
        desktop: { widgetLayout: mockDesktopLayout },
        mobile: { widgetLayout: mockMobileLayout },
      },
      ...overrides,
    },
  } as MockRootState);

  describe('getDesktopMobileMode', () => {
    it('should return 0 for desktop mode', () => {
      const state = createMockState({ desktopMobile: 0 });
      const result = getDesktopMobileMode(state);
      expect(result).toBe(0);
    });

    it('should return 1 for mobile mode', () => {
      const state = createMockState({ desktopMobile: 1 });
      const result = getDesktopMobileMode(state);
      expect(result).toBe(1);
    });

    it('should return 0 as default when desktopMobile is undefined', () => {
      const state = createMockState({ desktopMobile: undefined });
      const result = getDesktopMobileMode(state);
      expect(result).toBe(0);
    });
  });

  describe('getResponsiveLayouts', () => {
    it('should return responsive layouts object', () => {
      const state = createMockState();
      const result = getResponsiveLayouts(state);
      
      expect(result).toEqual({
        desktop: { widgetLayout: mockDesktopLayout },
        mobile: { widgetLayout: mockMobileLayout },
      });
    });

    it('should return undefined when responsiveLayouts is not set', () => {
      const state = createMockState({ responsiveLayouts: undefined });
      const result = getResponsiveLayouts(state);
      expect(result).toBeUndefined();
    });

    it('should return empty layouts when responsiveLayouts is empty', () => {
      const emptyLayouts = {
        desktop: { widgetLayout: [] },
        mobile: { widgetLayout: [] },
      };
      const state = createMockState({ responsiveLayouts: emptyLayouts });
      const result = getResponsiveLayouts(state);
      expect(result).toEqual(emptyLayouts);
    });
  });

  describe('getCurrentLayoutMode', () => {
    it('should return "desktop" when desktopMobile is 0', () => {
      const state = createMockState({ desktopMobile: 0 });
      const result = getCurrentLayoutMode(state);
      expect(result).toBe('desktop');
    });

    it('should return "mobile" when desktopMobile is 1', () => {
      const state = createMockState({ desktopMobile: 1 });
      const result = getCurrentLayoutMode(state);
      expect(result).toBe('mobile');
    });

    it('should return "desktop" as default when desktopMobile is undefined', () => {
      const state = createMockState({ desktopMobile: undefined });
      const result = getCurrentLayoutMode(state);
      expect(result).toBe('desktop');
    });

    it('should return "desktop" for any value other than 1', () => {
      const state = createMockState({ desktopMobile: 2 });
      const result = getCurrentLayoutMode(state);
      expect(result).toBe('desktop');
    });
  });

  describe('Selector memoization', () => {
    it('should return the same reference when state has not changed', () => {
      const state = createMockState();
      
      const result1 = getResponsiveLayouts(state);
      const result2 = getResponsiveLayouts(state);
      
      expect(result1).toBe(result2);
    });

    it('should return different reference when responsive layouts change', () => {
      const state1 = createMockState();
      const state2 = createMockState({
        responsiveLayouts: {
          desktop: { widgetLayout: [] },
          mobile: { widgetLayout: [] },
        },
      });
      
      const result1 = getResponsiveLayouts(state1);
      const result2 = getResponsiveLayouts(state2);
      
      expect(result1).not.toBe(result2);
    });
  });
});
