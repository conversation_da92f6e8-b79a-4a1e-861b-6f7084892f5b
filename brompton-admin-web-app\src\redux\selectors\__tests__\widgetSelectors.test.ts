// npm test -- src/redux/selectors/__tests__/widgetSelectors.test.ts
import { Layout } from 'react-grid-layout';
import { RootState } from '~/redux/store';
import { DashboardState } from '~/types/dashboard';
import { Widget } from '~/types/widgets';
import {
  getGlobalStartDate,
  getGlobalEndDate,
  getWidgets,
  getWidgetsLayout,
  getWidgetLayoutById,
  getDeletedWidgets,
  getLastWidgetId,
  getDesktopMobileMode,
  getResponsiveLayouts,
  getCurrentLayoutMode,
} from '../widgetSelectors';

// Mock widget data for testing
const mockWidget: Widget = {
  id: '1',
  type: 'stats',
  settings: {} as any, // Simplified for testing
};

const mockLayout: Layout = {
  i: '1',
  x: 0,
  y: 0,
  w: 4,
  h: 3,
  minW: 2,
  minH: 2,
};

// Helper function to create mock RootState
const createMockRootState = (dashboardOverrides: Partial<DashboardState> = {}): RootState => {
  const defaultDashboardState: DashboardState = {
    currentDashboardId: 1,
    dashboardTitle: 'Test Dashboard',
    userDetails: null,
    userToken: null,
    customer: null,
    enableZoom: false,
    userPreferences: {
      DATE_FORMAT: 'DD-MM-YYYY HH:mm:ss',
      DEFAULT_CUSTOMER: '',
      THOUSAND_SEPARATOR: 'enabled',
    },
    dashboardCrumb: [],
    mainPanel: 'chart',
    isLeftPanelOpen: true,
    isDirty: false,
    kisok: false,
    fullScreen: false,
    rightSideBar: false,
    dateFormat: 0,
    newMeasureId: 0,
    rightSideBarActiveTab: '/icons/alerts.svg',
    topPanel: {
      isVisible: true,
      timeRangeType: 6,
      refreshInterval: -1,
      samplePeriod: 2,
      assetTz: true,
    },
    tree: {
      currentSelectedNodeId: '-1',
      selectedViewMeasureId: '-1',
      selectedNodeIds: ['-1'],
      expandedNodeIds: ['-1'],
      dbMeasureIdToName: {},
    },
    chart: {
      startDate: new Date('2023-01-01').getTime(),
      endDate: new Date('2023-01-02').getTime(),
    },
    widget: {
      widgets: [],
      widgetLayout: [],
      deleteWidgets: [],
      lastWidgetId: 0,
    },
    desktopMobile: 0,
    responsiveLayouts: {
      desktop: { widgetLayout: [] },
      mobile: { widgetLayout: [] },
    },
    template: {
      assetTemplate: 0,
      templateId: 0,
      templateName: '',
      assetType: 0,
      metrics: [],
      idToName: {},
      topPanel: {
        timeRangeType: 6,
        refreshInterval: -1,
        samplePeriod: 2,
        assetTz: true,
      },
      chart: {
        startDate: new Date().getTime(),
        endDate: new Date().getTime(),
      },
    },
    metricMeasurements: {},
    ...dashboardOverrides,
  };

  return {
    dashboard: defaultDashboardState,
    dashboardlist: {
      currentCustomer: null,
      currentDashboardId: 0,
      searchValue: null,
    },
    authApi: {} as any,
    solar: {} as any,
    diagram: {} as any,
    _persist: {
      version: -1,
      rehydrated: true,
    },
  } as RootState;
};

describe('Widget Selectors', () => {
  describe('Chart Selectors', () => {
    describe('getGlobalStartDate', () => {
      it('should return the global start date from chart state', () => {
        const mockState = createMockRootState({
          chart: {
            startDate: 1672531200000, // 2023-01-01
            endDate: 1672617600000,   // 2023-01-02
          },
        });

        const result = getGlobalStartDate(mockState);
        expect(result).toBe(1672531200000);
      });

      it('should handle different start date values', () => {
        const customStartDate = new Date('2024-06-15').getTime();
        const mockState = createMockRootState({
          chart: {
            startDate: customStartDate,
            endDate: new Date().getTime(),
          },
        });

        const result = getGlobalStartDate(mockState);
        expect(result).toBe(customStartDate);
      });
    });

    describe('getGlobalEndDate', () => {
      it('should return the global end date from chart state', () => {
        const mockState = createMockRootState({
          chart: {
            startDate: 1672531200000,
            endDate: 1672617600000, // 2023-01-02
          },
        });

        const result = getGlobalEndDate(mockState);
        expect(result).toBe(1672617600000);
      });

      it('should handle different end date values', () => {
        const customEndDate = new Date('2024-12-31').getTime();
        const mockState = createMockRootState({
          chart: {
            startDate: new Date().getTime(),
            endDate: customEndDate,
          },
        });

        const result = getGlobalEndDate(mockState);
        expect(result).toBe(customEndDate);
      });
    });
  });

  describe('Widget State Selectors', () => {
    describe('getWidgets', () => {
      it('should return empty array when no widgets exist', () => {
        const mockState = createMockRootState();
        const result = getWidgets(mockState);
        expect(result).toEqual([]);
      });

      it('should return widgets array when widgets exist', () => {
        const widgets = [mockWidget];
        const mockState = createMockRootState({
          widget: {
            widgets,
            widgetLayout: [],
            deleteWidgets: [],
            lastWidgetId: 1,
          },
        });

        const result = getWidgets(mockState);
        expect(result).toEqual(widgets);
        expect(result).toHaveLength(1);
        expect(result[0].id).toBe('1');
        expect(result[0].type).toBe('stats');
      });

      it('should return multiple widgets correctly', () => {
        const widget2: Widget = {
          id: '2',
          type: 'stats',
          settings: {} as any,
        };

        const widgets = [mockWidget, widget2];
        const mockState = createMockRootState({
          widget: {
            widgets,
            widgetLayout: [],
            deleteWidgets: [],
            lastWidgetId: 2,
          },
        });

        const result = getWidgets(mockState);
        expect(result).toHaveLength(2);
        expect(result[0].id).toBe('1');
        expect(result[1].id).toBe('2');
      });
    });

    describe('getWidgetsLayout', () => {
      it('should return empty array when no widget layout exists', () => {
        const mockState = createMockRootState();
        const result = getWidgetsLayout(mockState);
        expect(result).toEqual([]);
      });

      it('should return widget layout array when layouts exist', () => {
        const layouts = [mockLayout];
        const mockState = createMockRootState({
          widget: {
            widgets: [],
            widgetLayout: layouts,
            deleteWidgets: [],
            lastWidgetId: 0,
          },
        });

        const result = getWidgetsLayout(mockState);
        expect(result).toEqual(layouts);
        expect(result).toHaveLength(1);
        expect(result[0].i).toBe('1');
      });

      it('should return multiple layouts correctly', () => {
        const layout2: Layout = {
          i: '2',
          x: 4,
          y: 0,
          w: 4,
          h: 3,
          minW: 2,
          minH: 2,
        };

        const layouts = [mockLayout, layout2];
        const mockState = createMockRootState({
          widget: {
            widgets: [],
            widgetLayout: layouts,
            deleteWidgets: [],
            lastWidgetId: 0,
          },
        });

        const result = getWidgetsLayout(mockState);
        expect(result).toHaveLength(2);
        expect(result[0].i).toBe('1');
        expect(result[1].i).toBe('2');
      });
    });

    describe('getWidgetLayoutById', () => {
      it('should return undefined when widget layout not found', () => {
        const mockState = createMockRootState();
        const selector = getWidgetLayoutById('non-existent');
        const result = selector(mockState);
        expect(result).toBeUndefined();
      });

      it('should return correct layout when widget exists', () => {
        const layouts = [mockLayout];
        const mockState = createMockRootState({
          widget: {
            widgets: [],
            widgetLayout: layouts,
            deleteWidgets: [],
            lastWidgetId: 0,
          },
        });

        const selector = getWidgetLayoutById('1');
        const result = selector(mockState);
        expect(result).toEqual(mockLayout);
        expect(result?.i).toBe('1');
      });

      it('should return correct layout from multiple layouts', () => {
        const layout2: Layout = {
          i: '2',
          x: 4,
          y: 0,
          w: 6,
          h: 4,
          minW: 3,
          minH: 3,
        };

        const layouts = [mockLayout, layout2];
        const mockState = createMockRootState({
          widget: {
            widgets: [],
            widgetLayout: layouts,
            deleteWidgets: [],
            lastWidgetId: 0,
          },
        });

        const selector = getWidgetLayoutById('2');
        const result = selector(mockState);
        expect(result).toEqual(layout2);
        expect(result?.w).toBe(6);
        expect(result?.h).toBe(4);
      });
    });

    describe('getDeletedWidgets', () => {
      it('should return empty array when no deleted widgets exist', () => {
        const mockState = createMockRootState();
        const result = getDeletedWidgets(mockState);
        expect(result).toEqual([]);
      });

      it('should return deleted widgets array when deleted widgets exist', () => {
        const deletedWidgets = ['widget-1', 'widget-2'];
        const mockState = createMockRootState({
          widget: {
            widgets: [],
            widgetLayout: [],
            deleteWidgets: deletedWidgets,
            lastWidgetId: 0,
          },
        });

        const result = getDeletedWidgets(mockState);
        expect(result).toEqual(deletedWidgets);
        expect(result).toHaveLength(2);
        expect(result).toContain('widget-1');
        expect(result).toContain('widget-2');
      });
    });

    describe('getLastWidgetId', () => {
      it('should return 0 when no widgets have been created', () => {
        const mockState = createMockRootState();
        const result = getLastWidgetId(mockState);
        expect(result).toBe(0);
      });

      it('should return correct widget count as last widget ID', () => {
        const widgets = [mockWidget, { id: '2', type: 'stats' as const, settings: {} as any }];
        const mockState = createMockRootState({
          widget: {
            widgets,
            widgetLayout: [],
            deleteWidgets: [],
            lastWidgetId: 42, // This is ignored by the selector
          },
        });

        const result = getLastWidgetId(mockState);
        expect(result).toBe(2); // Returns widgets.length
      });

      it('should handle large widget arrays', () => {
        const manyWidgets = Array.from({ length: 100 }, (_, i) => ({
          id: `widget-${i}`,
          type: 'stats' as const,
          settings: {} as any,
        }));

        const mockState = createMockRootState({
          widget: {
            widgets: manyWidgets,
            widgetLayout: [],
            deleteWidgets: [],
            lastWidgetId: 999999, // This is ignored by the selector
          },
        });

        const result = getLastWidgetId(mockState);
        expect(result).toBe(100); // Returns widgets.length
      });
    });
  });

  describe('Responsive Layout Selectors', () => {
    describe('getDesktopMobileMode', () => {
      it('should return 0 (desktop) by default', () => {
        const mockState = createMockRootState();
        const result = getDesktopMobileMode(mockState);
        expect(result).toBe(0);
      });

      it('should return 1 when in mobile mode', () => {
        const mockState = createMockRootState({
          desktopMobile: 1,
        });

        const result = getDesktopMobileMode(mockState);
        expect(result).toBe(1);
      });

      it('should return 0 when explicitly set to desktop mode', () => {
        const mockState = createMockRootState({
          desktopMobile: 0,
        });

        const result = getDesktopMobileMode(mockState);
        expect(result).toBe(0);
      });

      it('should handle undefined desktopMobile gracefully', () => {
        const mockState = createMockRootState({
          desktopMobile: undefined,
        });

        const result = getDesktopMobileMode(mockState);
        expect(result).toBe(0); // Should default to desktop
      });
    });

    describe('getResponsiveLayouts', () => {
      it('should return default responsive layouts when none exist', () => {
        const mockState = createMockRootState();
        const result = getResponsiveLayouts(mockState);

        expect(result).toEqual({
          desktop: { widgetLayout: [] },
          mobile: { widgetLayout: [] },
        });
      });

      it('should return responsive layouts with desktop and mobile configurations', () => {
        const desktopLayouts = [mockLayout];
        const mobileLayout: Layout = {
          i: '1',
          x: 0,
          y: 0,
          w: 12, // Full width for mobile
          h: 4,
          minW: 1,
          minH: 2,
        };
        const mobileLayouts = [mobileLayout];

        const mockState = createMockRootState({
          responsiveLayouts: {
            desktop: { widgetLayout: desktopLayouts },
            mobile: { widgetLayout: mobileLayouts },
          },
        });

        const result = getResponsiveLayouts(mockState);
        expect(result).toBeDefined();
        expect(result!.desktop.widgetLayout).toEqual(desktopLayouts);
        expect(result!.mobile.widgetLayout).toEqual(mobileLayouts);
        expect(result!.desktop.widgetLayout[0].w).toBe(4); // Desktop width
        expect(result!.mobile.widgetLayout[0].w).toBe(12); // Mobile width
      });

      it('should handle undefined responsive layouts gracefully', () => {
        const mockState = createMockRootState({
          responsiveLayouts: undefined,
        });

        const result = getResponsiveLayouts(mockState);
        expect(result).toBeUndefined();
      });
    });

    describe('getCurrentLayoutMode', () => {
      it('should return "desktop" when in desktop mode', () => {
        const mockState = createMockRootState({
          desktopMobile: 0,
        });

        const result = getCurrentLayoutMode(mockState);
        expect(result).toBe('desktop');
      });

      it('should return "mobile" when in mobile mode', () => {
        const mockState = createMockRootState({
          desktopMobile: 1,
        });

        const result = getCurrentLayoutMode(mockState);
        expect(result).toBe('mobile');
      });

      it('should return "desktop" when desktopMobile is undefined', () => {
        const mockState = createMockRootState({
          desktopMobile: undefined,
        });

        const result = getCurrentLayoutMode(mockState);
        expect(result).toBe('desktop');
      });

      it('should handle invalid desktopMobile values', () => {
        const mockState = createMockRootState({
          desktopMobile: 999 as any,
        });

        const result = getCurrentLayoutMode(mockState);
        expect(result).toBe('mobile'); // Any non-zero value should return mobile
      });
    });
  });
});
