import { createSelector } from 'reselect';
import { RootState } from '~/redux/store';

export const selectDashboardState = (state: RootState) => state.dashboard;

export const getMainPanel = createSelector(
  [selectDashboardState],
  (dashboard) => dashboard.mainPanel,
);

export const getIsLeftPanelOpen = createSelector(
  [selectDashboardState],
  (dashboard) => dashboard.isLeftPanelOpen,
);
export const getIsRightPanelOpen = createSelector(
  [selectDashboardState],
  (dashboard) => dashboard.rightSideBar,
);
export const getRightSideBarActiveTab = createSelector(
  [selectDashboardState],
  (dashboard) => dashboard.rightSideBarActiveTab,
);
export const getIsUserLoggedIn = createSelector(
  [selectDashboardState],
  (dashboard) => dashboard.userDetails,
);

export const getUserToken = createSelector(
  [selectDashboardState],
  (dashboard) => dashboard.userToken,
);

export const getDashboardState = createSelector([selectDashboardState], (dashboard) => {
  return {
    tree: {
      ...dashboard.tree,
    },
  };
});
export const getCurrentDashboardId = createSelector(
  [selectDashboardState],
  (dashboard) => dashboard.currentDashboardId,
);
export const getCurrentDashboardIdTitle = createSelector([selectDashboardState], (dashboard) => {
  return { id: dashboard.currentDashboardId ?? -1, title: dashboard.dashboardTitle };
});
export const getCurrentDashboardTitle = createSelector(
  [selectDashboardState],
  (dashboard) => dashboard.dashboardTitle,
);
export const getIsKiosk = createSelector([selectDashboardState], (dashboard) => dashboard.kisok);
export const getIsFullScreen = createSelector(
  [selectDashboardState],
  (dashboard) => dashboard.fullScreen,
);
export const getTopPanelVisibility = createSelector(
  [selectDashboardState],
  (dashboard) => dashboard.topPanel.isVisible,
);

export const getTimeFormat = createSelector([selectDashboardState], (dashboard) => {
  return dashboard.dateFormat;
});

export const getDashboardCrumb = createSelector([selectDashboardState], (dashboard) => {
  return dashboard.dashboardCrumb;
});

export const isDashboardDirty = createSelector([selectDashboardState], (dashboard) => {
  return dashboard.isDirty;
});

export const getNewMeasureId = createSelector([selectDashboardState], (dashboard) => {
  return dashboard.newMeasureId;
});

export const getMetrics = createSelector([selectDashboardState], (dashboard) => {
  if (dashboard.template?.metrics) {
    return dashboard.template.metrics;
  }
  return [];
});

export const getMetricsIdToName = createSelector([selectDashboardState], (dashboard) => {
  return dashboard.template.idToName;
});

export const getCurrentAssetType = createSelector([selectDashboardState], (dashboard) => {
  return dashboard.template.assetType;
});

export const getCurrentAssetTemplate = createSelector([selectDashboardState], (dashboard) => {
  return dashboard.template.assetTemplate;
});
export const isDashboardTemplatePage = createSelector([selectDashboardState], (dashboard) => {
  return true;
});

export const getDashboardTemplateId = createSelector([selectDashboardState], (dashboard) => {
  return dashboard.template.templateId;
});

export const getDashboardTemplateName = createSelector([selectDashboardState], (dashboard) => {
  return dashboard.template.templateName;
});

export const getDashboardTempalteTopPanel = createSelector([selectDashboardState], (dashboard) => {
  return dashboard.template.topPanel;
});

export const getZoomEnabled = createSelector([selectDashboardState], (dashboard) => {
  return dashboard.enableZoom;
});

export const getSelectedAssetFromWidgets = createSelector([selectDashboardState], (dashboard) => {
  const assets: Set<string> = new Set();
  dashboard.widget.widgets.forEach((widget) => {
    const { id, settings, type } = widget;
    switch (type) {
      case 'kpi-bar-chart':
      case 'kpi-color-box':
      case 'kpi-percentage':
      case 'kpi-value-indicator':
      case 'kpi-sparkline':
      case 'image-stats':
      case 'stats': {
        assets.add(settings?.assetMeasure?.assetId);
        break;
      }
      case 'table':
      case 'kpi-table': {
        settings.assetMeasure?.forEach((assetMeasure) => {
          assets.add(assetMeasure.assetId);
        });
        break;
      }
      case 'map': {
        settings.markers?.forEach((marker) => {
          marker.assetMeasures.forEach((assetMeasure) => {
            assets.add(assetMeasure.assetId);
          });
        });
        break;
      }
      case 'chart': {
        const chartType = settings.chartType;
        switch (chartType) {
          case 'bar':
          case 'scatter': {
            settings.settings.assetMeasure?.forEach((assetMeasure) => {
              assets.add(assetMeasure.assetId);
            });
            break;
          }
          case 'bullet':
          case 'heatmap':
          case 'indicator': {
            assets.add(settings.settings.assetMeasure.assetId);
          }
          default:
            break;
        }
      }
      default:
        break;
    }
  });
  return Array.from(assets);
});

export const getSelectedMeasurementsFromWidgets = createSelector(
  [selectDashboardState],
  (dashboard) => {
    const measurements: Set<string> = new Set();
    dashboard.widget.widgets.forEach((widget) => {
      const { id, settings, type } = widget;
      switch (type) {
        case 'kpi-bar-chart':
        case 'kpi-color-box':
        case 'kpi-percentage':
        case 'kpi-value-indicator':
        case 'kpi-sparkline':
        case 'image-stats':
        case 'stats': {
          settings.assetMeasure?.measureId.forEach((measure) => {
            measurements.add(measure);
          });
          break;
        }
        case 'table':
        case 'kpi-table': {
          settings.assetMeasure?.forEach((assetMeasure) => {
            assetMeasure.measureId.forEach((measure) => {
              measurements.add(measure);
            });
          });
          break;
        }
        case 'map': {
          settings.markers?.forEach((marker) => {
            marker.assetMeasures.forEach((assetMeasure) => {
              measurements.add(assetMeasure.measureId);
            });
          });
          break;
        }
        case 'chart': {
          const chartType = settings.chartType;
          switch (chartType) {
            case 'bar':
            case 'scatter': {
              settings.settings.assetMeasure?.forEach((assetMeasure) => {
                assetMeasure.measureId.forEach((measure) => {
                  measurements.add(measure);
                });
              });
              break;
            }
            case 'bullet':
            case 'heatmap':
            case 'indicator': {
              settings.settings.assetMeasure.measureId.forEach((measure) => {
                measurements.add(measure);
              });
            }
            default:
              break;
          }
        }
        default:
          break;
      }
    });
    return Array.from(measurements);
  },
);
