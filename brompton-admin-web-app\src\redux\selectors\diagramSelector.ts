// src/redux/selectors/diagramSelectors.ts
import { createSelector } from 'reselect';
import { RootState } from '~/redux/store';

const selectDiagramState = (state: RootState) => state.diagram;

// Selectors for each property in the diagram slice
export const getSelectedElement = createSelector(
  [selectDiagramState],
  (diagram) => diagram.selectedElement,
);

export const getEditDialogOpen = createSelector(
  [selectDiagramState],
  (diagram) => diagram.editDialogOpen,
);

export const getEditingElementId = createSelector(
  [selectDiagramState],
  (diagram) => diagram.editingElementId,
);

export const getElementAttrs = createSelector(
  [selectDiagramState],
  (diagram) => diagram.elementAttrs,
);

export const getElementName = createSelector(
  [selectDiagramState],
  (diagram) => diagram.elementName,
);

export const getElementType = createSelector(
  [selectDiagramState],
  (diagram) => diagram.elementType,
);

export const getBase64Image = createSelector(
  [selectDiagramState],
  (diagram) => diagram.base64Image,
);

export const getOpacity = createSelector([selectDiagramState], (diagram) => diagram.opacity);

export const getImageUploaded = createSelector(
  [selectDiagramState],
  (diagram) => diagram.imageUploaded,
);

export const getIconVisible = createSelector(
  [selectDiagramState],
  (diagram) => diagram.iconVisible,
);

export const getIconPosition = createSelector(
  [selectDiagramState],
  (diagram) => diagram.iconPosition,
);

export const getHoveredLink = createSelector(
  [selectDiagramState],
  (diagram) => diagram.hoveredLink,
);

export const getImageUploadModalOpen = createSelector(
  [selectDiagramState],
  (diagram) => diagram.imageUploadModalOpen,
);

export const getImageDropPosition = createSelector(
  [selectDiagramState],
  (diagram) => diagram.imageDropPosition,
);

export const getSelectedLink = createSelector(
  [selectDiagramState],
  (diagram) => diagram.selectedLink,
);

export const getEditLinkDialogOpen = createSelector(
  [selectDiagramState],
  (diagram) => diagram.editLinkDialogOpen,
);

export const getLinkAttrs = createSelector([selectDiagramState], (diagram) => diagram.linkAttrs);

export const getCurrentGraph = createSelector([selectDiagramState], (diagram) => diagram.diagram);

export const getDiagramName = createSelector([selectDiagramState], (diagram) => diagram.name);

export const getDiagramId = createSelector([selectDiagramState], (diagram) => diagram.diagramId);

// export const getElementsVariables = (elementId: string) =>
//   createSelector([selectDiagramState], (diagram) => {
//     return diagram.elementsVariables[elementId];
//   });

export const getElementsVariables = createSelector([selectDiagramState], (diagram) => {
  return diagram.elementsVariables;
});

export const getSelectedElements = createSelector([selectDiagramState], (diagram) => {
  return diagram.selectedElements;
});

export const getZoomLevel = createSelector([selectDiagramState], (diagram) => diagram.zoomLevel);
