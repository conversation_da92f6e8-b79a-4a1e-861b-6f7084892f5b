import { dashboardSlice } from '../dashboardSlice';
import { Layout } from 'react-grid-layout';

describe('dashboardSlice - Responsive Layout Actions', () => {
  const initialState = dashboardSlice.getInitialState();

  const mockDesktopLayout: Layout[] = [
    { i: '1', x: 0, y: 0, w: 6, h: 4 },
    { i: '2', x: 6, y: 0, w: 6, h: 4 },
  ];

  const mockMobileLayout: Layout[] = [
    { i: '1', x: 0, y: 0, w: 12, h: 4 },
    { i: '2', x: 0, y: 4, w: 12, h: 4 },
  ];

  describe('setDesktopMobileMode', () => {
    it('should switch from desktop to mobile mode', () => {
      const state = {
        ...initialState,
        desktopMobile: 0,
        widget: {
          ...initialState.widget,
          widgetLayout: mockDesktopLayout,
        },
        responsiveLayouts: {
          desktop: { widgetLayout: mockDesktopLayout },
          mobile: { widgetLayout: mockMobileLayout },
        },
      };

      const action = dashboardSlice.actions.setDesktopMobileMode(1);
      const newState = dashboardSlice.reducer(state, action);

      expect(newState.desktopMobile).toBe(1);
      expect(newState.widget.widgetLayout).toEqual(mockMobileLayout);
      expect(newState.responsiveLayouts?.desktop.widgetLayout).toEqual(mockDesktopLayout);
      expect(newState.isDirty).toBe(true);
    });

    it('should switch from mobile to desktop mode', () => {
      const state = {
        ...initialState,
        desktopMobile: 1,
        widget: {
          ...initialState.widget,
          widgetLayout: mockMobileLayout,
        },
        responsiveLayouts: {
          desktop: { widgetLayout: mockDesktopLayout },
          mobile: { widgetLayout: mockMobileLayout },
        },
      };

      const action = dashboardSlice.actions.setDesktopMobileMode(0);
      const newState = dashboardSlice.reducer(state, action);

      expect(newState.desktopMobile).toBe(0);
      expect(newState.widget.widgetLayout).toEqual(mockDesktopLayout);
      expect(newState.responsiveLayouts?.mobile.widgetLayout).toEqual(mockMobileLayout);
      expect(newState.isDirty).toBe(true);
    });

    it('should save current layout before switching modes', () => {
      const currentLayout: Layout[] = [
        { i: '1', x: 2, y: 2, w: 8, h: 6 },
      ];

      const state = {
        ...initialState,
        desktopMobile: 0,
        widget: {
          ...initialState.widget,
          widgetLayout: currentLayout,
        },
        responsiveLayouts: {
          desktop: { widgetLayout: mockDesktopLayout },
          mobile: { widgetLayout: mockMobileLayout },
        },
      };

      const action = dashboardSlice.actions.setDesktopMobileMode(1);
      const newState = dashboardSlice.reducer(state, action);

      // Current layout should be saved to desktop before switching
      expect(newState.responsiveLayouts?.desktop.widgetLayout).toEqual(currentLayout);
    });
  });

  describe('updateLayout', () => {
    it('should update both widget layout and current responsive layout', () => {
      const newLayout: Layout[] = [
        { i: '1', x: 1, y: 1, w: 5, h: 3 },
      ];

      const state = {
        ...initialState,
        desktopMobile: 0,
        responsiveLayouts: {
          desktop: { widgetLayout: mockDesktopLayout },
          mobile: { widgetLayout: mockMobileLayout },
        },
      };

      const action = dashboardSlice.actions.updateLayout(newLayout);
      const newState = dashboardSlice.reducer(state, action);

      expect(newState.widget.widgetLayout).toEqual(newLayout);
      expect(newState.responsiveLayouts?.desktop.widgetLayout).toEqual(newLayout);
      expect(newState.responsiveLayouts?.mobile.widgetLayout).toEqual(mockMobileLayout); // Should remain unchanged
      expect(newState.isDirty).toBe(true);
    });

    it('should update mobile layout when in mobile mode', () => {
      const newLayout: Layout[] = [
        { i: '1', x: 0, y: 0, w: 12, h: 6 },
      ];

      const state = {
        ...initialState,
        desktopMobile: 1,
        responsiveLayouts: {
          desktop: { widgetLayout: mockDesktopLayout },
          mobile: { widgetLayout: mockMobileLayout },
        },
      };

      const action = dashboardSlice.actions.updateLayout(newLayout);
      const newState = dashboardSlice.reducer(state, action);

      expect(newState.widget.widgetLayout).toEqual(newLayout);
      expect(newState.responsiveLayouts?.mobile.widgetLayout).toEqual(newLayout);
      expect(newState.responsiveLayouts?.desktop.widgetLayout).toEqual(mockDesktopLayout); // Should remain unchanged
    });
  });

  describe('setWidgetsLayout', () => {
    it('should update both widget layout and current responsive layout', () => {
      const newLayout: Layout[] = [
        { i: '3', x: 0, y: 8, w: 12, h: 2 },
      ];

      const state = {
        ...initialState,
        desktopMobile: 0,
        responsiveLayouts: {
          desktop: { widgetLayout: mockDesktopLayout },
          mobile: { widgetLayout: mockMobileLayout },
        },
      };

      const action = dashboardSlice.actions.setWidgetsLayout(newLayout);
      const newState = dashboardSlice.reducer(state, action);

      expect(newState.widget.widgetLayout).toEqual(newLayout);
      expect(newState.responsiveLayouts?.desktop.widgetLayout).toEqual(newLayout);
    });
  });

  describe('addWidget', () => {
    it('should update responsive layout when adding a widget', () => {
      const layoutWithNewWidget: Layout[] = [
        ...mockDesktopLayout,
        { i: '3', x: 0, y: 4, w: 6, h: 4 },
      ];

      const state = {
        ...initialState,
        desktopMobile: 0,
        widget: {
          ...initialState.widget,
          widgets: [],
          lastWidgetId: 2,
        },
        responsiveLayouts: {
          desktop: { widgetLayout: mockDesktopLayout },
          mobile: { widgetLayout: mockMobileLayout },
        },
      };

      const action = dashboardSlice.actions.addWidget({
        widgetMode: 'dashboard',
        type: 'stats',
        layout: layoutWithNewWidget,
        layoutItem: { i: 'a', x: 0, y: 4, w: 2, h: 2 },
      });

      const newState = dashboardSlice.reducer(state, action);

      expect(newState.widget.widgetLayout).toEqual(layoutWithNewWidget);
      expect(newState.responsiveLayouts?.desktop.widgetLayout).toEqual(layoutWithNewWidget);
    });
  });

  describe('setNewDashboard', () => {
    it('should initialize responsive layouts', () => {
      const state = {
        ...initialState,
        desktopMobile: 1,
        widget: {
          ...initialState.widget,
          widgets: [{ id: '1', type: 'stats' as const, settings: {} }],
          widgetLayout: mockMobileLayout,
        },
        responsiveLayouts: {
          desktop: { widgetLayout: mockDesktopLayout },
          mobile: { widgetLayout: mockMobileLayout },
        },
      };

      const action = dashboardSlice.actions.setNewDashboard();
      const newState = dashboardSlice.reducer(state, action);

      expect(newState.desktopMobile).toBe(0);
      expect(newState.widget.widgets).toEqual([]);
      expect(newState.widget.widgetLayout).toEqual([]);
      expect(newState.responsiveLayouts).toEqual({
        desktop: { widgetLayout: [] },
        mobile: { widgetLayout: [] },
      });
    });
  });

  describe('Initial State', () => {
    it('should have correct initial responsive layout state', () => {
      expect(initialState.desktopMobile).toBe(0);
      expect(initialState.responsiveLayouts).toEqual({
        desktop: { widgetLayout: [] },
        mobile: { widgetLayout: [] },
      });
    });
  });
});
