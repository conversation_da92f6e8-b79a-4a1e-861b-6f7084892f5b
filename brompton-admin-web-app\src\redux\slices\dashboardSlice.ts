import { createSlice, current, PayloadAction } from '@reduxjs/toolkit';
import { Layout } from 'react-grid-layout';
import { authApi } from '~/redux/api/authApi';
import { Customer } from '~/types/customers';
import { DashboardState, dateFormats, MainPanel, TimeRangeOptions } from '~/types/dashboard';
import { UserPreferences } from '~/types/userPreferences';
import { UserDetailsResponse } from '~/types/users';
import {
  BarChartWidget,
  BulletChartWidget,
  Forecast,
  GaugeChartWidget,
  HeatmapChartWidget,
  ImageWidget,
  isImageWidgetType,
  DiagramWidget,
  KPIBarChart,
  KPIColorBox,
  KpiCurrentWidget,
  KPIPercentage,
  KPISparkline,
  KPITable,
  KPIValueIndicator,
  MapWidget,
  OverriderAssetTz,
  RealTimeChart,
  SankeyChartWidget,
  ScatterChartWidget,
  StatsWidget,
  SVGTexts,
  TableWidget,
  TitleWidget,
  Weather,
  Widget,
  WidgetCommonSettings,
  WidgetMode,
  WidgetType,
  AlertWidget,
  DashboardWidget,
  MultiPlotWidget,
} from '~/types/widgets';
import {
  defaultOverrideDateSettings,
  getPreviousDate,
  getPreviousDateRelativeToEndTime,
} from '~/utils/utils';

export const DEFAULT_TITLE = {
  value: 'Title',
  isVisible: false,
  color: '#000000',
  fontWeight: 'bolder',
  fontSize: 12,
};
export const DEFAULT_FORECAST_DATA: Forecast = {
  period: '24hr',
  showForecast: false,
  forecastColor: '#000000',
  meanColor: '#000000',
  meanName: 'Mean',
  meanStyle: 'solid',
  showMean: false,
};
export const DEFAULT_LEGEND = -0.4;
export const DEFAULT_OVERRIDER_ASSET_TZ: OverriderAssetTz = {
  overrideAssetTz: false,
  overrideAssetTzValue: false,
};
export const DEFAULT_WIDGET_COMMON_SETTINGS: WidgetCommonSettings = {
  mode: 'dashboard',
  isValid: true,
  isChildWidget: false,
  title: DEFAULT_TITLE,
  isDirty: true,
  dashboard: null,
  isRealTime: false,
  retainPeriod: 1,
  refreshInterval: 10,
  dashboardOrTemplate: 'template',
  openDashboardInNewTab: false,
  assetOrAssetType: null,
};
export const DEFAULT_SANKY_CHART_SETTINGS: SankeyChartWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  ...DEFAULT_OVERRIDER_ASSET_TZ,
  aggBy: 1,
  samplePeriod: 0,
  globalSamplePeriod: false,
  dashboard: null,
  showColor: false,
  connections: [],
  Label: [],
  ...defaultOverrideDateSettings(),
};
export const DEFAULT_BAR_CHART_SETTINGS: BarChartWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  aggBy: 1,
  samplePeriod: 2,
  dbMeasureIdToSetting: {},
  selectedTitles: [],
  dbMeasureIdToName: {},
  assetMeasure: [],
  dbMeasureIdToAnnotation: {},
  showRangeSlider: false,
  ...defaultOverrideDateSettings(),
  overrideGlobalBarColor: false,
  barColors: [],
  globalSamplePeriod: false,
  legendY: DEFAULT_LEGEND,
  showThreshold: false,
  treshdold: {
    thresholdName: '',
    thresholdValue: 0,
    thresholdColor: '#000000',
    thresholdStyle: 'solid',
  },
  min: {
    show: false,
  },
  max: {
    show: false,
  },
  avg: {
    show: false,
  },
  showStacked: {
    show: false,
  },
  dashboard: null,
  showDelta: false,
  deltaLabel: 'Delta',
  showSum: false,
  sumLabel: 'Sum',
  ...DEFAULT_OVERRIDER_ASSET_TZ,
  ...DEFAULT_FORECAST_DATA,
};
export const DEFAULT_INDICATOR_CHART_SETTINGS: GaugeChartWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  aggBy: 1,
  samplePeriod: 2,
  dbMeasureIdToSetting: {},
  selectedAssetId: '',
  selectedDbMeasureId: '',
  barColor: '#008000',
  assetMeasure: {
    assetId: '',
    measureId: [],
  },
  threshHoldColor: '#FF8800',
  threshHoldValue: 80,
  showThreshHoldValue: true,

  showIndicator1: true,
  indicator1Color: '#d3d3d3',
  indicator1Value: 50,

  showIndicator2: true,
  indicator2Value: 75,
  indicator2Color: '#808080',

  minValue: 0,
  maxValue: 100,
  margin: {
    t: 20,
    b: 20,
    l: 20,
    r: 40,
  },
  ...defaultOverrideDateSettings(),
  globalSamplePeriod: false,
  dashboard: null,
  dbMeasureIdToName: {},
  ...DEFAULT_OVERRIDER_ASSET_TZ,
};
export const DEFAULT_BULLET_CHART_SETTINGS: BulletChartWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  aggBy: 1,
  samplePeriod: 2,
  assetMeasure: {
    assetId: '',
    measureId: [],
  },
  dbMeasureIdToSetting: {},
  selectedAssetId: '',
  selectedDbMeasureId: '',
  barColor: '#008000',
  dbMeasureIdToName: {},
  threshHoldColor: '#FF8800',
  threshHoldValue: 80,
  showThreshHoldValue: true,

  showIndicator1: true,
  indicator1Color: '#d3d3d3',
  indicator1Value: 50,

  showIndicator2: true,
  indicator2Value: 75,
  indicator2Color: '#808080',

  minValue: 0,
  maxValue: 100,
  margin: {
    t: 50,
    b: 20,
    l: 20,
    r: 20,
  },
  ...defaultOverrideDateSettings(),
  dashboard: null,
  globalSamplePeriod: false,
  ...DEFAULT_OVERRIDER_ASSET_TZ,
};
export const DEFAULT_SCATTER_CHART_SETTINGS: ScatterChartWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  aggBy: 1,
  samplePeriod: 2,
  dbMeasureIdToSetting: {},
  selectedTitles: [],
  showStacked: false,
  showArea: false,
  dbMeasureIdToName: {},
  showRangeSlider: false,
  dbMeasureIdToAnnotation: {},
  assetMeasure: [],
  ...defaultOverrideDateSettings(),
  barColors: [],
  overrideGlobalBarColor: false,
  globalSamplePeriod: false,
  legendY: DEFAULT_LEGEND,
  showThreshold: false,
  treshdold: {
    thresholdName: '',
    thresholdValue: 0,
    thresholdColor: '#000000',
    thresholdStyle: 'solid',
  },
  selectedSparkMeasure: {
    measureId: '',
    assetId: '',
  },
  showSparkLine: false,
  min: {
    show: false,
  },
  max: {
    show: false,
  },
  avg: {
    show: false,
  },
  showDelta: false,
  deltaLabel: 'Delta',
  showSum: false,
  sumLabel: 'Sum',
  dashboard: null,
  ...DEFAULT_OVERRIDER_ASSET_TZ,
  ...DEFAULT_FORECAST_DATA,
};
export const DEFAULT_TABLE_SETTINGS: TableWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  fontSize: 12,
  fontWeight: 'bolder',
  aggBy: 1,
  samplePeriod: 2,
  selectedDbMeasureId: '',
  selectedTitles: [],
  dashboard: null,
  dbMeasureIdToName: {},
  assetMeasure: [],
  ...defaultOverrideDateSettings(),
  globalSamplePeriod: false,
  ...DEFAULT_OVERRIDER_ASSET_TZ,
};
export const DEFAULT_HEAT_MAP_SETTINGS: HeatmapChartWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  aggBy: 1,
  selectedAssetId: '',
  assetMeasure: {
    assetId: '',
    measureId: [],
  },
  dbMeasureIdToName: {},
  samplePeriod: 2,
  selectedDbMeasureId: '',
  showTotal: false,
  groupX: 'Hour',
  pastelColor: 'Jet',
  groupY: 'DayOfWeek',
  ...defaultOverrideDateSettings(),
  globalSamplePeriod: false,
  dashboard: null,
  ...DEFAULT_OVERRIDER_ASSET_TZ,
};
export const DEFAULT_TITLE_WIDGET: TitleWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  ...defaultOverrideDateSettings(),
  ...DEFAULT_OVERRIDER_ASSET_TZ,
  title: {
    ...DEFAULT_TITLE,
    isVisible: false,
  },
  fontSize: 50,
  fontWeight: 'bolder',
  valueMode: 'fixed',
  fixedValue: 'Title',
  fixedFontSize: 50,
  fixedFontWeight: 'bolder',
  showUnit: true,
  aggBy: 1,
  samplePeriod: 2,
  globalSamplePeriod: false,
  selectedDbMeasureId: '',
};
export const DEFAULT_MAP_WIDGET_SETTINGS: MapWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  zoomLevel: 5,
  changeMapCenter: false,
  mapCenter: {
    lat: -0.257195,
    lon: -51.581861,
  },
  markers: [],
  aggBy: 1,
  samplePeriod: 2,
  ...defaultOverrideDateSettings(),
  globalSamplePeriod: false,
  dashboard: null,
  ...DEFAULT_OVERRIDER_ASSET_TZ,
};
export const DEFAULT_KPI_VALUE_INDICATOR_SETTINGS: KPIValueIndicator = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  ...defaultOverrideDateSettings(),
  aggBy: 1,
  samplePeriod: 2,
  lineColor: '#000000',
  globalSamplePeriod: false,
  dbMeasureIdToName: {},
  prefix: {
    isVisible: false,
    value: '',
  },
  suffix: {
    isVisible: false,
    value: '',
  },
  assetMeasure: {
    assetId: '',
    measureId: [],
  },
  selectedAssetId: '',
  selectedDbMeasureId: '',
  dashboard: null,
  ...DEFAULT_OVERRIDER_ASSET_TZ,
};
export const DEFAULT_KPI_COLOR_BOX_SETTINGS: KPIColorBox = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  ...defaultOverrideDateSettings(),
  aggBy: 1,
  samplePeriod: 2,
  globalSamplePeriod: false,
  selectedAssetId: '',
  selectedDbMeasureId: '',
  dbMeasureIdToName: {},
  assetMeasure: {
    assetId: '',
    measureId: [],
  },
  prefix: {
    isVisible: false,
    value: '',
  },
  suffix: {
    isVisible: false,
    value: '',
  },
  positive: {
    font: {
      color: '#58ba54',
    },
    backgroundColor: '#ffffff',
  },
  negative: {
    font: {
      color: '#FF0000',
    },
    backgroundColor: '#ffffff',
  },
  sparkLineColor: '#5959d1',
  menuOption: 'Stats',
  dashboard: null,
  ...DEFAULT_OVERRIDER_ASSET_TZ,
  ...DEFAULT_FORECAST_DATA,
};
export const DEFAULT_KPI_SPARKLINE_SETTINGS: KPISparkline = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  value: {
    isOverRide: false,
    color: '#5959d1',
    fontWeight: 'bolder',
    fontSize: 35,
  },
  dbMeasureIdToName: {},
  prefix: {
    isVisible: false,
    value: '',
  },
  suffix: {
    isVisible: false,
    value: '',
  },
  sparkLineColor: '#5959d1',
  tooltip: 'Sparkline Tooltip',
  ...defaultOverrideDateSettings(),
  dbMeasureIdToSetting: {},
  aggBy: 1,
  samplePeriod: 2,
  globalSamplePeriod: false,
  showSparkLine: true,
  selectedAssetId: '',
  assetMeasure: {
    assetId: '',
    measureId: [],
  },
  selectedDbMeasureId: '',
  dashboard: null,
  ...DEFAULT_OVERRIDER_ASSET_TZ,
};
export const DEFAULT_KPI_PERCENTAGE_SETTINGS: KPIPercentage = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  ...defaultOverrideDateSettings(),
  selectedTitles: [],
  dbMeasureIdToSetting: {},
  selectedAssetId: '',
  assetMeasure: {
    assetId: '',
    measureId: [],
  },
  dbMeasureIdToName: {},
  selectedDbMeasureId: '',
  aggBy: 1,
  samplePeriod: 2,
  globalSamplePeriod: false,
  positiveColor: '#008000',
  negativeColor: '#FF0000',
  dashboard: null,
  ...DEFAULT_OVERRIDER_ASSET_TZ,
};
export const DEFAULT_KPI_TABLE_SETTINGS: KPITable = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  aggBy: 1,
  samplePeriod: 2,
  ...defaultOverrideDateSettings(),
  globalSamplePeriod: false,
  selectedTitles: [],
  assetMeasure: [],
  dbMeasureIdToSetting: {},
  dbMeasureIdToName: {},
  dashboard: null,
  max: {
    label: 'Max',
    show: false,
  },
  min: {
    label: 'Min',
    show: false,
  },
  mean: {
    label: 'Mean',
    show: false,
  },
  delta: {
    label: 'Detla',
    show: false,
  },
  sum: {
    label: 'Sum',
    show: false,
  },
  current: {
    label: 'Current',
    show: false,
  },
  ...DEFAULT_OVERRIDER_ASSET_TZ,
};
export const DEFAULT_ALERT_WIDGET_SETTINGS: AlertWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS, // Ensure this object is properly defined
  ...defaultOverrideDateSettings(), // Ensure this function returns an object
  ...DEFAULT_OVERRIDER_ASSET_TZ, // Ensure this object is properly defined
  isValid: false,
  assetMeasure: [],
  dbMeasureIdToName: {},
  selectedTitles: [],
  aggBy: 1,
  samplePeriod: 2,
  overrideGlobalSettings: false,
  globalSamplePeriod: false,
  assetTypes: [],
  measurementTypes: [],
  assetTypeMetrics: [
    {
      assetType: '',
      metrics: [],
    },
  ],
};

export const DEFAULT_DASHBOARD_WIDGET_SETTINGS: DashboardWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS, // Ensure this object is properly defined
  ...defaultOverrideDateSettings(), // Ensure this function returns an object
  ...DEFAULT_OVERRIDER_ASSET_TZ, // Ensure this object is properly defined
  assetMeasure: [],
  dbMeasureIdToName: {},
  selectedTitles: [],
  aggBy: 1,
  samplePeriod: 2,
  assetId: '',
  metricToMeasurementMap: {},
  overrideGlobalSettings: false,
  globalSamplePeriod: false,
  assetTypes: [],
  measurementTypes: [],
  dashboardTemplateData: null,
  assetTypeMetrics: [
    {
      assetType: '',
      metrics: [],
    },
  ],
  assetOption: {
    id: 0,
    label: '',
  },
  dashboardTemplateOption: {
    id: 0,
    label: '',
  },
};

export const DEFAULT_MULTI_PLOT_WIDGET_SETTINGS: MultiPlotWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS, // Ensure this object is properly defined
  ...defaultOverrideDateSettings(), // Ensure this function returns an object
  ...DEFAULT_OVERRIDER_ASSET_TZ, // Ensure this object is properly defined
  assetMeasure: {
    assetId: '',
    measureId: [],
  },
  dbMeasureIdToName: {},
  selectedTitles: [],
  aggBy: 1,
  samplePeriod: 2,
  overrideGlobalSettings: false,
  globalSamplePeriod: false,
  layoutType: 'grid',
  plotsPerRow: 1,
  legend: { y: -0.35 },
  subplots: [],
};

export const DEFAULT_STATS_WIDGET_SETTINGS: StatsWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  fontSize: 12,
  fontWeight: 'bolder',
  dbMeasureIdToName: {},
  aggBy: 1,
  samplePeriod: 2,
  globalSamplePeriod: false,
  assetMeasure: {
    assetId: '',
    measureId: [],
  },
  selectedAssetId: '',
  selectedDbMeasureId: '',
  showMin: true,
  showMinLable: true,
  minColor: '#000000',
  min: {
    label: 'Min',
    color: '#000000',
    fontSize: 20,
    fontWeight: 'bolder',
  },
  max: {
    label: 'Max',
    color: '#000000',
    fontSize: 20,
    fontWeight: 'bolder',
  },
  avg: {
    label: 'Avg',
    color: '#000000',
    fontSize: 20,
    fontWeight: 'bolder',
  },
  delta: {
    label: 'Delta',
    color: '#000000',
    fontSize: 20,
    fontWeight: 'bolder',
  },
  showSum: true,
  showSumLabel: true,
  sumColor: '#000000',
  sumBorder: true,
  sum: {
    label: 'Sum',
    color: '#000000',
    fontSize: 20,
    fontWeight: 'bolder',
  },
  total: {
    label: 'Total',
    color: '#000000',
    fontSize: 20,
    fontWeight: 'bolder',
  },
  showCurrent: true,
  currentColor: '#000000',
  showCurrentBorder: true,
  showCurrentLabel: true,
  current: {
    label: 'Last Value',
    fontSize: 20,
    fontWeight: 'bolder',
    color: '#000000',
  },
  minBorder: true,
  showMax: true,
  showMaxLabel: true,
  maxColor: '#000000',
  maxBorder: true,
  showAvg: true,
  showAvgLabel: true,
  avgColor: '#000000',
  avgBorder: true,
  showDelta: true,
  showDeltaLabel: true,
  deltaColor: '#000000',
  deltaBorder: true,
  showTotal: true,
  showTotalLabel: true,
  totalColor: '#000000',
  totalBorder: true,
  dashboard: null,
  ...defaultOverrideDateSettings(),
  ...DEFAULT_OVERRIDER_ASSET_TZ,
};
export const DEFAULT_KPI_CURRENT_WIDGET: KpiCurrentWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  isEditable: false,
  dbMeasureIdToName: {},
  assetMeasure: { assetId: '', measureId: [] },
  selectedAssetId: '',
  selectedDbMeasureId: '',
  image: null,
  dashboard: null,
  aggBy: 1,
  samplePeriod: 2,
  globalSamplePeriod: false,
  font: {
    color: '#000000',
    size: 12,
    weight: 'bolder',
  },
  ...defaultOverrideDateSettings(),
  ...DEFAULT_OVERRIDER_ASSET_TZ,
  samples: [],
  placement: 'Image-Center',
  imageSize: 'Medium',
};
export const DEFAULT_IMAGE_WIDGET: ImageWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  isEditable: true,
  imgDashboard: {
    dashboard: null,
    openDashboardInNewTab: false,
    dashboardOrTemplate: 'template',
    assetOrAssetType: null,
  },
  uploadedImage: null,
  allowUpload: false,
  measureIdToImageTextDetails: {},
  selectedTitles: [],
  dbMeasureIdToName: {},
  assetMeasure: [],
  labelAndUnits: {},
  svgTexts: [],
  image: null,
  font: {
    color: '#000000',
    size: 12,
    weight: 'bolder',
  },
  margin: {
    b: 5,
    l: 5,
    r: 5,
    t: 5,
  },
  dashboard: null,
  aggBy: 1,
  samplePeriod: 2,
  globalSamplePeriod: false,
  ...defaultOverrideDateSettings(),
  ...DEFAULT_OVERRIDER_ASSET_TZ,
};
export const DEFAULT_KPI_BAR_CHART_SETTINGS: KPIBarChart = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  assetMeasure: {
    assetId: '',
    measureId: [],
  },
  dbMeasureIdToName: {},
  selectedAssetId: '',
  selectedDbMeasureId: '',
  aggBy: 1,
  samplePeriod: 2,
  globalSamplePeriod: false,
  selectedSamplePeriod: 'D',
  overrideBarColor: false,
  barColor: '#000000',
  showPrevious: false,
  ...defaultOverrideDateSettings(),
  ...DEFAULT_OVERRIDER_ASSET_TZ,
};

export const DEFAULT_REAL_TIME_CHART_SETTINGS: RealTimeChart = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  fontSize: 12,
  dbMeasureIdToName: {},
  assetMeasure: {
    assetId: '',
    measureId: [],
  },
  fontWeight: 'bolder',
  aggBy: 1,
  samplePeriod: 2,
  selectedDbMeasureId: '',
  retention: 0,
  selectedTitles: [],
  dashboard: null,
  ...defaultOverrideDateSettings(),
  globalSamplePeriod: false,
  ...DEFAULT_OVERRIDER_ASSET_TZ,
};

export const DEFAULT_JOINT_JS_WIDGET: DiagramWidget = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  isEditable: false,
  jsonFile: '',
  disableZoom: false,
  zoomLevel: 1,
  selectedDbMeasureId: '',
  isIconWidget: false,
  showCurrent: true,
  currentColor: '#000000',
  showCurrentBorder: true,
  showCurrentLabel: true,
  measureVariables: [],
  selectedDiagram: null,
  elementIdVariabels: {},
  elementVariable: [],
  current: {
    fontSize: 20,
    fontWeight: 'bolder',
    color: '#000000',
  },
  aggBy: 1,
  samplePeriod: 2,
  globalSamplePeriod: false,
  ...defaultOverrideDateSettings(),
  dashboard: null,
  ...DEFAULT_OVERRIDER_ASSET_TZ,
};

export const DEFAULT_NEW_WEATHER_WIDGET: Weather = {
  ...DEFAULT_WIDGET_COMMON_SETTINGS,
  stationId: '',
  overrideGlobalSettings: false,
  timeRange: 6,
  startDate: 6,
  endDate: 6,
};
export const initialState: DashboardState = {
  currentDashboardId: -1,
  dashboardTitle: '',
  userDetails: null,
  userToken: null,
  customer: null,
  enableZoom: false,
  userPreferences: {
    DATE_FORMAT: dateFormats[0],
    DEFAULT_CUSTOMER: '',
    THOUSAND_SEPARATOR: 'enabled',
  },
  dashboardCrumb: [],
  mainPanel: 'chart',
  isLeftPanelOpen: true,
  isDirty: true,
  kisok: false,
  fullScreen: false,
  rightSideBar: false,
  dateFormat: 0,
  newMeasureId: 0,
  rightSideBarActiveTab: '/icons/alerts.svg',
  topPanel: {
    isVisible: true,
    timeRangeType: 6,
    refreshInterval: -1,
    samplePeriod: 2,
    assetTz: true,
  },
  tree: {
    currentSelectedNodeId: '-1',
    selectedViewMeasureId: '-1',
    selectedNodeIds: ['-1'],
    expandedNodeIds: ['-1'],
    dbMeasureIdToName: {},
  },
  chart: {
    startDate: new Date(new Date().getTime() - 360 * 60000).getTime(),
    endDate: new Date().getTime(),
  },
  widget: {
    widgets: [],
    widgetLayout: [],
    deleteWidgets: [],
    lastWidgetId: 5,
  },
  desktopMobile: 0, // Default to desktop
  responsiveLayouts: {
    desktop: { widgetLayout: [] },
    mobile: { widgetLayout: [] },
  },
  template: {
    assetTemplate: 0,
    templateId: 0,
    templateName: '',
    assetType: 0,
    metrics: [],
    idToName: {},
    topPanel: {
      timeRangeType: 6,
      refreshInterval: -1,
      samplePeriod: 2,
      assetTz: true,
    },
    chart: {
      startDate: new Date(new Date().getTime() - 360 * 60000).getTime(),
      endDate: new Date().getTime(),
    },
  },
  metricMeasurements: {}, // Ensure this line is present
};

export const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    setActiveCustomer: (state, { payload }: PayloadAction<Customer>) => {
      if (state.customer) {
        if (state.customer.nameId !== payload.nameId) {
          state.currentDashboardId = -1;
          state.mainPanel = 'chart';
          state.tree = {
            currentSelectedNodeId: '-1',
            selectedNodeIds: ['-1'],
            expandedNodeIds: ['-1'],
            selectedViewMeasureId: '-1',
            dbMeasureIdToName: {},
          };
          state.chart = {
            startDate: new Date(new Date().getTime() - 360 * 60000).getTime(),
            endDate: new Date().getTime(),
          };

          state.widget.widgets = [];
          state.widget.widgetLayout = [];

          state.widget.widgets.forEach((widget) => {
            if (widget.type === 'chart' && widget.settings.chartType === 'heatmap') {
              widget.settings.settings.selectedDbMeasureId = '';
            } else if (widget.type === 'stats') {
              widget.settings.selectedDbMeasureId = '';
            }
          });
        }
      }
      state.customer = payload;
    },
    setWidgetsZoom: (state, { payload }: PayloadAction<boolean>) => {
      state.enableZoom = payload;
    },
    setDateFormat: (state, { payload }: PayloadAction<number>) => {
      state.dateFormat = payload;
    },
    setIsLeftPanelOpen: (state, { payload }: PayloadAction<boolean>) => {
      state.isLeftPanelOpen = payload;
    },
    setNewMeasureId: (state, { payload }: PayloadAction<number>) => {
      state.newMeasureId = payload;
    },
    setSelectedViewMeasureId: (state, { payload }: PayloadAction<string>) => {
      state.tree.selectedViewMeasureId = payload;
    },
    setTemplate: (
      state,
      {
        payload,
      }: PayloadAction<{
        assetTemplate: number;
        assetType: number;
        metrics: number[];
        idToName: Record<string, string>;
      }>,
    ) => {
      state.template = {
        assetTemplate: payload.assetTemplate,
        assetType: payload.assetType,
        metrics: payload.metrics,
        idToName: payload.idToName,
        templateId: 0,
        templateName: '',
        topPanel: {
          timeRangeType: 6,
          refreshInterval: -1,
          samplePeriod: 2,
          assetTz: true,
        },
        chart: {
          startDate: new Date(new Date().getTime() - 360 * 60000).getTime(),
          endDate: new Date().getTime(),
        },
      };
    },
    setAssetType: (state, { payload }: PayloadAction<number>) => {
      state.template.assetType = payload;
    },
    removeTemplateSpecificTitleFromWidget: (state, { payload }: PayloadAction<string>) => {
      state.widget.widgets = state.widget.widgets.map((widget) => {
        if (widget.type === 'table' || widget.type === 'kpi-table') {
          widget.settings.selectedTitles = widget.settings.selectedTitles.filter(
            (title) => title !== payload,
          );
        }
        if (
          (widget.type === 'kpi-bar-chart' || widget.type === 'stats') &&
          widget.settings.selectedDbMeasureId === payload
        ) {
          widget.settings.selectedDbMeasureId = '';
        }
        if (widget.type === 'chart') {
          if (widget.settings.chartType === 'bar' || widget.settings.chartType === 'scatter') {
            widget.settings.settings.selectedTitles =
              widget.settings.settings.selectedTitles.filter((title) => title !== payload);
          }
          if (
            (widget.settings.chartType === 'heatmap' ||
              widget.settings.chartType === 'bullet' ||
              widget.settings.chartType === 'indicator') &&
            widget.settings.settings.selectedDbMeasureId === payload
          ) {
            widget.settings.settings.selectedDbMeasureId = '';
          }
        }
        return widget;
      });
    },
    removeWidgetTitles: (state) => {
      state.template.idToName = {};
      state.template.metrics = [];
      state.widget.widgets = state.widget.widgets.map((widget) => {
        if (widget.type === 'table') {
          widget.settings.selectedTitles = [];
        }
        if (widget.type === 'kpi-bar-chart') {
          widget.settings.selectedDbMeasureId = '';
        }
        if (widget.type === 'stats') {
          widget.settings.selectedDbMeasureId = '';
        }
        if (widget.type === 'chart') {
          if (widget.settings.chartType === 'bar' || widget.settings.chartType === 'scatter') {
            widget.settings.settings.selectedTitles = [];
          }
          if (
            widget.settings.chartType === 'heatmap' ||
            widget.settings.chartType === 'bullet' ||
            widget.settings.chartType === 'indicator'
          ) {
            widget.settings.settings.selectedDbMeasureId = '';
          }
        }
        if (widget.type === 'kpi-table') {
          widget.settings.selectedTitles = [];
        }
        if (widget.type === 'kpi-color-box') {
          widget.settings.selectedDbMeasureId = '';
        }
        if (widget.type === 'kpi-value-indicator') {
          widget.settings.selectedDbMeasureId = '';
        }
        return widget;
      });
    },
    setAssetTemplate: (state, { payload }: PayloadAction<number>) => {
      state.template.assetTemplate = payload;
    },
    createNewDashboardTemplate: (state) => {
      state.template = {
        assetTemplate: 0,
        assetType: 0,
        metrics: [],
        idToName: {},
        templateId: 0,
        templateName: '',
        topPanel: {
          timeRangeType: 6,
          refreshInterval: -1,
          samplePeriod: 2,
          assetTz: true,
        },
        chart: {
          startDate: new Date(new Date().getTime() - 360 * 60000).getTime(),
          endDate: new Date().getTime(),
        },
      };
    },
    setTemplateSamplePeriod: (state, { payload }: PayloadAction<number>) => {
      state.template.topPanel.samplePeriod = payload;
    },

    setTemplateAssetTz: (state, { payload }: PayloadAction<boolean>) => {
      state.template.topPanel.assetTz = payload;
    },
    setTemplateRefreshInterval: (state, { payload }: PayloadAction<number>) => {
      state.template.topPanel.refreshInterval = payload;
    },
    setTemplateTimeRangeType: (state, { payload }: PayloadAction<number>) => {
      state.template.topPanel.timeRangeType = payload;
      state.template.topPanel.timeRangeType = payload;

      const minutes: number = TimeRangeOptions[payload].serverValue;
      const timeRange = TimeRangeOptions[payload];
      if ((timeRange.value >= 1 && timeRange.value <= 8) || timeRange.value === 13) {
        state.topPanel.samplePeriod = 1; // 1 min
      }
      if (timeRange.value === 9 || timeRange.value === 10) {
        state.template.topPanel.samplePeriod = 2; // 5 min
      }
      if (timeRange.value === 11 || timeRange.value === 12 || timeRange.value === 14) {
        state.template.topPanel.samplePeriod = 6; //  1 hour
      }
      if (timeRange.value === 15) {
        state.template.topPanel.samplePeriod = 12; //  daily
      }
      if (timeRange.value === 16) {
        state.template.topPanel.samplePeriod = 13; //  weekly
      }
    },
    setTemplateChartStartDate: (state, { payload }: PayloadAction<Date>) => {
      state.template.chart.startDate = payload.getTime();
    },
    setTemplateChartEndDate: (state, { payload }: PayloadAction<Date>) => {
      state.template.chart.endDate = payload.getTime();
    },
    setMetrics: (state, { payload }: PayloadAction<number[]>) => {
      state.template.metrics = payload;
    },
    setMetricsIdToName: (
      state,
      {
        payload,
      }: PayloadAction<{
        metricId: string;
        metricName: string;
      }>,
    ) => {
      state.template.idToName[payload.metricId] = payload.metricName;
    },
    unsetMetricsIdToName: (
      state,
      {
        payload,
      }: PayloadAction<{
        metricId: string;
      }>,
    ) => {
      delete state.template.idToName[payload.metricId];
    },
    setTemplateName: (state, { payload }: PayloadAction<string>) => {
      state.template.templateName = payload;
    },
    setTemplateId: (state, { payload }: PayloadAction<number>) => {
      state.template.templateId = payload;
    },
    setUserPreferences: (state, { payload }: PayloadAction<UserPreferences>) => {
      const { DATE_FORMAT, DEFAULT_CUSTOMER, THOUSAND_SEPARATOR } = payload;
      state.userPreferences.DATE_FORMAT = DATE_FORMAT || initialState.userPreferences.DATE_FORMAT;
      state.userPreferences.DEFAULT_CUSTOMER =
        DEFAULT_CUSTOMER || initialState.userPreferences.DEFAULT_CUSTOMER;
      state.userPreferences.THOUSAND_SEPARATOR =
        THOUSAND_SEPARATOR || initialState.userPreferences.THOUSAND_SEPARATOR;
    },
    setDashboardCrumb: (
      state,
      {
        payload,
      }: PayloadAction<{
        dashboardId: number;
        title: string;
        templateId?: number;
        assetType?: number;
        assetId?: number;
      }>,
    ) => {
      if (state.dashboardCrumb.length === 0) {
        state.dashboardCrumb.push({
          dashboardId: state.currentDashboardId,
          parentDashboardId: null,
          templateId: payload.templateId,
          dashboardTitle: state.dashboardTitle,
          assetType: payload.assetType,
          assetId: payload.assetId,
        });
      }
      state.dashboardCrumb.push({
        dashboardId: payload.dashboardId,
        parentDashboardId: state.currentDashboardId,
        templateId: payload.templateId,
        dashboardTitle: payload.title,
        assetType: payload.assetType,
        assetId: payload.assetId,
      });
    },
    removeDashboardCrumb: (state, { payload }: PayloadAction<number>) => {
      state.dashboardCrumb = state.dashboardCrumb.slice(0, payload + 1);
    },
    resetDashboardCrumb: (state) => {
      state.dashboardCrumb = [];
    },
    setFullScreen: (state, { payload }: PayloadAction<boolean>) => {
      state.fullScreen = payload;
    },
    setRightSideBar: (state, { payload }: PayloadAction<boolean>) => {
      state.rightSideBar = payload;
    },
    setUserToken: (state, { payload }: PayloadAction<string>) => {
      state.userToken = payload;
    },
    resetUserToken: (state) => {
      state.userToken = null;
    },
    setRightSideBarActiveTab: (
      state,
      {
        payload,
      }: PayloadAction<
        | '/icons/alerts.svg'
        | '/icons/llm.svg'
        | '/icons/system-status.svg'
        | 'release-notes'
        | 'CO2e'
        | 'Reports'
      >,
    ) => {
      state.rightSideBarActiveTab = payload;
    },
    setCurrentWidgetSettings: (state, { payload }: PayloadAction<Widget>) => {
      const { settings } = payload;
      state.widget.widgets.find((widget) => {
        if (widget.id === payload.id) {
          widget.settings = settings;
        }
      });
    },
    setWidget: (
      state,
      {
        payload,
      }: PayloadAction<{
        widgets: Widget[];
        widgetLayout: Layout[];
        lastWidgetId: number;
        deleteWidgets: string[];
      }>,
    ) => {
      state.widget = payload;
    },
    setWidgetsLayout: (state, { payload }: PayloadAction<Layout[]>) => {
      state.widget.widgetLayout = payload;
      // Also update the current responsive layout
      if (state.responsiveLayouts) {
        const currentMode = state.desktopMobile === 0 ? 'desktop' : 'mobile';
        state.responsiveLayouts[currentMode].widgetLayout = payload;
      }
    },
    setResponsiveLayouts: (
      state,
      {
        payload,
      }: PayloadAction<{
        desktop: { widgetLayout: Layout[] };
        mobile: { widgetLayout: Layout[] };
      }>,
    ) => {
      state.responsiveLayouts = payload;
    },
    setDesktopMobileMode: (state, { payload }: PayloadAction<number>) => {
      // Save current layout to the current mode before switching
      if (state.responsiveLayouts) {
        const currentMode = state.desktopMobile === 0 ? 'desktop' : 'mobile';
        state.responsiveLayouts[currentMode].widgetLayout = [...state.widget.widgetLayout];
      }

      // Switch to new mode
      state.desktopMobile = payload;

      // Load layout for the new mode
      if (state.responsiveLayouts) {
        const newMode = payload === 0 ? 'desktop' : 'mobile';
        state.widget.widgetLayout = [...state.responsiveLayouts[newMode].widgetLayout];
      }

      // state.isDirty = true;
    },
    setCurrentSelectedNodeId: (state, { payload }: PayloadAction<string>) => {
      state.tree.currentSelectedNodeId = payload;
    },
    setSelectedNodeIds: (state, { payload }: PayloadAction<string[]>) => {
      state.tree.selectedNodeIds = payload;
    },
    removeSelectedNodeId: (state, { payload }: PayloadAction<string>) => {
      state.tree.selectedNodeIds = state.tree.selectedNodeIds.filter(
        (nodeId) => nodeId !== payload,
      );
    },
    setExpandedNodeIds: (state, { payload }: PayloadAction<string[]>) => {
      state.tree.expandedNodeIds = payload;
      state.isDirty = true;
    },
    setNewDashboard: (state) => {
      state.currentDashboardId = 0;
      state.dashboardTitle = '';
      state.topPanel = initialState.topPanel;
      state.tree = initialState.tree;
      state.kisok = initialState.kisok;
      state.fullScreen = initialState.fullScreen;
      state.rightSideBar = initialState.rightSideBar;
      state.widget.widgets = [];
      state.widget.widgetLayout = [];
      state.widget.lastWidgetId = 0;
      state.tree = initialState.tree;
      state.chart = initialState.chart;
      state.isLeftPanelOpen = initialState.isLeftPanelOpen;
      state.mainPanel = initialState.mainPanel;
      state.kisok = initialState.kisok;
      state.desktopMobile = 0;
      state.responsiveLayouts = {
        desktop: { widgetLayout: [] },
        mobile: { widgetLayout: [] },
      };
    },
    selectCheckbox: (
      state,
      {
        payload: { assetId, metricId, metricName },
      }: PayloadAction<{
        assetId: string;
        metricId: string;
        metricName: string;
      }>,
    ) => {
      state.tree.dbMeasureIdToName[metricId] = metricName;
      state.isDirty = true;
    },
    unSelectAllCheckbox: (state) => {
      state.tree.dbMeasureIdToName = {};
      state.isDirty = true;
    },
    unselectCheckbox: (
      state,
      {
        payload: { assetId, metricId },
      }: PayloadAction<{
        assetId: string;
        metricId: string;
      }>,
    ) => {
      delete state.tree.dbMeasureIdToName[metricId];
      state.isDirty = true;
      state.widget.widgets.forEach((widget) => {
        if (widget.type === 'chart') {
          const chartWidget = widget.settings;
          const chartType = chartWidget.chartType;

          if (chartType === 'heatmap' || chartType === 'bullet' || chartType === 'indicator') {
            if (chartWidget.settings.selectedDbMeasureId === metricId) {
              chartWidget.settings.selectedDbMeasureId = '';
            }
          } else if (chartType === 'bar' || chartType === 'scatter') {
            chartWidget.settings.selectedTitles = chartWidget.settings.selectedTitles.filter(
              (title) => title !== metricId,
            );

            if (chartWidget.settings.dbMeasureIdToSetting[metricId]) {
              delete chartWidget.settings.dbMeasureIdToSetting[metricId];
            }
            if (chartType === 'scatter') {
              chartWidget.settings.selectedSparkTitle = undefined;
            }
          } else if (chartType === 'sankey') {
            const index = chartWidget.settings.Label.find((label) => label.sourceName === metricId);
            let connection = chartWidget.settings.connections
              .map((connection) => {
                const label = chartWidget.settings.Label[Number(connection.source)];
                const destination = chartWidget.settings.Label[Number(connection.destination)];
                return {
                  ...connection,
                  source: label?.sourceName ?? '',
                  destination: destination?.sourceName ?? '',
                };
              })
              .filter((conn) => conn.source !== index?.sourceName);
            const labels = chartWidget.settings.Label.filter(
              (label) => label.sourceName !== metricId,
            );
            connection = connection.map((connection) => {
              const label = labels.findIndex((label) => label.sourceName === connection.source);
              const destination = labels.findIndex(
                (label) => label.sourceName === connection.destination,
              );
              return {
                ...connection,
                source: label.toString(),
                destination: destination.toString(),
              };
            });
            chartWidget.settings = {
              ...chartWidget.settings,
              connections: connection,
              Label: labels,
            };
          }
        } else if (
          widget.type === 'stats' ||
          widget.type === 'kpi-sparkline' ||
          widget.type === 'kpi-value-indicator' ||
          widget.type === 'kpi-color-box' ||
          widget.type === 'kpi-percentage' ||
          widget.type === 'image-stats' ||
          widget.type === 'kpi-bar-chart'
        ) {
          const statsWidget = widget.settings;

          if (statsWidget.selectedDbMeasureId === metricId) {
            statsWidget.selectedDbMeasureId = '';
          }
        } else if (
          widget.type === 'table' ||
          widget.type === 'image' ||
          widget.type === 'static' ||
          widget.type === 'solar_panel' ||
          widget.type === 'vertical' ||
          widget.type === 'voltage' ||
          widget.type === 'kpi-table'
        ) {
          const currentWidget = widget.settings;

          currentWidget.selectedTitles = currentWidget.selectedTitles.filter(
            (title) => title !== metricId,
          );
        } else if (widget.type === 'map') {
          const mapWidget = widget.settings;

          mapWidget.markers = mapWidget.markers.map((marker) => {
            marker.selectedTitles = marker.selectedTitles.filter((title) => title !== metricId);
            return marker;
          });
        }
      });
    },
    setWidgetDirty: (state, { payload }: PayloadAction<boolean>) => {
      state.widget.widgets.forEach((widget) => {
        if (widget.type === 'chart') {
          widget.settings.settings.isDirty = payload;
        } else {
          widget.settings.isDirty = payload;
        }
      });
    },
    setSpecificWidgetDirty: (
      state,
      { payload }: PayloadAction<{ widgetId: string; isDirty: boolean }>,
    ) => {
      state.widget.widgets.forEach((widget) => {
        if (widget.id === payload.widgetId) {
          if (widget.type === 'chart') {
            widget.settings.settings.isDirty = payload.isDirty;
          } else {
            widget.settings.isDirty = payload.isDirty;
          }
        }
      });
    },
    setTopPanelVisibility: (state, { payload }: PayloadAction<boolean>) => {
      state.topPanel.isVisible = payload;
    },
    setRefreshTimeInterval: (state, { payload }: PayloadAction<number>) => {
      state.topPanel.refreshInterval = payload;
    },
    setSamplePeriod: (state, { payload }: PayloadAction<number>) => {
      state.topPanel.samplePeriod = payload;
    },
    setAssetTz: (state, { payload }: PayloadAction<boolean>) => {
      state.topPanel.assetTz = payload;
      if (!payload) {
        const currentDate = new Date().getTime();
        if (state.chart.endDate > currentDate) {
          state.chart.endDate = currentDate;
        }
      }
    },
    setIsDirty: (state, { payload }: PayloadAction<boolean>) => {
      state.isDirty = payload;
    },
    setCurrentDashboardId: (state, { payload }: PayloadAction<number>) => {
      state.currentDashboardId = payload;
      state.mainPanel = 'chart';
    },
    setCurrentDashboardTitle: (state, { payload }: PayloadAction<string>) => {
      state.dashboardTitle = payload;
    },
    setTimeRange: (
      state,
      { payload }: PayloadAction<{ timeRangeType: number; startDate: number; endDate: number }>,
    ) => {
      state.topPanel.timeRangeType = payload.timeRangeType;
      state.chart.startDate = payload.startDate;
      state.chart.endDate = payload.endDate;
    },
    setTimeRangeType: (state, { payload }: PayloadAction<number>) => {
      state.topPanel.timeRangeType = payload;

      const minutes: number = TimeRangeOptions[payload].serverValue;
      const timeRange = TimeRangeOptions[payload];
      if ((timeRange.value >= 1 && timeRange.value <= 8) || timeRange.value === 13) {
        state.topPanel.samplePeriod = 1; // 1 min
      }
      if (timeRange.value === 9 || timeRange.value === 10) {
        state.topPanel.samplePeriod = 2; // 5 min
      }
      if (timeRange.value === 11 || timeRange.value === 12 || timeRange.value === 14) {
        state.topPanel.samplePeriod = 6; //  1 hour
      }
      if (timeRange.value === 15) {
        state.topPanel.samplePeriod = 12; //  daily
      }
      if (timeRange.value === 16) {
        state.topPanel.samplePeriod = 13; //  weekly
      }
      if (payload !== 0) {
        state.chart.startDate = getPreviousDate(minutes);
        state.chart.endDate = new Date().getTime();
      } else {
        state.topPanel.refreshInterval = -1;
      }
      if (state.customer && state.customer.nameId === 'premier_food') {
        let statsSamplePeriod: number;
        let heatMapSamplePeriod: number;
        if (payload === 1 || payload === 2 || payload === 3 || payload === 4 || payload === 5) {
          statsSamplePeriod = 1;
          heatMapSamplePeriod = 2;
        } else if (payload === 6 || payload === 7 || payload === 8 || payload === 9) {
          statsSamplePeriod = 2;
          heatMapSamplePeriod = 6;
        } else if (payload === 10) {
          statsSamplePeriod = 12;
          heatMapSamplePeriod = 6;
        } else if (payload === 11) {
          statsSamplePeriod = 12;
          heatMapSamplePeriod = 6;
        } else if (payload === 12) {
          statsSamplePeriod = 13;
          heatMapSamplePeriod = 6;
        } else if (payload === 13) {
          statsSamplePeriod = 6;
          heatMapSamplePeriod = 6;
        } else if (payload === 14) {
          statsSamplePeriod = 12;
          heatMapSamplePeriod = 6;
        } else if (payload === 15) {
          statsSamplePeriod = 13;
          heatMapSamplePeriod = 12;
        }

        state.widget.widgets.forEach((widget) => {
          if (widget.type === 'stats') {
            widget.settings.samplePeriod = statsSamplePeriod;
          } else if (widget.type === 'chart') {
            if (widget.settings.chartType === 'heatmap') {
              widget.settings.settings.samplePeriod = heatMapSamplePeriod;
            } else {
              widget.settings.settings.samplePeriod = statsSamplePeriod;
            }
          }
        });
      }
    },
    seWidgetSpecificRefreshInterval: (state, { payload }: PayloadAction<{ widgetId: string }>) => {
      const specifiedWidget = state.widget.widgets.find((widget) => widget.id === payload.widgetId);
      if (specifiedWidget) {
        const isRealTime =
          specifiedWidget.type === 'chart'
            ? specifiedWidget.settings.settings.isRealTime
            : specifiedWidget.settings.isRealTime;
        if (isRealTime) {
        }
      }
    },
    refreshTimeRange: (state) => {
      const timeRange = state.topPanel.timeRangeType;
      const minutes = TimeRangeOptions[timeRange].serverValue;
      const end = new Date().getTime();
      state.chart.startDate = getPreviousDate(minutes);
      state.chart.endDate = end;
      state.widget.widgets.forEach((widget) => {
        if (widget.type === 'chart' && widget.settings.settings.overrideGlobalSettings) {
          widget.settings.settings.timeRange;
          const minutes = TimeRangeOptions[widget.settings.settings.timeRange].serverValue;
          const start = getPreviousDate(minutes);
          widget.settings.settings.startDate = start;
          widget.settings.settings.endDate = end;
        }
        if (widget.type === 'stats' && widget.settings.overrideGlobalSettings) {
          const minutes = TimeRangeOptions[widget.settings.timeRange].serverValue;
          const start = getPreviousDate(minutes);
          widget.settings.startDate = start;
          widget.settings.endDate = end;
        }
        if (widget.type === 'table' && widget.settings.overrideGlobalSettings) {
          const minutes = TimeRangeOptions[widget.settings.timeRange].serverValue;
          const start = getPreviousDate(minutes);
          widget.settings.startDate = start;
          widget.settings.endDate = end;
        }
        if (widget.type === 'map' && widget.settings.overrideGlobalSettings) {
          const minutes = TimeRangeOptions[widget.settings.timeRange].serverValue;
          const start = getPreviousDate(minutes);
          widget.settings.startDate = start;
          widget.settings.endDate = end;
        }
        if (widget.type === 'table' && widget.settings.overrideGlobalSettings) {
          const minutes = TimeRangeOptions[widget.settings.timeRange].serverValue;
          const start = getPreviousDate(minutes);
          widget.settings.startDate = start;
          widget.settings.endDate = end;
        }
        if (widget.type === 'kpi-color-box' && widget.settings.overrideGlobalSettings) {
          const minutes = TimeRangeOptions[widget.settings.timeRange].serverValue;
          const start = getPreviousDate(minutes);
          widget.settings.startDate = start;
          widget.settings.endDate = end;
        }
        if (widget.type === 'kpi-percentage' && widget.settings.overrideGlobalSettings) {
          const minutes = TimeRangeOptions[widget.settings.timeRange].serverValue;
          const start = getPreviousDate(minutes);
          widget.settings.startDate = start;
          widget.settings.endDate = end;
        }
        if (widget.type === 'kpi-sparkline' && widget.settings.overrideGlobalSettings) {
          const minutes = TimeRangeOptions[widget.settings.timeRange].serverValue;
          const start = getPreviousDate(minutes);
          widget.settings.startDate = start;
          widget.settings.endDate = end;
        }
        if (widget.type === 'kpi-table' && widget.settings.overrideGlobalSettings) {
          const minutes = TimeRangeOptions[widget.settings.timeRange].serverValue;
          const start = getPreviousDate(minutes);
          widget.settings.startDate = start;
          widget.settings.endDate = end;
        }
        if (widget.type === 'kpi-value-indicator' && widget.settings.overrideGlobalSettings) {
          const minutes = TimeRangeOptions[widget.settings.timeRange].serverValue;
          const start = getPreviousDate(minutes);
          widget.settings.startDate = start;
          widget.settings.endDate = end;
        }
        if (widget.type === 'Weather' && widget.settings.overrideGlobalSettings) {
          const minutes = TimeRangeOptions[widget.settings.timeRange].serverValue;
          const start = getPreviousDate(minutes);
          widget.settings.startDate = start;
          widget.settings.endDate = end;
        }
      });
    },
    updateLayout: (state, { payload }: PayloadAction<Layout[]>) => {
      state.widget.widgetLayout = payload;
      // Also update the current responsive layout
      if (state.responsiveLayouts) {
        const currentMode = state.desktopMobile === 0 ? 'desktop' : 'mobile';
        state.responsiveLayouts[currentMode].widgetLayout = payload;
      }
      state.isDirty = true;
    },
    setStaticLayout: (state, { payload }: PayloadAction<string>) => {
      state.widget.widgetLayout.find((widget) => {
        if (widget.i === payload) {
          widget.static = !current(widget).static;
        }
      });
    },
    cloneWidget: (state, { payload }: PayloadAction<string>) => {
      const widget = state.widget.widgets.find((widget) => widget.id === payload);
      const layout = state.widget.widgetLayout.find((widgetLayout) => widgetLayout.i === payload);
      if (widget && layout) {
        const widgets = [...state.widget.widgets].sort((a, b) => Number(a.id) - Number(b.id));
        const actualLastWidgetId = widgets.length > 0 ? Number(widgets.at(-1)?.id) : 0;
        if (widget.type === 'chart') {
          widget.settings.settings.isDirty = true;
        } else {
          widget.settings.isDirty = true;
        }
        const newWidget = { ...widget, id: (actualLastWidgetId + 1).toString() };
        state.widget.widgets.push(newWidget);
        state.widget.widgetLayout.push({ ...layout, i: (actualLastWidgetId + 1).toString() });
      }
    },
    addWidget: (
      state,
      {
        payload,
      }: PayloadAction<{
        widgetMode: WidgetMode;
        type: WidgetType;
        layout: Layout[];
        layoutItem: Layout;
      }>,
    ) => {
      const { type, layout, layoutItem } = payload;
      const widgets = [...state.widget.widgets].sort((a, b) => Number(a.id) - Number(b.id));
      const actualLastWidgetId = widgets.length > 0 ? Number(widgets.at(-1)?.id) : 0;
      const lastWidgetId =
        state.widget.lastWidgetId !== actualLastWidgetId
          ? actualLastWidgetId + 1
          : state.widget.lastWidgetId + 1;
      let widgetHeight = 2;
      let widgetWidth = 6;
      let chartType = 'bar';
      let widgetType = payload.type;
      if (type.startsWith('chart')) {
        chartType = type.split('-')[1]?.toLocaleLowerCase();
        widgetType = 'chart';
      }
      switch (widgetType) {
        case 'stats':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'stats',
            settings: {
              ...DEFAULT_STATS_WIDGET_SETTINGS,
              mode: payload.widgetMode,
              selectedDbMeasureId: '',
            },
          });
          widgetHeight = 2;
          widgetWidth = 6;
          break;
        case 'chart':
          if (chartType === 'bar') {
            state.widget.widgets.push({
              id: lastWidgetId.toString(),
              type: 'chart',
              settings: {
                chartType: 'bar',
                settings: {
                  ...DEFAULT_BAR_CHART_SETTINGS,
                  mode: payload.widgetMode,
                  selectedTitles: [],
                  barColors: [],
                },
              },
            });
          } else if (chartType === 'scatter') {
            state.widget.widgets.push({
              id: lastWidgetId.toString(),
              type: 'chart',
              settings: {
                chartType: 'scatter',
                settings: {
                  ...DEFAULT_SCATTER_CHART_SETTINGS,
                  mode: payload.widgetMode,
                  selectedTitles: [],
                },
              },
            });
          } else if (chartType === 'heatmap') {
            state.widget.widgets.push({
              id: lastWidgetId.toString(),
              type: 'chart',
              settings: {
                chartType: 'heatmap',
                settings: {
                  ...DEFAULT_HEAT_MAP_SETTINGS,
                  mode: payload.widgetMode,
                  selectedDbMeasureId: '',
                },
              },
            });
          } else if (chartType === 'indicator') {
            state.widget.widgets.push({
              id: lastWidgetId.toString(),
              type: 'chart',
              settings: {
                chartType: 'indicator',
                settings: {
                  ...DEFAULT_INDICATOR_CHART_SETTINGS,
                  mode: payload.widgetMode,
                  selectedDbMeasureId: '',
                },
              },
            });
          } else if (chartType === 'sankey') {
            state.widget.widgets.push({
              id: lastWidgetId.toString(),
              type: 'chart',
              settings: {
                chartType: 'sankey',
                settings: {
                  ...DEFAULT_SANKY_CHART_SETTINGS,
                  connections: [],
                  mode: payload.widgetMode,
                  Label: [],
                },
              },
            });
          } else if (chartType === 'bullet') {
            state.widget.widgets.push({
              id: lastWidgetId.toString(),
              type: 'chart',
              settings: {
                chartType: 'bullet',
                settings: {
                  ...DEFAULT_BULLET_CHART_SETTINGS,
                  selectedDbMeasureId: '',
                  mode: payload.widgetMode,
                },
              },
            });
          } else if (chartType === 'gauge') {
            state.widget.widgets.push({
              id: lastWidgetId.toString(),
              type: 'chart',
              settings: {
                chartType: 'indicator',
                settings: {
                  ...DEFAULT_INDICATOR_CHART_SETTINGS,
                  selectedDbMeasureId: '',
                  mode: payload.widgetMode,
                },
              },
            });
          }
          widgetHeight = 10;
          widgetWidth = 6;
          break;
        case 'alert-widget':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'alert-widget',
            settings: {
              ...DEFAULT_ALERT_WIDGET_SETTINGS,
              mode: payload.widgetMode,
            },
          });
          widgetHeight = 13;
          widgetWidth = 6;
          break;
        case 'dashboard-widget':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'dashboard-widget',
            settings: {
              ...DEFAULT_DASHBOARD_WIDGET_SETTINGS,
              mode: payload.widgetMode,
            },
          });
          widgetHeight = 10;
          widgetWidth = 13;
          break;
        case 'multi-plot':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'multi-plot',
            settings: {
              ...DEFAULT_MULTI_PLOT_WIDGET_SETTINGS,
              mode: payload.widgetMode,
            },
          });
          widgetHeight = 10;
          widgetWidth = 13;
          break;
        case 'table':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'table',
            settings: { ...DEFAULT_TABLE_SETTINGS, mode: payload.widgetMode },
          });
          widgetHeight = 13;
          widgetWidth = 6;
          break;
        case 'title':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'title',
            settings: { ...DEFAULT_TITLE_WIDGET, mode: payload.widgetMode },
          });
          widgetHeight = 2;
          widgetWidth = 5;
          break;
        case 'image':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'image',
            settings: { ...DEFAULT_IMAGE_WIDGET, mode: payload.widgetMode },
          });
          widgetWidth = 8;
          widgetHeight = 7;
          break;
        case 'image-stats':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'image-stats',
            settings: { ...DEFAULT_KPI_CURRENT_WIDGET, mode: payload.widgetMode },
          });
          widgetWidth = 4;
          widgetHeight = 8;
          break;
        case 'map':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'map',
            settings: { ...DEFAULT_MAP_WIDGET_SETTINGS, mode: payload.widgetMode },
          });
          widgetWidth = 13;
          widgetHeight = 8;
          break;
        case 'kpi-sparkline':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'kpi-sparkline',
            settings: { ...DEFAULT_KPI_SPARKLINE_SETTINGS, mode: payload.widgetMode },
          });
          widgetWidth = 5;
          widgetHeight = 7;
          break;
        case 'kpi-value-indicator':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'kpi-value-indicator',
            settings: { ...DEFAULT_KPI_VALUE_INDICATOR_SETTINGS, mode: payload.widgetMode },
          });
          widgetWidth = 4;
          widgetHeight = 6;
          break;
        case 'kpi-color-box':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'kpi-color-box',
            settings: { ...DEFAULT_KPI_COLOR_BOX_SETTINGS, mode: payload.widgetMode },
          });
          widgetWidth = 4;
          widgetHeight = 8;
          break;
        case 'kpi-percentage':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'kpi-percentage',
            settings: { ...DEFAULT_KPI_PERCENTAGE_SETTINGS, mode: payload.widgetMode },
          });
          widgetWidth = 7;
          widgetHeight = 7;
          break;
        case 'kpi-table':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'kpi-table',
            settings: { ...DEFAULT_KPI_TABLE_SETTINGS, mode: payload.widgetMode },
          });
          widgetWidth = 10;
          widgetHeight = 7;
          break;
        case 'Weather':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'Weather',
            settings: { ...DEFAULT_NEW_WEATHER_WIDGET, mode: payload.widgetMode },
          });
          widgetWidth = 6;
          widgetHeight = 5;
          break;
        case 'kpi-bar-chart':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'kpi-bar-chart',
            settings: { ...DEFAULT_KPI_BAR_CHART_SETTINGS, mode: payload.widgetMode },
          });
          widgetWidth = 6;
          widgetHeight = 10;
          break;
        case 'real-time':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'real-time',
            settings: { ...DEFAULT_REAL_TIME_CHART_SETTINGS, mode: payload.widgetMode },
          });
          widgetWidth = 6;
          widgetHeight = 10;
          break;
        case 'Diagram':
          state.widget.widgets.push({
            id: lastWidgetId.toString(),
            type: 'Diagram',
            settings: { ...DEFAULT_JOINT_JS_WIDGET, mode: payload.widgetMode },
          });
          widgetWidth = 6;
          widgetHeight = 10;
          break;
      }

      layout.forEach((widgetLayout) => {
        if (widgetLayout.i === layoutItem.i) {
          widgetLayout.h = widgetHeight;
          widgetLayout.w = widgetWidth;
          widgetLayout.i = lastWidgetId.toString();
        }
      });
      state.widget.widgetLayout = layout;

      // Also update the current responsive layout
      if (state.responsiveLayouts) {
        const currentMode = state.desktopMobile === 0 ? 'desktop' : 'mobile';
        state.responsiveLayouts[currentMode].widgetLayout = layout;
      }
    },
    setCurrentSVGText: (
      state,
      { payload }: PayloadAction<{ widgetId: string; svgText: SVGTexts[] }>,
    ) => {
      state.widget.widgets.forEach((widget) => {
        if (widget.id === payload.widgetId && 'svgTexts' in widget.settings) {
          widget.settings.svgTexts = payload.svgText;
        }
      });
    },
    setCurrentSVGTextNew: (
      state,
      {
        payload,
      }: PayloadAction<{
        widgetId: string;
        measureId: string;
        positionX: number;
        positionY: number;
      }>,
    ) => {
      state.widget.widgets
        .filter((widget) => widget.id === payload.widgetId)
        .forEach((widget) => {
          if (isImageWidgetType(widget)) {
            const measureIdToImageTextDetail =
              widget.settings.measureIdToImageTextDetails[payload.measureId];
            measureIdToImageTextDetail.positionX = payload.positionX;
            measureIdToImageTextDetail.positionY = payload.positionY;
            widget.settings.svgTexts.forEach(
              (svgText: { id: string; position: { x: number; y: number } }) => {
                if (svgText.id === payload.measureId) {
                  svgText.position.x = payload.positionX;
                  svgText.position.y = payload.positionY;
                }
              },
            );
          }
        });
    },
    setChartStartDate: (state, { payload }: PayloadAction<Date>) => {
      state.chart.startDate = payload.getTime();
      const globalendDate = state.chart.startDate;
      state.widget.widgets.forEach((widget) => {
        if (widget.type === 'chart') {
          if (
            widget.settings.settings?.overrideGlobalSettings &&
            widget.settings.settings.isRelativeToGlboalEndTime &&
            widget.settings.settings.timeRange >= 1 &&
            widget.settings.settings.timeRange <= 12
          ) {
            const minutes: number =
              TimeRangeOptions[widget.settings.settings.timeRange].serverValue;
            widget.settings.settings.startDate = getPreviousDateRelativeToEndTime(
              minutes,
              globalendDate,
            );
            widget.settings.settings.endDate = globalendDate;
          }
        } else if (
          'overrideGlobalSettings' in widget.settings &&
          'isRelativeToGlboalEndTime' in widget.settings &&
          widget.settings.overrideGlobalSettings &&
          widget.settings.isRelativeToGlboalEndTime &&
          widget.settings.timeRange >= 1 &&
          widget.settings.timeRange <= 12
        ) {
          const minutes: number = TimeRangeOptions[widget.settings.timeRange].serverValue;
          widget.settings.startDate = getPreviousDateRelativeToEndTime(minutes, globalendDate);
          widget.settings.endDate = globalendDate;
        }
      });
      state.isDirty = true;
    },
    deleteSpecificWidget: (state, { payload }: PayloadAction<string>) => {
      state.widget.widgets = state.widget.widgets.filter((widget) => widget.id !== payload);
      state.widget.widgetLayout = state.widget.widgetLayout.filter(
        (widgetLayout) => widgetLayout.i !== payload,
      );
      state.widget.deleteWidgets.push(payload);
    },
    setChartEndDate: (state, { payload }: PayloadAction<Date>) => {
      state.isDirty = true;
      const globalendDate = payload.getTime();
      state.chart.endDate = globalendDate;
      state.widget.widgets.forEach((widget) => {
        if (widget.type === 'chart') {
          if (
            widget.settings.settings?.overrideGlobalSettings &&
            widget.settings.settings.isRelativeToGlboalEndTime &&
            widget.settings.settings.timeRange >= 1 &&
            widget.settings.settings.timeRange <= 12
          ) {
            const minutes: number =
              TimeRangeOptions[widget.settings.settings.timeRange].serverValue;
            widget.settings.settings.startDate = getPreviousDateRelativeToEndTime(
              minutes,
              globalendDate,
            );
            widget.settings.settings.endDate = globalendDate;
          }
        } else if (
          'overrideGlobalSettings' in widget.settings &&
          'isRelativeToGlboalEndTime' in widget.settings &&
          widget.settings.overrideGlobalSettings &&
          widget.settings.isRelativeToGlboalEndTime &&
          widget.settings.timeRange >= 1 &&
          widget.settings.timeRange <= 12
        ) {
          const minutes: number = TimeRangeOptions[widget.settings.timeRange].serverValue;
          widget.settings.startDate = getPreviousDateRelativeToEndTime(minutes, globalendDate);
          widget.settings.endDate = globalendDate;
        }
      });
    },
    selectMainPanel: (state, { payload }: PayloadAction<MainPanel>) => {
      state.mainPanel = payload;
    },
    setUserDetails: (state, { payload }: PayloadAction<UserDetailsResponse>) => {
      state.userDetails = payload;
    },
    logout: (state) => {
      authApi.util.resetApiState();
      state.userDetails = null;
      state.userToken = null;
      state.customer = null;
      state.mainPanel = 'chart';
      state.topPanel = initialState.topPanel;
      state.tree = initialState.tree;
      state.userPreferences = initialState.userPreferences;
      state.currentDashboardId = -1;
      state.dashboardTitle = '';
      state.widget.widgets = initialState.widget.widgets;
      state.widget.widgetLayout = initialState.widget.widgetLayout;
      state.dateFormat = initialState.dateFormat;
      localStorage.clear();
    },
    setMetricMeasurements: (
      state,
      { payload }: PayloadAction<Record<string, { metricName: string; measurement: string }>>,
    ) => {
      state.metricMeasurements = payload;
    },
    updateMeasurement: (
      state,
      {
        payload,
      }: PayloadAction<{ metricId: string; measurementId: number; measurementTag: string }>,
    ) => {
      if (state.metricMeasurements[payload.metricId]) {
        state.metricMeasurements[payload.metricId].measurement = payload.measurementTag;
      }
    },
  },
});
