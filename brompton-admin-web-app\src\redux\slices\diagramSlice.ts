import { dia } from '@joint/core';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface DiagramState {
  diagramId: number;
  name: string;
  zoomLevel: number;
  customElementData: Record<
    string,
    {
      textLabels: string[];
      relatedVariables: string[];
    }
  >;
  selectedElements: dia.Element[];
  diagram: dia.Graph | null;
  customElements: dia.Cell[];
  selectedElement: dia.Element | null;
  editDialogOpen: boolean;
  editingElementId: string | number | null;
  elementAttrs: {
    name: string;
    color: string;
    borderColor: string;
    borderWidth: string;
    borderStyle: 'solid' | 'dotted' | 'dashed';
  };
  elementsVariables: Record<
    string,
    {
      label: string;
      variable: string;
    }[]
  >;
  elementName: string;
  elementType: string;
  base64Image: string | null;
  opacity: number;
  imageUploaded: boolean;
  iconVisible: boolean;
  iconPosition: { top: number; left: number };
  hoveredLink: dia.Link | null;
  imageUploadModalOpen: boolean;
  imageDropPosition: { x: number; y: number } | null;
  selectedLink: dia.Link | null;
  editLinkDialogOpen: boolean;
  linkAttrs: {
    width: number;
    style: string;
    endStyle: string;
    color: string;
    direction: string;
    animation: {
      enabled: boolean;
      speed: number;
    };
    outlineStrokeWidth: number;
    outlineStrokeColor: string;
    liquidStrokeWidth: number;
    liquidStrokeColor: string;
    liquidDashArray: string;
  };
}

const initialState: DiagramState = {
  diagramId: 0,
  name: '',
  zoomLevel: 1,
  customElementData: {},
  selectedElements: [],
  diagram: null,
  customElements: [],
  selectedElement: null,
  editDialogOpen: false,
  editingElementId: null,
  elementsVariables: {},
  elementAttrs: {
    color: '',
    borderColor: '#000000',
    borderWidth: '1',
    name: '',
    borderStyle: 'solid',
  },
  elementName: '',
  elementType: '',
  base64Image: null,
  opacity: 1,
  imageUploaded: false,
  iconVisible: false,
  iconPosition: { top: 0, left: 0 },
  hoveredLink: null,
  imageUploadModalOpen: false,
  imageDropPosition: null,
  selectedLink: null,
  editLinkDialogOpen: false,
  linkAttrs: {
    width: 3,
    style: 'solid',
    endStyle: 'none',
    color: '#000000',
    direction: 'forward',
    animation: {
      enabled: false,
      speed: 0,
    },
    outlineStrokeWidth: 0,
    outlineStrokeColor: '',
    liquidStrokeWidth: 0,
    liquidStrokeColor: '',
    liquidDashArray: '',
  },
};

export const diagramSlice = createSlice({
  name: 'diagram',
  initialState,
  reducers: {
    setCustomElementData: (
      state,
      action: PayloadAction<{
        groupId: string;
        textLabels: string[];
        relatedVariables: string[];
      }>,
    ) => {
      const { groupId, textLabels, relatedVariables } = action.payload;

      state.customElementData[groupId] = {
        textLabels,
        relatedVariables,
      };
    },
    setZoomLevel: (state, action: PayloadAction<number>) => {
      state.zoomLevel = action.payload;
    },
    updateCustomElementLabel: (
      state,
      action: PayloadAction<{ groupId: string; index: number; newLabel: string }>,
    ) => {
      const { groupId, index, newLabel } = action.payload;

      if (state.customElementData[groupId]) {
        // Update the specific textLabel at the given index
        state.customElementData[groupId].textLabels[index] = newLabel;
      }
    },
    addSelectedElement(state, action: PayloadAction<{ element: dia.Element }>) {
      const ifExists = state.selectedElements.find(
        (element) => element.id === action.payload.element.id,
      );
      if (!ifExists) {
        state.selectedElements.push(action.payload.element);
      }
    },
    removeAllSlectedElemts: (state) => {
      state.selectedElements = [];
    },
    setCustomElements(state, action) {
      state.customElements = action.payload;
    },
    addCustomElement(state, action) {
      state.customElements.push(action.payload);
    },
    setDiagramName: (state, action) => {
      state.name = action.payload;
    },
    resetDiagramName: (state) => {
      state.name = '';
    },
    setDiagramId: (state, action: PayloadAction<number>) => {
      state.diagramId = action.payload;
    },
    addElementVariable: (
      state,
      action: PayloadAction<{ key: string; value: { label: string; variable: string } }>,
    ) => {
      if (!state.elementsVariables[action.payload.key]) {
        state.elementsVariables[action.payload.key] = [];
      }
      state.elementsVariables[action.payload.key].push(action.payload.value);
    },
    setElementVairiables: (
      state,
      action: PayloadAction<
        Record<
          string,
          {
            label: string;
            variable: string;
          }[]
        >
      >,
    ) => {
      state.elementsVariables = action.payload;
    },
    setElementsVariables: (
      state,
      action: PayloadAction<{
        key: string;
        value: {
          label: string;
          variable: string;
        }[];
      }>,
    ) => {
      state.elementsVariables[action.payload.key] = action.payload.value;
    },
    removeElementsVariables: (
      state,
      action: PayloadAction<{
        key: string;
        index: number;
      }>,
    ) => {
      const { key, index } = action.payload;
      if (state.elementsVariables[key]) {
        // Check if the index is within bounds
        if (index >= 0 && index < state.elementsVariables[key].length) {
          // Remove the variable at the specified index
          state.elementsVariables[key].splice(index, 1);

          // If the array becomes empty, remove the key from the object
          if (state.elementsVariables[key].length === 0) {
            delete state.elementsVariables[key];
          }
        }
      }
    },
    resetDiagramId: (state) => {
      state.diagramId = 0;
    },
    setDiagram: (state, action: PayloadAction<dia.Graph>) => {
      state.diagram = action.payload;
    },
    resetDiagram: (state) => {
      state.name = '';
      state.diagramId = 0;
      state.diagram?.clear();
      state.customElements = [];
    },
    setSelectedElement(state, action: PayloadAction<dia.Element | null>) {
      state.selectedElement = action.payload;
    },
    setEditDialogOpen(state, action: PayloadAction<boolean>) {
      state.editDialogOpen = action.payload;
    },
    setEditingElementId(state, action: PayloadAction<string | number | null>) {
      state.editingElementId = action.payload;
    },
    setElementAttrs(
      state,
      action: PayloadAction<{
        color: string;
        borderColor: string;
        borderWidth: string;
        name: string;
        borderStyle: 'solid' | 'dotted' | 'dashed';
      }>,
    ) {
      state.elementAttrs = action.payload;
    },
    setElementName(state, action: PayloadAction<string>) {
      state.elementName = action.payload;
    },
    setElementType(state, action: PayloadAction<string>) {
      state.elementType = action.payload;
    },
    setBase64Image(state, action: PayloadAction<string | null>) {
      state.base64Image = action.payload;
    },
    setOpacity(state, action: PayloadAction<number>) {
      state.opacity = action.payload;
    },
    setImageUploaded(state, action: PayloadAction<boolean>) {
      state.imageUploaded = action.payload;
    },
    setIconVisible(state, action: PayloadAction<boolean>) {
      state.iconVisible = action.payload;
    },
    setIconPosition(state, action: PayloadAction<{ top: number; left: number }>) {
      state.iconPosition = action.payload;
    },
    setHoveredLink(state, action: PayloadAction<dia.Link | null>) {
      state.hoveredLink = action.payload;
    },
    setImageUploadModalOpen(state, action: PayloadAction<boolean>) {
      state.imageUploadModalOpen = action.payload;
    },
    setImageDropPosition(state, action: PayloadAction<{ x: number; y: number } | null>) {
      state.imageDropPosition = action.payload;
    },
    setSelectedLink(state, action: PayloadAction<dia.Link | null>) {
      state.selectedLink = action.payload;
    },
    setEditLinkDialogOpen(state, action: PayloadAction<boolean>) {
      state.editLinkDialogOpen = action.payload;
    },
    setLinkAttrs(
      state,
      action: PayloadAction<{
        width?: number;
        style?: string;
        endStyle?: string;
        color?: string;
        direction?: string;
        outlineStrokeWidth?: number;
        outlineStrokeColor?: string;
        liquidStrokeWidth?: number;
        liquidStrokeColor?: string;
        liquidDashArray?: string;
        animation?: {
          enabled?: boolean;
          speed?: number;
        };
      }>,
    ) {
      state.linkAttrs = {
        ...state.linkAttrs,
        ...action.payload,
        animation: {
          ...state.linkAttrs.animation,
          ...action.payload.animation,
        },
      };
    },

    // Add other state setters as needed for each property
  },
});

export default diagramSlice.reducer;
