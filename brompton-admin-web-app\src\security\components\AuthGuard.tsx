import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useGetUserPreferencesQuery } from '~/redux/api/usersApi';
import { getIsUserLoggedIn } from '~/redux/selectors/dashboardSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { extractToken } from '~/utils/customBaseQuery';
export const AuthGuard = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const isUserLoggedIn = useSelector(getIsUserLoggedIn);
  const cookieToken = typeof window !== 'undefined' ? extractToken(document.cookie) : '';
  const dispatch = useDispatch();

  const [isUnsupportedBrowser, setIsUnsupportedBrowser] = useState(false);
  const [browserName, setBrowserName] = useState<string>('Unknown');
  const [hasCheckedBrowser, setHasCheckedBrowser] = useState(false); // new

  const { data: userPreferences, isSuccess: isUserPreferencesSuccess } = useGetUserPreferencesQuery(
    undefined,
    { skip: !isUserLoggedIn },
  );

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const ua = navigator.userAgent;
      let name = 'Unknown';
      let supported = false;

      if (ua.includes('Edg')) {
        name = 'Microsoft Edge';
        supported = true;
      } else if (ua.includes('Chrome') && !ua.includes('Edg')) {
        name = 'Google Chrome';
        supported = true;
      } else if (ua.includes('Firefox')) {
        name = 'Firefox';
      } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
        name = 'Safari';
      } else if (ua.includes('Opera') || ua.includes('OPR')) {
        name = 'Opera';
      }

      setBrowserName(name);
      setIsUnsupportedBrowser(!supported);
      setHasCheckedBrowser(true); // detection complete
    }
  }, []);

  useEffect(() => {
    if (router.isReady) {
      if (!isUserLoggedIn || cookieToken === '') {
        if (
          !['/login', '/logout', '/forgot-password', '/forgot-password/reset'].includes(
            router.pathname,
          )
        ) {
          const redirectUrl = encodeURIComponent(router.asPath);
          router.push(`/login?redirect=${redirectUrl}`);
        }
      }
    }
  }, [router, isUserLoggedIn, cookieToken]);

  useEffect(() => {
    if (isUserPreferencesSuccess) {
      dispatch(dashboardSlice.actions.setUserPreferences(userPreferences.preferences));
    }
  }, [isUserPreferencesSuccess]);

  if (!hasCheckedBrowser) {
    // Optionally show a loading spinner here
    return null;
  }

  if (isUnsupportedBrowser) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center' }}>
        <h2>Unsupported Browser</h2>
        <p>
          This application supports only <strong>Google Chrome</strong> or{' '}
          <strong>Microsoft Edge</strong>.
        </p>
        <p>Please switch to a supported browser for the best experience.</p>
      </div>
    );
  }

  return <>{children}</>;
};
