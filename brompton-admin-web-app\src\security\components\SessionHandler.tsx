import CloseIcon from '@mui/icons-material/Close';
import WarningIcon from '@mui/icons-material/Warning';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Typography,
} from '@mui/material';
import { jwtDecode } from 'jwt-decode';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { UserToken } from '~/hooks/useRolePermission';
import { getUserToken } from '~/redux/selectors/dashboardSelectors';

const SessionHandler = () => {
  const [showPopup, setShowPopup] = useState(false);
  const userToken = useSelector(getUserToken);
  const router = useRouter();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const tokenExpirationRef = useRef<number | null>(null);

  const isPopupDismissed = useMemo(
    () =>
      localStorage.getItem('sessionPopupDismissed') === 'true' &&
      !['/login', '/logout', '/forgot-password', '/forgot-password/reset'].includes(
        router.pathname,
      ),
    [router.pathname],
  );

  // Function to check session expiration
  const checkSessionExpiration = useCallback(() => {
    if (tokenExpirationRef.current) {
      const currentTime = Date.now();
      const timeRemaining = tokenExpirationRef.current - currentTime;

      if (timeRemaining <= 0) {
        // Token has expired, log out the user
        localStorage.removeItem('sessionPopupDismissed');
        router.push('/logout');
        return;
      }

      // If the session has more than 5 minutes remaining, do not show the popup
      if (timeRemaining > 5 * 60 * 1000) {
        setShowPopup(false); // Ensure popup is not displayed
        return;
      }

      // If the session will expire in 5 minutes or less, show the popup
      setShowPopup(true);
    }
  }, [router]);

  // Setup effect to initialize and clean up interval
  useEffect(() => {
    if (userToken && !isPopupDismissed) {
      const token = jwtDecode(userToken) as UserToken;
      tokenExpirationRef.current = token.exp * 1000;

      // Check expiration immediately after login
      checkSessionExpiration();

      // Set an interval to check the expiration every 1 minute
      intervalRef.current = setInterval(() => {
        checkSessionExpiration();
      }, 60000);

      // Clean up interval on component unmount
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      };
    }
  }, [userToken, isPopupDismissed, checkSessionExpiration]);

  const handleClose = () => {
    setShowPopup(false);
    localStorage.setItem('sessionPopupDismissed', 'true');
  };

  return (
    <>
      {!['/login', '/logout', '/forgot-password', '/forgot-password/reset'].includes(
        router.pathname,
      ) && (
        <Dialog
          open={showPopup}
          onClose={handleClose}
          sx={{
            '& .MuiDialog-paper': {
              padding: 2,
              borderRadius: 2,
            },
          }}
        >
          <DialogTitle
            sx={{
              color: 'error.main',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              position: 'relative',
            }}
          >
            <WarningIcon fontSize="large" />
            Session Expiration Warning
            <IconButton
              onClick={handleClose}
              sx={{
                position: 'absolute',
                right: 8,
                top: 8,
              }}
              size="small"
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>

          <DialogContent>
            <Typography variant="body1" sx={{ marginBottom: 2 }}>
              Your session will expire in 5 minutes! Do not forget to save your changes.
            </Typography>
          </DialogContent>

          <DialogActions>
            <Button onClick={handleClose} color="primary" variant="contained">
              Close
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </>
  );
};

export default SessionHandler;
