import { Box, useMediaQuery } from '@mui/material';
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';
import LeftMenuDrawer from '~/components/common/LeftMenu/LeftMenuDrawer';
import { getIsFullScreen, getIsUserLoggedIn } from '~/redux/selectors/dashboardSelectors';
import { leftMenuWidth } from '~/types/dashboard';
const UIWrapper = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const isUserLoggedIn = useSelector(getIsUserLoggedIn);
  const fullScreen = useSelector(getIsFullScreen);

  // Add media query for landscape mode
  const isLandscape = useMediaQuery('(max-height: 500px) and (orientation: landscape)');

  // Define the width of the mobile sidebar in landscape mode
  const mobileSidebarWidth = 45; // This should match the width in LeftMenuDrawer.tsx

  return (
    <Box
      sx={
        router.pathname !== '/login' &&
        router.pathname !== '/logout' &&
        router.pathname !== '/forgot-password/reset' &&
        router.pathname !== '/forgot-password' &&
        !fullScreen
          ? {
              marginLeft: leftMenuWidth + 'px',
              width: `calc(100% - ${leftMenuWidth}px)`,
              // On portrait small devices, cover full viewport width
              '@media (max-width: 600px)': {
                marginLeft: 0,
                width: '100vw',
                maxWidth: '100vw',
              },
              // On landscape small devices, add space for the sidebar
              '@media (max-height: 500px) and (orientation: landscape)': {
                marginLeft: mobileSidebarWidth + 'px',
                width: `calc(100% - ${mobileSidebarWidth}px)`,
              },
            }
          : undefined
      }
    >
      {isUserLoggedIn &&
        !fullScreen &&
        router.pathname !== '/login' &&
        router.pathname !== '/forgot-password/reset' &&
        router.pathname !== '/forgot-password' &&
        router.pathname !== '/logout' && <LeftMenuDrawer />}
      {children}
    </Box>
  );
};

export default UIWrapper;
