import { useMediaQuery } from '@mui/material';
import { Container } from '@mui/system';
import { openobserveRum } from '@openobserve/browser-rum';
import { ThunkDispatch } from '@reduxjs/toolkit';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AnyAction } from 'redux';
import { useLoginUserMutation } from '~/redux/api/authApi';
import { customersApi } from '~/redux/api/customersApi';
import { dashboardApi } from '~/redux/api/dashboardApi';
import { useGetCurrentUserDetailsQuery, useGetUserPreferencesQuery } from '~/redux/api/usersApi';
import { getActiveCustomer } from '~/redux/selectors/customerSelectors';
import { getCurrentDashboardId } from '~/redux/selectors/dashboardSelectors';
import { dashboardSlice } from '~/redux/slices/dashboardSlice';
import { RootState } from '~/redux/store';
import SignInForm from '~/security/components/SignInForm';
import { Customer } from '~/types/customers';

export default function SignInContainer(): JSX.Element {
  const router = useRouter();
  const mobile = useMediaQuery('@media (max-width: 768px)');
  const dispatch = useDispatch<ThunkDispatch<RootState, any, AnyAction>>();
  const ActiveCustomer = useSelector(getActiveCustomer);
  const { setUserDetails, setActiveCustomer } = dashboardSlice.actions;
  const currentDashboardId = useSelector(getCurrentDashboardId);
  const [loginUser, { isLoading: isLoginLoading, error, isSuccess: isLoginSuccess, data }] =
    useLoginUserMutation();

  useEffect(() => {
    // Prefetch the /dashboard route
    // router.prefetch('/dashboard/-1');
    router.prefetch('/customer/');
  }, [router]);

  const {
    data: userDetails,
    isLoading: isUserDetailsLoading,
    isSuccess: isUserDetailsSuccess,
    isError: isUserDetailsError,
    refetch: fetchUserDetails,
  } = useGetCurrentUserDetailsQuery(undefined, {
    skip: !isLoginSuccess, // Fetch user details only if login is successful
  });
  const {
    data: userPreferences,
    isLoading: isUserPreferencesLoading,
    isSuccess: isUserPreferencesSuccess,
    refetch: fetchUserPreferences,
  } = useGetUserPreferencesQuery(undefined, {
    skip: !isLoginSuccess,
  });
  useEffect(() => {
    if (isLoginSuccess) {
      // If login is successful, manually refetch the getCurrentUserDetails query
      fetchUserDetails();
      fetchUserPreferences();
    }
  }, [isLoginSuccess, fetchUserDetails, fetchUserPreferences]);

  useEffect(() => {
    const fetchData = async () => {
      if (
        isLoginSuccess &&
        isUserDetailsSuccess &&
        isUserPreferencesSuccess &&
        userPreferences?.preferences &&
        data
      ) {
        localStorage.removeItem('sessionPopupDismissed');
        dispatch(setUserDetails(userDetails));
        dispatch(dashboardSlice.actions.setUserToken(data.access_token));
        dispatch(dashboardSlice.actions.setTimeRangeType(6));
        dispatch(dashboardSlice.actions.setUserPreferences(userPreferences.preferences));
        openobserveRum.setUser({
          id: userDetails.id.toString(),
          name: userDetails.username,
          email: userDetails.email,
        });
        openobserveRum.startSessionReplayRecording();
        const url = router.query.redirect as string;
        if (url && url !== '/' && url !== '/logout') {
          const redirectUrl = decodeURIComponent(url);

          // get current host
          const currentHost = window.location.origin;
          const redirectQueryUrl = new URL(currentHost + redirectUrl);

          const [_, __, customerId, ___, dashboardId] = redirectQueryUrl.pathname.split('/');

          const { data: customerList } = await dispatch(
            customersApi.endpoints?.getCustomers.initiate({ is_logo: false }),
          );
          if (mobile) {
            dispatch(dashboardSlice.actions.setAssetTz(false));
          }
          if (customerList && customerList.length > 0) {
            const selectedCustomer: Customer | undefined =
              customerList.find((item) => item.id === Number(customerId)) ??
              customerList.find(
                (customer) =>
                  customer.id ===
                  Number(
                    userPreferences.preferences.DEFAULT_CUSTOMER
                      ? userPreferences.preferences.DEFAULT_CUSTOMER
                      : ActiveCustomer?.id,
                  ),
              ) ??
              customerList[0];

            if (selectedCustomer) {
              dispatch(setActiveCustomer(selectedCustomer));
              dispatch(dashboardSlice.actions.setActiveCustomer(selectedCustomer));

              const { data: dashboardList } = await dispatch(
                dashboardApi.endpoints.getDashboardByCustomerId.initiate({
                  customerId: selectedCustomer.id,
                  search: null,
                }),
              );

              if (dashboardList && dashboardList.items.length > 0) {
                let defaultDashboard = dashboardList.items.find(
                  (dashboard) => dashboard.id === Number(currentDashboardId),
                );
                if (dashboardId) {
                  defaultDashboard = dashboardList.items.find(
                    (dashboard) => dashboard.id === Number(dashboardId),
                  );
                }
                if (!defaultDashboard) {
                  defaultDashboard =
                    dashboardList.items.find((item) => item.default === true) ??
                    dashboardList.items[0];
                }

                dispatch(
                  dashboardSlice.actions.setCurrentDashboardId(
                    Number(dashboardId) === 0 ? 0 : defaultDashboard.id,
                  ),
                );
                dispatch(
                  dashboardSlice.actions.setCurrentDashboardTitle(
                    Number(dashboardId) === 0 ? '' : defaultDashboard.title,
                  ),
                );

                if (redirectUrl.includes('/customer') && redirectUrl.includes('/dashboard')) {
                  const queryParams = new URLSearchParams();
                  // getting data from redirect url search param
                  //?redirect=/customer/84/dashboard/96?gtr=10&s=1717146516597&e=1717751316597&sample_period=9
                  // with alert id url
                  //http://localhost:3000/customer/8/dashboard/0?gtr=6&s=1724989581718&e=1725011181718&alertId=370&measurement=25748&asset=544&threshold=25&deadband=2
                  const query = new URLSearchParams(redirectQueryUrl.search);
                  const startTime = query.get('s');
                  if (startTime) {
                    queryParams.set('s', startTime);
                  }
                  const endTime = query.get('e');
                  if (endTime) {
                    queryParams.set('e', endTime);
                  }
                  const sample_period = query.get('sample_period');
                  if (sample_period) {
                    queryParams.set('sample_period', sample_period);
                  }
                  const gtr = query.get('gtr');
                  if (gtr) {
                    queryParams.set('gtr', gtr);
                  }
                  const eventId = query.get('event_id');
                  if (eventId) {
                    queryParams.set('event_id', eventId);
                    if (!selectedCustomer) {
                      router.push(`/unauthorized`);
                      return;
                    }
                  }
                  router.push(
                    `/customer/${selectedCustomer?.id}/dashboard/${
                      Number(dashboardId) === 0 ? 0 : defaultDashboard.id
                    }?${queryParams.toString()}`,
                  );
                } else {
                  router.push(`/customer/${selectedCustomer.id}`);
                }
              } else {
                const query = new URLSearchParams(redirectQueryUrl.search);
                const eventId = query.get('event_id');
                if (eventId) {
                  const customerSelection = customerList.find(
                    (customer) => customer.id === Number(customerId),
                  );
                  if (!customerSelection) {
                    router.push(`/unauthorized`);
                    return;
                  }
                }
                router.push(`/customer/${selectedCustomer.id}`);
              }
            } else {
              // router.push(`/customer`);
            }
          }
        } else {
          if (
            userDetails &&
            userDetails.scoped_roles[0] &&
            userDetails.scoped_roles[0].customer_ids[0] &&
            !userPreferences.preferences.DEFAULT_CUSTOMER
          ) {
            router.push(`/customer/${userDetails.scoped_roles[0].customer_ids[0]}`);
          } else if (
            userPreferences.preferences.DEFAULT_CUSTOMER &&
            !(
              userDetails &&
              userDetails.scoped_roles[0] &&
              userDetails.scoped_roles[0].customer_ids[0]
            )
          ) {
            router.push(`/customer/${userPreferences.preferences.DEFAULT_CUSTOMER}`);
          } else {
            router.push(`/customer`);
          }
        }
      }
    };

    fetchData();
  }, [
    router,
    isUserDetailsSuccess,
    isLoginSuccess,
    userDetails,
    dispatch,
    setUserDetails,
    isUserPreferencesSuccess,
    currentDashboardId,
    userPreferences?.preferences,
    data,
  ]);

  return (
    <Container maxWidth="xs" sx={{ pt: 16 }}>
      <SignInForm
        loading={
          isLoginLoading ||
          isUserDetailsLoading ||
          isUserPreferencesLoading ||
          isLoginSuccess ||
          isUserPreferencesSuccess ||
          isUserDetailsSuccess
        }
        errorMessage={error || isUserDetailsError ? 'Invalid login' : undefined}
        onValidSubmit={loginUser}
      />
    </Container>
  );
}
