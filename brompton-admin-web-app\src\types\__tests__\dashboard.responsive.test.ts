import { DashboardState } from '../dashboard';
import { Layout } from 'react-grid-layout';

/**
 * Type tests for responsive layout functionality
 * These tests ensure that TypeScript types are correctly defined
 */
describe('Dashboard Types - Responsive Layout', () => {
  describe('DashboardState type', () => {
    it('should allow desktopMobile property', () => {
      const dashboardState: Partial<DashboardState> = {
        desktopMobile: 0,
      };

      expect(typeof dashboardState.desktopMobile).toBe('number');
    });

    it('should allow desktopMobile to be undefined', () => {
      const dashboardState: Partial<DashboardState> = {};

      expect(dashboardState.desktopMobile).toBeUndefined();
    });

    it('should allow responsiveLayouts property', () => {
      const mockLayout: Layout[] = [
        { i: '1', x: 0, y: 0, w: 6, h: 4 },
      ];

      const dashboardState: Partial<DashboardState> = {
        responsiveLayouts: {
          desktop: { widgetLayout: mockLayout },
          mobile: { widgetLayout: mockLayout },
        },
      };

      expect(dashboardState.responsiveLayouts).toBeDefined();
      expect(dashboardState.responsiveLayouts?.desktop).toBeDefined();
      expect(dashboardState.responsiveLayouts?.mobile).toBeDefined();
    });

    it('should allow responsiveLayouts to be undefined', () => {
      const dashboardState: Partial<DashboardState> = {};

      expect(dashboardState.responsiveLayouts).toBeUndefined();
    });

    it('should maintain existing widget structure', () => {
      const mockLayout: Layout[] = [
        { i: '1', x: 0, y: 0, w: 6, h: 4 },
      ];

      const dashboardState: Partial<DashboardState> = {
        widget: {
          widgets: [],
          deleteWidgets: [],
          widgetLayout: mockLayout,
          lastWidgetId: 0,
        },
      };

      expect(dashboardState.widget).toBeDefined();
      expect(Array.isArray(dashboardState.widget?.widgets)).toBe(true);
      expect(Array.isArray(dashboardState.widget?.widgetLayout)).toBe(true);
    });

    it('should allow complete responsive dashboard state', () => {
      const desktopLayout: Layout[] = [
        { i: '1', x: 0, y: 0, w: 6, h: 4 },
        { i: '2', x: 6, y: 0, w: 6, h: 4 },
      ];

      const mobileLayout: Layout[] = [
        { i: '1', x: 0, y: 0, w: 12, h: 4 },
        { i: '2', x: 0, y: 4, w: 12, h: 4 },
      ];

      const dashboardState: Partial<DashboardState> = {
        currentDashboardId: 1,
        dashboardTitle: 'Test Dashboard',
        desktopMobile: 0,
        widget: {
          widgets: [
            { id: '1', type: 'stats', settings: {} },
            { id: '2', type: 'chart', settings: { chartType: 'bar', settings: {} } },
          ],
          widgetLayout: desktopLayout,
          deleteWidgets: [],
          lastWidgetId: 2,
        },
        responsiveLayouts: {
          desktop: { widgetLayout: desktopLayout },
          mobile: { widgetLayout: mobileLayout },
        },
        isDirty: true,
      };

      // Type assertions to ensure all properties are correctly typed
      expect(typeof dashboardState.currentDashboardId).toBe('number');
      expect(typeof dashboardState.dashboardTitle).toBe('string');
      expect(typeof dashboardState.desktopMobile).toBe('number');
      expect(typeof dashboardState.isDirty).toBe('boolean');
      
      expect(dashboardState.widget).toBeDefined();
      expect(Array.isArray(dashboardState.widget?.widgets)).toBe(true);
      expect(Array.isArray(dashboardState.widget?.widgetLayout)).toBe(true);
      
      expect(dashboardState.responsiveLayouts).toBeDefined();
      expect(dashboardState.responsiveLayouts?.desktop).toBeDefined();
      expect(dashboardState.responsiveLayouts?.mobile).toBeDefined();
      expect(Array.isArray(dashboardState.responsiveLayouts?.desktop.widgetLayout)).toBe(true);
      expect(Array.isArray(dashboardState.responsiveLayouts?.mobile.widgetLayout)).toBe(true);
    });
  });

  describe('Layout type compatibility', () => {
    it('should be compatible with react-grid-layout Layout type', () => {
      const layout: Layout = { i: '1', x: 0, y: 0, w: 6, h: 4 };
      
      const dashboardState: Partial<DashboardState> = {
        widget: {
          widgets: [],
          widgetLayout: [layout],
          deleteWidgets: [],
          lastWidgetId: 0,
        },
        responsiveLayouts: {
          desktop: { widgetLayout: [layout] },
          mobile: { widgetLayout: [layout] },
        },
      };

      expect(dashboardState.widget?.widgetLayout[0]).toEqual(layout);
      expect(dashboardState.responsiveLayouts?.desktop.widgetLayout[0]).toEqual(layout);
      expect(dashboardState.responsiveLayouts?.mobile.widgetLayout[0]).toEqual(layout);
    });

    it('should handle complex layout properties', () => {
      const complexLayout: Layout = {
        i: 'widget-1',
        x: 2,
        y: 3,
        w: 8,
        h: 6,
        minW: 2,
        maxW: 12,
        minH: 2,
        maxH: 10,
        static: false,
        isDraggable: true,
        isResizable: true,
      };

      const dashboardState: Partial<DashboardState> = {
        responsiveLayouts: {
          desktop: { widgetLayout: [complexLayout] },
          mobile: { widgetLayout: [complexLayout] },
        },
      };

      const desktopLayout = dashboardState.responsiveLayouts?.desktop.widgetLayout[0];
      expect(desktopLayout?.i).toBe('widget-1');
      expect(desktopLayout?.static).toBe(false);
      expect(desktopLayout?.isDraggable).toBe(true);
      expect(desktopLayout?.isResizable).toBe(true);
    });
  });

  describe('Type safety', () => {
    it('should enforce correct desktopMobile values', () => {
      // These should be valid
      const validStates: Array<Partial<DashboardState>> = [
        { desktopMobile: 0 },
        { desktopMobile: 1 },
        { desktopMobile: undefined },
      ];

      validStates.forEach((state) => {
        expect(state.desktopMobile === undefined || 
               state.desktopMobile === 0 || 
               state.desktopMobile === 1).toBe(true);
      });
    });

    it('should enforce correct responsiveLayouts structure', () => {
      const validLayout: Layout[] = [
        { i: '1', x: 0, y: 0, w: 6, h: 4 },
      ];

      const validResponsiveLayouts = {
        desktop: { widgetLayout: validLayout },
        mobile: { widgetLayout: validLayout },
      };

      const dashboardState: Partial<DashboardState> = {
        responsiveLayouts: validResponsiveLayouts,
      };

      expect(dashboardState.responsiveLayouts).toEqual(validResponsiveLayouts);
    });

    it('should maintain backward compatibility with existing DashboardState', () => {
      // Existing dashboard state without responsive layouts should still be valid
      const legacyDashboardState: Partial<DashboardState> = {
        currentDashboardId: 1,
        dashboardTitle: 'Legacy Dashboard',
        widget: {
          widgets: [],
          widgetLayout: [],
          deleteWidgets: [],
          lastWidgetId: 0,
        },
        isDirty: false,
      };

      // Should not require responsive layout properties
      expect(legacyDashboardState.desktopMobile).toBeUndefined();
      expect(legacyDashboardState.responsiveLayouts).toBeUndefined();
      
      // But should still be a valid DashboardState
      expect(legacyDashboardState.currentDashboardId).toBe(1);
      expect(legacyDashboardState.widget).toBeDefined();
    });
  });
});
