import * as yup from 'yup';

export const customerSchema = yup.object({
  nameId: yup
    .string()
    .matches(
      /^[a-z0-9_-]*$/,
      'Name id must be lower case and may only contain numbers, dash and lowdash',
    )
    .required('Please enter a name id'),
  name: yup.string().required('Please enter a name'),
  address: yup.string().required('Please enter an address'),
  logo: yup
    .mixed()
    .required('Please enter a logo')
    .test(
      'fileType',
      'Logo file type must be one of: image/jpeg, image/png, image/gif',
      (value) => {
        if (!value) return true;
        const file = value as File;
        const fileType = file.type;
        const validImageTypes = ['image/jpeg', 'image/png', 'image/gif'];
        return validImageTypes.includes(fileType);
      },
    )
    .test('fileSize', 'Logo file size must be less than 2MB', (value) => {
      if (!value) return true;
      const file = value as File;
      const isValidFileSize = file.size <= 2 * 1024 * 1024;
      return isValidFileSize;
    }),
});

export const editCustomerSchema = yup.object({
  nameId: yup
    .string()
    .matches(
      /^[a-z0-9_-]*$/,
      'Name id must be lower case and may only contain numbers, dash and lowdash',
    )
    .required('Please enter a name id'),
  name: yup.string().required('Please enter a name'),
  address: yup.string().required('Please enter an address'),
  logo: yup
    .string()
    .nullable()
    .notRequired()
    .test(
      'fileType',
      'Logo file type must be one of: image/jpeg, image/png, image/gif',
      (value) => {
        if (!value) return true; // Skip validation if no value
        const matches = value.match(/^data:(image\/[a-zA-Z]+);base64,/);
        if (!matches) return false; // Invalid Base64 string
        const fileType = matches[1];
        const validImageTypes = ['image/jpeg', 'image/png', 'image/gif'];
        return validImageTypes.includes(fileType);
      },
    )
    .test('fileSize', 'Logo file size must be less than 2MB', (value) => {
      if (!value) return true;
      const base64Length = value.split(',')[1]?.length || 0;
      const fileSizeInBytes = (base64Length * 3) / 4 - (base64Length % 4 === 0 ? 0 : 1);
      const maxFileSizeInBytes = 2 * 1024 * 1024; // 2MB
      return fileSizeInBytes <= maxFileSizeInBytes;
    }),
});

export type Customer = yup.InferType<typeof customerSchema> & { id: number };

export type EditCustomer = yup.InferType<typeof editCustomerSchema> & { id: number };

export type NewCustomer = Omit<Customer, 'id'>;

export type CustomerDto = Omit<Customer, 'nameId'> & { name_id: string };

export type CollectionDto<T> = { items: T[]; total: number };

export type CustomerCollection = CollectionDto<CustomerDto>;
