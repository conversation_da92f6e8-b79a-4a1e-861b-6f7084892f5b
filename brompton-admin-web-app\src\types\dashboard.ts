import { Layout } from 'react-grid-layout';
import { Customer } from '~/types/customers';
import { UserDetailsResponse } from '~/types/users';
import { Widget } from '~/types/widgets';
import { UserPreferences } from './userPreferences';
import { Asset } from './asset';

export type MainPanel =
  | 'chart'
  | 'assetDetails'
  | 'newAsset'
  | 'newMeasure'
  | 'editMeasure'
  | 'MeasureDetails'
  | 'editAsset'
  | 'addAssetTemplate'
  | 'calc-engine'
  | 'create-alert'
  | 'view-alerts'
  | 'dashboard-template'
  | 'newAssetTemplate'
  | 'none';
export type DashboardCrumbsState = {
  dashboardId: number;
  parentDashboardId: number | null;
  templateId?: number;
  assetType?: number;
  assetId?: number;
  dashboardTitle: string;
};
export type DashboardState = {
  currentDashboardId: number;
  dashboardTitle: string;
  userDetails: UserDetailsResponse | null;
  userToken: string | null;
  userPreferences: UserPreferences;
  customer: Customer | null;
  isLeftPanelOpen: boolean;
  kisok: boolean;
  enableZoom: boolean;
  fullScreen: boolean;
  rightSideBar: boolean;
  rightSideBarActiveTab:
    | '/icons/alerts.svg'
    | '/icons/llm.svg'
    | '/icons/system-status.svg'
    | 'release-notes'
    | 'CO2e'
    | 'Reports';
  dateFormat: number;
  isDirty: boolean;
  topPanel: {
    isVisible: boolean;
    timeRangeType: number;
    refreshInterval: number;
    samplePeriod: number;
    assetTz: boolean;
  };
  mainPanel: MainPanel;
  newMeasureId: number;
  dashboardCrumb: DashboardCrumbsState[];
  tree: {
    currentSelectedNodeId: string;
    selectedViewMeasureId: string;
    selectedNodeIds: string[];
    expandedNodeIds: string[];
    dbMeasureIdToName: Record<string, string>;
  };
  chart: {
    startDate: number;
    endDate: number;
  };
  template: {
    templateId: number;
    templateName: string;
    assetType: number;
    assetTemplate: number;
    metrics: number[];
    idToName: Record<string, string>;
    topPanel: {
      timeRangeType: number;
      refreshInterval: number;
      samplePeriod: number;
      assetTz: boolean;
    };
    chart: {
      startDate: number;
      endDate: number;
    };
  };
  widget: {
    widgets: Widget[];
    deleteWidgets: string[];
    widgetLayout: Layout[];
    lastWidgetId: number;
  };
  desktopMobile?: number; // 0 for desktop, 1 for mobile
  responsiveLayouts?: {
    desktop: {
      widgetLayout: Layout[];
    };
    mobile: {
      widgetLayout: Layout[];
    };
  };
  metricMeasurements: Record<string, { metricName: string; measurement: string }>; // Add this line
};

export const dateFormats = [
  'DD-MM-YYYY HH:mm:ss',
  'DD-MM-YYYY hh:mm:ss a',
  'MM-DD-YYYY HH:mm:ss',
  'MM-DD-YYYY hh:mm:ss a',
  'DD-MMM-YYYY HH:mm:ss',
  'DD-MMM-YYYY hh:mm:ss a',
  'DD-MMM-YY HH:mm:ss',
  'DD-MMM-YY hh:mm:ss a',
  'MMM-DD-YYYY HH:mm:ss',
  'MMM-DD-YYYY hh:mm:ss a',
  'MMM-DD-YY HH:mm:ss',
  'MMM-DD-YY hh:mm:ss a',
];

export const currencyOptions = [
  'USD',
  'EUR',
  'GBP',
  'CNY',
  'JPY',
  'KRW',
  'INR',
  'AUD',
  'CAD',
  'SGD',
];
export type TimeRangeOption = {
  label: string;
  value: number;
  serverValue: number;
};

export const TimeRangeOptions: TimeRangeOption[] = [
  { label: 'Custom', value: 0, serverValue: 360 },
  { label: 'Last 5 minutes', value: 1, serverValue: 5 },
  { label: 'Last 15 minutes', value: 2, serverValue: 15 },
  { label: 'Last 30 minutes', value: 3, serverValue: 30 },
  { label: 'Last 1 hour', value: 4, serverValue: 60 },
  { label: 'Last 3 hours', value: 5, serverValue: 180 },
  { label: 'Last 6 hours', value: 6, serverValue: 360 },
  { label: 'Last 12 hours', value: 7, serverValue: 720 },
  { label: 'Last 24 hours', value: 8, serverValue: 1440 },
  { label: 'Last 2 days', value: 9, serverValue: 2880 },
  { label: 'Last 7 days', value: 10, serverValue: 10080 },
  { label: 'Last 30 days', value: 11, serverValue: 43200 },
  { label: 'Last 90 days', value: 12, serverValue: 129600 },
  { label: 'Current Date', value: 13, serverValue: -4 },
  { label: 'Current Week', value: 14, serverValue: -1 },
  { label: 'Current Month', value: 15, serverValue: -2 },
  { label: 'Current Year', value: 16, serverValue: -3 },
];

export type AggOption = {
  label: string;
  value: number;
  serverValue: string;
};

export const AggByOptions: AggOption[] = [
  { label: 'None', value: 0, serverValue: '' },
  { label: 'TWA', value: 1, serverValue: 'twa' },
  { label: 'Average', value: 2, serverValue: 'avg' },
  { label: 'Min', value: 3, serverValue: 'min' },
  { label: 'Max', value: 4, serverValue: 'max' },
  { label: 'STD.P', value: 5, serverValue: 'std.p' },
  { label: 'RateTotal', value: 6, serverValue: 'ratetotal' },
  { label: 'Delta-TWA', value: 7, serverValue: 'deltatwa' },
  { label: 'Delta-Avg', value: 8, serverValue: 'deltaavg' },
  { label: 'Delta-Max', value: 9, serverValue: 'deltamax' },
  // { label: 'Total', value: 5, serverValue: 'total' },
];

export type TwaOption = {
  label: string;
  value: number;
  serverValue: string;
  hour: number;
};
export const SamplePeriodOptions: TwaOption[] = [
  { label: 'None', value: 0, serverValue: '', hour: 1 },
  { label: '1 Minute', value: 1, serverValue: '1min', hour: 1 / 60 },
  { label: '5 Minutes', value: 2, serverValue: '5min', hour: 5 / 60 },
  { label: '10 Minutes', value: 3, serverValue: '10min', hour: 10 / 60 },
  { label: '15 Minutes', value: 4, serverValue: '15min', hour: 15 / 60 },
  { label: '20 Minutes', value: 5, serverValue: '20min', hour: 20 / 60 },
  { label: '30 Minutes', value: 6, serverValue: '30min', hour: 30 / 60 },
  { label: '1 Hour', value: 7, serverValue: '1hr', hour: 1 },
  { label: '2 Hours', value: 8, serverValue: '2hr', hour: 2 },
  { label: '4 Hours', value: 9, serverValue: '4hr', hour: 4 },
  { label: '6 Hours', value: 10, serverValue: '6hr', hour: 6 },
  { label: '8 Hours', value: 11, serverValue: '8hr', hour: 8 },
  { label: '12 Hours', value: 12, serverValue: '12hr', hour: 12 },
  { label: 'Daily', value: 13, serverValue: 'DAILY', hour: 24 },
  { label: 'Weekly', value: 14, serverValue: 'WEEKLY', hour: 24 * 7 },
  { label: 'Monthly', value: 15, serverValue: 'MONTHLY', hour: 30 * 24 },
];

export const TopPanelSamplePeriodOptions: TwaOption[] = [
  { label: '1 Minute', value: 1, serverValue: '1min', hour: 1 / 60 },
  { label: '5 Minutes', value: 2, serverValue: '5min', hour: 5 / 60 },
  { label: '15 Minutes', value: 3, serverValue: '15min', hour: 15 / 60 },
  { label: '20 Minutes', value: 4, serverValue: '20min', hour: 20 / 60 },
  { label: '30 Minutes', value: 5, serverValue: '30min', hour: 30 / 60 },
  { label: '1 Hour', value: 6, serverValue: '1hr', hour: 1 },
  { label: '2 Hours', value: 7, serverValue: '2hr', hour: 2 },
  { label: '4 Hours', value: 8, serverValue: '4hr', hour: 4 },
  { label: '6 Hours', value: 9, serverValue: '6hr', hour: 6 },
  { label: '8 Hours', value: 10, serverValue: '8hr', hour: 8 },
  { label: '12 Hours', value: 11, serverValue: '12hr', hour: 12 },
  { label: 'Daily', value: 12, serverValue: 'DAILY', hour: 24 },
  { label: 'Weekly', value: 13, serverValue: 'WEEKLY', hour: 24 * 7 },
  { label: '30 Days', value: 14, serverValue: '30day', hour: 30 * 24 },
];
export const GroupByOptions = ['Date', 'Month', 'DayOfWeek', 'Hour', 'DayOfMonth', 'Year', 'Week'];

export type DashboardParams = {
  title: string;
  data: string;
  customerId: number;
  id: number;
  description: string;
  asset_id?: number;
  dashboard_template_id?: number;
  favourite?: boolean;
};

export type CreateDashboardParam = Omit<DashboardParams, 'id'>;

export type DashboardMetaData = { title: string; id: number; description: string };

export type DashboardDetails = {
  id: number;
  title: string;
  description: string;
  data: DashboardState;
  customerId: number;
};

export type DashboardDTO = {
  id: number;
  title: string;
  data: string;
  default: boolean;
  favorite: boolean;
  dashboardTemplate: {
    id: number;
    title: string;
  } | null;
  asset: null | Asset;
};

export type DashboardCollection = {
  items: DashboardDTO[];
  total: number;
};

export const leftMenuWidth = 105;
