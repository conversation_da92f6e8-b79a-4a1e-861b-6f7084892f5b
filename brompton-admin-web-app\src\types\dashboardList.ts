import * as yup from 'yup';
import { Customer } from './customers';

export type dashboardList = {
  currentCustomer: Customer | null;
  currentDashboardId: number;
  searchValue: string | null;
};

export const dashboardTitleSchema = yup.object({
  title: yup.string().required('Please enter a title'),
  description: yup.string().default(''),
});

export type DashboarForm = yup.InferType<typeof dashboardTitleSchema>;

export const dashboardTemplateTitleSchema = yup.object({
  title: yup.string().required('Please enter a title'),
  description: yup.string().default(''),
  save_as_global_dashboard_template: yup.boolean(),
});

export type DashboarTemplateForm = yup.InferType<typeof dashboardTemplateTitleSchema>;
