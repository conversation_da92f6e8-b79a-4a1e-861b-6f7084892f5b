import * as yup from 'yup';
import {
  EditAssetMeasurement,
  AssetMeasurementSchema,
  EditAssetMeasurementForm,
  UnitOfMeasure,
} from '~/measurements/domain/types';
import { Asset, EditAsset } from '~/types/asset';

export type TreeNodeType = 'customer' | 'asset' | 'measurement' | 'metric';

export type AssetMeasurementDetailsParams = {
  customerId: number;
  assetId: string;
  measId: string;
};

export type AssetParams = {
  customerId: number;
  assetId: string;
};

export type editAssetParams = {
  customerId: number;
  assetId: string;
  editAsset: EditAsset;
};
export type DeleteMeasureParams = {
  customerId: number;
  assetId: string;
  measId: string;
};

export type EditMeasureParams = {
  customerId: number;
  assetId: string;
  measId: string;
  editAssetMeasurement: EditAssetMeasurementForm;
};
export type AssetMeasurementDetails = yup.InferType<typeof AssetMeasurementSchema> & {
  id: number;
  measurementId: number;
  metricId?: number | null;
  units?: string;
};

export type AssetDto = Omit<
  Asset,
  'assetTypeId' | 'isCustPrimary' | 'childrenIds' | 'parentIds' | 'timeZone'
> & {
  type_id: number;
  is_customer_primary: boolean;
  children_ids: number[];
  parent_ids: number[];
  time_zone?: string;
};
export type AssetMeasurementDetailsExtended = AssetMeasurementDetails & {
  latestReading: number | null;
  latestReadingTime: number | null;
  m_type: number | null;
  a_type: number | null;
  asset_path: string;
  datasource: number | null;
};
export type AssetMeasurementDetailsWithUnitOfMeasure = AssetMeasurementDetails & {
  unitOfMeasure: UnitOfMeasure | null;
  measurement_id: number | null;
};
export type measurementsDTO = {
  total: number;
  items: AssetMeasurementDetails[];
};
export type measurementsUnitsDTO = {
  total: number;
  items: AssetMeasurementDetailsWithUnitOfMeasure[];
};
export type extendedMeasurementsDTO = {
  total: number;
  items: AssetMeasurementDetailsExtended[];
};

export type AssetMeasurementDetailsWithMetric = yup.InferType<typeof AssetMeasurementSchema> & {
  id: number;
  measurement_id: number;
  metric_id: number | null;
  metricName: string | null;
  asset_path: string;
  datasource: number;
  parent_asset: number;
  asset_measurement_id: number;
};
export type measurementsMetricsDTO = {
  total: number;
  items: AssetMeasurementDetailsWithMetric[];
};

export type Metric = {
  id: number;
  name: string;
};

export type CalculationTemplate = {
  id: number;
  created: string;
  updated: string;
  createdby: number;
  updatedby: number;
  name: string;
  expression: string;
  description: string;
  importsList: any; // Replace with specific type if known
  dataType: number;
};

export type CalculationMetricInstance = {
  id: number;
  calculation: CalculationTemplate;
  outputMetric: number;
  ispersisted: boolean;
  pollPeriod: number;
  created: string;
  createdby: number;
};

export type CalcMetricInput = {
  id: number;
  inputLabel: string;
  calculationMetricInstance: CalculationMetricInstance;
  comment: string;
  created: string;
  createdby: number;
  metric?: Metric;
  constantNumber?: string | null;
  constantString?: string | null;
};

export type CalcMetricInstanceWithInputs = {
  instanceId: number;
  inputs: CalcMetricInput[];
};
export type CalcMetricInstanceWithInputsDTO = {
  total: number;
  items: CalcMetricInstanceWithInputs[];
};
export type CalcMetricInputDTO = {
  inputLabel: string;
  metric?: string; // Optional metric ID
  constantType?: 'number' | 'string'; // Type of constant (number or string)
  constantValue?: string; // Value of the constant
  comment?: string; // Optional comment
};

export type CalcMetricDTO = {
  templateId: number | null; // ID of the calculation template
  ispersisted: boolean; // Whether the metric is persisted
  pollPeriod?: number; // Optional polling period
  metricId: string; // ID of the metric
  iswriteback: boolean; // Whether writeback is enabled
  variables: CalcMetricInputDTO[]; // List of input details
};

export type CalcMetricDTORequest = {
  data: CalcMetricDTO[]; // Array of calculation metric DTOs
};

export type CalcMetricDTOResponse = {
  id: number; // ID of the response
  instanceId: number; // ID of the calculation instance
  templateId: number; // ID of the calculation template
  customerId: number; // ID of the customer
  outputMetricId: number; // ID of the output measurement
  isPersisted: boolean; // Whether the metric is persisted
  pollPeriod?: number; // Optional polling period
  inputs: CalcMetricInputDTO[]; // List of input details
};
