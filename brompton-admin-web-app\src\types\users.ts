import * as yup from 'yup';
import { Customer } from './customers';

type UserRoleKey = 'SUPER_ADMIN' | 'ADMIN' | 'USER' | 'POWER_USER';

export type UserDetails = {
  id: number;
  username: string;
  email: string;
  role: User<PERSON>oleKey;
};

export type ScopedRole = {
  role: UserRoleKey;
  customer_ids: number[];
};

export type ScopedUser = {
  username: string;
  password: string;
  first_name: string;
  last_name: string;
  scoped_roles: ScopedRole[];
  email: string;
  phone_no: string;
  country_code: string;
};

export type GlobalRole = string | null;
export type UserDetailsResponse = {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  global_role: GlobalRole;
  scoped_roles: ScopedRole[];
};
export const userSchema = yup.object({
  username: yup
    .string()
    .matches(
      // /^[a-z0-9_-]*$/,
      /^[a-z0-9_-]*$/i,
      'Name id must be lower case and may only contain numbers, dash and lowdash',
    )
    .required('Please enter a name id'),
  password: yup.string().required('Please enter password'),
  first_name: yup
    .string()
    .required('Please enter first name')
    .matches(/^[a-zA-Z\s]+$/, 'First name can only contain letters'),
  last_name: yup
    .string()
    .required('Please enter last name')
    .matches(/^[a-zA-Z\s]+$/, 'Last name can only contain letters'),
  customer_ids_user: yup.array().required('Please select customers.'),
  customer_ids_admin: yup.array().required('Please select customers.'),
  customer_ids_power_user: yup.array().required('Please select customers.'),
  email: yup
    .string()
    .required('Please enter email id')
    .matches(
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      'Please enter a valid email address',
    ),
  country_code: yup
    .string()
    .matches(/^\+\d+$/, 'Please enter a valid country code')
    .max(6, 'Country code must be less than 6 characters')
    .required('Please enter country code'),
  phone_no: yup
    .string()
    .required('Please enter phone number')
    .matches(/^\d{10}$/, 'Please enter a valid 10-digit phone number'),
});
export type User = yup.InferType<typeof userSchema> & { id: number; user_role: string };
export type NewUser = Omit<User, 'id'>;

export const forgotPassowrdSchema = yup.object({
  userNameOrEmail: yup
    .string()
    .required('Please enter your username or email address')
    .test(
      'userNameOrEmail',
      'Please enter a valid username or email address',
      (value) =>
        /^[a-z0-9_-]+$/.test(value || '') || // Checks for valid username format
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value || ''), // Checks for valid email format
    ),
});

export const forgottenPaswordSchema = yup.object({
  newPassword: yup
    .string()
    .required('Please enter a password.')
    .min(8, 'Password must be at least 8 characters long'),
  confirmNewPassword: yup
    .string()
    .required('Please enter confirm password.')
    .oneOf([yup.ref('newPassword'), ''], 'Passwords must match'),
});

export type ScopedRoleWithCustomer = {
  role: UserRoleKey;
  customers: Customer[];
};
export type UserDto = {
  id: number;
  first_name: string;
  last_name: string;
  username: string;
  email: string;
  scoped_roles: ScopedRoleWithCustomer[];
  enabled: boolean;
  country_code: string;
  phone_no: string;
};
export type UserDetailDto = {
  id: number;
  first_name: string;
  last_name: string;
  username: string;
  email: string;
  scoped_roles: {
    role: UserRoleKey;
    customer_ids: number[];
  }[];
  enabled: boolean;
  country_code: string;
  phone_no: string;
};
export type EditUserDto = {
  id: number;
  first_name: string;
  last_name: string;
  username: string;
  email: string;
  scoped_roles: ScopedRole[];
  enabled: boolean;
  country_code: string;
  phone_number: string;
};
export const userDtoSchema = yup.object({
  id: yup.number().required('Please enter user id'),
  first_name: yup
    .string()
    .required('Please enter first name')
    .matches(/^[a-zA-Z\s]+$/, 'First name can only contain letters'),
  last_name: yup
    .string()
    .required('Please enter last name')
    .matches(/^[a-zA-Z\s]+$/, 'Last name can only contain letters'),
  username: yup
    .string()
    .matches(
      /^[a-z0-9_-]*$/,
      'Name id must be lower case and may only contain numbers, dash and lowdash',
    )
    .required('Please enter a name id'),
  email: yup
    .string()
    .required('Please enter email id')
    .matches(
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      'Please enter a valid email address',
    ),
  // scoped_roles: yup.array().required('Please select scoped roles'),
  customer_ids_user: yup.array().required('Please select customers.'),
  customer_ids_admin: yup.array().required('Please select customers.'),
  customer_ids_power_user: yup.array().required('Please select customers.'),
  enabled: yup.boolean().required('Please specify if the user is enabled'),
  country_code: yup
    .string()
    .matches(/^\+\d+$/, 'Please enter a valid country code')
    .max(6, 'Country code must be less than 6 characters')
    .required('Please enter country code'),
  phone_no: yup
    .string()
    .required('Please enter phone number')
    .matches(/^\d{10}$/, 'Please enter a valid 10-digit phone number'),
});
export type EditUser = yup.InferType<typeof userDtoSchema> & { id: number };

export type UserCollection = {
  items: UserDto[];
  total: number;
};

export type searchUser = {
  customer_name?: string;
  customer_id?: string;
  user_name?: string;
  email?: string;
  role?: string;
};

export const resetPasswordSchema = yup.object({
  current_password: yup.string().required('Please enter current password'),
  new_password: yup.string().required('Please enter password'),
  confirm_password: yup
    .string()
    .oneOf([yup.ref('new_password')], 'Passwords must match')
    .required('Please confirm password'),
});
export type ResetPassword = yup.InferType<typeof resetPasswordSchema>;

export interface customerImage {
  id: number;
  customer: number;
  logo?: string; // Optional if it can be null or undefined
}

export interface CustomerImageDTO {
  total: number;
  items: customerImage[];
}
