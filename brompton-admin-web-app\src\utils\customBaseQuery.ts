import {
  BaseQueryFn,
  FetchArgs,
  fetchBaseQuery,
  FetchBaseQueryError,
} from '@reduxjs/toolkit/query';
import Router from 'next/router';
import { RootState } from '~/redux/store';

export function extractToken(str: string): string {
  const match = str.match(/BE-CSRFToken=([^;]+)/);
  return match ? decodeURIComponent(match[1]) : '';
}

export const baseQuery = fetchBaseQuery({
  baseUrl: process.env.NEXT_PUBLIC_BE_ADMIN_API_URL,
  credentials: 'include',
  prepareHeaders: (headers, { getState }) => {
    headers.set('Be-csrftoken', extractToken(document.cookie));
    const customer = (getState() as RootState).dashboard.customer;
    if (customer) {
      headers.set('x-active-customer-id', customer?.id.toString());
    }
    return headers;
  },
});

export const customBaseQuery: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const result = await baseQuery(args, api, extraOptions);
  const redirectKeywordExists = Router.asPath.includes('redirect=');
  if (
    result.error &&
    'status' in result.error &&
    result.error.status === 401 &&
    Router.pathname !== '/login' &&
    !redirectKeywordExists
  ) {
    localStorage.removeItem('sessionPopupDismissed');
    await Router.push('/logout?redirect=' + encodeURIComponent(Router.asPath));
  }

  return result;
};
