import { expect } from '@playwright/test';

//const axios = require('axios');
//const fetch = require('node-fetch');

export const userTestCases = [
  // 1 description: 'Retrieve user info'
  {
    description: 'Retrieve user info',
    requestConfig: {
      method: 'GET',
      url: 'https://test.pivotol.ai/api/v0/users/me',
    },
    expectedStatus: 200,
    validate: (response) => {
      expect(response).toHaveProperty('username', 'test');
      expect(response).toHaveProperty('email', expect.any(String));
    },
  },
  // 2 description: 'Update user preferences'
  {
    description: 'Update user preferences',
    requestConfig: {
      method: 'POST',
      url: 'https://test.pivotol.ai/api/v0/users/preference',
      headers: { 'Content-Type': 'application/json' },
      body: {
        preferences: {
          DATE_FORMAT: 'DD-MM-YYYY',
          CURRENCY: 'INR',
          DEFAULT_CUSTOMER: '1',
        },
      },
    },
    expectedStatus: 204,
    validate: (response) => {
      expect(response).toBeNull();
    },
  },
  // 3 description: 'Retrieve user preferences'
  {
    description: 'Retrieve user preferences',
    requestConfig: {
      method: 'GET',
      url: 'https://test.pivotol.ai/api/v0/users/preference',
    },
    expectedStatus: 200,
    validate: (response) => {
      expect(response).toHaveProperty('preferences');
      expect(response.preferences).toEqual(expect.any(Object)); // Ensures preferences is an object
      console.log('User preferences:', response.preferences);
    },
  },
  // 4 description: 'Create a new user or handle if user already exists'
  {
    description: 'Create a new user or handle if user already exists',
    requestConfig: {
      method: 'POST',
      url: 'https://test.pivotol.ai/api/v0/users',
      headers: { 'Content-Type': 'application/json' },
      body: {
        username: 'customer_user',
        password: 'asdfasdf',
        first_name: 'Just',
        last_name: 'Customer',
        scoped_roles: [
          {
            role: 'USER',
            customer_ids: [1],
          },
        ],
        email: '<EMAIL>',
      },
    },
    expectedStatus: 201,
    validate: async (response, requestConfig, authTokens) => {
      console.log('Response received for user creation:', response);

      if (response.conflict) {
        console.warn('User already exists. Verifying in the existing list of users.');

        try {
          const listRequestOptions = {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${authTokens}`, // Use token for authentication
            },
            redirect: 'follow',
          };

          const listResponse = await fetch(
            'https://test.pivotol.ai/api/v0/users?limit=100',
            listRequestOptions,
          ).then((res) => {
            if (!res.ok) {
              console.warn(`Failed to fetch user list. Status: ${res.status}`);
              return null; // Handle failure gracefully
            }
            return res.json();
          });

          if (!listResponse) {
            console.warn('User list could not be fetched. Skipping verification.');
          } else {
            const existingUser = listResponse.items.find(
              (user) => user.username === 'customer_user',
            );
            expect(existingUser).toBeDefined();
            expect(existingUser).toHaveProperty('username', 'customer_user');
            console.log('User confirmed in the existing list:', existingUser);
          }
        } catch (error) {
          console.error('Error while verifying existing user:', error.message);
          // Log error without throwing to allow other tests to continue
        }
      } else {
        expect(response).toHaveProperty('id', expect.any(Number));
        expect(response).toHaveProperty('username', 'customer_user');
      }
    },
  },
];
