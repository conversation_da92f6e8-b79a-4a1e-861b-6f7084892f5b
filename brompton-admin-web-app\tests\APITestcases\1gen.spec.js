const { test, expect, request } = require('@playwright/test');

async function apiTest({ method, url, headers, payload, expectedStatus, expectedContent }) {
  // Send API request based on the provided method
  const response = await request[method.toLowerCase()](url, {
    headers,
    data: payload,
  });

  // Log response status and body for debugging
  console.log(`Status: ${response.status()}`);
  const responseBody = await response.text();
  console.log(`Response Body: ${responseBody}`);

  // Validate the response status
  expect(response.status()).toBe(expectedStatus);

  // If specific content is expected in the response, check for it
  if (expectedContent) {
    expect(responseBody).toContain(expectedContent);
  }

  // Parse JSON response only if expected status indicates a successful response
  if (expectedStatus === 200 || expectedStatus === 201) {
    return JSON.parse(responseBody);
  }

  return responseBody;
}

// Generic test case
test.describe('Generic API Test Suite', () => {
  test('POST /assets-backoffice/asset-types - create asset type', async ({ request }) => {
    const headers = {
      'BE-CsrfToken': 'CUnoHH49TZxEya4km6MHjPzjE0Fd10LSOuRcyrX22mI=',
      'Content-Type': 'application/json',
      Cookie:
        'BE-AccessToken=<your_access_token_here>; BE-CSRFToken=CUnoHH49TZxEya4km6MHjPzjE0Fd10LSOuRcyrX22mI%3D',
    };

    const payload = {
      name: 'Rocket Engine',
    };

    await apiTest({
      method: 'POST',
      url: 'https://test.brompton.ai/api/v0/assets-backoffice/asset-types',
      headers,
      payload,
      expectedStatus: 201,
      expectedContent: '"name":"Rocket Engine"', // Replace with actual expected content in response
    });
  });

  test('POST /customers/84/dashboards - create/update dashboard', async ({ request }) => {
    const headers = {
      'BE-CsrfToken': 'rvI8VKVKSQdSn7K2r4d8FEWHn8I21sv1el6iDlF8Jlg=',
      'Content-Type': 'application/json',
      Cookie:
        'BE-AccessToken=<your_access_token_here>; BE-CSRFToken=rvI8VKVKSQdSn7K2r4d8FEWHn8I21sv1el6iDlF8Jlg%3D',
    };

    const payload = {
      title: 'Overview',
      data: '{"currentDashboardId":89,"dashboardTitle":"Overview", ...}', // Truncated for readability
    };

    await apiTest({
      method: 'POST',
      url: 'https://test.brompton.ai/apiv0/customers/84/dashboards',
      headers,
      payload,
      expectedStatus: 200,
      expectedContent: 'expected_content_here', // Replace with actual expected content in response
    });
  });

  test('POST /users - create a new user', async ({ request }) => {
    const headers = {
      'BE-CsrfToken': 'OHi34V+PkJef3vbYUN4Xe9t35klLsZ4Pss4dZBfa3Q4=',
      'Content-Type': 'application/json',
      Cookie:
        'BE-AccessToken=<your_access_token_here>; BE-CSRFToken=OHi34V%2BPkJef3vbYUN4Xe9t35klLsZ4Pss4dZBfa3Q4%3D',
    };

    const payload = {
      username: 'customer_user',
      password: 'asdfasdf',
      first_name: 'Just',
      last_name: 'Customer',
      scoped_roles: [
        {
          role: 'USER',
          cusotmer_ids: [1],
        },
      ],
      email: '<EMAIL>',
    };

    await apiTest({
      method: 'POST',
      url: 'https://test.brompton.ai/api/v0/users',
      headers,
      payload,
      expectedStatus: 201,
      expectedContent: '"username":"customer_user"', // Adjust to actual response content
    });
  });
});
