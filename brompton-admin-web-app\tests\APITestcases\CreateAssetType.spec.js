const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite', () => {
  test('POST /assets-backoffice/asset-types creates a new asset type successfully', async ({
    request,
  }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': 'CUnoHH49TZxEya4km6MHjPzjE0Fd10LSOuRcyrX22mI=',
      'Content-Type': 'application/json',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDExOSwxMDYsMTE4LDEwOCw4NiwxMTEsMTEyLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI3LDEyMywxMjYsMTI0LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMTksMTA2LDExOCwxMDgsODYsMTExLDExMiw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNywxMjMsMTI2LDEyNCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTY3MTExLCJleHAiOjE3MzE1NzQzMTF9.JsPv4jroIa7lDKBpslUY3Xi_qxY1by7M77X_AdwFbVo; BE-CSRFToken=CUnoHH49TZxEya4km6MHjPzjE0Fd10LSOuRcyrX22mI%3D',
    };

    // Define request body
    const body = {
      name: 'Rocket Engine',
    };

    // Make POST request
    const response = await request.post(
      'https://test.brompton.ai/api/v0/assets-backoffice/asset-types',
      {
        headers: headers,
        data: body,
      },
    );

    // Log response status and body for debugging
    console.log(`Status: ${response.status()}`);
    const responseBody = await response.text(); // Use `.text()` to capture non-JSON error messages as well
    console.log(`Response Body: ${responseBody}`);

    // Check response status
    expect(response.status()).toBe(201); // Assuming 201 Created indicates successful creation

    // Verify response body if needed
    if (response.status() === 201) {
      const jsonResponse = JSON.parse(responseBody); // Parse JSON only if status is 201
      expect(jsonResponse).toHaveProperty('id'); // Check that the new asset type has an 'id'
      expect(jsonResponse.name).toBe('Rocket Engine'); // Verify that the name matches
    }
  });
});
