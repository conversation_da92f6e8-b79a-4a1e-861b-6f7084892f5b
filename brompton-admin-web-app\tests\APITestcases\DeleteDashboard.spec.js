const { test, expect } = require('@playwright/test');

test.describe('API Testing for DELETE Dashboard', () => {
  test('should successfully delete the specified dashboard with status 200', async ({
    request,
  }) => {
    // Define headers
    const headers = {
      'BE-CsrfToken': 'Yf9Bn/G/kuc2LkOCTqv+hm8/aTbVVsXCIPbx/dzOzao=',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdLCJVU0VSIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdLCJQT1dFUl9VU0VSIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTA5NTE3LCJleHAiOjE3MzE1MTY3MTd9.-zqZuiBFWTGu1WBJNCc8BIaD0wYuGr_DxUWIOTNJDLo; BE-CSRFToken=Yf9Bn%2FG%2Fkuc2LkOCTqv%2Bhm8%2FaTbVVsXCIPbx%2FdzOzao%3D',
    };

    // Replace :customerId with actual customer ID in URL
    const customerId = '8'; // Example customer ID
    const dashboardId = '128'; // Example dashboard ID
    const url = `https://test.brompton.ai/api/v0/customers/${customerId}/dashboards/${dashboardId}`;

    // Send DELETE request
    const response = await request.delete(url, { headers });

    // Validate the response status is 200, indicating success
    expect(response.status()).toBe(200);

    // Optionally, check the response body if there is any message or confirmation
    const responseBody = await response.text();
    console.log('Response:', responseBody);
    expect(responseBody).toContain('expected_content_here'); // Adjust based on actual response content
  });
});
