const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite', () => {
  test('GET /customers/apple returns customer data successfully', async ({ request }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': '9sXG/ZKkajj2LmAkQTGxKRc94dGlb/oJPih65I6VFOc=',
      'Content-Type': 'application/json',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDExOSwxMDYsMTE4LDEwOCw4NiwxMTEsMTEyLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI3LDEyMywxMjYsMTI0LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMTksMTA2LDExOCwxMDgsODYsMTExLDExMiw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNywxMjMsMTI2LDEyNCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTYyOTQ4LCJleHAiOjE3MzE1NzAxNDh9.RcD9sd0mPeWLZ2oP17_cuIInfhnpoNxtCpR5ooRmR3A; BE-CSRFToken=9sXG%2FZKkajj2LmAkQTGxKRc94dGlb%2FoJPih65I6VFOc%3D',
    };

    // Make GET request
    const response = await request.get('https://test.brompton.ai/api/v0/customers/apple', {
      headers: headers,
    });

    // Check response status
    expect(response.status()).toBe(200);

    // Verify response body
    const responseBody = await response.json();
    console.log(responseBody);

    // Perform assertions on the actual response data
    expect(responseBody).toHaveProperty('id', 128);
    expect(responseBody).toHaveProperty('name', 'Apple');
    expect(responseBody).toHaveProperty('name_id', 'apple');
    expect(responseBody).toHaveProperty('address', 'Palo Altro');
  });
});
