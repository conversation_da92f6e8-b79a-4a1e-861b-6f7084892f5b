// tests/api.test.js
const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite', () => {
  test('GET /customers/8/assets/348/measurements/22329/location retrieves location data successfully', async ({
    request,
  }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': 'Y+Hn/0hF93VzC3JOnbB8/Ks8Tbk+Ochm1fyiQqDv1r0=',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDExOSwxMDYsMTE4LDEwOCw4NiwxMTEsMTEyLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI3LDEyMywxMjYsMTI0LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMTksMTA2LDExOCwxMDgsODYsMTExLDExMiw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNywxMjMsMTI2LDEyNCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTY1ODcyLCJleHAiOjE3MzE1NzMwNzJ9.Tqk1YziB9AKGZDBojMTn6-s30GKjxUKkupqvm_ysjgg; BE-CSRFToken=Y%2BHn%2F0hF93VzC3JOnbB8%2FKs8Tbk%2BOchm1fyiQqDv1r0%3D',
    };

    // Make GET request
    const response = await request.get(
      'https://test.brompton.ai/api/v0/customers/8/assets/348/measurements/22329/location',
      {
        headers: headers,
      },
    );

    // Check response status
    expect(response.status()).toBe(200);

    // Verify response body if needed
    const responseBody = await response.json();
    console.log(responseBody);

    // Perform assertions on the response data (adjust based on expected structure)
    expect(responseBody).toHaveProperty('location'); // Adjust this based on actual response structure
    expect(responseBody.location).toBeInstanceOf(Object); // Check if 'location' is an object
  });
});
