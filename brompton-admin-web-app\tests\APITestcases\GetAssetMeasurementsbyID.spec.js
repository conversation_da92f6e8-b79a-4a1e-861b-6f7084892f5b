// tests/api.test.js
const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite', () => {
  test('GET /customers/1/assets/50/measurements/14661 retrieves measurement data successfully', async ({
    request,
  }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': 'xL+J0OZ8ZsXyTRqX9MYCn3aPWFovXl7evSMkiD8+IyA=',
      'Content-Type': 'application/json',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDExOSwxMDYsMTE4LDEwOCw4NiwxMTEsMTEyLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI3LDEyMywxMjYsMTI0LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMTksMTA2LDExOCwxMDgsODYsMTExLDExMiw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNywxMjMsMTI2LDEyNCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTY0NjUxLCJleHAiOjE3MzE1NzE4NTF9.MppdwqYGaKxHjRUvoZrP8DKyx7i3fjMmnWJRMGAnu6k; BE-CSRFToken=xL%2BJ0OZ8ZsXyTRqX9MYCn3aPWFovXl7evSMkiD8%2BIyA%3D',
    };

    // Make GET request
    const response = await request.get(
      'https://test.brompton.ai/api/v0/customers/1/assets/50/measurements/14661',
      {
        headers: headers,
      },
    );

    // Check response status
    expect(response.status()).toBe(200);

    // Verify response body if needed
    const responseBody = await response.json();
    console.log(responseBody);

    // Perform assertions on the response data (adjust based on expected structure)
    expect(responseBody).toHaveProperty('data'); // Adjust this based on the actual response format
    expect(responseBody.data).toBeInstanceOf(Object); // Check if 'data' is an object, assuming it contains measurement data
  });
});
