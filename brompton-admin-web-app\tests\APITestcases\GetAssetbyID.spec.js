const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite', () => {
  test('GET /customers/8/assets/2 returns asset data successfully', async ({ request }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': '83PypQuFBRWVFjq7OF8FTVVx0NjV2ih7MyFZOp1g74I=',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDExOSwxMDYsMTE4LDEwOCw4NiwxMTEsMTEyLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI3LDEyMywxMjYsMTI0LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMTksMTA2LDExOCwxMDgsODYsMTExLDExMiw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNywxMjMsMTI2LDEyNCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTYzMzUwLCJleHAiOjE3MzE1NzA1NTB9.vha0fZph_22kEiFeRas0ZZs5HHZBUPu_iU5I9ulsSAo; BE-CSRFToken=83PypQuFBRWVFjq7OF8FTVVx0NjV2ih7MyFZOp1g74I%3D',
    };

    // Make GET request
    const response = await request.get('https://test.brompton.ai/api/v0/customers/8/assets/2', {
      headers: headers,
    });

    // Check response status
    expect(response.status()).toBe(200);

    // Verify response body
    const responseBody = await response.json();
    console.log(responseBody);

    // Perform assertions on response data (adjust based on expected response structure)
    expect(responseBody).toHaveProperty('id', 2); // Example check for asset ID
    expect(responseBody).toHaveProperty('customer_id', 8); // Example check for customer ID
  });
});
