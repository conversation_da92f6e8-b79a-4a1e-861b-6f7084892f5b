// tests/api.test.js
const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite', () => {
  test('GET /customers/8/users returns successful response', async ({ request }) => {
    // Set headers as required
    const headers = {
      'BE-CsrfToken': 'YcDklQW8b23SWSB05sEXbvD5WIKhg1wrdPeyhftEEjQ=',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDExOSwxMDYsMTE4LDEwOCw4NiwxMTEsMTEyLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI3LDEyMywxMjYsMTI0LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMTksMTA2LDExOCwxMDgsODYsMTExLDExMiw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNywxMjMsMTI2LDEyNCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTYyNDExLCJleHAiOjE3MzE1Njk2MTF9.lSbq6TyBi78fXhSfEfgWTA7y-ZtdRa5kFPnnRJWoqm0; BE-CSRFToken=YcDklQW8b23SWSB05sEXbvD5WIKhg1wrdPeyhftEEjQ%3D',
    };

    // Make the request
    const response = await request.get('https://test.brompton.ai/api/v0/customers/8/users', {
      headers: headers,
    });

    // Verify response status is 200 OK
    expect(response.status()).toBe(200);

    // Verify response body content if needed
    const responseBody = await response.json();
    console.log(responseBody);

    // Perform any other assertions on the response data if required
    expect(responseBody).toHaveProperty('data'); // Adjust based on expected structure
  });
});
