const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite - Login', () => {
  test('POST /sessions - should successfully create a session', async ({ request }) => {
    // Set headers
    const headers = {
      'Content-Type': 'application/json',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDEwNiwxMTgsODYsMTExLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI2LDExOSwxMTIsMTI3LDEyMywxMjQsMTA4LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMDYsMTE4LDg2LDExMSw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNiwxMTksMTEyLDEyNywxMjMsMTI0LDEwOCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTA2LDExOCw4NiwxMTEsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjYsMTE5LDExMiwxMjcsMTIzLDEyNCwxMDgsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTgzNDc5LCJleHAiOjE3MzE1OTA2Nzl9.sBcwQNlxiVLrd2L6v45QjwPVERaIWSwzsBUINTZdfsU; BE-CSRFToken=ecIB5u1MR1fD25E7j4dBex5%2FSPR%2FFTfnU4LlvChy3jU%3D',
    };

    // Define request body
    const body = {
      username: 'test',
      password: 'asdfasdf',
    };

    // Make POST request
    const response = await request.post('https://test.brompton.ai/api/v0/sessions', {
      headers: headers,
      data: body,
    });

    // Log response status and body for debugging
    console.log(`Status: ${response.status()}`);
    const responseBody = await response.text();
    console.log(`Response Body: ${responseBody}`);

    // Check response status
    expect(response.status()).toBe(201); // Expect 201 Created

    // Verify response body if needed
    const jsonResponse = JSON.parse(responseBody);
    expect(jsonResponse).toHaveProperty('access_token'); // Check that the response contains an access_token property
    expect(jsonResponse.access_token).not.toBeNull(); // Check that access_token is not null or undefined
    expect(jsonResponse).toHaveProperty('csrf_token');
    expect(jsonResponse.csrf_token).not.toBeNull();
  });
});
