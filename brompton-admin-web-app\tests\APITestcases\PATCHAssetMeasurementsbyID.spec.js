// tests/api.test.js
const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite', () => {
  test('PATCH /customers/1/assets/50/measurements/14664 updates meter factor successfully', async ({
    request,
  }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': 'DPu1JZ/AW21pqSb9XwwmHJigCIv1H6K6bZBHxBH4ZQA=',
      'Content-Type': 'application/json',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDExOSwxMDYsMTE4LDEwOCw4NiwxMTEsMTEyLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI3LDEyMywxMjYsMTI0LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMTksMTA2LDExOCwxMDgsODYsMTExLDExMiw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNywxMjMsMTI2LDEyNCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTY0ODc3LCJleHAiOjE3MzE1NzIwNzd9.YVfThFjn3mj3yxDFL680XRZqZoMgqWpWBXW6Hk2_vzg; BE-CSRFToken=DPu1JZ%2FAW21pqSb9XwwmHJigCIv1H6K6bZBHxBH4ZQA%3D',
    };

    // Define request body
    const body = {
      meter_factor: 2,
    };

    // Make PATCH request
    const response = await request.patch(
      'https://test.brompton.ai/api/v0/customers/1/assets/50/measurements/14664',
      {
        headers: headers,
        data: body,
      },
    );

    // Check response status
    expect(response.status()).toBe(200);

    // Verify response body if needed
    const responseBody = await response.json();
    console.log(responseBody);

    // Perform assertions to confirm the meter factor was updated
    expect(responseBody).toHaveProperty('meter_factor', 2);
  });
});
