// tests/api.test.js
const { test, expect, request } = require('@playwright/test');

test.describe('API Test Suite', () => {
  test('PATCH /customers/1/users/5 updates user successfully', async ({ request }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': 'Vhq++a5DWKhox13ugna3eRbl2OuJHMMa2xoAaHN1Dc4=',
      'Content-Type': 'application/json',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDExOSwxMDYsMTE4LDEwOCw4NiwxMTEsMTEyLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI3LDEyMywxMjYsMTI0LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMTksMTA2LDExOCwxMDgsODYsMTExLDExMiw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNywxMjMsMTI2LDEyNCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTYyNjk2LCJleHAiOjE3MzE1Njk4OTZ9.h1JLAKJZQP2z8FYug67jtbXS7lu6UwuXDIpP4FDQcCE; BE-CSRFToken=Vhq%2B%2Ba5DWKhox13ugna3eRbl2OuJHMMa2xoAaHN1Dc4%3D',
    };

    // Define request body
    const body = {
      first_name: '<PERSON>',
    };

    // Make PATCH request
    const response = await request.patch('https://test.brompton.ai/api/v0/customers/1/users/5', {
      headers: headers,
      data: body,
    });

    // Check response status
    expect(response.status()).toBe(200);

    // Verify response body if needed
    const responseBody = await response.json();
    console.log(responseBody);

    // Assert the response contains the updated first name
    expect(responseBody).toHaveProperty('first_name', 'Mario');
  });
});
