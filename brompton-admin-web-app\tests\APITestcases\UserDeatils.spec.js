const { test, expect } = require('@playwright/test');

test.describe('API Testing for GET User Details', () => {
  test('should return user details with status 200', async ({ request }) => {
    // Define headers
    const headers = {
      'BE-CSRFToken': 'GBBmrcqsEnUMNBhKwuPtzlBlXKk4v4i8wYRQMzMldoI=',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdLCJVU0VSIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdLCJQT1dFUl9VU0VSIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTA3MTM5LCJleHAiOjE3MzE1MTQzMzl9.K6WYDasw-CKZC_H-fN5ZvaKSLZJl3ZmyFufHo35fnNk; BE-CSRFToken=GBBmrcqsEnUMNBhKwuPtzlBlXKk4v4i8wYRQMzMldoI%3D',
    };

    // Send GET request
    const response = await request.get('https://test.brompton.ai/api/v0/users/me', { headers });

    // Validate the response status is 200
    expect(response.status()).toBe(200);

    // Check that the response body contains user data
    const responseBody = await response.text();
    console.log('Response:', responseBody);
    expect(responseBody).not.toBe('');
  });
});
