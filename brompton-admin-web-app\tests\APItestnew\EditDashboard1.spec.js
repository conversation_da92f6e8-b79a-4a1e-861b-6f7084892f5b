const { test, expect } = require('@playwright/test');

test.describe('API Test Suite - Update Dashboard', () => {
  test('PATCH /customers/8/dashboards/58 - should update the dashboard successfully', async ({
    request,
  }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': 'ecIB5u1MR1fD25E7j4dBex5/SPR/FTfnU4LlvChy3jU=',
      'Content-Type': 'application/json',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDEwNiwxMTgsODYsMTExLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI2LDExOSwxMTIsMTI3LDEyMywxMjQsMTA4LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMDYsMTE4LDg2LDExMSw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNiwxMTksMTEyLDEyNywxMjMsMTI0LDEwOCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTA2LDExOCw4NiwxMTEsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjYsMTE5LDExMiwxMjcsMTIzLDEyNCwxMDgsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTgzNDc5LCJleHAiOjE3MzE1OTA2Nzl9.sBcwQNlxiVLrd2L6v45QjwPVERaIWSwzsBUINTZdfsU; BE-CSRFToken=ecIB5u1MR1fD25E7j4dBex5%2FSPR%2FFTfnU4LlvChy3jU%3D',
    };

    // Define request body
    const body = {
      data: {
        currentDashboardId: 65,
        dashboardTitle: 'animated dashboard',
        userDetails: {
          id: 2,
          username: 'test',
          email: '<EMAIL>',
          enabled: true,
          first_name: 'Dev',
          last_name: 'Test',
          global_role: 'ADMIN',
          scoped_roles: [],
        },
        customer: {
          nameId: 'brompton_energy_inc_',
          id: 8,
          name: 'Brompton Energy Inc.',
          address: 'Houston, TX',
        },
        userPreferences: {
          CURRENCY: 'EUR',
          DATE_FORMAT: 'DD-MM-YYYY HH:mm:ss',
          DEFAULT_CUSTOMER: '8',
        },
        dashboardCrumb: [],
        mainPanel: 'chart',
        isLeftPanelOpen: true,
        isDirty: false,
        kisok: true,
        fullScreen: true,
        rightSideBar: false,
        dateFormat: 0,
        rightSideBarActiveTab: '/icons/alerts.svg',
        topPanel: {
          isVisible: true,
          timeRangeType: 6,
          refreshInterval: 60000,
          samplePeriod: 2,
          assetTz: true,
        },
        tree: {
          currentSelectedNodeId: 'm:257:11286',
          selectedNodeIds: ['-1', 'm:257:11290', 'm:257:11286'],
          expandedNodeIds: ['-1', '257:23', '258', '257'],
          selectedViewMeasureId: '-1',
          dbMeasureIdToName: {
            11286: 'Brenes\\MAINPANEL\\AVERAGE_PHASE_VOLTAGE',
            11290: 'Brenes\\MAINPANEL\\AVERAGE_LINE_VOLTAGE',
          },
        },
        chart: {
          startDate: 1715062366072,
          endDate: 1715083966072,
        },
        widget: {
          widgets: [
            {
              id: '1',
              type: 'stats',
              settings: {
                title: {
                  value: 'Average Line Voltage',
                  isVisible: true,
                },
                aggBy: 1,
                samplePeriod: 2,
                selectedDbMeasureId: '11290',
                showMin: false,
                showMax: false,
                showAvg: true,
                showDelta: false,
                showTotal: false,
                isValid: true,
              },
            },
            {
              id: '5',
              type: 'solar_panel',
              settings: {
                title: {
                  value: 'Title',
                  isVisible: false,
                },
                isEditable: false,
                fontSize: 12,
                svgTexts: [
                  {
                    title: 'Real Power Volume',
                    position: { x: 100, y: 20 },
                    id: 'REAL_POWER_VOLUME',
                  },
                  { title: 'Batter Volume', position: { x: 550, y: 20 }, id: 'BATTERY_VOLUME' },
                  {
                    title: 'Solar Panel Volume,',
                    position: { x: 10, y: 240 },
                    id: 'SOLAR_PANEL_VOLUME',
                  },
                  {
                    title: 'Grid Panel Volume,',
                    position: { x: 550, y: 190 },
                    id: 'GRID_PANEL_VOLUME',
                  },
                ],
                isValid: true,
              },
            },
          ],
          widgetLayout: [
            { w: 4, h: 2, x: 3, y: 18, i: '1', moved: false, static: false },
            { w: 11, h: 18, x: 0, y: 0, i: '5', moved: false, static: false },
          ],
          lastWidgetId: 5,
          deleteWidgets: [],
        },
      },
      title: 'animated dashboard',
    };

    // Make PATCH request
    const response = await request.patch(
      'https://test.brompton.ai/api/v0/customers/8/dashboards/58',
      {
        headers: headers,
        data: body,
      },
    );

    // Log response status and body for debugging
    console.log(`Status: ${response.status()}`);
    const responseBody = await response.text();
    console.log(`Response Body: ${responseBody}`);

    // Check response status
    expect(response.status()).toBe(200); // Assuming 200 indicates success

    // Verify response body if needed
    const jsonResponse = JSON.parse(responseBody);

    // Perform assertions on the response data (customize based on actual API response structure)
    expect(jsonResponse).toHaveProperty('id'); // Assuming the response contains 'id'
    expect(jsonResponse.title).toBe('animated dashboard'); // Verify title
  });
});
