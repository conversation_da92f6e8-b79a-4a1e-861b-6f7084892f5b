const { test, expect } = require('@playwright/test');

test.describe('API Test Suite - Get Customer Logo', () => {
  test('GET /customers/85/logo - should retrieve the customer logo successfully', async ({
    request,
  }) => {
    // Set headers
    const headers = {
      'BE-CsrfToken': 'ecIB5u1MR1fD25E7j4dBex5/SPR/FTfnU4LlvChy3jU=',
      Authorization: 'Basic Og==',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbMTI4LDgsMTIxLDEwNiwxMTgsODYsMTExLDg1LDEwNyw4NCwxMTMsMTE0LDExNywxMjAsMTI2LDExOSwxMTIsMTI3LDEyMywxMjQsMTA4LDEwOSwxMjUsMTIyLDExMCw5LDgyXSwiVVNFUiI6WzEyOCw4LDEyMSwxMDYsMTE4LDg2LDExMSw4NSwxMDcsODQsMTEzLDExNCwxMTcsMTIwLDEyNiwxMTksMTEyLDEyNywxMjMsMTI0LDEwOCwxMDksMTI1LDEyMiwxMTAsOSw4Ml0sIlBPV0VSX1VTRVIiOlsxMjgsOCwxMjEsMTA2LDExOCw4NiwxMTEsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjYsMTE5LDExMiwxMjcsMTIzLDEyNCwxMDgsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTgzNDc5LCJleHAiOjE3MzE1OTA2Nzl9.sBcwQNlxiVLrd2L6v45QjwPVERaIWSwzsBUINTZdfsU; BE-CSRFToken=ecIB5u1MR1fD25E7j4dBex5%2FSPR%2FFTfnU4LlvChy3jU%3D',
    };

    // Make GET request
    const response = await request.get('https://test.brompton.ai/api/v0/customers/85/logo', {
      headers: headers,
    });

    // Log response status and body for debugging
    console.log(`Status: ${response.status()}`);
    const responseBody = await response.text();
    console.log(`Response Body: ${responseBody}`);

    // Check response status
    expect(response.status()).toBe(200); // Assuming 200 indicates successful retrieval

    // Verify response content if needed
    expect(responseBody).toContain('logo'); // Customize based on actual logo response structure or content
  });
});
