const { test, expect } = require('@playwright/test');

// Function to authenticate and retrieve tokens
async function authenticate(request) {
  const authResponse = await request.post('https://test.brompton.ai/api/v0/sessions', {
    headers: { 'Content-Type': 'application/json' },
    data: { username: 'test', password: 'asdfasdf' },
  });

  const authData = await authResponse.json();
  const accessToken = authData.access_token;
  const csrfToken = authData.csrf_token;

  if (!accessToken || !csrfToken) {
    throw new Error('Authentication failed: No access or CSRF token returned');
  }

  return { accessToken, csrfToken };
}

// Generic API request function
async function makeApiRequest(
  request,
  authTokens,
  { method, url, headers = {}, body = null },
  expectedStatus,
  expectedProps,
) {
  const response = await request[method.toLowerCase()](url, {
    headers: {
      ...headers,
      'BE-CsrfToken': authTokens.csrfToken,
      'Content-Type': 'application/json',
      Cookie: `BE-AccessToken=${authTokens.accessToken}`,
    },
    data: body,
  });

  // Verify status code
  expect(response.status()).toBe(expectedStatus);

  // Parse and verify response body
  if (expectedProps) {
    const responseBody = await response.json();
    for (const [key, value] of Object.entries(expectedProps)) {
      expect(responseBody).toHaveProperty(key, value);
    }
    return responseBody;
  }
  return response;
}

test.describe('API Test Suite', () => {
  let authTokens;

  test.beforeAll(async ({ request }) => {
    authTokens = await authenticate(request);
  });

  const apiEndpoints = [
    {
      name: 'Create Session',
      method: 'POST',
      url: 'https://test.brompton.ai/api/v0/sessions',
      body: { username: 'test', password: 'asdfasdf' },
      expectedStatus: 201,
      expectedProps: { access_token: expect.any(String), csrf_token: expect.any(String) },
    },
    {
      name: 'Get User Info',
      method: 'GET',
      url: 'https://test.brompton.ai/api/v0/users/me',
      expectedStatus: 200,
      expectedProps: { username: 'test', email: expect.any(String) },
    },
    {
      name: 'Update Dashboard',
      method: 'PATCH',
      url: 'https://test.brompton.ai/api/v0/customers/8/dashboards/58',
      body: {
        data: { currentDashboardId: 65, dashboardTitle: 'animated dashboard' },
        title: 'animated dashboard',
      },
      expectedStatus: 200,
      expectedProps: { id: expect.any(Number), title: 'animated dashboard' },
    },
    {
      name: 'Retrieve Customer Logo',
      method: 'GET',
      url: 'https://test.brompton.ai/api/v0/customers/85/logo',
      expectedStatus: 200,
      expectedProps: null,
    },
    {
      name: 'Delete Measurement',
      method: 'DELETE',
      url: 'https://test.brompton.ai/api/v0/customers/1/assets/2/measurements/9500',
      expectedStatus: 204,
      expectedProps: null,
    },
  ];

  apiEndpoints.forEach(({ name, method, url, body, expectedStatus, expectedProps }) => {
    test(`${method} ${name}`, async ({ request }) => {
      await makeApiRequest(
        request,
        authTokens,
        { method, url, body },
        expectedStatus,
        expectedProps,
      );
    });
  });
});
