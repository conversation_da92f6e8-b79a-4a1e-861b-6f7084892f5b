/**
 * Authenticate and retrieve tokens
 */
const { test, expect } = require('@playwright/test');
const { console } = require('inspector');

async function authenticate(request) {
  try {
    const authResponse = await request.post('https://test.brompton.ai/api/v0/sessions', {
      headers: { 'Content-Type': 'application/json' },
      data: { username: 'test', password: 'asdfasdf' },
    });

    const status = authResponse.status();
    const responseBody = await authResponse.text();

    console.log('Authentication Response:', { status, responseBody });

    if (![200, 201].includes(status)) {
      throw new Error(`Authentication failed. Status: ${status}, Response: ${responseBody}`);
    }

    const authData = JSON.parse(responseBody);
    if (!authData.access_token || !authData.csrf_token) {
      throw new Error('Authentication tokens are missing in the response.');
    }

    return {
      accessToken: `BE-AccessToken=${authData.access_token}; BE-CSRFToken=${authData.csrf_token}`,
      csrfToken: authData.csrf_token,
    };
  } catch (error) {
    console.error('Error during authentication:', error.message);
    throw error;
  }
}

/**
 * Generic API request function with retry on 401 Unauthorized
 */

async function makeApiRequest(
  request,
  authTokens,
  { method, url, headers = {}, body = null },
  expectedStatus,
) {
  // const authTokens = await AuthTokens();
  console.log('authotoken -', authTokens);
  if (!authTokens || !authTokens.csrfToken || !authTokens.accessToken) {
    console.error('authTokens is invalid or undefined. Check authentication.');
    console.log('Returned authTokens:', authTokens);
    throw new Error('authTokens is missing or invalid. Ensure authentication succeeded.');
  }

  const requestOptions = {
    headers: {
      ...headers,
      'BE-CSRFToken': authTokens.csrfToken,
      Cookie: authTokens.accessToken,
    },
  };

  if (method.toUpperCase() !== 'GET' && body) {
    requestOptions.data = body;
  }

  try {
    let response = await request[method.toLowerCase()](url, requestOptions);

    if (response.status() === 401) {
      console.warn('401 Unauthorized - Re-authenticating...');
      authTokens = await authenticate(request);
      console.log('Re-authenticated successfully. New Tokens:', authTokens);

      requestOptions.headers['BE-CSRFToken'] = authTokens.csrfToken;
      requestOptions.headers['Cookie'] = authTokens.accessToken;

      response = await request[method.toLowerCase()](url, requestOptions);
    }

    const status = response.status();
    const responseBody = await response.text();

    if (status === 409) {
      console.warn(`Conflict detected at ${url}. Response: ${responseBody}`);
      const conflictData = JSON.parse(responseBody);
      return { conflict: true, conflictData };
    }

    if (status !== expectedStatus) {
      throw new Error(
        `Expected status ${expectedStatus}, got ${status}. Response: ${responseBody}`,
      );
    }

    return responseBody ? JSON.parse(responseBody) : null;
  } catch (error) {
    console.error('Error during API request:', error.message);
    throw error;
  }
}

/**
 * Data-driven test cases
 */
const testCases = [
  /*  {
        description: 'Retrieve user info',
        requestConfig: {
            method: 'GET',
            url: 'https://test.brompton.ai/api/v0/users/me',
        },
        expectedStatus: 200,
        validate: (response) => {
            expect(response).toHaveProperty('username', 'test');
            expect(response).toHaveProperty('email', expect.any(String));
        },
    },
    {
        description: 'Retrieve customer logo',
        requestConfig: { method: 'GET', url: 'https://test.brompton.ai/api/v0/customers/85/logo' },
        expectedStatus: 200,
        validate: (response) => {
            expect(response).toHaveProperty('logo', expect.any(String));
        },
    },
 */
  {
    description: 'Create or update a dashboard',
    requestConfig: {
      method: 'POST',
      url: 'https://test.brompton.ai/api/v0/customers/8/dashboards',
      headers: { 'Content-Type': 'application/json' },
      body: {
        title: 'Overview',
        data: JSON.stringify({ currentDashboardId: 89, dashboardTitle: 'Overview' }),
      },
    },
    expectedStatus: 201, // Expecting a "Created" response
    validate: async (response, request, authTokens) => {
      if (response.conflict) {
        console.warn('Dashboard already exists. Attempting to update...');

        // Retrieve all dashboards to find the existing one
        const dashboardsResponse = await makeApiRequest(
          request,
          authTokens,
          {
            method: 'GET',
            url: 'https://test.brompton.ai/api/v0/customers/8/dashboards',
          },
          200, // Expected status for retrieval
        );

        // Locate the conflicting dashboard by title
        const existingDashboard = dashboardsResponse.items.find(
          (dashboard) => dashboard.title === 'Overview',
        );

        if (!existingDashboard) {
          throw new Error('Cannot update dashboard: No matching dashboard found in the list.');
        }

        const existingDashboardId = existingDashboard.id;

        // Update the existing dashboard
        const updateResponse = await makeApiRequest(
          request,
          authTokens,
          {
            method: 'PATCH',
            url: `https://test.brompton.ai/api/v0/customers/8/dashboards/${existingDashboardId}`,
            headers: { 'Content-Type': 'application/json' },
            body: {
              title: 'Updated Overview',
              data: JSON.stringify({ currentDashboardId: 86, dashboardTitle: 'Updated Overview' }),
            },
          },
          200, // Expected status for update
        );

        console.log('Dashboard updated successfully:', updateResponse);
        expect(updateResponse).toHaveProperty('title', 'Updated Overview');
      } else {
        // Validate response for creation
        expect(response).toHaveProperty('id', expect.any(Number));
        expect(response).toHaveProperty('title', 'Overview');
      }
    },
  },

  /*  
    {
        description: 'Retrieve dashboards for a customer',
        requestConfig: { method: 'GET', url: 'https://test.brompton.ai/api/v0/customers/9/dashboards' },
        expectedStatus: 200,
        validate: (response) => {
            expect(response.items).toBeInstanceOf(Array);
            expect(response.total).toBeGreaterThanOrEqual(0);
        },
    },
    {
        description: 'Update user preferences',
        requestConfig: {
            method: 'POST',
            url: 'https://test.brompton.ai/api/v0/users/preference',
            headers: { "Content-Type": "application/json" },
            body: {
                preferences: {
                    DATE_FORMAT: "DD-MM-YYYY",
                    CURRENCY: "INR",
                    DEFAULT_CUSTOMER: "1",
                },
            },
        },
        expectedStatus: 204,
        validate: (response) => {
            expect(response).toBeNull();
        },
    },
    */
];

/**
 * Test suite
 */
test.describe('Generic API Test Suite', () => {
  let authTokens;

  test.beforeAll(async ({ request }) => {
    try {
      authTokens = await authenticate(request);
      if (!authTokens || !authTokens.csrfToken || !authTokens.accessToken) {
        throw new Error('Authentication failed. Tokens are missing or invalid.');
      }
      console.log('Authentication successful. Tokens:', authTokens);
    } catch (error) {
      console.error('Failed to authenticate:', error.message);
      throw error;
    }
  });

  for (const testCase of testCases) {
    test(testCase.description, async ({ request }) => {
      if (!authTokens || !authTokens.csrfToken || !authTokens.accessToken) {
        console.error('authTokens is undefined or invalid. Ensure authentication succeeded.');
        throw new Error('authTokens is undefined or invalid.');
      }

      const configWithAuth = {
        ...testCase.requestConfig,
        headers: {
          ...testCase.requestConfig.headers,
          'BE-CSRFToken': authTokens.csrfToken,
          Cookie: authTokens.accessToken,
        },
      };
      console.log('authTokens=', authTokens);
      try {
        const response = await makeApiRequest(
          request,
          authTokens,
          configWithAuth,
          testCase.expectedStatus,
        );
        testCase.validate(response);
      } catch (error) {
        console.error(`Test case failed: ${testCase.description}. Error: ${error.message}`);
        throw error;
      }
    });
  }
});
/*
test.describe('Generic API Test Suite', () => {
    let authTokens;

    // Authenticate before running tests
    test.beforeAll(async ({ request }) => {
        try {
            authTokens = await authenticate(request);

            if (!authTokens || !authTokens.csrfToken || !authTokens.accessToken) {
                console.error('Authentication failed. Tokens are missing or invalid.');
                throw new Error('authTokens is missing or invalid.');
            }

            console.log('Authentication successful. Tokens:', authTokens);
        } catch (error) {
            console.error('Failed to authenticate:', error.message);
            throw error; // Stop tests if authentication fails
        }
    });
});*/
