import { test, expect, Locator } from '@playwright/test';
test.beforeEach(async ({ page }) => {
  // opening the URL
  await page.goto('https://test.pivotol.ai/login'); // 60 seconds

  // Go to the username
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('asdfasdf');

  // click on Login Button
  await page.click('#__next > div > div > form > div > button');
  await page.waitForTimeout(10000);

  // new dashboard click
  await page.click('/html/body/div/div/div[2]/div[3]/button');
  await page.waitForTimeout(3000);
  //click on add dashboard
  await page.locator('id=widgets-icon').click();
  await page.waitForTimeout(2000);

  //drag the widget
  //await page.locator('#stats').dragTo(page.locator('.react-grid-layout.layout'));
  //await page.waitForTimeout(2000);
  await page.locator('#title').dragTo(page.locator('.react-grid-layout.layout'));
  await page.waitForTimeout(2000);
  //await page.locator('#table').dragTo(page.locator('.react-grid-layout.layout'));
  //await page.waitForTimeout(2000);
  //await page.getByRole('widget-settings-icon').click({ force: true });
  //await page.getByRole('button').click({ force: true });
  //wait for page
  await page.waitForTimeout(1000);
  //click on widget setting icon
  //await page.getByLabel('Options').click();
  //await page.locator('#save').click();
  await page.click('//*[@id="__next"]/div/div/form/div/a');
  await page.close();
});
