import { test, expect, locator } from '@playwright/test';
import { LoginDetails } from '../../POM/LoginDetails';

test('alertecreate', async ({ page }) => {
  //Login
  const Login1 = new LoginDetails(page);
  await Login1.lauchURL(); // launching the URL
  await Login1.login('test', 'asdfasdf'); // Valid Deatils

  // click on new dashboard
  await page.getByText('Add Dashboard').click();
  await page.waitForTimeout(3000);

  await page.getByLabel('Select Customer').click();
  // await page.getByRole('combobox', { name: 'Brompton Energy Inc.' }).click();
  await page.getByRole('option', { name: 'Brompton Energy Inc.', exact: true }).click();
  //page.getByTestId('ArrowDropDownIcon').nth(2);//.waitFor({ state: 'visible', timeout: 2000 });
  await page.waitForTimeout(2000);

  const d1 = page.locator('//*[@id="combo-box-demo"]');
  await d1.fill('today-12');
  await d1.click();
  //await d1.click();
  await page.waitForTimeout(5000);

  const c1 = page.locator('//*[@id=":rv:-m:509:15445"]/div/div/label/span[2]/div/p');
  await c1.click({ button: 'right' });

  /*  await page.locator('input[type="checkbox"]').first().click();
      console.log('Clicked the first checkbox!');

      const r1= page.locator('//*[@id=":rn:-m:509:15445"]/div/div/label/span[2]/div/p[1]');
      await r1.click({button: 'right'});

        await page.waitForTimeout(8000);
       //await page.click('//*[@id="basic-menu"]/div[3]/ul/li[4]');
      */
  await page.close();
});
