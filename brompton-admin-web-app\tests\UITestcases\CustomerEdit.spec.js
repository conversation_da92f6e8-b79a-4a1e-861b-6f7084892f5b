import { test, expect, locator } from '@playwright/test';
import { LoginDetails } from '../../POM/LoginDetails';

test('Login', async ({ page }) => {
  //Login
  const Login1 = new LoginDetails(page);
  await Login1.lauchURL(); // launching the URL
  await Login1.login('test', 'asdfasdf'); // Valid Deatils

  // click on new dashboard
  await page.getByText('Add Dashboard').click();
  await page.waitForTimeout(3000);
  //click on customer
  await page.click('//*[@id="__next"]/div/div[1]/div/ul/li[2]/a/div[2]/span');
  await page.waitForTimeout(2000);
  /*// create new customer
await page.click('//*[@id="__next"]/div/div[2]/p/button');
await page.getByLabel('Name id *').click();
await page.getByLabel('Name id *').fill('testautocust');
await page.getByLabel('Name *').click();
await page.getByLabel('Name *').fill('testautocust');
await page.getByLabel('Address *').click();
await page.getByLabel('Address *').fill('testauto');
await page.waitForSelector('.MuiInputBase-input.MuiOutlinedInput-input', { timeout: 10000 });
// select choose file
const fileInput = page.locator('input[type="file"]');
await fileInput.setInputFiles('C:\\Users\\<USER>\\Pictures\\Saved Pictures\\logo.jfif');
// click on add button
await page.getByRole('button', { name: 'Add' }).click();
await page.waitForTimeout(3000);
*/
  // search filed
  const searchFieldc = page.locator('input[placeholder="Search…"]'); // Use the appropriate selector
  await searchFieldc.fill('testautocust');
  await page.waitForTimeout(2000);

  // click on Edit button
  const edit = page.click(
    '//*[@id="__next"]/div/div[2]/div[2]/div/div[2]/div[2]/div/div/div[2]/div[5]/button[2]',
  );
  await page.waitForTimeout(2000);
  // Change the name
  await page.getByLabel('Name *').click();
  await page.getByLabel('Name *').fill('Martin mick');
  //Change the address
  await page.getByLabel('Address *').click();
  await page.getByLabel('Address *').fill('New York');
  //Click On Update Button
  await page.getByRole('button', { name: 'Update' }).click();
  await page.reload();
  // again check changes
  const searchFieldc1 = page.locator('input[placeholder="Search…"]'); // Use the appropriate selector
  await searchFieldc1.fill('testautocust');

  await page.waitForTimeout(5000);

  await page.close();
});
/*
test('CustomerEdit' , async ({page}) => {
 //Login 
 const Login2= new LoginDetails(page)
 await Login2.lauchURL(); // launching the URL
 await Login2.login('test', 'asdfasdf'); // Valid Deatils

//await Login1.customer1();
//await Login1.newcustomer();
await Login2.nameid('ChirsTest');
await Login2.name('Jack Will');
await Login2.address('New York');

await page.waitForSelector('.MuiInputBase-input.MuiOutlinedInput-input', { timeout: 10000 });
// select choose file
const fileInput = page.locator('input[type="file"]');
await fileInput.setInputFiles('C:\\Users\\<USER>\\Pictures\\Saved Pictures\\logo.jfif');
// click on add button
await page.getByRole('button', { name: 'Add' }).click();
await page.waitForTimeout(3000);


await page.close();
});
*/
