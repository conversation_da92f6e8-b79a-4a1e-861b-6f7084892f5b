const { test, expect } = require('@playwright/test');

test.describe('API Testing for GET Customer Logo', () => {
  test('should return a successful response with status 200', async ({ request }) => {
    // Define headers as per the request
    const headers = {
      'BE-CsrfToken': '772lprdimeVJMMntEHfsWSSxf2q0JEEBQS0AjEbM0lk=',
      Authorization: 'Basic Og==',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdLCJVU0VSIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdLCJQT1dFUl9VU0VSIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTA2NjI3LCJleHAiOjE3MzE1MTM4Mjd9.-_9EwUPcO7_zxOG7y6D1tR_4C0egre2cywtDd2341cI; BE-CSRFToken=772lprdimeVJMMntEHfsWSSxf2q0JEEBQS0AjEbM0lk%3D',
    };

    // Send GET request
    const response = await request.get('https://test.brompton.ai/api/v0/customers/85/logo', {
      headers,
    });

    // Validate the response status is 200
    expect(response.status()).toBe(200);

    // Check that the response body is not empty
    const responseBody = await response.text();
    console.log('Response:', responseBody);
    expect(responseBody).not.toBe('');
  });
});
