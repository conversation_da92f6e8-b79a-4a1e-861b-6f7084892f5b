const { test, expect } = require('@playwright/test');

test.describe('API Testing for GET Customer Logo', () => {
  test('should return a successful response with status 200', async ({ request }) => {
    // Define headers
    const headers = {
      'BE-CsrfToken': 'h9/0gN5YKJP3DcZuIhYKk62arf0EllUP7aVhJ1ndWNU=',
      Authorization: 'Basic Og==',
      Cookie:
        'BE-AccessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJzdWIiOjIsInJvbGVzIjp7IkFETUlOIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdLCJVU0VSIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdLCJQT1dFUl9VU0VSIjpbOCwxMjEsMTE5LDEwNiwxMTgsMTA4LDg2LDExMSwxMTIsODUsMTA3LDg0LDExMywxMTQsMTE3LDEyMCwxMjcsMTIzLDEyNiwxMjQsMTA5LDEyNSwxMjIsMTEwLDksODJdfSwiaWF0IjoxNzMxNTA2Mzc5LCJleHAiOjE3MzE1MTM1Nzl9.6HpPNF0xApYLdtQAJc6WubNL0kfECznrD-y1mhjYcLo; BE-CSRFToken=h9%2F0gN5YKJP3DcZuIhYKk62arf0EllUP7aVhJ1ndWNU%3D',
    };

    // Make the GET request
    const response = await request.get('https://test.brompton.ai/api/v0/customers/85/logo', {
      headers,
    });

    // Validate the status code is 200
    expect(response.status()).toBe(200);

    // Log and validate the response text (customize validation based on actual response format)
    const responseBody = await response.text();
    console.log('Response:', responseBody);
    expect(responseBody).not.toBe('');
  });
});
