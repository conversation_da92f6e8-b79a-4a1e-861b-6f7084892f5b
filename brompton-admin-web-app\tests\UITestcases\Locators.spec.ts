//const {test, expect} = require('@playwright/test')
import { test, expect } from '@playwright/test';

test('Locators', async ({ page }) => {
  page.on('console', (msg) => console.log('PAGE LOG:', msg.text()));

  //await page.goto('https://test.pivotol.ai/');
  await page.goto('https://test.pivotol.ai/login', { timeout: 120000 }); // 60 seconds
  //await page.goto('https://test.pivotol.ai/login');
  //await page.waitForSelector('your-selector'); // Replace 'your-selector' with a specific element that should be visible after loading

  // click on login button
  await page.fill('input[type="text"]', 'test');
  await page.fill('input[type="password"]', 'asdfasdf');
  // Submit the form
  await page.click('button[type="submit"]');
  // Expect the page to navigate to the dashboard
  //await page.waitForURL('https://test.pivotol.ai/dashboard');
  await page.close();
});
