import { test, expect } from '@playwright/test';
import { pagedetails } from '../POM1/pagedetails';

test('loginpage', async ({ page }) => {
  //Login
  const Login1 = new pagedetails(page);
  await Login1.lauchURL();
  //login with invalid credentials
  await Login1.invalidlogin('abcabc', 'xyzxyz');
  await page.reload('login1');
  //login with valid credentials
  await Login1.login('test', 'asdfasdf');
  await page.waitForTimeout(2000);
  // new dashboard click
  await page.click(
    '#__next > div > div.MuiStack-root.css-92qf02 > div > div > div.MuiBox-root.css-xy51px > div.MuiBox-root.css-9nra4q > button',
  );
  // click on widget
  await page.locator('id=widgets-icon').click();
  //await page.waitForTimeout(10000);

  //drag the widget
  await page.locator('#stats').dragTo(page.locator('.react-grid-layout.layout'));
  await page.waitForTimeout(2000);
  await page.locator('#title').dragTo(page.locator('.react-grid-layout.layout'));
  await page.waitForTimeout(2000);
  await page.locator('#table').dragTo(page.locator('.react-grid-layout.layout'));
  await page.waitForTimeout(2000);
  await page.locator('id=widgets-icon').click();
});
