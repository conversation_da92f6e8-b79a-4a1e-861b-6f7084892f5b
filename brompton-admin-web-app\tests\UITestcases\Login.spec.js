var { test, expect } = require('@playwright/test');

test('Login', async ({ page }) => {
  // opeing the URL
  await page.goto('https://test.brompton.ai/login');

  // Go to the username
  await page.locator('id=:r0:').fill('test');
  await page.locator('id=:r0:').click();

  await page.waitForTimeout(2000);

  // Go to Password id=:r1:
  await page.locator('id=:r1:').fill('asdfasdf');
  await page.locator('id=:r1:').click();

  // click on Login Button
  await page.click('#__next > div > div > form > div > button');
  await page.waitForTimeout(2000);

  // new dashboard click
  await page.click(
    '#__next > div > div.MuiStack-root.css-92qf02 > div > div > div.MuiBox-root.css-xy51px > div.MuiBox-root.css-9nra4q > button',
  );

  // click on widget
  await page.locator('id=widgets-icon').click();
  //await page.waitForTimeout(10000);

  //drag the widget
  await page.locator('#stats').dragTo(page.locator('.react-grid-layout.layout'));
  await page.waitForTimeout(2000);
  await page.locator('#title').dragTo(page.locator('.react-grid-layout.layout'));
  await page.waitForTimeout(2000);
  await page.locator('#table').dragTo(page.locator('.react-grid-layout.layout'));
  await page.waitForTimeout(2000);
  await page.locator('id=widgets-icon').click();

  //await page.locator('#save').click();
  await page.close();
});
