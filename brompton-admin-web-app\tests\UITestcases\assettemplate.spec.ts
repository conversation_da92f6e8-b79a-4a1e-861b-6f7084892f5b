import { test, expect } from '@playwright/test';
test('assettemplate', async ({ page }) => {
  // opening the URL
  await page.goto('https://test.pivotol.ai/login'); // 60 seconds
  // Go to the username  and password
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('normaltest');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('password123');
  // click on Login Button
  await page.click('#__next > div > div > form > div > button');
  await page.waitForTimeout(3000);
  // click on new dashboard
  await page.getByText('Add Dashboard').click({ timeout: 60000 });
  await page.waitForTimeout(6000);
  // click on asset section
  await page.getByText('Assets').click();
  await page.waitForTimeout(6000);
  // click on add asset template
  await page.getByRole('link', { name: 'Add Asset template', exact: true }).click();
  await page.waitForTimeout(6000);
  await page.getByLabel('Select an asset type').click();
  await page.getByRole('option', { name: 'Renewable > Battery Bank (24)', exact: true }).click();
  await page.getByLabel('Manufacturer').click();
  await page.getByLabel('Manufacturer').fill('manufact');
  await page.getByLabel('Model Number').click();
  await page.getByLabel('Model Number').fill('man12');
  //click on Next button
  await page.click('//*[@id="__next"]/div/div[2]/div[2]/div[2]/div/button[2]');
  await page.waitForTimeout(3000);
  await page.getByLabel('Type id *').click();
  await page.getByRole('option', { name: 'Acceleration', exact: true }).click();
  await page.getByLabel('Data Type *').click();
  await page.getByRole('option', { name: 'INT', exact: true }).click();
  await page.getByLabel('Select value type *').click();
  await page.getByRole('option', { name: 'count', exact: true }).click();
  await page.getByLabel('Select Metric *').click();
  await page.getByRole('option', { name: 'FullPackEnergy', exact: true }).click();
  await page.getByLabel('Description').click();
  await page.getByLabel('Description').fill('test');
  await page.getByLabel('Location').click();
  await page.getByRole('option', { name: 'Across', exact: true }).click();
  await page.getByLabel('Data Source').click();
  await page.getByRole('option', { name: 'CO2e', exact: true }).click();
  await page.getByLabel('Meter Factor').click();
  await page.getByLabel('Meter Factor').fill('0');
  await page.click('//*[@id="__next"]/div/div[2]/div[2]/div[2]/form/div/button[1]');
  await page.click('//*[@id="__next"]/div/div[2]/div[2]/div[2]/div[2]/button[2]');
  await page.waitForTimeout(3000);
  await page.close();
});
