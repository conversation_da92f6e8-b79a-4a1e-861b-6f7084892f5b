import { test, expect, locator } from '@playwright/test';
import { LoginDetails } from '../../POM/LoginDetails';

test('loginpage', async ({ page }) => {
  //Login
  const Login1 = new LoginDetails(page);
  await Login1.lauchURL(); // launching the URL
  await Login1.login('test', 'asdfasdf'); // Valid Deatils

  await page.click('//*[@id="__next"]/div/div[1]/div/div[2]/button/div');
  await page.waitForTimeout(2000);
  await page.click('//html/body/div[2]/div[3]/li[4]/div[2]/span');
  await page.waitForTimeout(2000);

  //  await page.getByRole('button', { name: 'PROCEED' }).click();
  //await page.keyboard.press('Escape');
  await page.getByLabel('Asset Type').click();
  const a1 = page.getByRole('option', { name: 'Renewable > Battery Bank (24)', exact: true });
  await a1.click();
  await page.waitForTimeout(2000);

  await page.getByLabel('Asset Template').click();
  await page.getByRole('option', { name: 'Ch1 - chris' }).click();
  // await page.getByRole('option', { name: 'Ch1 - chris', exact: true }).click();
  page.getByTestId('ArrowDropDownIcon').nth(2).waitFor({ state: 'visible', timeout: 2000 });
  console.log('asset template selected');
  await page.waitForTimeout(4000);

  //const a3= page.locator('.MuiAutocomplete-popup.MuiAutocomplete-option[data-option-index="0"]').click();//('lable=["Asset Template"].MuiAutocomplete-endAdornment');
  //await a3.fill('Ch1 - chris');
  //await a3.click();

  // await page.getByLabel('Asset Type').press('Tab');
  // Replace with the actual selector
  //await page.keyboard.press('Escape');
  //await page.getByLabel('Asset Type').click({ force: true });
  //const a2 = page.getByLabel('Asset Template');
  //await a2.selectOption({ label: 'Ch1 - chris' });

  await page.getByLabel('Asset Template').press('Tab');
  // Replace with the actual selector
  await page.locator('#widgets-icon').click();
  await page.waitForTimeout(4000);
  await page.locator('#table').dragTo(page.locator('.react-grid-layout.layout'));
  // await page.getByText('Submit').click();
  await page.locator('id=widgets-icon').click();
  await page.waitForTimeout(6000);
});
