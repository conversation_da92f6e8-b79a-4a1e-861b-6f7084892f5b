import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://test.pivotol.ai/login');
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('asdfasdf');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.locator('#widgets-icon').click();
  await page.locator('#stats').dragTo(page.locator('.react-grid-layout.layout'));
  await page.locator('#title').dragTo(page.locator('.react-grid-layout.layout'));
  await page.locator('#table').dragTo(page.locator('.react-grid-layout.layout'));

  //   await page.waitForTimeout(500000);
  //   await page.getByRole('button', { name: 'Update' }).click();
  //   await page.getByRole('button', { name: 'Update' }).click();
});
