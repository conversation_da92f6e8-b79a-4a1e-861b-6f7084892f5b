import { test, expect } from '@playwright/test';
test('editmeasure', async ({ page }) => {
  // opening the URL
  await page.goto('https://test.pivotol.ai/login'); // 60 seconds
  // Go to the username  and password
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('normaltest');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('password123');
  // click on Login Button
  await page.click('#__next > div > div > form > div > button');
  await page.waitForTimeout(3000);
  // click on new dashboard
  await page.getByText('Add Dashboard').click({ timeout: 80000 });
  // select customer
  await page.getByLabel('Select Customer').click();
  await page.getByRole('combobox', { name: 'Customer' }).click();
  await page.getByRole('option', { name: 'Customer', exact: true }).click();
  await page.waitForTimeout(8000);
  // click on arrow for select measure
  const clicl = page.locator('//*[@id=":r1b:-639"]/div/div[1]/svg/path').first(); // Adjust the index as needed
  await clicl.click();
  console.log('test');
  await page.waitForTimeout(2000);

  await page.waitForTimeout(8000);
  await page.getByText('Submit').click();
  await page.waitForTimeout(3000);
  await page.close();
});
