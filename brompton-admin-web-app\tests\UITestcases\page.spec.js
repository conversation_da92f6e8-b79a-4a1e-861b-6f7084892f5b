import { test, expect } from '@playwright/test';

test('Locators', async ({ page }) => {
  // opening the URL
  await page.goto('https://test.pivotol.ai/login', { timeout: 6000 }); // 60 seconds

  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('asdfasdf');

  await page.getByRole('button', { name: 'Log in' }).click();
  //await page.getByRole('button', { name:'MuiTouchRipple-root css-w0pj6f'}).click();
  await page.close();
});
