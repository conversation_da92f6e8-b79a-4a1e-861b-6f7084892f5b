import { test, expect } from '@playwright/test';

test('drag and configure Alerts widget', async ({ page }) => {
  await page.goto('https://test.pivotol.ai/login');

  // **Login**
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.waitForTimeout(3000);

  // **Navigate to Dashboard**
  await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
  await page.getByRole('menuitem', { name: 'Add New' }).click();

  // Open widgets menu
  await page.locator('#widgets-icon').click();
  await page.waitForTimeout(3000);

  // ✅ Drag and drop the Alerts widget (identified by text)
  const alertsWidget = page.locator('span.MuiListItemText-primary', { hasText: 'Alerts' }).first();
  const layoutArea = page.locator('.react-grid-layout.layout');
  await alertsWidget.dragTo(layoutArea);

  await page.waitForTimeout(2000);

  // Re-open widgets menu to keep UI consistent
  await page.locator('#widgets-icon').click();
  await page.waitForTimeout(3000);

  // ✅ Hover over the correct widget card based on unique text inside
  const widgetCard = page
    .locator('.MuiBox-root.css-1j35o1p', {
      hasText: 'Alerts',
    })
    .first();
  await widgetCard.hover();

  // ✅ Find MoreVertIcon inside this widget card and click
  const optionsIcon = widgetCard.locator('[data-testid="MoreVertIcon"]');
  await optionsIcon.waitFor({ state: 'visible', timeout: 10000 });
  await optionsIcon.click();

  // ✅ Click 'Widget Settings'
  await page.getByRole('menuitem', { name: 'Widget Settings' }).click();

  // ✅ Configure Asset & Measurement
  await page.getByLabel('Select Asset').click();
  await page.getByRole('option', { name: 'Brenes > MAINPANEL', exact: true }).click();

  await page.getByLabel('Select Measurements').click();
  await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();

  await page.getByRole('button', { name: 'Update' }).click();
  await page.waitForTimeout(3000);
});
