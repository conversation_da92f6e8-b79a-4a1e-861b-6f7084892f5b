import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://test.pivotol.ai/login');

  // Login
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.waitForTimeout(5000);

  // Navigate to Assets
  await page.getByRole('button', { name: 'Assets' }).click();
  await page.waitForTimeout(5000);
  await page.getByRole('menuitem', { name: 'Manage Assets' }).click();
  await page.waitForTimeout(15000);

  // Right-click on 'Animesh' tree item and ensure context menu appears
  const treeItem = await page.getByRole('treeitem', { name: '<PERSON>imesh' }).locator('div').nth(2);
  await treeItem.waitFor({ state: 'visible', timeout: 10000 });
  await treeItem.click({ button: 'right' });

  // Wait for the menu and then click 'New measurement'
  const newMeasurementMenuItem = page.getByRole('menuitem', { name: 'New measurement' });
  await newMeasurementMenuItem.waitFor({ state: 'visible', timeout: 10000 }); // Increased timeout
  await newMeasurementMenuItem.click();
  await page.waitForTimeout(3000);
  // Fill in the form
  await page.getByLabel('Tag *').fill('createmeasure');
  await page.getByLabel('Description').fill('test');

  // Select measurement type
  await page.getByLabel('Select measurement type *').click();
  await page.getByRole('option', { name: 'Acceleration' }).click();

  // Select data type
  await page.getByLabel('Select data type *').click();
  await page.getByRole('option', { name: 'BOOLEAN' }).click();

  // Select value type
  await page.getByLabel('Select value type *').click();
  await page.getByRole('option', { name: 'nominal' }).click();

  // Select unit of measure
  await page.getByLabel('Select unit of measure').click();
  await page.getByRole('option', { name: 'ft/s²' }).click();

  // Select location
  await page.getByLabel('Select location').click();
  await page.getByRole('option', { name: 'EndOfLine' }).click();

  // Select datasource
  await page.getByLabel('Select datasource').click();
  await page.getByRole('option', { name: 'Weather' }).click();

  // Fill meter factor
  await page.getByLabel('Meter factor').fill('9');

  // Submit form
  await page.getByRole('button', { name: 'Submit' }).click();
  await page.waitForTimeout(3000);
  await page.close();
});
