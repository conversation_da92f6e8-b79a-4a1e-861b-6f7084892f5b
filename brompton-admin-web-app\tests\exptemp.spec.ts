import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://test.pivotol.ai/login');
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.setViewportSize({ width: 1920, height: 1080 });
  await page.waitForTimeout(3000);
  await page.getByRole('button', { name: 'CalcEngine' }).click();
  await page.waitForTimeout(3000);
  await page.getByRole('menuitem', { name: 'Expression templates' }).click();
  await page.getByRole('button', { name: 'Add Expression Template' }).click();
  await page.getByLabel('Expression Name').click();
  await page.getByLabel('Expression Name').fill('test unique');
  await page.getByLabel('Description').click();
  await page.getByLabel('Description').fill('exp temp');
  await page.getByRole('combobox').click();
  await page.getByRole('option', { name: 'INT' }).click();
  await page.getByLabel('Expression', { exact: true }).click();
  await page.getByLabel('Expression', { exact: true }).fill('$A+$B');
  await page.getByLabel('Variable $A sample value').click();
  await page.getByLabel('Variable $A sample value').fill('4');
  await page.getByLabel('Variable $B sample value').click();
  await page.getByLabel('Variable $B sample value').fill('5');
  await page.getByRole('button', { name: 'Save' }).click();
  await page.close();
});
