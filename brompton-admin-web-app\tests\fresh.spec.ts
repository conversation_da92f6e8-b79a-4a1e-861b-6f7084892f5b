import { test, expect } from '@playwright/test';

import fs from 'fs';
import path from 'path';

// 🔐 Password Toggle Setup
const passwordFile = path.resolve(__dirname, 'currentPassword.txt');
const passwordA = 'password12';
const passwordB = 'password123';

function getCurrentPassword(): string {
  try {
    return fs.readFileSync(passwordFile, 'utf-8').trim();
  } catch {
    return passwordA;
  }
}

function getNewPassword(current: string): string {
  return current === passwordA ? passwordB : passwordA;
}

function savePassword(newPassword: string): void {
  fs.writeFileSync(passwordFile, newPassword, 'utf-8');
}

// Test Suite for all modules
test.describe('Playwright Test Suite', () => {
  test.describe('Asset Template Module', () => {
    test('assettemplate', async ({ page }) => {
      await page.goto('https://test.pivotol.ai/login');

      // Login
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();

      await page.waitForTimeout(3000); // Reduce unnecessary delay

      // Click "Assets" button to expand menu
      await page.getByRole('button', { name: 'Assets' }).click();
      await page.waitForTimeout(5000); // Reduce unnecessary delay
      // Wait for "Manage Templates" menu item to appear and click
      const manageTemplates = page.getByRole('menuitem', { name: 'Manage Templates' });
      await manageTemplates.waitFor({ state: 'visible', timeout: 10000 });
      await manageTemplates.click();

      // Try different locators to ensure correct selection
      const addAssetTemplate = page.locator('a[href="/create-asset-template"]');
      await addAssetTemplate.waitFor({ state: 'visible', timeout: 15000 });
      await addAssetTemplate.click();

      // Ensure dropdown options are visible
      await page.waitForTimeout(5000); // Short delay to allow rendering
      // Fill in the form
      await page.getByRole('combobox', { name: 'Asset Type' }).click();
      await page.getByRole('option', { name: 'Power > AC Generator (16)', exact: true }).click();
      await page.getByLabel('Manufacturer').fill('Flower');
      await page.getByLabel('Model Number').fill('786786');
      await page.getByLabel('Save as Global Asset Template').check();
      await page.getByRole('button', { name: 'Next' }).click();

      // Submit Form
      await page.getByLabel('Type Id *').click();
      await page.getByRole('option', { name: 'Acceleration' }).click();
      await page.getByLabel('Data Type *').click();
      await page.getByRole('option', { name: 'BOOLEAN' }).click();
      await page.getByLabel('Select value type *').click();
      await page.getByRole('option', { name: 'calculated' }).click();
      await page.getByLabel('Metric *').click();
      await page.getByRole('option', { name: 's' }).click();
      await page.getByLabel('Description').click();
      await page.getByLabel('Description').fill('test');
      await page.getByLabel('Location').click();
      await page.getByRole('option', { name: 'Hot Side' }).click();
      await page.getByLabel('Data Source').click();
      await page.getByRole('option', { name: 'Weather' }).click();
      await page.getByLabel('Meter Factor').click();
      await page.getByLabel('Meter Factor').fill('3');
      await page.getByRole('button', { name: 'Add' }).click();
      await page.getByRole('button', { name: 'Save & Finish' }).click();
      await page.close();
    });
  });

  test.describe('Alert Module', () => {
    test('create alert for ApparentPowerDemand', async ({ page }) => {
      // opening the URL
      await page.goto('https://test.pivotol.ai/login');
      await page.getByLabel('Username *').click();
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').click();
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.waitForTimeout(3000);
      await page.getByRole('button', { name: 'Alerts' }).click();
      await page.waitForTimeout(5000);
      await page.getByRole('menuitem', { name: 'Manage' }).click();
      await page.getByRole('button', { name: 'Add New' }).click();
      await page.getByLabel('Asset').click();
      await page.getByRole('combobox', { name: 'Asset' }).fill('mqtt');
      await page.getByRole('option', { name: 'Brenes > MAINPANEL_MQTT', exact: true }).click();
      await page.getByLabel('Asset').click();
      await page.getByRole('option', { name: 'Brenes > MAINPANEL_MQTT', exact: true }).click();
      await page.getByLabel('Measurement', { exact: true }).click();
      await page
        .getByRole('option', { name: 'Brenes\\MAINPANEL_MQTT\\AverageCurrent', exact: true })
        .click();
      await page.getByLabel('Description', { exact: true }).click();
      await page.getByLabel('Description', { exact: true }).fill('alert test');
      await page.getByLabel('Aggregate *').click();
      await page.getByRole('option', { name: 'AVG', exact: true }).click();
      await page.getByLabel('Aggregate Period').click();
      await page.getByRole('option', { name: '10min' }).click();
      await page.getByLabel('Threshold Type').click();
      await page.getByRole('option', { name: 'NOMINAL' }).click();
      await page.getByLabel('Comparision condition').click();
      await page.getByRole('option', { name: 'LT' }).click();
      await page.getByLabel('Threshold Value').click();
      await page.getByLabel('Threshold Value').fill('12');
      await page.getByLabel('Reset Deadband Value').click();
      await page.getByLabel('Reset Deadband Value').fill('1');
      await page.getByLabel('User').click();
      await page.getByRole('option', { name: 'normaltest' }).click();
      await page.getByLabel('Email').check();
      await page.getByRole('button', { name: 'Submit' }).click();
      // Close the page
      await page.close();
    });
  });

  test.describe('Calculation Measurement Module', () => {
    test('calcmeasure', async ({ page }) => {
      // opening the URL
      await page.goto('https://test.pivotol.ai/login'); // 60 seconds
      // Go to the username  and password
      await page.getByLabel('Username *').click();
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').click();
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      // click on Login Button
      await page.click('#__next > div > div > form > div > button');
      await page.waitForTimeout(3000);

      // Navigate to Assets -> Manage Assets
      await page.getByRole('button', { name: 'Assets' }).click();
      await page.waitForTimeout(3000);
      await page.getByRole('menuitem', { name: 'Manage Assets' }).click();
      await page.waitForTimeout(10000);
      // Ensure the asset tree is loaded before proceeding
      //await page.waitForSelector('.MuiTreeItem-content', { timeout: 10000 });

      await page
        .locator('div.MuiBox-root.css-c36nl0:has-text("AssetAllFlows")')
        .click({ button: 'right' });
      await page.waitForTimeout(8000);

      await page.getByRole('menuitem', { name: 'New measurement' }).click();
      await page.waitForTimeout(3000);

      //create new measurement
      await page.getByLabel('Tag *').click();
      await page.getByLabel('Tag *').fill('test create measure');
      await page.getByLabel('Description').click();
      await page.getByLabel('Description').fill('test');
      await page.getByLabel('Select measurement type *').click();
      await page.getByRole('option', { name: 'Acceleration', exact: true }).click();
      await page.getByLabel('Select data type *').click();
      await page.getByRole('option', { name: 'INT', exact: true }).click();
      await page.getByLabel('Select value type *').click();
      await page.getByRole('option', { name: 'count', exact: true }).click();
      await page.getByLabel('Select unit of measure').click();
      await page.getByRole('option', { name: 'ft/s²', exact: true }).click();
      await page.getByLabel('Select location').click();
      await page.getByRole('option', { name: 'Bottom', exact: true }).click();
      await page.getByLabel('Select datasource').click();
      await page.getByRole('option', { name: 'Calculation', exact: true }).click();
      await page.getByLabel('Meter factor').click();
      await page.getByLabel('Meter factor').fill('0');
      await page.waitForTimeout(2000);

      // click on next button

      await page.locator('button.MuiButton-containedPrimary.MuiButton-sizeLarge').click();
      // fill form
      await page.getByRole('combobox').nth(1).click();
      await page.getByRole('option', { name: 'Euler Distance (2D)' }).click();
      await page.getByLabel('Select Measure').click();
      await page.locator('#measurement-list-option-0').click();
      await page.locator('#measurement-list').nth(1).click();
      await page.getByRole('option', { name: 'Animesh\\WindTurbine\\Efficiency' }).click();
      await page.getByLabel('Variable $A add comments').click();
      await page.getByLabel('Variable $A add comments').fill('asd');
      await page.getByLabel('Variable $B add comments').click();
      await page.getByLabel('Variable $B add comments').fill('dsa');
      await page.getByLabel('Persistance').check();
      await page.getByRole('combobox').nth(4).click();
      await page.getByRole('option', { name: '30min' }).click();
      await page.getByLabel('writeback').check();
      await page.getByRole('button', { name: 'Submit' }).click();
      await page.close();
    });
  });

  test.describe('Create Asset Module', () => {
    test('createasset', async ({ page }) => {
      // Opening the login page
      await page.goto('https://test.pivotol.ai/login');

      // Fill in Username and Password
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');

      // Click on Login Button
      await page.click('#__next > div > div > form > div > button');
      await page.waitForTimeout(2000); // Wait for login to process

      // Navigate to Assets -> Manage Assets
      await page.locator('div.MuiListItemText-root span', { hasText: 'Assets' }).click();
      await page.getByRole('menuitem', { name: 'Manage Assets' }).click();
      await page.waitForTimeout(2000);

      // Ensure the asset tree is loaded before proceeding
      await page.waitForSelector('.MuiTreeItem-content', { timeout: 10000 });

      // Right-click on an existing asset entry
      const assetEntry = page.locator('.MuiTreeItem-content.Mui-expanded.Mui-selected');
      await assetEntry.click({ button: 'right' });
      await page.waitForTimeout(2000);
      // Ensure the context menu appears
      //await page.waitForSelector('.MuiList-root.MuiList-padding.MuiMenu-list', { timeout: 5000 });

      // Click on "New root asset" using text instead of dynamic class names
      const newRootAsset = page.getByRole('menuitem', { name: 'New root asset' });
      await newRootAsset.click();

      // Fill in asset details
      await page.getByLabel('Tag *').fill('TestAsset');

      await page.getByLabel('Select an asset type').click();
      await page.getByRole('option', { name: 'Power > AC Buss (0)', exact: true }).click();

      // Ensure dropdown is visible before selecting timezone
      await page
        .getByTestId('ArrowDropDownIcon')
        .nth(2)
        .waitFor({ state: 'visible', timeout: 2000 });

      await page.getByLabel('Description').fill('TestAssets');

      await page.getByLabel('Select a time zone').click();
      await page.getByRole('option', { name: 'Africa/Abidjan', exact: true }).click();

      // Ensure dropdown is visible before selecting latitude/longitude
      await page
        .getByTestId('ArrowDropDownIcon')
        .nth(1)
        .waitFor({ state: 'visible', timeout: 2000 });

      await page.getByLabel('Latitude').fill('0');
      await page.getByLabel('Longitude').fill('0');

      // Submit the form
      await page.getByText('Submit').click();
      await page.waitForTimeout(2000); // Wait for submission process

      // Close the page
      await page.close();
    });
  });

  test.describe('Create Customer Module', () => {
    test('createcustomer', async ({ page }) => {
      // Open Login Page
      await page.goto('https://test.pivotol.ai/login');

      // Login Flow
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.waitForTimeout(3000); // Wait for login to complete

      // Navigate through dashboard
      await page.getByRole('button', { name: 'D', exact: true }).click();

      // Ensure "Customer Management" is loaded before clicking
      await page.waitForSelector('text=Customer Management', { timeout: 10000 });
      await page.getByText('Customer Management').click();

      // **Fix: Handle the "Proceed" button conditionally**
      const proceedButton = page.locator('button:has-text("Proceed")');
      const isProceedVisible = await proceedButton.isVisible();

      if (isProceedVisible) {
        console.log('Proceed button is visible, clicking it.');
        await proceedButton.click();
      } else {
        console.log('Proceed button is NOT visible, skipping this step.');
      }

      // **Fix: Click on the "Add new Customer" button**
      const addCustomerButton = page.locator(
        'button.MuiButtonBase-root:has-text("Add new Customer")',
      );

      await addCustomerButton.waitFor({ state: 'visible', timeout: 10000 });
      await addCustomerButton.click();

      // Fill Customer Details
      await page.getByLabel('Name id *').fill('sam');
      await page.getByLabel('Name *').fill('sam');
      await page.getByLabel('Address *').fill('neartree');

      // **Fix: Ensure File Upload Works Correctly**
      const fileInput = page.locator('input[type="file"]');
      await fileInput.setInputFiles('C:/Users/<USER>/OneDrive/Pictures/logo/sm_5afa55dd1cd4e.jpg');

      // Click Add
      await page.getByRole('button', { name: 'Add' }).click();

      // Close Browser
      await page.close();
    });
  });

  // Create Asset Type Module Test
  test.describe('Create Asset Type Module', () => {
    test('createassettype', async ({ page }) => {
      await page.goto('https://test.pivotol.ai/login');

      // Login Flow
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.waitForTimeout(3000); // Wait for login

      // Navigate to Asset Management
      await page.getByRole('button', { name: 'Add Dashboard' }).click();
      await page.getByRole('button', { name: 'Assets' }).click();
      await page.getByRole('menuitem', { name: 'Manage Types' }).click();
      await page.getByRole('button', { name: 'Add new Asset type' }).click();

      // Fill Asset Type Form
      await page.getByLabel('Asset Type Name').fill('test flow');

      // Open Parent Type Dropdown
      await page.getByLabel('Parent Type').click();
      await page.waitForTimeout(1000); // Ensure dropdown loads

      // **Fix: Select "Power > AC Generator" Correctly**
      const options = page.locator('li[role="option"]:has-text("Power > AC Generator")');

      if ((await options.count()) > 1) {
        console.log('Multiple options found, selecting the first one.');
        await options.first().click();
      } else {
        console.log('Only one matching option found, selecting it.');
        await options.click();
      }

      // Save Asset Type
      await page.getByRole('button', { name: 'Save' }).click();
      // Close the page
      await page.close();
    });
  });
  // Add User Module Test
  test.describe('User Management Module', () => {
    test('test', async ({ page }) => {
      await page.goto('https://test.pivotol.ai/login');
      await page.getByLabel('Username *').click();
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').click();
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.getByRole('button', { name: 'Add Dashboard' }).click();
      await page.waitForTimeout(5000);
      await page.getByRole('link', { name: 'Users' }).click();
      await page.getByRole('button', { name: 'Add User' }).click();
      await page.getByLabel('First Name *').click();
      await page.getByLabel('First Name *').fill('additional');
      await page.getByLabel('Last Name *').click();
      await page.getByLabel('Last Name *').fill('join');
      await page.getByLabel('Username *').click();
      await page.getByLabel('Username *').fill('joinone');
      await page.getByLabel('Password *').click();
      await page.getByLabel('Password *').fill('joinone');
      await page.getByLabel('Email *').click();
      await page.getByLabel('Email *').fill('<EMAIL>');
      await page.getByLabel('Country Code *').click();
      await page.getByLabel('Country Code *').fill('+91');
      await page.getByLabel('Phone Number *').click();
      await page.getByLabel('Phone Number *').fill('9763707433');
      await page.getByLabel('Select Customer for User').click();
      await page.getByRole('option', { name: 'Brompton Energy Inc.' }).click();
      await page.getByLabel('Select Customer for Power User').click();
      await page.getByRole('option', { name: 'Carbon Energy' }).click();
      await page.getByLabel('Select Customer for Admin').click();
      await page.getByRole('option', { name: 'Pivotal Energy' }).click();
      await page.getByRole('button', { name: 'Submit' }).click();
      await page.close();
    });
  });
  // Forget Password Test
  test.describe('Authentication Module', () => {
    test('forgetpassword', async ({ page }) => {
      await page.goto('https://test.pivotol.ai/login');
      await page.locator('//*[@id="__next"]/div/div/form/div/a').click();
      await page.getByLabel('Username or Email').click();
      await page.getByLabel('Username or Email').fill('<EMAIL>');
      await page.waitForTimeout(1000);
      await page.getByText('SUBMIT').click();
      await page.waitForTimeout(1000);
    });
  });

  test.describe('Invalid Login Tests', () => {
    test('invalidlogin', async ({ page }) => {
      await page.goto('https://test.pivotol.ai/login');
      await page.getByLabel('Username *').click();
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').click();
      await page.getByLabel('Password *').fill('asdfasdfes');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.waitForTimeout(1000);
      await page.close();
    });
  });

  // Measurement Module Tests
  test.describe('Measurement Module', () => {
    test('measurement', async ({ page }) => {
      // opening the URL
      await page.goto('https://test.pivotol.ai/login');

      // Login
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.waitForTimeout(5000);

      // Navigate to Assets
      await page.getByRole('button', { name: 'Assets' }).click();
      await page.waitForTimeout(5000);
      await page.getByRole('menuitem', { name: 'Manage Assets' }).click();
      await page.waitForTimeout(15000);

      // Right-click on 'Animesh' tree item and ensure context menu appears
      const treeItem = await page.getByRole('treeitem', { name: 'Animesh' }).locator('div').nth(2);
      await treeItem.waitFor({ state: 'visible', timeout: 10000 });
      await treeItem.click({ button: 'right' });

      // Wait for the menu and then click 'New measurement'
      const newMeasurementMenuItem = page.getByRole('menuitem', { name: 'New measurement' });
      await newMeasurementMenuItem.waitFor({ state: 'visible', timeout: 10000 }); // Increased timeout
      await newMeasurementMenuItem.click();
      await page.waitForTimeout(3000);
      // Fill in the form
      await page.getByLabel('Tag *').fill('createmeasure');
      await page.getByLabel('Description').fill('test');

      // Select measurement type
      await page.getByLabel('Select measurement type *').click();
      await page.getByRole('option', { name: 'Acceleration' }).click();

      // Select data type
      await page.getByLabel('Select data type *').click();
      await page.getByRole('option', { name: 'BOOLEAN' }).click();

      // Select value type
      await page.getByLabel('Select value type *').click();
      await page.getByRole('option', { name: 'nominal' }).click();

      // Select unit of measure
      await page.getByLabel('Select unit of measure').click();
      await page.getByRole('option', { name: 'ft/s²' }).click();

      // Select location
      await page.getByLabel('Select location').click();
      await page.getByRole('option', { name: 'EndOfLine' }).click();

      // Select datasource
      await page.getByLabel('Select datasource').click();
      await page.getByRole('option', { name: 'Weather' }).click();

      // Fill meter factor
      await page.getByLabel('Meter factor').fill('9');

      // Submit form
      await page.getByRole('button', { name: 'Submit' }).click();
      await page.waitForTimeout(3000);
      await page.close();
    });
  });
  // Template Instance Module Tests
  test.describe('Template Instance Module', () => {
    test('templateinstance', async ({ page }) => {
      await page.goto('https://test.pivotol.ai/login');

      // Login
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();

      await page.waitForTimeout(3000); // Reduce unnecessary delay

      // Click "Assets" button to expand menu
      await page.getByRole('button', { name: 'Assets' }).click();
      await page.waitForTimeout(3000); // Reduce unnecessary delay
      // Wait for "Manage Templates" menu item to appear and click
      const manageTemplates = page.getByRole('menuitem', { name: 'Manage Templates' });
      await manageTemplates.waitFor({ state: 'visible', timeout: 15000 });
      await manageTemplates.click();

      // Wait for the "Add Asset template" link inside the expanded menu
      await page.waitForTimeout(2000); // Ensure animation completes if needed

      const addAssetTemplateInstance = page.locator('a[href="/create-asset-template-instance"]');
      await addAssetTemplateInstance.waitFor({ state: 'visible', timeout: 15000 });
      await addAssetTemplateInstance.click();
      await page.waitForTimeout(2000);

      // Ensure dropdown options are visible
      await page.getByLabel('Asset Type').click();
      await page.getByRole('option', { name: 'Renewable > Battery Bank (26)' }).click();
      await page
        .getByRole('row', { name: '11 TESLA Megapack1 Yes Export' })
        .getByRole('radio')
        .check();
      await page.getByRole('button', { name: 'Next', exact: true }).click();
      await page.getByLabel('Unit Of Group *').click();
      await page.getByRole('option', { name: 'us' }).click();
      await page.getByLabel('Asset Tag *').click();
      await page.getByLabel('Asset Tag *').fill('US');
      await page.getByRole('button', { name: 'Next' }).click();
      await page.getByRole('button', { name: 'Save & Finish' }).click();
      await page.waitForTimeout(2000);
      await page.close();
    });
  });
  // Authentication Module Tests
  test.describe('Authentication Module', () => {
    test('login', async ({ page }) => {
      // opening the URL
      await page.goto('https://test.pivotol.ai/login'); // 60 seconds

      // Go to the username and password
      await page.getByLabel('Username *').click();
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').click();
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');

      // click on Login Button
      await page.click('#__next > div > div > form > div > button');
      await page.waitForTimeout(1000);
    });
  });

  // Dashboard Module Tests
  test.describe('Dashboard Module', () => {
    test('dashboardtemplate', async ({ page }) => {
      // **Open Login Page & Login**
      await page.goto('https://test.pivotol.ai/login');
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.waitForTimeout(5000);

      // **Navigate to Dashboard**
      await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
      await page.waitForTimeout(10000);
      await page.locator('li:has-text("New Dashboard Template")').first().click();

      // **Select Asset Type**
      await page.locator('input[aria-autocomplete="list"]').first().fill('Meter > Electrical');
      await page.getByRole('option', { name: 'Meter > Electrical (6) > Extended (26)' }).click();

      // **Select Asset Template**
      await page
        .locator('input[aria-autocomplete="list"]')
        .nth(1)
        .fill('AcuvimII_SFTP - Accuenergy');
      await page.getByRole('option', { name: 'AcuvimII_SFTP - Accuenergy' }).click();

      // Open widgets menu
      await page.locator('#widgets-icon').click();
      await page.waitForTimeout(2000);

      // Drag and drop the table widget into the layout area
      await page.locator('#table').dragTo(page.locator('.react-grid-layout.layout'));
      await page.waitForTimeout(2000);

      // Re-open widgets menu
      await page.locator('#widgets-icon').click();
      await page.waitForTimeout(2000);

      // Hover over the parent element to reveal options
      const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
      await parentElement.hover();

      // Locate and click the 'MoreVertIcon' options button
      const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
      await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
      await optionsIcon.click();

      // Click on settings menu item
      await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[4]');
      await page.waitForTimeout(6000);

      // **Select "AverageCurrent" in Dropdown**
      const comboBox = page.locator('div.MuiSelect-select.MuiSelect-outlined.MuiSelect-multiple');
      await comboBox.click();
      const menuItem = page.getByRole('option', { name: 'AverageCurrent', exact: true });

      // **Ensure dropdown is ready before selecting**
      await menuItem.waitFor({ state: 'visible', timeout: 10000 });
      await menuItem.click();

      // **Close Dropdown to Avoid UI Blocking**
      await page.keyboard.press('Escape');

      // **Click the Update Button**
      await page.locator('button:has-text("Update")').waitFor({ state: 'visible', timeout: 15000 });
      await page.locator('button:has-text("Update")').click();

      //click on save button
      const saveButtons = page.getByRole('button', { name: 'Save' });

      // Ensure the button is visible by scrolling into view
      await saveButtons.scrollIntoViewIfNeeded();

      // Click the button, forcing the interaction even if it's hidden
      await saveButtons.click({ force: true });

      // **Fill Dashboard Title**
      await page.locator('input[name="title"]').fill('fillnew');

      // **Check "Save as Global Dashboard Template"**
      const checkbox = page.locator('input[name="save_as_global_dashboard_template"]');
      await checkbox.waitFor({ state: 'attached', timeout: 10000 });
      await checkbox.check();

      // **Click the Save Button**
      await page.locator('button[type="submit"].MuiButton-containedPrimary').click();
      await page.waitForTimeout(3000);

      // **Close Page**
      await page.close();
    });
  });
  test.describe('Expression Template Module', () => {
    // Other asset management tests ...

    test('Expression Template', async ({ page }) => {
      await page.goto('https://test.pivotol.ai/login');
      await page.getByLabel('Username *').click();
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').click();
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.setViewportSize({ width: 1920, height: 1080 });
      await page.waitForTimeout(3000);
      await page.getByRole('button', { name: 'CalcEngine' }).click();

      await page.getByRole('menuitem', { name: 'Expression templates' }).click();
      await page.getByRole('button', { name: 'Add Expression Template' }).click();
      await page.getByLabel('Expression Name').click();
      await page.getByLabel('Expression Name').fill('test unique');
      await page.getByLabel('Description').click();
      await page.getByLabel('Description').fill('exp temp');
      await page.getByRole('combobox').click();
      await page.getByRole('option', { name: 'INT' }).click();
      await page.getByLabel('Expression', { exact: true }).click();
      await page.getByLabel('Expression', { exact: true }).fill('$A+$B');
      await page.getByLabel('Variable $A sample value').click();
      await page.getByLabel('Variable $A sample value').fill('4');
      await page.getByLabel('Variable $B sample value').click();
      await page.getByLabel('Variable $B sample value').fill('5');
      await page.getByRole('button', { name: 'Save' }).click();
      await page.close();
    });
  });
  // Customer Management Module Tests
  test.describe('Customer Management Module', () => {
    test('updateCustomer', async ({ page }) => {
      // Open Login Page
      await page.goto('https://test.pivotol.ai/login');

      // Login Flow
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.waitForTimeout(3000);

      //click on D
      await page.getByRole('button', { name: 'D', exact: true }).click();

      // Ensure "Customer Management" is loaded before clicking
      await page.waitForSelector('text=Customer Management', { timeout: 5000 });
      await page.getByText('Customer Management').click();

      // Handle "Proceed" button conditionally
      const proceedButton = page.locator('button:has-text("Proceed")');
      if (await proceedButton.isVisible()) {
        console.log('Proceed button is visible, clicking it.');
        await proceedButton.click();
      } else {
        console.log('Proceed button is NOT visible, skipping this step.');
      }
      await page.waitForTimeout(5000);
      // await page.locator('#combo-box-demo').click();

      await page.getByLabel('Customer').click();
      await page.getByRole('option', { name: 'san' }).click();
      await page.locator('div:nth-child(7) > div > .MuiButtonBase-root').click();
      await page.getByLabel('Name *').click();
      await page.getByLabel('Name *').fill('san');
      await page.getByLabel('Address *').click();
      await page.getByLabel('Address *').fill('seaviews');

      // Ensure File Upload Works Correctly
      const fileInput = page.locator('input[type="file"]');
      if (await fileInput.isVisible()) {
        await fileInput.setInputFiles(
          'C:/Users/<USER>/OneDrive/Pictures/logo/sm_5afa55dd1cd4e.jpg',
        );
      } else {
        console.log('File input not found, skipping file upload.');
      }

      await page.getByRole('button', { name: 'Update' }).click();
      await page.waitForTimeout(1000);
      // Close Page
      await page.close();
    });
  });
  // Asset Details Module
  test.describe('Asset Details Module', () => {
    test('asset details', async ({ page }) => {
      // Opening the login page
      await page.goto('https://test.pivotol.ai/login');

      // Fill in Username and Password
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');

      // Click on Login Button
      await page.click('#__next > div > div > form > div > button');
      await page.waitForTimeout(3000); // Wait for login to process

      // Navigate to Assets -> Manage Assets
      await page.getByRole('button', { name: 'Assets' }).click();
      await page.waitForTimeout(3000);
      await page.getByRole('menuitem', { name: 'Manage Assets' }).click();
      await page.waitForTimeout(10000);

      // Ensure the asset tree is loaded before proceeding
      await page
        .locator('div.MuiBox-root.css-c36nl0:has-text("MAINPANEL_MQTT")')
        .click({ button: 'right' });
      await page.waitForTimeout(8000);

      // Click on Details
      await page.getByRole('menuitem', { name: 'Details' }).click();
      await page.waitForTimeout(3000);

      // Open the trend chart for the specific row
      await page
        .getByRole('row', {
          name: 'Brenes\\MAINPANEL_MQTT\\ActivePowerDemand 2025-03-05 23:26:02 -0.0466 (watts)',
        })
        .locator('a')
        .click();

      // Interact with time range filters
      await page.getByLabel('Time Range').click();
      await page.getByRole('button', { name: 'Last 90 days' }).click();
      await page.getByRole('button', { name: 'Apply' }).click();

      // Check checkbox and submit
      await page.getByRole('checkbox').check();
      await page.getByRole('button', { name: 'Submit' }).click();
      await page.waitForTimeout(3000);

      // Close the page
      await page.close();
    });
  });
  // New test suite for configuring a table widget with a measurement
  test.describe('Widget Configuration Module', () => {
    test('add and configure table widget with measurement', async ({ page }) => {
      await page.goto('https://test.pivotol.ai/login');

      // **Login**
      await page.getByLabel('Username *').click();
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').click();
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.waitForTimeout(3000);

      // **Navigate to Dashboard**
      await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
      await page.waitForTimeout(8000);
      await page.getByRole('menuitem', { name: 'Add New' }).click();

      // Open widgets menu
      await page.locator('#widgets-icon').click();
      await page.waitForTimeout(2000);

      // Drag and drop the table widget into the layout area
      await page.locator('#table').dragTo(page.locator('.react-grid-layout.layout'));
      await page.waitForTimeout(2000);

      // Re-open widgets menu
      await page.locator('#widgets-icon').click();
      await page.waitForTimeout(2000);

      // Hover over the parent element to reveal options
      const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
      await parentElement.hover();

      // Locate and click the 'MoreVertIcon' options button
      const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
      await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
      await optionsIcon.click();

      // Click on settings menu item
      await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[4]');
      await page.waitForTimeout(6000);

      // **Select Asset**
      await page
        .locator('div')
        .filter({ hasText: /^Select Asset$/ })
        .getByLabel('Open')
        .click();
      await page.getByRole('option', { name: 'Brenes > MAINPANEL_MQTT', exact: true }).click();

      // **Select Measurement**
      await page.getByLabel('Select Measurement').click();
      await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();
      await page.waitForTimeout(1000);

      // **Click the Update button**
      await page.getByRole('button', { name: 'Update' }).click();
      await page.waitForTimeout(5000);

      await page.getByLabel('Time Range').click();
      await page.getByRole('button', { name: 'Last 90 days' }).click();
      await page.getByRole('button', { name: 'Apply' }).click();
      await page.waitForTimeout(3000);

      // **Close Page**
      await page.close();
    });
  });
  // Unit of Groups Module Tests
  test.describe('Unit of Groups Module', () => {
    test('add new unit of group', async ({ page }) => {
      await page.goto('https://test.pivotol.ai/login');

      // **Login**
      await page.getByLabel('Username *').click();
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').click();
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.setViewportSize({ width: 1920, height: 1200 });

      // **Navigate to CalcEngine > Unit of Groups**
      await page.getByRole('button', { name: 'CalcEngine' }).click();
      await page.waitForTimeout(3000);
      await page.getByRole('menuitem', { name: 'Unit of Groups' }).click();

      // **Click Add New Unit**
      await page.getByRole('button', { name: 'Add New Unit' }).click();

      // **Select Measurement Type**
      await page.getByRole('combobox', { name: 'Measurement Type' }).click();
      await page.getByRole('option', { name: 'Acceleration' }).click();

      // **Select Unit of Group**
      await page.getByLabel('Unit of Group').click();
      await page.getByRole('option', { name: 'us' }).click();

      // **Select Unit of Measure**
      await page.getByRole('combobox', { name: 'Unit of Measure' }).click();
      await page.getByRole('option', { name: 'mm/s²' }).click();

      // **Check "Is Default" Checkbox**
      await page.getByRole('checkbox', { name: 'Is Default' }).check();

      // **Save the Unit**
      await page.getByRole('button', { name: 'Save' }).click();

      // **Close the Page**
      await page.close();
    });
  });
  // Unit of Measure Module Tests
  test.describe('Unit of Measure Module', () => {
    test('add new unit of measure', async ({ page }) => {
      await page.goto('https://test.pivotol.ai/login');
      await page.getByLabel('Username *').click();
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').click();
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.waitForTimeout(2000);
      await page.getByRole('button', { name: 'CalcEngine' }).click();
      await page.waitForTimeout(3000);
      await page.getByRole('menuitem', { name: 'Unit of Measure' }).click();
      await page.waitForTimeout(3000);
      await page.getByRole('button', { name: 'Add Unit of Measure' }).click();
      await page.getByLabel('Unit of measure', { exact: true }).click();
      await page.getByLabel('Unit of measure', { exact: true }).fill('test100');
      await page.getByRole('combobox', { name: 'Measurement Type' }).click();
      await page.getByRole('option', { name: 'Acceleration' }).click();
      await page.getByRole('button', { name: 'Add' }).click();
      await page.waitForTimeout(1000);
      await page.close();
    });
  });
  // Asset Management Module
  test.describe('Asset Management Module', () => {
    test('update asset', async ({ page }) => {
      // Opening the login page
      await page.goto('https://test.pivotol.ai/login');

      // Fill in Username and Password
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');

      // Click on Login Button
      await page.click('#__next > div > div > form > div > button');
      await page.waitForTimeout(3000);
      await page.setViewportSize({ width: 1920, height: 1080 });

      // Navigate to Assets -> Manage Assets
      await page.locator('div.MuiListItemText-root span', { hasText: 'Assets' }).click();
      await page.waitForTimeout(3000);
      await page.getByRole('menuitem', { name: 'Manage Assets' }).click();
      await page.waitForTimeout(8000);

      // Right-click on AssetAllFlows
      await page
        .locator('div.MuiBox-root.css-c36nl0:has-text("AssetAllFlows")')
        .click({ button: 'right' });
      await page.waitForTimeout(8000);

      await page.getByRole('menuitem', { name: 'Edit' }).click();
      await page.waitForTimeout(3000);

      // Fill in asset details
      await page.getByLabel('Tag *').fill('AssetAllFlows');

      // ✅ Fix: Click on the correct Asset Type dropdown
      //await page.getByLabel('Asset type').click();

      // Select asset type not editable on edit page
      //await page.getByRole('option', { name: 'Power > AC Buss (0)', exact: true }).click();

      // Ensure dropdown is visible before selecting timezone
      await page
        .getByTestId('ArrowDropDownIcon')
        .nth(2)
        .waitFor({ state: 'visible', timeout: 2000 });

      await page.getByLabel('Description').fill('TestAssets');

      await page.getByLabel('Select a time zone').click();
      await page.getByRole('option', { name: 'Africa/Abidjan', exact: true }).click();

      // Ensure dropdown is visible before selecting latitude/longitude
      await page
        .getByTestId('ArrowDropDownIcon')
        .nth(1)
        .waitFor({ state: 'visible', timeout: 2000 });

      await page.getByLabel('Latitude').fill('0');
      await page.getByLabel('Longitude').fill('0');

      // Submit the form
      await page.getByText('Submit').click();
      await page.waitForTimeout(2000);

      // Close the page
      await page.close();
    });
  });
  // User Preferences Module
  test.describe('User Preferences Module', () => {
    test('update user preferences', async ({ page }) => {
      await page.goto('https://test.pivotol.ai/login');
      await page.getByLabel('Username *').click();
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').click();
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.setViewportSize({ width: 1920, height: 1080 });
      await page.getByRole('button', { name: 'D', exact: true }).click();
      await page.waitForTimeout(3000);
      await page.getByText('User Preferences').click();
      await page.waitForTimeout(5000);

      // Scroll manually to top to avoid overlapping elements
      await page.evaluate(() => window.scrollTo(0, 0));

      // Wait for 'Select Customer' dropdown to be attached and visible
      const customerDropdown = page.getByLabel('Select Customer');
      await customerDropdown.waitFor({ state: 'visible' });

      // Force hover (sometimes helps trigger visibility in MUI)
      await customerDropdown.hover();

      // Click only after ensuring it's stable and not overlapped
      await customerDropdown.click();

      // Proceed with selecting the option
      await page.getByRole('option', { name: 'Brompton Energy Inc.' }).click();

      //await page.getByLabel('Select Customer').click();
      //await page.getByRole('option', { name: 'Brompton Energy Inc.' }).click();
      await page.getByLabel('Select Date-Time Format').click();
      await page.getByRole('option', { name: 'DD-MM-YYYY hh:mm:ss a' }).click();
      //await page.locator('svg.MuiSvgIcon-root[data-testid="CheckBoxOutlineBlankIcon"]').click();

      await page.getByRole('button', { name: 'Save' }).click();
      await page.waitForTimeout(1000);
      await page.close();
    });
  });

  // Measurement Module Tests
  test.describe(' TimeVaryingFactor Module', () => {
    test('create TimeVaryingFactor', async ({ page }) => {
      await page.goto('https://test.pivotol.ai/login');

      // **Login**
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.waitForTimeout(3000);

      // **Navigate to Assets > Manage Assets**
      await page.getByRole('button', { name: 'Assets' }).click();
      await page.waitForTimeout(3000);
      await page.getByRole('menuitem', { name: 'Manage Assets' }).click();
      await page.waitForTimeout(10000);

      // **Right-click on 'Animesh' tree item and ensure context menu appears**
      const treeItem = await page.getByRole('treeitem', { name: 'Animesh' }).locator('div').nth(2);
      await treeItem.waitFor({ state: 'visible', timeout: 10000 });
      await treeItem.click({ button: 'right' });

      // **Click 'New Measurement'**
      const newMeasurementMenuItem = page.getByRole('menuitem', { name: 'New measurement' });
      await newMeasurementMenuItem.waitFor({ state: 'visible', timeout: 10000 });
      await newMeasurementMenuItem.click();
      await page.waitForTimeout(3000);

      // **Fill in the form**
      await page.getByLabel('Tag *').fill('createtvf');
      await page.getByLabel('Description').fill('test');

      // **Select Measurement Type**
      await page.getByLabel('Select measurement type *').click();
      await page.getByRole('option', { name: 'Acceleration' }).click();

      // **Select Data Type**
      await page.getByLabel('Select data type *').click();
      await page.getByRole('option', { name: 'BOOLEAN' }).click();

      // **Select Value Type**
      await page.getByLabel('Select value type *').click();
      await page.getByRole('option', { name: 'nominal' }).click();

      // **Select Unit of Measure**
      await page.getByLabel('Select unit of measure').click();
      await page.getByRole('option', { name: 'ft/s²' }).click();

      // **Select Location**
      await page.getByLabel('Select location').click();
      await page.getByRole('option', { name: 'EndOfLine' }).click();

      // **Select Data Source as "TimeVaryingFactor"**
      await page.getByLabel('Select datasource').click();
      await page.getByRole('option', { name: 'TimeVaryingFactor' }).click();

      // **Fill Meter Factor**
      await page.getByLabel('Meter factor').fill('6');

      // **Proceed to Next Step**
      await page.getByRole('button', { name: 'Next' }).click();

      // **Select Target Option**
      await page.getByRole('combobox').nth(1).click();
      await page.getByRole('option', { name: 'Target' }).click();

      // **Add Effective Date**
      await page.getByRole('button', { name: 'Add Effective Date' }).click();
      await page.getByLabel('Choose date').click();
      await page.getByRole('gridcell', { name: '20' }).click();

      // **Select a Day**
      await page.getByRole('button', { name: 'Tuesday' }).click();
      await page.getByRole('option', { name: 'Friday' }).click();

      // **Choose Time**
      await page.getByLabel('Choose time').click();
      await page.getByLabel('4 hours').click();
      await page.getByLabel('20 minutes').click();
      await page.getByLabel('PM').click();

      // **Fill Additional Time Details**
      await page.getByRole('spinbutton').click();
      await page.getByRole('spinbutton').fill('07');

      // **Save Measurement**
      await page.getByRole('button', { name: 'Save' }).click();

      // **Wait and Close**
      await page.waitForTimeout(3000);
      await page.close();
    });
  });
  //});
  test.describe('Reset Password Module', () => {
    test('change password and toggle', async ({ page }) => {
      const currentPassword: string = getCurrentPassword();
      const newPassword: string = getNewPassword(currentPassword);

      await page.goto('https://test.pivotol.ai/login');
      await page.getByLabel('Username *').fill('normaltest');
      await page.getByLabel('Password *').fill(currentPassword);
      await page.getByRole('button', { name: 'Log in' }).click();

      await page.getByRole('button', { name: 'S', exact: true }).click();
      await page.waitForTimeout(3000);
      await page.getByText('User Preferences').click();
      await page.waitForTimeout(3000);
      await page.getByLabel('Current Password *').fill(currentPassword);
      await page.getByLabel('New Password *').fill(newPassword);
      await page.getByLabel('Confirm Password *').fill(newPassword);
      await page.getByRole('button', { name: 'Submit' }).click();

      // Ensure the button is visible and stable
      //await submitButton.waitFor({ state: 'visible' });

      // Scroll into view to avoid layout shift issues
      //await submitButton.scrollIntoViewIfNeeded();

      // Small buffer to settle layout before clicking (optional)
      //await page.waitForTimeout(300);

      // Now click the button
      //await submitButton.click();

      await page.waitForTimeout(2000);
      await page.close();

      savePassword(newPassword); // 🔁 Save for next run
    });
  });
});
test.describe('Dynamic Charts Module', () => {
  test('load dynamic chart with filters', async ({ page }) => {
    await page.goto('https://test.pivotol.ai/login');

    // Login
    await page.getByLabel('Username *').click();
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').click();
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');
    await page.getByRole('button', { name: 'Log in' }).click();

    // Navigate to dashboard menu and click on Dynamic Charts
    await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
    await page.waitForTimeout(1000);
    await page.getByRole('menuitem', { name: 'Dynamic Charts' }).click();
    await page.waitForTimeout(3000);

    // Set filters
    await page.getByLabel('Time Range').click();
    await page.getByRole('button', { name: 'Last 90 days' }).click();
    await page.getByRole('button', { name: 'Apply' }).click();

    await page.getByLabel('Minutes').click();
    await page.getByRole('option', { name: '1 Hour' }).click();

    await page.getByLabel('TWA').click();
    await page.getByRole('option', { name: 'Average' }).click();

    await page.getByLabel('Select Asset').click();
    await page.getByRole('option', { name: 'Brenes > MAINPANEL_MQTT', exact: true }).click();

    await page.getByLabel('Select Measure').click();
    await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();

    // Select checkbox and submit
    await page.getByRole('checkbox').check();
    await page.getByRole('button', { name: 'Submit' }).click();

    await page.waitForTimeout(3000);
    await page.close();
  });
});
test.describe('Edit Asset Type Module', () => {
  test('editassettype', async ({ page }) => {
    // Open the URL
    await page.goto('https://test.pivotol.ai/login');

    // Fill in Username and Password
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');

    // Click on Login Button
    await page.click('#__next > div > div > form > div > button');
    await page.waitForTimeout(3000);

    // Navigate to Asset Management
    await page.getByRole('button', { name: 'Assets' }).click();
    await page.getByRole('menuitem', { name: 'Manage Types' }).click();

    // Go to next page 18 times to locate the row
    for (let i = 0; i < 18; i++) {
      await page.getByRole('button', { name: 'Go to next page' }).click();
    }

    // Locate the specific row and its edit icon
    const rowLocator = page.getByRole('row', { name: 'Test100 test100 Aircraft' });
    const editIcon = rowLocator.locator('[data-testid="ModeEditIcon"]');

    await editIcon.click();
    await page.waitForTimeout(3000);

    // Edit asset type
    await page.getByLabel('Asset Type Name').click();
    await page.getByLabel('Asset Type Name').fill('Test100');
    await page.getByLabel('Parent Type').click();
    await page.getByRole('option', { name: 'Location > Aircraft', exact: true }).click();

    // Submit changes
    await page.getByRole('button', { name: 'SAVE' }).click();

    await page.waitForTimeout(6000);
    await page.close();
  });
});

test.describe('Update Measurement Module', () => {
  test('edit simple measurement under Acceleration', async ({ page }) => {
    await page.goto('https://test.pivotol.ai/login');

    // Login
    await page.getByLabel('Username *').click();
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').click();
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');
    await page.getByRole('button', { name: 'Log in' }).click();

    // Navigate to Assets -> Manage Assets
    await page.locator('div.MuiListItemText-root span', { hasText: 'Assets' }).click();
    await page.waitForTimeout(3000);
    await page.getByRole('menuitem', { name: 'Manage Assets' }).click();
    await page.waitForTimeout(8000);

    // Expand tree items
    await page.getByRole('treeitem', { name: 'AssetAllFlows' }).getByRole('button').click();
    await page.getByRole('treeitem', { name: 'Acceleration' }).getByRole('button').click();
    await page.waitForTimeout(3000);

    // Right-click on measurement named "simple"
    await page.locator('p:has-text("simple")').click({ button: 'right' });

    // Wait and click 'Edit'
    const editOption = page.getByRole('menuitem', { name: 'Edit' });
    await editOption.waitFor({ state: 'visible', timeout: 3000 });
    await editOption.click();

    // Fill out the measurement form
    await page.getByLabel('Tag *').click();
    await page.getByLabel('Tag *').fill('simple');
    await page.getByLabel('Description').click();
    await page.getByLabel('Description').fill('edit measure');
    await page.getByLabel('Select measurement type *').click();
    await page.getByRole('option', { name: 'Acceleration' }).click();
    await page.getByLabel('Select value type *').click();
    await page.getByRole('option', { name: 'lo lo alarm' }).click();
    await page.getByLabel('Select unit of measure').click();
    await page.getByRole('option', { name: 'ft/s²' }).click();
    await page.getByLabel('Select location').click();
    await page.getByRole('option', { name: 'Discharge' }).click();
    await page.getByLabel('Meter factor').click();
    await page.getByLabel('Meter factor').fill('5');

    // Submit the form
    await page.getByRole('button', { name: 'Submit' }).click();
    await page.close();
  });
});
test.describe('Dashboard Widget Module', () => {
  test('configure image widget', async ({ page }) => {
    await page.goto('https://test.pivotol.ai/login');

    // **Login**
    await page.getByLabel('Username *').click();
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').click();
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');
    await page.getByRole('button', { name: 'Log in' }).click();
    await page.waitForTimeout(3000);

    // **Navigate to Dashboard**
    await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
    //await page.waitForTimeout(5000);

    await page.getByRole('menuitem', { name: 'Add New' }).click();

    // Open widgets menu
    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(2000);

    // Drag and drop the image widget into the layout area
    await page.locator('#image').dragTo(page.locator('.react-grid-layout.layout'));
    await page.waitForTimeout(2000);

    // Re-open widgets menu
    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(4000);

    // Hover over the parent element to reveal options
    const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
    await parentElement.hover();

    // Locate and click the 'MoreVertIcon' options button
    const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
    await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
    await optionsIcon.click();

    // Click on settings menu item
    await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[2]');
    await page.waitForTimeout(5000);

    // Select the first radio option
    await page.locator('label:has([data-testid="RadioButtonUncheckedIcon"])').first().click();

    // **Select Asset**
    await page
      .locator('div')
      .filter({ hasText: /^Select Asset$/ })
      .getByLabel('Open')
      .click();
    await page.getByRole('option', { name: 'Brenes > MAINPANEL_MQTT', exact: true }).click();

    // **Select Measurement**
    await page.getByLabel('Select Measurement').click();
    await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();
    await page.waitForTimeout(1000);

    // **Click the Update button**
    await page.getByRole('button', { name: 'Update' }).click();
    await page.waitForTimeout(5000);

    await page.getByLabel('Time Range').click();
    await page.getByRole('button', { name: 'Last 90 days' }).click();
    await page.getByRole('button', { name: 'Apply' }).click();
    await page.waitForTimeout(3000);

    // **Close Page**
    await page.close();
  });
});

test.describe('Dashboard Title Widget Module', () => {
  test('add and configure title widget', async ({ page }) => {
    await page.goto('https://test.pivotol.ai/login');

    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');
    await page.getByRole('button', { name: 'Log in' }).click();
    await page.waitForTimeout(3000);

    await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
    await page.waitForTimeout(5000);

    await page.getByRole('menuitem', { name: 'Add New' }).click();
    await page.waitForTimeout(2000);
    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(2000);
    await page.locator('#title').dragTo(page.locator('.react-grid-layout.layout'));
    await page.waitForTimeout(2000);

    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(2000);

    const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
    await parentElement.hover();

    const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
    await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
    await optionsIcon.click();

    await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[2]');
    await page.waitForTimeout(5000);

    await page.getByLabel('Title', { exact: true }).fill('TEST TITLE');

    await page.getByRole('button', { name: 'Update' }).click();
    await page.waitForTimeout(3000);

    await page.close();
  });
});
test.describe('Alert Analytics Module', () => {
  test('analytics graph interaction', async ({ page }) => {
    await page.goto('https://test.pivotol.ai/login');

    // Login
    await page.getByLabel('Username *').click();
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').click();
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');
    await page.getByRole('button', { name: 'Log in' }).click();

    // Navigate to Alerts > Analytics
    await page.getByRole('button', { name: 'Alerts' }).click();
    await page.waitForTimeout(2000);
    await page.getByRole('menuitem', { name: 'Analytics' }).click();
    await page.waitForTimeout(2000);

    // Select Interval
    await page.getByLabel('Interval').click();
    await page.getByRole('option', { name: 'Daily' }).click();

    // Select Asset
    await page.getByLabel('Select Asset').click();
    await page.getByRole('combobox', { name: 'Select Asset' }).fill('mqtt');
    await page.getByRole('option', { name: 'Brenes > MAINPANEL_MQTT', exact: true }).click();

    // Select Measurement
    await page.getByLabel('Select Measurement').click();
    await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();

    // Interact with chart
    await page.locator('.nsewdrag').first().click();

    await page.waitForTimeout(3000);
    await page.close();
  });
});
test.describe('Alerts Module - Excursions', () => {
  test('navigate to Excursions page', async ({ page }) => {
    await page.goto('https://test.pivotol.ai/login');

    // Login
    await page.getByLabel('Username *').click();
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').click();
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');
    await page.getByRole('button', { name: 'Log in' }).click();

    // Navigate to Alerts > Excursions
    await page.getByRole('button', { name: 'Alerts' }).click();
    await page.getByRole('menuitem', { name: 'Excursions' }).click();

    await page.waitForTimeout(2000); // Wait to ensure the page is loaded
    await page.close();
  });
});
test.describe('Dashboard stats', () => {
  test('configure Stats widget in dashboard (without checkbox)', async ({ page }) => {
    await page.goto('https://test.pivotol.ai/login');

    await page.getByLabel('Username *').click();
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').click();
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');
    await page.getByRole('button', { name: 'Log in' }).click();
    await page.waitForTimeout(3000);

    await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
    await page.getByRole('menuitem', { name: 'Add New' }).click();

    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(2000);
    await page.locator('#stats').dragTo(page.locator('.react-grid-layout.layout'));
    await page.waitForTimeout(2000);

    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(5000);

    const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
    await parentElement.hover();

    const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
    await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
    await optionsIcon.click();

    await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[2]');
    await page.waitForTimeout(5000);

    await page
      .locator('div')
      .filter({ hasText: /^Select Asset$/ })
      .getByLabel('Open')
      .click();
    await page.getByRole('option', { name: 'Brenes > MAINPANEL_MQTT', exact: true }).click();

    await page.getByLabel('Select Measurement').click();
    await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();
    await page.waitForTimeout(1000);

    await page.getByRole('button', { name: 'Update' }).click();
    await page.waitForTimeout(5000);

    await page.getByLabel('Time Range').click();
    await page.getByRole('button', { name: 'Last 90 days' }).click();
    await page.getByRole('button', { name: 'Apply' }).click();
    await page.waitForTimeout(3000);
    await page.close();
  });
});
test.describe('Widgets Module - Heatmap', () => {
  test('drag and configure Heatmap widget', async ({ page }) => {
    await page.goto('https://test.pivotol.ai/login');

    // Login
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');
    await page.getByRole('button', { name: 'Log in' }).click();
    await page.waitForTimeout(3000);

    // Navigate to Dashboard
    await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
    await page.getByRole('menuitem', { name: 'Add New' }).click();

    // Open widgets menu
    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(2000);

    // Drag and drop the Heatmap widget
    const heatmapWidgetToDrag = page.locator('span.MuiListItemText-primary', {
      hasText: 'Heatmap',
    });
    const layoutArea = page.locator('.react-grid-layout.layout');
    await heatmapWidgetToDrag.dragTo(layoutArea);
    await page.waitForTimeout(2000);

    // Re-open widgets menu
    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(2000);

    // Hover over the widget and click options
    const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
    await parentElement.hover();
    const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
    await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
    await optionsIcon.click();

    // Click settings menu item
    await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[4]');
    await page.waitForTimeout(5000);

    // Select Asset
    await page
      .locator('div')
      .filter({ hasText: /^Select Asset$/ })
      .getByLabel('Open')
      .click();
    await page.getByRole('option', { name: 'Brenes > MAINPANEL_MQTT', exact: true }).click();

    // Select Measurement
    await page.getByLabel('Select Measurement').click();
    await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();
    await page.waitForTimeout(1000);

    // Update widget
    await page.getByRole('button', { name: 'Update' }).click();
    await page.waitForTimeout(5000);

    await page.getByLabel('Time Range').click();
    await page.getByRole('button', { name: 'Last 90 days' }).click();
    await page.getByRole('button', { name: 'Apply' }).click();
    await page.waitForTimeout(3000);

    await page.close();
  });
});
test.describe('Measurement Browser Module', () => {
  test('Measurement browser flow', async ({ page }) => {
    // Opening the login page
    await page.goto('https://test.pivotol.ai/login');

    // Fill in Username and Password
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');

    // Click on Login Button
    await page.click('#__next > div > div > form > div > button');
    await page.waitForTimeout(3000); // Wait for login to process
    await page.setViewportSize({ width: 1920, height: 1080 });

    // Navigate to Assets -> Measurement Browser
    await page.getByRole('button', { name: 'Assets' }).click();
    await page.waitForTimeout(3000);
    await page.getByRole('menuitem', { name: 'Measurement Browser' }).click();
    await page.waitForTimeout(5000);

    // Click the specific measurement row's "Trend" link
    await page
      .getByRole('row', {
        name: 'Animesh/WindTurbine AC Generator Animesh/Windturbine/Active Power kW Calculation Power N/A N/A',
      })
      .locator('a')
      .click();

    // Select time range
    await page.getByLabel('Time Range').click();
    await page.getByRole('button', { name: 'Last 90 days' }).click();
    await page.getByRole('button', { name: 'Apply' }).click();

    // Check the checkbox and submit
    await page.getByRole('checkbox').check();
    await page.getByRole('button', { name: 'Submit' }).click();

    await page.waitForTimeout(3000);
    await page.close();
  });
});

test.describe('drag and configure Trend Chart widget', () => {
  test('should login, add Chart widget, drag Trend, and configure settings', async ({ page }) => {
    await page.goto('https://test.pivotol.ai/login');

    // **Login**
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');
    await page.getByRole('button', { name: 'Log in' }).click();
    await page.waitForTimeout(3000);

    // **Navigate to Dashboard**
    await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
    await page.waitForTimeout(5000);

    // **Add New Widget**
    await page.getByRole('menuitem', { name: 'Add New' }).click();

    // **Open widgets menu**
    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(4000);

    // **Click "Chart"**
    await page.locator('span.MuiTypography-root', { hasText: 'Chart' }).click();

    // **Wait for "Trend" to appear**
    await page.waitForSelector('text=Trend', { timeout: 10000 });

    // **Drag and drop "Trend"**
    const trendWidget = page.getByText('Trend', { exact: true });
    const dropTarget = page.locator('.react-grid-layout.layout');
    await trendWidget.dragTo(dropTarget);
    await page.waitForTimeout(2000);

    // **Reopen widgets menu to trigger settings**
    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(2000);

    // **Hover to reveal options**
    const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
    await parentElement.hover();

    // **Click MoreVertIcon**
    const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
    await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
    await optionsIcon.click();

    // **Click "Settings" menu item**
    await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[4]');
    await page.waitForTimeout(6000);

    // **Configure Asset**
    await page
      .locator('div')
      .filter({ hasText: /^Select Asset$/ })
      .getByLabel('Open')
      .click();
    await page.getByRole('option', { name: 'Brenes > MAINPANEL', exact: true }).click();

    // **Configure Measurement**
    await page.getByLabel('Select Measurement').click();
    await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();
    await page.waitForTimeout(1000);

    // **Apply settings**
    await page.getByRole('button', { name: 'Update' }).click();
    await page.waitForTimeout(5000);

    await page.getByLabel('Time Range').click();
    await page.getByRole('button', { name: 'Last 90 days' }).click();
    await page.getByRole('button', { name: 'Apply' }).click();
    await page.waitForTimeout(3000);

    // **Close Page**
    await page.close();
  });
});
test.describe('drag and configure BarChart widgets', () => {
  test('drag and configure bar Chart widget', async ({ page }) => {
    await page.goto('https://test.pivotol.ai/login');

    // **Login**
    await page.getByLabel('Username *').click();
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').click();
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');
    await page.getByRole('button', { name: 'Log in' }).click();
    await page.waitForTimeout(3000);

    // **Navigate to Dashboard**
    await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
    //await page.waitForTimeout(5000);

    await page.getByRole('menuitem', { name: 'Add New' }).click();

    // Open widgets menu
    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(4000);

    // Click on the "Chart" option
    await page.locator('span.MuiTypography-root', { hasText: 'Chart' }).click();

    // Ensure the "Bar" element is visible
    await page.waitForSelector('text=Bar', { timeout: 10000 });

    // Drag and drop the "Bar" widget
    const trendWidget = page.getByText('Bar', { exact: true });
    const dropTarget = page.locator('.react-grid-layout.layout'); // Replace with the actual drop zone selector
    await trendWidget.dragTo(dropTarget);
    await page.waitForTimeout(2000);

    // Re-open widgets menu
    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(2000);

    // Hover over the parent element to reveal options
    const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
    await parentElement.hover();

    // Locate and click the 'MoreVertIcon' options button
    const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
    await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
    await optionsIcon.click();

    // Click on settings menu item
    await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[4]');
    await page.waitForTimeout(6000);

    // **Select Asset**
    await page
      .locator('div')
      .filter({ hasText: /^Select Asset$/ })
      .getByLabel('Open')
      .click();
    await page.getByRole('option', { name: 'Brenes > MAINPANEL', exact: true }).click();

    // **Select Measurement**
    await page.getByLabel('Select Measurement').click();
    await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();
    await page.waitForTimeout(1000);

    // **Click the Update button**
    await page.getByRole('button', { name: 'Update' }).click();
    await page.waitForTimeout(5000);

    await page.getByLabel('Time Range').click();
    await page.getByRole('button', { name: 'Last 90 days' }).click();
    await page.getByRole('button', { name: 'Apply' }).click();
    await page.waitForTimeout(3000);
    // **Close Page**
    await page.close();
  });
});
test.describe('drag and configure Gauge Chart widgets', () => {
  test('drag and configure Gauge widget', async ({ page }) => {
    await page.goto('https://test.pivotol.ai/login');

    // Login
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');
    await page.getByRole('button', { name: 'Log in' }).click();
    await page.waitForTimeout(3000);

    // Navigate to Dashboard
    await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
    await page.getByRole('menuitem', { name: 'Add New' }).click();

    // Open widgets menu and drag Gauge widget
    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(2000);

    const gaugeWidget = page.locator('span.MuiListItemText-primary', { hasText: 'Gauge' });
    const layoutArea = page.locator('.react-grid-layout.layout');
    await gaugeWidget.dragTo(layoutArea);
    await page.waitForTimeout(2000);

    // Open widget options
    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(2000);
    const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
    await parentElement.hover();
    const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
    await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
    await optionsIcon.click();

    // Click settings
    await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[4]');
    await page.waitForTimeout(5000);

    // Configure asset and measurement
    await page
      .locator('div')
      .filter({ hasText: /^Select Asset$/ })
      .getByLabel('Open')
      .click();
    await page.getByRole('option', { name: 'Brenes > MAINPANEL', exact: true }).click();
    await page.getByLabel('Select Measurement').click();
    await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();
    await page.waitForTimeout(1000);

    // Update
    await page.getByRole('button', { name: 'Update' }).click();
    await page.waitForTimeout(5000);

    // Time range selection
    await page.getByLabel('Time Range').click();
    await page.getByRole('button', { name: 'Last 90 days' }).click();
    await page.getByRole('button', { name: 'Apply' }).click();
    await page.waitForTimeout(3000);

    await page.close();
  });
});
test.describe('drag and configure Bullet Chart widgets', () => {
  test('drag and configure Bullet Chart widget', async ({ page }) => {
    await page.goto('https://test.pivotol.ai/login');

    // Login
    await page.getByLabel('Username *').click();
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').click();
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');
    await page.getByRole('button', { name: 'Log in' }).click();
    await page.waitForTimeout(3000);

    // Navigate to Dashboard
    await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
    await page.waitForTimeout(5000);
    await page.getByRole('menuitem', { name: 'Add New' }).click();

    // Open widgets menu and click Chart
    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(4000);
    await page.locator('span.MuiTypography-root', { hasText: 'Chart' }).click();

    // Drag and drop the Bullet widget
    await page.waitForSelector('text=Bullet', { timeout: 10000 });
    const bulletWidget = page.getByText('Bullet', { exact: true });
    const dropTarget = page.locator('.react-grid-layout.layout');
    await bulletWidget.dragTo(dropTarget);
    await page.waitForTimeout(2000);

    // Open widget options
    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(2000);
    const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
    await parentElement.hover();
    const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
    await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
    await optionsIcon.click();

    // Open settings
    await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[4]');
    await page.waitForTimeout(6000);

    // Select asset and measurement
    await page
      .locator('div')
      .filter({ hasText: /^Select Asset$/ })
      .getByLabel('Open')
      .click();
    await page.getByRole('option', { name: 'Brenes > MAINPANEL', exact: true }).click();
    await page.getByLabel('Select Measurement').click();
    await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();
    await page.waitForTimeout(1000);

    // Update and select time range
    await page.getByRole('button', { name: 'Update' }).click();
    await page.waitForTimeout(5000);
    await page.getByLabel('Time Range').click();
    await page.getByRole('button', { name: 'Last 90 days' }).click();
    await page.getByRole('button', { name: 'Apply' }).click();
    await page.waitForTimeout(3000);

    await page.close();
  });
});
test.describe('drag and configure Chart widgets', () => {
  test('drag and configure Dashboardtile widget', async ({ page }) => {
    await page.goto('https://test.pivotol.ai/login');

    // **Login**
    await page.getByLabel('Username *').fill('test');
    await page.getByLabel('Password *').fill('Br0mpt0n!0T');
    await page.getByRole('button', { name: 'Log in' }).click();
    await page.waitForTimeout(3000);

    // **Navigate to Dashboard**
    await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
    await page.getByRole('menuitem', { name: 'Add New' }).click();

    // Open widgets menu
    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(2000);

    // ✅ Drag and drop the Dashboardtile widget (uses 'Dashboard' as text)
    const dashboardTileWidget = page
      .locator('span.MuiListItemText-primary', { hasText: 'Dashboard' })
      .nth(1);
    const layoutArea = page.locator('.react-grid-layout.layout');
    await dashboardTileWidget.dragTo(layoutArea);
    await page.waitForTimeout(2000);

    // Re-open widgets menu
    await page.locator('#widgets-icon').click();
    await page.waitForTimeout(2000);

    // Hover to reveal options
    const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
    await parentElement.hover();

    const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
    await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
    await optionsIcon.click();

    // Open settings menu
    await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[2]');
    await page.waitForTimeout(5000);

    // ✅ Select Asset
    await page
      .locator('div')
      .filter({ hasText: /^Select Asset$/ })
      .getByLabel('Open')
      .click();
    await page.getByRole('option', { name: 'Brenes > MAINPANEL_MQTT', exact: true }).click();

    // ✅ Select Measurement (Dashboard Template)
    await page.getByLabel('Dashoard Template').click();
    await page.getByRole('option', { name: 'sp temp-0603', exact: true }).click();
    await page.waitForTimeout(1000);

    // ✅ Update widget
    await page.getByRole('button', { name: 'Update' }).click();
    await page.waitForTimeout(5000);

    // ✅ Apply Time Range
    await page.getByLabel('Time Range').click();
    await page.getByRole('button', { name: 'Last 90 days' }).click();
    await page.getByRole('button', { name: 'Apply' }).click();
    await page.waitForTimeout(3000);

    await page.close();
  });
  test.describe('KPI Sparkline widgets', () => {
    test('drag and configure KPI Sparkline widget', async ({ page }) => {
      await page.goto('https://test.pivotol.ai/login');

      // **Login**
      await page.getByLabel('Username *').click();
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').click();
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.waitForTimeout(3000);

      // **Navigate to Dashboard**
      await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
      await page.waitForTimeout(5000);
      await page.getByRole('menuitem', { name: 'Add New' }).click();

      // **Open widgets menu**
      await page.locator('#widgets-icon').click();
      await page.waitForTimeout(4000);

      // **Click on "KPI" category**
      await page.locator('span.MuiTypography-root', { hasText: 'KPI' }).click();

      // **Wait for KPI Sparkline to appear**
      await page.waitForSelector('text=KPI Sparkline', { timeout: 10000 });

      // **Drag and drop KPI Sparkline widget**
      const sparklineWidget = page.getByText('KPI Sparkline', { exact: true });
      const dropTarget = page.locator('.react-grid-layout.layout');
      await sparklineWidget.dragTo(dropTarget);
      await page.waitForTimeout(2000);

      // **Re-open widgets menu**
      await page.locator('#widgets-icon').click();
      await page.waitForTimeout(2000);

      // **Hover to reveal options**
      const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
      await parentElement.hover();

      const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
      await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
      await optionsIcon.click();

      // **Open Settings**
      await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[2]');
      await page.waitForTimeout(6000);

      // **Select Asset**
      await page
        .locator('div')
        .filter({ hasText: /^Select Asset$/ })
        .getByLabel('Open')
        .click();
      await page.getByRole('option', { name: 'Brenes > MAINPANEL', exact: true }).click();

      // **Select Measurement**
      await page.getByLabel('Select Measurement').click();
      await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();
      await page.waitForTimeout(1000);

      // **Click Update**
      await page.getByRole('button', { name: 'Update' }).click();
      await page.waitForTimeout(5000);

      // **Select Time Range**
      await page.getByLabel('Time Range').click();
      await page.getByRole('button', { name: 'Last 90 days' }).click();
      await page.getByRole('button', { name: 'Apply' }).click();
      await page.waitForTimeout(3000);

      await page.close();
    });
  });
  test.describe(' KPI Percentage widgets', () => {
    test('drag and configure KPI Percentage widget', async ({ page }) => {
      await page.goto('https://test.pivotol.ai/login');

      // **Login**
      await page.getByLabel('Username *').click();
      await page.getByLabel('Username *').fill('test');
      await page.getByLabel('Password *').click();
      await page.getByLabel('Password *').fill('Br0mpt0n!0T');
      await page.getByRole('button', { name: 'Log in' }).click();
      await page.waitForTimeout(3000);

      // **Navigate to Dashboard**
      await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
      await page.waitForTimeout(5000);
      await page.getByRole('menuitem', { name: 'Add New' }).click();

      // **Open widgets menu**
      await page.locator('#widgets-icon').click();
      await page.waitForTimeout(4000);

      // **Click on "KPI"**
      await page.locator('span.MuiTypography-root', { hasText: 'KPI' }).click();

      // **Wait for KPI Percentage to appear**
      await page.waitForSelector('text=KPI Percentage', { timeout: 10000 });

      // **Drag and drop the KPI Percentage widget**
      const kpiWidget = page.getByText('KPI Percentage', { exact: true });
      const dropTarget = page.locator('.react-grid-layout.layout');
      await kpiWidget.dragTo(dropTarget);
      await page.waitForTimeout(2000);

      // **Re-open widgets menu**
      await page.locator('#widgets-icon').click();
      await page.waitForTimeout(2000);

      // **Hover to reveal options**
      const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
      await parentElement.hover();

      const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
      await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
      await optionsIcon.click();

      // **Open settings menu**
      await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[2]');
      await page.waitForTimeout(6000);

      // **Select Asset**
      await page
        .locator('div')
        .filter({ hasText: /^Select Asset$/ })
        .getByLabel('Open')
        .click();
      await page.getByRole('option', { name: 'Brenes > MAINPANEL', exact: true }).click();

      // **Select Measurement**
      await page.getByLabel('Select Measurement').click();
      await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();
      await page.waitForTimeout(1000);

      // **Click Update**
      await page.getByRole('button', { name: 'Update' }).click();
      await page.waitForTimeout(3000);

      // **Select Time Range**
      await page.getByLabel('Time Range').click();
      await page.getByRole('button', { name: 'Last 90 days' }).click();
      await page.getByRole('button', { name: 'Apply' }).click();
      await page.waitForTimeout(3000);

      await page.close();
    });
  });
});
