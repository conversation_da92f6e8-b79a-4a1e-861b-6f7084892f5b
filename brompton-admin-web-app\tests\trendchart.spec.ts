import { test, expect } from '@playwright/test';

test('drag and configure Trend Chart widget', async ({ page }) => {
  await page.goto('https://test.pivotol.ai/login');

  // **Login**
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();
  await page.waitForTimeout(3000);

  // **Navigate to Dashboard**
  await page.locator('svg path[d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"]').click();
  await page.waitForTimeout(5000);

  await page.getByRole('menuitem', { name: 'Add New' }).click();

  // Open widgets menu
  await page.locator('#widgets-icon').click();
  await page.waitForTimeout(4000);

  // Click on the "Chart" option
  await page.locator('span.MuiTypography-root', { hasText: 'Chart' }).click();

  // Ensure the "Trend" element is visible
  await page.waitForSelector('text=Trend', { timeout: 10000 });

  // Drag and drop the "Trend" widget
  const trendWidget = page.getByText('Trend', { exact: true });
  const dropTarget = page.locator('.react-grid-layout.layout'); // Replace with the actual drop zone selector
  await trendWidget.dragTo(dropTarget);
  await page.waitForTimeout(2000);

  // Re-open widgets menu
  await page.locator('#widgets-icon').click();
  await page.waitForTimeout(2000);

  // Hover over the parent element to reveal options
  const parentElement = page.locator('.MuiBox-root.css-1j35o1p');
  await parentElement.hover();

  // Locate and click the 'MoreVertIcon' options button
  const optionsIcon = page.locator('[data-testid="MoreVertIcon"]');
  await optionsIcon.waitFor({ state: 'visible', timeout: 120000 });
  await optionsIcon.click();

  // Click on settings menu item
  await page.click('//*[@id="widget-settings-menu-1"]/div[3]/ul/li[4]');
  await page.waitForTimeout(6000);

  // **Select Asset**
  await page
    .locator('div')
    .filter({ hasText: /^Select Asset$/ })
    .getByLabel('Open')
    .click();
  await page.getByRole('option', { name: 'Brenes > MAINPANEL', exact: true }).click();

  // **Select Measurement**
  await page.getByLabel('Select Measurement').click();
  await page.getByRole('option', { name: 'Averagecurrent', exact: true }).click();
  await page.waitForTimeout(1000);

  // **Click the Update button**
  await page.getByRole('button', { name: 'Update' }).click();
  await page.waitForTimeout(5000);

  await page.getByLabel('Time Range').click();
  await page.getByRole('button', { name: 'Last 90 days' }).click();
  await page.getByRole('button', { name: 'Apply' }).click();
  await page.waitForTimeout(3000);
  // **Close Page**
  await page.close();
});
