import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://test.pivotol.ai/login');
  await page.getByLabel('Username *').click();
  await page.getByLabel('Username *').fill('test');
  await page.getByLabel('Password *').click();
  await page.getByLabel('Password *').fill('Br0mpt0n!0T');
  await page.getByRole('button', { name: 'Log in' }).click();

  // Navigate to Assets -> Manage Assets
  await page.locator('div.MuiListItemText-root span', { hasText: 'Assets' }).click();
  await page.waitForTimeout(3000);
  await page.getByRole('menuitem', { name: 'Manage Assets' }).click();
  await page.waitForTimeout(8000);

  await page.getByRole('treeitem', { name: 'AssetAllFlows' }).getByRole('button').click();
  await page.getByRole('treeitem', { name: 'Acceleration' }).getByRole('button').click();
  await page.waitForTimeout(3000);
  await page.locator('p:has-text("simple")').click({ button: 'right' });

  // Wait explicitly for the menu to appear before clicking 'Edit'
  const editOption = page.getByRole('menuitem', { name: 'Edit' });
  await editOption.waitFor({ state: 'visible', timeout: 3000 });
  await editOption.click();

  // Proceed with form filling
  await page.getByLabel('Tag *').click();
  await page.getByLabel('Tag *').fill('simple');
  await page.getByLabel('Description').click();
  await page.getByLabel('Description').fill('edit measure');
  await page.getByLabel('Select measurement type *').click();
  await page.getByRole('option', { name: 'Acceleration' }).click();
  await page.getByLabel('Select value type *').click();
  await page.getByRole('option', { name: 'lo lo alarm' }).click();
  await page.getByLabel('Select unit of measure').click();
  await page.getByRole('option', { name: 'ft/s²' }).click();
  await page.getByLabel('Select location').click();
  await page.getByRole('option', { name: 'Discharge' }).click();
  await page.getByLabel('Meter factor').click();
  await page.getByLabel('Meter factor').fill('5');
  await page.getByRole('button', { name: 'Submit' }).click();
  await page.close();
});
